--
-- PostgreSQL database dump
--

-- Dumped from database version 15.14 (Debian 15.14-1.pgdg12+1)
-- Dumped by pg_dump version 17.5 (Ubuntu 17.5-0ubuntu0.25.04.1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: vector; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS vector WITH SCHEMA public;


--
-- Name: EXTENSION vector; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION vector IS 'vector data type and ivfflat and hnsw access methods';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: agents; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.agents (
    id integer NOT NULL,
    name character varying NOT NULL,
    workflow json,
    is_active boolean NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    description text,
    category character varying,
    created_by integer NOT NULL
);


ALTER TABLE public.agents OWNER TO "user";

--
-- Name: agents_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.agents_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.agents_id_seq OWNER TO "user";

--
-- Name: agents_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.agents_id_seq OWNED BY public.agents.id;


--
-- Name: alembic_version; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.alembic_version (
    version_num character varying(32) NOT NULL
);


ALTER TABLE public.alembic_version OWNER TO "user";

--
-- Name: call_history; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.call_history (
    id integer NOT NULL,
    customer_id integer NOT NULL,
    number_id integer,
    call_sid character varying,
    call_status character varying,
    call_duration integer,
    call_metadata json,
    system_prompt text,
    first_message text,
    created_at timestamp with time zone DEFAULT now(),
    completed_at timestamp with time zone,
    user_id integer
);


ALTER TABLE public.call_history OWNER TO "user";

--
-- Name: call_history_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.call_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.call_history_id_seq OWNER TO "user";

--
-- Name: call_history_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.call_history_id_seq OWNED BY public.call_history.id;


--
-- Name: conversation_logs; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.conversation_logs (
    id integer NOT NULL,
    call_history_id integer NOT NULL,
    event_type character varying,
    "timestamp" timestamp with time zone DEFAULT now(),
    data json,
    source character varying,
    user_id integer
);


ALTER TABLE public.conversation_logs OWNER TO "user";

--
-- Name: conversation_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.conversation_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.conversation_logs_id_seq OWNER TO "user";

--
-- Name: conversation_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.conversation_logs_id_seq OWNED BY public.conversation_logs.id;


--
-- Name: customers; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.customers (
    id integer NOT NULL,
    name character varying NOT NULL,
    domain character varying NOT NULL,
    phone_number character varying,
    address character varying,
    city character varying,
    state character varying,
    zip_code character varying,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone,
    user_id integer
);


ALTER TABLE public.customers OWNER TO "user";

--
-- Name: customers_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.customers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.customers_id_seq OWNER TO "user";

--
-- Name: customers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.customers_id_seq OWNED BY public.customers.id;


--
-- Name: documents; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.documents (
    id integer NOT NULL,
    customer_id integer NOT NULL,
    title character varying NOT NULL,
    body_md text NOT NULL,
    tags character varying[],
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone,
    user_id integer
);


ALTER TABLE public.documents OWNER TO "user";

--
-- Name: documents_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.documents_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.documents_id_seq OWNER TO "user";

--
-- Name: documents_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.documents_id_seq OWNED BY public.documents.id;


--
-- Name: embeddings; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.embeddings (
    id integer NOT NULL,
    customer_id integer NOT NULL,
    document_id integer NOT NULL,
    chunk_text text NOT NULL,
    embedding public.vector(1536) NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    user_id integer
);


ALTER TABLE public.embeddings OWNER TO "user";

--
-- Name: embeddings_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.embeddings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.embeddings_id_seq OWNER TO "user";

--
-- Name: embeddings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.embeddings_id_seq OWNED BY public.embeddings.id;


--
-- Name: jobs; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.jobs (
    id integer NOT NULL,
    customer_id integer NOT NULL,
    service_type character varying NOT NULL,
    scheduled_time timestamp with time zone NOT NULL,
    status character varying,
    notes text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone,
    user_id integer
);


ALTER TABLE public.jobs OWNER TO "user";

--
-- Name: jobs_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.jobs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.jobs_id_seq OWNER TO "user";

--
-- Name: jobs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.jobs_id_seq OWNED BY public.jobs.id;


--
-- Name: performance_metrics; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.performance_metrics (
    id integer NOT NULL,
    request_id character varying,
    chat_history_id character varying,
    service character varying NOT NULL,
    action character varying NOT NULL,
    latency double precision NOT NULL,
    "timestamp" timestamp with time zone DEFAULT now(),
    user_id integer
);


ALTER TABLE public.performance_metrics OWNER TO "user";

--
-- Name: performance_metrics_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.performance_metrics_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.performance_metrics_id_seq OWNER TO "user";

--
-- Name: performance_metrics_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.performance_metrics_id_seq OWNED BY public.performance_metrics.id;


--
-- Name: phone_numbers; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.phone_numbers (
    id integer NOT NULL,
    customer_id integer NOT NULL,
    provider character varying NOT NULL,
    account_sid character varying NOT NULL,
    phone_number character varying NOT NULL,
    friendly_name character varying,
    capabilities character varying[],
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone,
    default_agent_id integer,
    routing_config json,
    is_active boolean DEFAULT true NOT NULL,
    business_hours json,
    after_hours_agent_id integer,
    max_queue_time integer DEFAULT 300,
    user_id integer
);


ALTER TABLE public.phone_numbers OWNER TO "user";

--
-- Name: phone_numbers_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.phone_numbers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.phone_numbers_id_seq OWNER TO "user";

--
-- Name: phone_numbers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.phone_numbers_id_seq OWNED BY public.phone_numbers.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.users (
    id integer NOT NULL,
    email character varying,
    full_name character varying,
    hashed_password character varying NOT NULL,
    is_active boolean,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone,
    reset_password_token character varying,
    reset_password_token_expires_at timestamp with time zone
);


ALTER TABLE public.users OWNER TO "user";

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO "user";

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: agents id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.agents ALTER COLUMN id SET DEFAULT nextval('public.agents_id_seq'::regclass);


--
-- Name: call_history id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.call_history ALTER COLUMN id SET DEFAULT nextval('public.call_history_id_seq'::regclass);


--
-- Name: conversation_logs id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.conversation_logs ALTER COLUMN id SET DEFAULT nextval('public.conversation_logs_id_seq'::regclass);


--
-- Name: customers id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.customers ALTER COLUMN id SET DEFAULT nextval('public.customers_id_seq'::regclass);


--
-- Name: documents id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.documents ALTER COLUMN id SET DEFAULT nextval('public.documents_id_seq'::regclass);


--
-- Name: embeddings id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.embeddings ALTER COLUMN id SET DEFAULT nextval('public.embeddings_id_seq'::regclass);


--
-- Name: jobs id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.jobs ALTER COLUMN id SET DEFAULT nextval('public.jobs_id_seq'::regclass);


--
-- Name: performance_metrics id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.performance_metrics ALTER COLUMN id SET DEFAULT nextval('public.performance_metrics_id_seq'::regclass);


--
-- Name: phone_numbers id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.phone_numbers ALTER COLUMN id SET DEFAULT nextval('public.phone_numbers_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Data for Name: agents; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.agents (id, name, workflow, is_active, created_at, updated_at, description, category, created_by) FROM stdin;
1	home service	{}	f	2025-08-20 22:45:40.934742+00	2025-08-20 22:45:40.934742+00	AI agent for home services	\N	1
2	Customer Support Agent Workflow Copy	{"name": "Customer Support Agent Workflow", "description": "Orchestrates the customer support agent's conversational flow.", "active": true, "id": "agent-workflow-1", "nodes": [{"id": "get_user_input_node", "name": "Get User Input", "type": "pythonFunction", "functionPath": "orchestrator.services.websocket.io.get_user_input", "dependencies": ["db"]}, {"id": "llm_conversation_node", "name": "LLM Conversation", "type": "pythonFunction", "functionPath": "orchestrator.nodes.llm.generate_response", "dependencies": ["llm_service", "db"]}], "connections": {"get_user_input_node": {"conditional": {"type": "pythonFunction", "conditionFunctionPath": "orchestrator.graph.should_continue", "paths": {"True": [{"node": "llm_conversation_node", "type": "main", "index": 0}], "False": [{"node": "END", "type": "main", "index": 0}]}}}, "llm_conversation_node": {"conditional": {"type": "pythonFunction", "conditionFunctionPath": "orchestrator.graph.should_continue_on_error", "paths": {"True": [{"node": "get_user_input_node", "type": "main", "index": 0}], "False": [{"node": "END", "type": "main", "index": 0}]}}}}, "start_node_id": "get_user_input_node", "first_message": "Hello! How can I assist you today?", "speech_config": {"stt_vad_threshold": 0.7, "tts_voice_id": "default", "tts_speaking_rate": 1}, "conversation_settings": {"silence_timeout_seconds": 10, "reprompt_messages": ["Are you still there?", "Did you have any other questions?"], "max_reprompts": 2, "no_input_fallback_message": "I didn't hear anything. Can you please repeat that?", "no_match_fallback_message": "I'm sorry, I didn't understand that. Could you rephrase your request?"}, "tools": [{"type": "webhook", "name": "book_appointment", "description": "book a 30-minute appointment", "api_schema": {"url": "https://api.cal.com/v2/bookings", "method": "POST", "request_body_schema": {"id": "body", "type": "object", "description": "Timeslot to book", "properties": [{"id": "start", "type": "string", "value_type": "llm_prompt", "description": "The start time of the booking in ISO 8601 format in UTC timezone.", "required": true}, {"id": "eventTypeSlug", "type": "string", "value_type": "constant", "constant_value": "30min", "required": true}]}, "request_headers": [{"type": "value", "name": "Content-Type", "value": "application/json"}]}}]}	f	2025-08-21 01:12:33.547476+00	2025-08-21 01:12:33.547476+00	Orchestrates the customer support agent's conversational flow.	\N	1
\.


--
-- Data for Name: alembic_version; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.alembic_version (version_num) FROM stdin;
903bb4ced89f
\.


--
-- Data for Name: call_history; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.call_history (id, customer_id, number_id, call_sid, call_status, call_duration, call_metadata, system_prompt, first_message, created_at, completed_at, user_id) FROM stdin;
\.


--
-- Data for Name: conversation_logs; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.conversation_logs (id, call_history_id, event_type, "timestamp", data, source, user_id) FROM stdin;
\.


--
-- Data for Name: customers; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.customers (id, name, domain, phone_number, address, city, state, zip_code, created_at, updated_at, user_id) FROM stdin;
1	Test Customer	test.customer	\N	\N	\N	\N	\N	2025-08-20 22:14:14.979383+00	\N	\N
\.


--
-- Data for Name: documents; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.documents (id, customer_id, title, body_md, tags, created_at, updated_at, user_id) FROM stdin;
\.


--
-- Data for Name: embeddings; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.embeddings (id, customer_id, document_id, chunk_text, embedding, created_at, user_id) FROM stdin;
\.


--
-- Data for Name: jobs; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.jobs (id, customer_id, service_type, scheduled_time, status, notes, created_at, updated_at, user_id) FROM stdin;
\.


--
-- Data for Name: performance_metrics; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.performance_metrics (id, request_id, chat_history_id, service, action, latency, "timestamp", user_id) FROM stdin;
\.


--
-- Data for Name: phone_numbers; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.phone_numbers (id, customer_id, provider, account_sid, phone_number, friendly_name, capabilities, created_at, updated_at, default_agent_id, routing_config, is_active, business_hours, after_hours_agent_id, max_queue_time, user_id) FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.users (id, email, full_name, hashed_password, is_active, created_at, updated_at, reset_password_token, reset_password_token_expires_at) FROM stdin;
1	<EMAIL>	Pankaj Kumar	$2b$12$pydmA3Op13QAvWTguqFHYOE7TBSHCffEb7Fes4ftTE7M6lg214.n6	t	2025-08-20 22:14:20.697447+00	\N	\N	\N
2	<EMAIL>	Test User	$2b$12$vLiu8PYUbVTUEJayISM3CuEpHytxq5mYZuHiS5Iu9T9iiY6zeLpPi	t	2025-08-21 20:52:00.219464+00	\N	\N	\N
3	<EMAIL>	Test User	$2b$12$N7e5/Lpyxhx2aQ/0Tc5SYe1VEcyWXdYTkG5ruHiiAMzNpALE8Jfc6	t	2025-08-21 22:18:48.783427+00	\N	\N	\N
\.


--
-- Name: agents_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.agents_id_seq', 2, true);


--
-- Name: call_history_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.call_history_id_seq', 1, false);


--
-- Name: conversation_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.conversation_logs_id_seq', 1, false);


--
-- Name: customers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.customers_id_seq', 3, true);


--
-- Name: documents_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.documents_id_seq', 1, false);


--
-- Name: embeddings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.embeddings_id_seq', 1, false);


--
-- Name: jobs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.jobs_id_seq', 1, false);


--
-- Name: performance_metrics_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.performance_metrics_id_seq', 1, false);


--
-- Name: phone_numbers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.phone_numbers_id_seq', 1, false);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.users_id_seq', 3, true);


--
-- Name: agents agents_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.agents
    ADD CONSTRAINT agents_pkey PRIMARY KEY (id);


--
-- Name: alembic_version alembic_version_pkc; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.alembic_version
    ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);


--
-- Name: call_history call_history_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.call_history
    ADD CONSTRAINT call_history_pkey PRIMARY KEY (id);


--
-- Name: conversation_logs conversation_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.conversation_logs
    ADD CONSTRAINT conversation_logs_pkey PRIMARY KEY (id);


--
-- Name: customers customers_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.customers
    ADD CONSTRAINT customers_pkey PRIMARY KEY (id);


--
-- Name: documents documents_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_pkey PRIMARY KEY (id);


--
-- Name: embeddings embeddings_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.embeddings
    ADD CONSTRAINT embeddings_pkey PRIMARY KEY (id);


--
-- Name: jobs jobs_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.jobs
    ADD CONSTRAINT jobs_pkey PRIMARY KEY (id);


--
-- Name: performance_metrics performance_metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.performance_metrics
    ADD CONSTRAINT performance_metrics_pkey PRIMARY KEY (id);


--
-- Name: phone_numbers phone_numbers_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.phone_numbers
    ADD CONSTRAINT phone_numbers_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: ix_agents_id; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_agents_id ON public.agents USING btree (id);


--
-- Name: ix_agents_name; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_agents_name ON public.agents USING btree (name);


--
-- Name: ix_call_history_call_sid; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX ix_call_history_call_sid ON public.call_history USING btree (call_sid);


--
-- Name: ix_call_history_id; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_call_history_id ON public.call_history USING btree (id);


--
-- Name: ix_conversation_logs_id; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_conversation_logs_id ON public.conversation_logs USING btree (id);


--
-- Name: ix_customers_domain; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX ix_customers_domain ON public.customers USING btree (domain);


--
-- Name: ix_customers_id; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_customers_id ON public.customers USING btree (id);


--
-- Name: ix_customers_name; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_customers_name ON public.customers USING btree (name);


--
-- Name: ix_documents_id; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_documents_id ON public.documents USING btree (id);


--
-- Name: ix_documents_title; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_documents_title ON public.documents USING btree (title);


--
-- Name: ix_embeddings_id; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_embeddings_id ON public.embeddings USING btree (id);


--
-- Name: ix_jobs_id; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_jobs_id ON public.jobs USING btree (id);


--
-- Name: ix_performance_metrics_action; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_performance_metrics_action ON public.performance_metrics USING btree (action);


--
-- Name: ix_performance_metrics_chat_history_id; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_performance_metrics_chat_history_id ON public.performance_metrics USING btree (chat_history_id);


--
-- Name: ix_performance_metrics_id; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_performance_metrics_id ON public.performance_metrics USING btree (id);


--
-- Name: ix_performance_metrics_request_id; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_performance_metrics_request_id ON public.performance_metrics USING btree (request_id);


--
-- Name: ix_performance_metrics_service; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_performance_metrics_service ON public.performance_metrics USING btree (service);


--
-- Name: ix_phone_numbers_account_sid; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_phone_numbers_account_sid ON public.phone_numbers USING btree (account_sid);


--
-- Name: ix_phone_numbers_id; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_phone_numbers_id ON public.phone_numbers USING btree (id);


--
-- Name: ix_phone_numbers_phone_number; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX ix_phone_numbers_phone_number ON public.phone_numbers USING btree (phone_number);


--
-- Name: ix_users_email; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX ix_users_email ON public.users USING btree (email);


--
-- Name: ix_users_id; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX ix_users_id ON public.users USING btree (id);


--
-- Name: ix_users_reset_password_token; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX ix_users_reset_password_token ON public.users USING btree (reset_password_token);


--
-- Name: agents agents_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.agents
    ADD CONSTRAINT agents_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: call_history call_history_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.call_history
    ADD CONSTRAINT call_history_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id);


--
-- Name: call_history call_history_number_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.call_history
    ADD CONSTRAINT call_history_number_id_fkey FOREIGN KEY (number_id) REFERENCES public.phone_numbers(id);


--
-- Name: call_history call_history_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.call_history
    ADD CONSTRAINT call_history_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: conversation_logs conversation_logs_call_history_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.conversation_logs
    ADD CONSTRAINT conversation_logs_call_history_id_fkey FOREIGN KEY (call_history_id) REFERENCES public.call_history(id);


--
-- Name: conversation_logs conversation_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.conversation_logs
    ADD CONSTRAINT conversation_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: customers customers_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.customers
    ADD CONSTRAINT customers_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: documents documents_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id);


--
-- Name: documents documents_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: embeddings embeddings_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.embeddings
    ADD CONSTRAINT embeddings_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id);


--
-- Name: embeddings embeddings_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.embeddings
    ADD CONSTRAINT embeddings_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.documents(id);


--
-- Name: embeddings embeddings_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.embeddings
    ADD CONSTRAINT embeddings_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: phone_numbers fk_phone_numbers_after_hours_agent_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.phone_numbers
    ADD CONSTRAINT fk_phone_numbers_after_hours_agent_id FOREIGN KEY (after_hours_agent_id) REFERENCES public.agents(id);


--
-- Name: phone_numbers fk_phone_numbers_default_agent_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.phone_numbers
    ADD CONSTRAINT fk_phone_numbers_default_agent_id FOREIGN KEY (default_agent_id) REFERENCES public.agents(id);


--
-- Name: jobs jobs_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.jobs
    ADD CONSTRAINT jobs_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id);


--
-- Name: jobs jobs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.jobs
    ADD CONSTRAINT jobs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: performance_metrics performance_metrics_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.performance_metrics
    ADD CONSTRAINT performance_metrics_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: phone_numbers phone_numbers_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.phone_numbers
    ADD CONSTRAINT phone_numbers_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id);


--
-- Name: phone_numbers phone_numbers_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.phone_numbers
    ADD CONSTRAINT phone_numbers_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- PostgreSQL database dump complete
--

