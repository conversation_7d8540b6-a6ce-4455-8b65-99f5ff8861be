--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13 (Debian 15.13-1.pgdg120+1)
-- Dumped by pg_dump version 15.13 (Debian 15.13-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: alembic_version; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.alembic_version (version_num) FROM stdin;
\.


--
-- Data for Name: forecasts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.forecasts (id, store_id, forecast_date, predicted_sales, confidence_lower, confidence_upper, forecast_period, raw_forecast_data, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: holidays; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.holidays (id, holiday_date, name, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: product_performance; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.product_performance (id, product_id, store_id, total_sold, total_revenue, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_abandoned_checkout_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_abandoned_checkout_line_items (id, external_id, created_at, updated_at, image_src, quantity, sku, title, variant_title) FROM stdin;
\.


--
-- Data for Name: shopify_abandoned_checkouts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_abandoned_checkouts (id, external_id, created_at, updated_at, abandoned_checkout_url, completed_at, default_cursor, discount_codes, line_items_quantity, name, note, taxes_included) FROM stdin;
\.


--
-- Data for Name: shopify_abandonments; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_abandonments (id, external_id, created_at, updated_at, abandonment_type, cart_url, customer_has_no_draft_order_since_abandonment, customer_has_no_order_since_abandonment, days_since_last_abandonment_email, email_sent_at, email_state, hours_since_last_abandoned_checkout, inventory_available, is_from_custom_storefront, is_from_online_store, is_from_shop_app, is_from_shop_pay, is_most_significant_abandonment, last_browse_abandonment_date, last_cart_abandonment_date, last_checkout_abandonment_date, most_recent_step, visit_started_at) FROM stdin;
\.


--
-- Data for Name: shopify_access_scopes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_access_scopes (id, external_id, created_at, updated_at, description, handle) FROM stdin;
\.


--
-- Data for Name: shopify_add_all_products_operations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_add_all_products_operations (id, external_id, created_at, updated_at, processed_row_count, status) FROM stdin;
\.


--
-- Data for Name: shopify_additional_fee_sales; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_additional_fee_sales (id, external_id, created_at, updated_at, action_type, line_type, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_additional_fees; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_additional_fees (id, external_id, created_at, updated_at, name) FROM stdin;
\.


--
-- Data for Name: shopify_adjustment_sales; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_adjustment_sales (id, external_id, created_at, updated_at, action_type, line_type, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_all_discount_itemss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_all_discount_itemss (id, external_id, created_at, updated_at, all_items) FROM stdin;
\.


--
-- Data for Name: shopify_android_applications; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_android_applications (id, external_id, created_at, updated_at, app_links_enabled, application_id, sha256_cert_fingerprints) FROM stdin;
\.


--
-- Data for Name: shopify_api_versions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_api_versions (id, external_id, created_at, updated_at, display_name, handle, supported) FROM stdin;
\.


--
-- Data for Name: shopify_app_catalogs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_catalogs (id, external_id, created_at, updated_at, status, title) FROM stdin;
\.


--
-- Data for Name: shopify_app_credits; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_credits (id, store_id, external_id, created_at, updated_at, description, test, amount_id) FROM stdin;
\.


--
-- Data for Name: shopify_app_discount_types; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_discount_types (id, store_id, external_id, created_at, updated_at, app_key, description, discount_class, function_id, target_type, title, app_id, app_bridge_id) FROM stdin;
\.


--
-- Data for Name: shopify_app_feedbacks; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_feedbacks (id, external_id, created_at, updated_at, feedback_generated_at, state) FROM stdin;
\.


--
-- Data for Name: shopify_app_installations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_installations (id, store_id, external_id, created_at, updated_at, launch_url, uninstall_url, access_scopes_id, active_subscriptions_id, app_id, channel_id, metafield_id, private_metafield_id, publication_id, subscriptions_id) FROM stdin;
\.


--
-- Data for Name: shopify_app_plan_v2s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_plan_v2s (id, store_id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_app_purchase_one_times; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_purchase_one_times (id, store_id, external_id, created_at, updated_at, name, status, test, price_id) FROM stdin;
\.


--
-- Data for Name: shopify_app_recurring_pricings; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_recurring_pricings (id, external_id, created_at, updated_at, "interval") FROM stdin;
\.


--
-- Data for Name: shopify_app_revenue_attribution_records; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_revenue_attribution_records (id, store_id, external_id, created_at, updated_at, captured_at, idempotency_key, test, type, amount_id) FROM stdin;
\.


--
-- Data for Name: shopify_app_revoke_access_scopes_app_revoke_scope__6977bs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_revoke_access_scopes_app_revoke_scope__6977bs (id, external_id, created_at, updated_at, code, field, message) FROM stdin;
\.


--
-- Data for Name: shopify_app_subscription_discount_amounts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_subscription_discount_amounts (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_app_subscription_discount_percentages; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_subscription_discount_percentages (id, external_id, created_at, updated_at, percentage) FROM stdin;
\.


--
-- Data for Name: shopify_app_subscription_discounts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_subscription_discounts (id, external_id, created_at, updated_at, duration_limit_in_intervals, remaining_duration_in_intervals) FROM stdin;
\.


--
-- Data for Name: shopify_app_subscription_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_subscription_line_items (id, store_id, external_id, created_at, updated_at, plan_id) FROM stdin;
\.


--
-- Data for Name: shopify_app_subscriptions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_subscriptions (id, store_id, external_id, created_at, updated_at, current_period_end, name, return_url, status, test, trial_days, line_items_id) FROM stdin;
\.


--
-- Data for Name: shopify_app_usage_pricings; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_usage_pricings (id, external_id, created_at, updated_at, "interval", terms) FROM stdin;
\.


--
-- Data for Name: shopify_app_usage_records; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_app_usage_records (id, external_id, created_at, updated_at, description, idempotency_key) FROM stdin;
\.


--
-- Data for Name: shopify_apple_applications; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_apple_applications (id, external_id, created_at, updated_at, app_clip_application_id, app_clips_enabled, app_id, shared_web_credentials_enabled, universal_links_enabled) FROM stdin;
\.


--
-- Data for Name: shopify_apps; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_apps (id, store_id, external_id, created_at, updated_at, api_key, app_store_app_url, app_store_developer_url, description, developer_name, developer_type, developer_url, embedded, features, handle, install_url, is_post_purchase_app_in_use, launch_url, previously_installed, pricing_details, pricing_details_summary, privacy_policy_url, public_category, published, shopify_developed, title, uninstall_message, uninstall_url, webhook_api_version, available_access_scopes_id, banner_id, failed_requirements_id, feedback_id, icon_id, installation_id, navigation_items_id, optional_access_scopes_id, requested_access_scopes_id, screenshots_id) FROM stdin;
\.


--
-- Data for Name: shopify_article_authors; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_article_authors (id, external_id, created_at, updated_at, name) FROM stdin;
\.


--
-- Data for Name: shopify_articles; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_articles (id, external_id, created_at, updated_at, default_cursor, handle, image_src, is_published, published_at, tags, template_suffix, title) FROM stdin;
\.


--
-- Data for Name: shopify_attributes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_attributes (id, store_id, external_id, created_at, updated_at, key, value) FROM stdin;
\.


--
-- Data for Name: shopify_automatic_discount_applications; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_automatic_discount_applications (id, external_id, created_at, updated_at, allocation_method, index, target_selection, target_type, title) FROM stdin;
\.


--
-- Data for Name: shopify_available_channel_definitions_by_channels; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_available_channel_definitions_by_channels (id, external_id, created_at, updated_at, channel_name) FROM stdin;
\.


--
-- Data for Name: shopify_basic_events; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_basic_events (id, external_id, created_at, updated_at, action, app_title, attribute_to_app, attribute_to_user, critical_alert, has_additional_content, subject_id, subject_type) FROM stdin;
\.


--
-- Data for Name: shopify_blog_feeds; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_blog_feeds (id, external_id, created_at, updated_at, location, path) FROM stdin;
\.


--
-- Data for Name: shopify_blogs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_blogs (id, external_id, created_at, updated_at, comment_policy, handle, tags, template_suffix, title) FROM stdin;
\.


--
-- Data for Name: shopify_bulk_operations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_bulk_operations (id, external_id, created_at, updated_at, completed_at, error_code, partial_data_url, query, status, type, url) FROM stdin;
\.


--
-- Data for Name: shopify_bundles_features; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_bundles_features (id, external_id, created_at, updated_at, eligible_for_bundles, ineligibility_reason, sells_bundles) FROM stdin;
\.


--
-- Data for Name: shopify_business_entity_addresss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_business_entity_addresss (id, external_id, created_at, updated_at, address1, address2, city, country_code, province, zip) FROM stdin;
\.


--
-- Data for Name: shopify_business_entitys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_business_entitys (id, external_id, created_at, updated_at, company_name, display_name, "primary") FROM stdin;
\.


--
-- Data for Name: shopify_buyer_experience_configurations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_buyer_experience_configurations (id, external_id, created_at, updated_at, checkout_to_draft, editable_shipping_address, pay_now_only) FROM stdin;
\.


--
-- Data for Name: shopify_calculated_automatic_discount_applications; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_calculated_automatic_discount_applications (id, external_id, created_at, updated_at, allocation_method, applied_to, description, target_selection, target_type) FROM stdin;
\.


--
-- Data for Name: shopify_calculated_discount_allocations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_calculated_discount_allocations (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_calculated_discount_code_applications; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_calculated_discount_code_applications (id, external_id, created_at, updated_at, allocation_method, applied_to, code, description, target_selection, target_type) FROM stdin;
\.


--
-- Data for Name: shopify_calculated_draft_order_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_calculated_draft_order_line_items (id, external_id, created_at, updated_at, custom, image_src, is_gift_card, name, quantity, requires_shipping, sku, taxable, title, uuid, variant_title, vendor) FROM stdin;
\.


--
-- Data for Name: shopify_calculated_draft_orders; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_calculated_draft_orders (id, external_id, created_at, updated_at, accept_automatic_discounts, billing_address_matches_shipping_address, currency_code, discount_codes, market_name, market_region_country_code, phone, presentment_currency_code, taxes_included, total_quantity_of_line_items, transformer_fingerprint) FROM stdin;
\.


--
-- Data for Name: shopify_calculated_exchange_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_calculated_exchange_line_items (id, external_id, created_at, updated_at, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_calculated_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_calculated_line_items (id, external_id, created_at, updated_at, editable_quantity, editable_quantity_before_changes, has_staged_line_item_discount, image_src, quantity, restockable, restocking, sku, title, variant_title) FROM stdin;
\.


--
-- Data for Name: shopify_calculated_manual_discount_applications; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_calculated_manual_discount_applications (id, external_id, created_at, updated_at, allocation_method, applied_to, description, target_selection, target_type) FROM stdin;
\.


--
-- Data for Name: shopify_calculated_orders; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_calculated_orders (id, external_id, created_at, updated_at, committed, notification_preview_title, subtotal_line_items_quantity) FROM stdin;
\.


--
-- Data for Name: shopify_calculated_restocking_fees; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_calculated_restocking_fees (id, external_id, created_at, updated_at, percentage) FROM stdin;
\.


--
-- Data for Name: shopify_calculated_return_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_calculated_return_line_items (id, external_id, created_at, updated_at, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_calculated_return_shipping_fees; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_calculated_return_shipping_fees (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_calculated_returns; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_calculated_returns (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_calculated_script_discount_applications; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_calculated_script_discount_applications (id, external_id, created_at, updated_at, allocation_method, applied_to, description, target_selection, target_type) FROM stdin;
\.


--
-- Data for Name: shopify_calculated_shipping_lines; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_calculated_shipping_lines (id, external_id, created_at, updated_at, staged_status, title) FROM stdin;
\.


--
-- Data for Name: shopify_card_payment_detailss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_card_payment_detailss (id, external_id, created_at, updated_at, avs_result_code, bin, company, cvv_result_code, expiration_month, expiration_year, name, number, payment_method_name, wallet) FROM stdin;
\.


--
-- Data for Name: shopify_cart_transform_eligible_operationss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_cart_transform_eligible_operationss (id, external_id, created_at, updated_at, expand_operation, merge_operation, update_operation) FROM stdin;
\.


--
-- Data for Name: shopify_cart_transform_features; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_cart_transform_features (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_cart_transforms; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_cart_transforms (id, external_id, created_at, updated_at, block_on_failure, function_id) FROM stdin;
\.


--
-- Data for Name: shopify_cash_rounding_adjustments; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_cash_rounding_adjustments (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_cash_tracking_adjustments; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_cash_tracking_adjustments (id, external_id, created_at, updated_at, note, "time") FROM stdin;
\.


--
-- Data for Name: shopify_cash_tracking_sessions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_cash_tracking_sessions (id, external_id, created_at, updated_at, cash_tracking_enabled, closing_note, closing_time, opening_note, opening_time, register_name) FROM stdin;
\.


--
-- Data for Name: shopify_catalog_csv_operations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_catalog_csv_operations (id, external_id, created_at, updated_at, processed_row_count, status) FROM stdin;
\.


--
-- Data for Name: shopify_channel_definitions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_channel_definitions (id, external_id, created_at, updated_at, channel_name, handle, is_marketplace, sub_channel_name, svg_icon) FROM stdin;
\.


--
-- Data for Name: shopify_channel_informations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_channel_informations (id, external_id, created_at, updated_at, channel_id) FROM stdin;
\.


--
-- Data for Name: shopify_channels; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_channels (id, external_id, created_at, updated_at, handle, has_collection, name, overview_path, supports_future_publishing) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_button_color_roless; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_button_color_roless (id, external_id, created_at, updated_at, accent, background, border, decorative, icon, text) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_buttons; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_buttons (id, external_id, created_at, updated_at, background, block_padding, border, corner_radius, inline_padding) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_buyer_journeys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_buyer_journeys (id, external_id, created_at, updated_at, visibility) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_cart_links; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_cart_links (id, external_id, created_at, updated_at, visibility) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_checkboxs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_checkboxs (id, external_id, created_at, updated_at, corner_radius) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_choice_list_groups; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_choice_list_groups (id, external_id, created_at, updated_at, spacing) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_choice_lists; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_choice_lists (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_color_globals; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_color_globals (id, external_id, created_at, updated_at, accent, brand, critical, decorative, info, success, warning) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_color_roless; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_color_roless (id, external_id, created_at, updated_at, accent, background, border, decorative, icon, text) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_color_schemes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_color_schemes (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_color_schemess; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_color_schemess (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_colorss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_colorss (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_container_dividers; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_container_dividers (id, external_id, created_at, updated_at, border_style, border_width, visibility) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_contents; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_contents (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_control_color_roless; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_control_color_roless (id, external_id, created_at, updated_at, accent, background, border, decorative, icon, text) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_controls; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_controls (id, external_id, created_at, updated_at, border, color, corner_radius, label_position) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_corner_radius_variabless; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_corner_radius_variabless (id, external_id, created_at, updated_at, base, large, small) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_custom_fonts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_custom_fonts (id, external_id, created_at, updated_at, generic_file_id, sources, weight) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_customizationss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_customizationss (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_design_systems; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_design_systems (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_divider_styles; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_divider_styles (id, external_id, created_at, updated_at, border_style, border_width) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_express_checkout_buttons; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_express_checkout_buttons (id, external_id, created_at, updated_at, corner_radius) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_express_checkouts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_express_checkouts (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_font_groups; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_font_groups (id, external_id, created_at, updated_at, loading_strategy, name) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_font_sizes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_font_sizes (id, external_id, created_at, updated_at, base, ratio) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_footer_contents; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_footer_contents (id, external_id, created_at, updated_at, visibility) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_footers; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_footers (id, external_id, created_at, updated_at, alignment, color_scheme, divided, padding, "position") FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_globals; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_globals (id, external_id, created_at, updated_at, corner_radius) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_header_cart_links; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_header_cart_links (id, external_id, created_at, updated_at, content_type, image_src) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_headers; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_headers (id, external_id, created_at, updated_at, alignment, color_scheme, divided, padding, "position") FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_heading_levels; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_heading_levels (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_images; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_images (id, external_id, created_at, updated_at, image_src) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_logos; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_logos (id, external_id, created_at, updated_at, image_src, max_width, visibility) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_main_sections; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_main_sections (id, external_id, created_at, updated_at, background, border, border_style, border_width, color_scheme, corner_radius, padding, shadow) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_mains; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_mains (id, external_id, created_at, updated_at, color_scheme) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_merchandise_thumbnails; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_merchandise_thumbnails (id, external_id, created_at, updated_at, border, corner_radius) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_order_summary_sections; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_order_summary_sections (id, external_id, created_at, updated_at, background, border, border_style, border_width, color_scheme, corner_radius, padding, shadow) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_order_summarys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_order_summarys (id, external_id, created_at, updated_at, color_scheme) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_selects; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_selects (id, external_id, created_at, updated_at, border) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_shopify_fonts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_shopify_fonts (id, external_id, created_at, updated_at, sources, weight) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_text_fields; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_text_fields (id, external_id, created_at, updated_at, border) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_typography_style_globals; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_typography_style_globals (id, external_id, created_at, updated_at, kerning, letter_case) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_typography_styles; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_typography_styles (id, external_id, created_at, updated_at, font, kerning, letter_case, size, weight) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_branding_typographys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_branding_typographys (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_brandings; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_brandings (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_checkout_profiles; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_checkout_profiles (id, external_id, created_at, updated_at, edited_at, is_published, name, typ_osp_pages_active) FROM stdin;
\.


--
-- Data for Name: shopify_collection_publications; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_collection_publications (id, external_id, created_at, updated_at, is_published, publish_date) FROM stdin;
\.


--
-- Data for Name: shopify_collection_rule_category_conditions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_collection_rule_category_conditions (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_collection_rule_conditionss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_collection_rule_conditionss (id, external_id, created_at, updated_at, allowed_relations, default_relation, rule_type) FROM stdin;
\.


--
-- Data for Name: shopify_collection_rule_metafield_conditions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_collection_rule_metafield_conditions (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_collection_rule_product_category_conditions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_collection_rule_product_category_conditions (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_collection_rule_sets; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_collection_rule_sets (id, external_id, created_at, updated_at, applied_disjunctively) FROM stdin;
\.


--
-- Data for Name: shopify_collection_rule_text_conditions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_collection_rule_text_conditions (id, external_id, created_at, updated_at, value) FROM stdin;
\.


--
-- Data for Name: shopify_collection_rules; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_collection_rules (id, external_id, created_at, updated_at, "column", condition, relation) FROM stdin;
\.


--
-- Data for Name: shopify_collections; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_collections (id, external_id, created_at, updated_at, description, handle, has_product, image_src, publication_count, published_on_channel, published_on_current_channel, published_on_current_publication, published_on_publication, sort_order, template_suffix, title) FROM stdin;
\.


--
-- Data for Name: shopify_combined_listing_childs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_combined_listing_childs (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_combined_listings; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_combined_listings (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_comment_authors; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_comment_authors (id, external_id, created_at, updated_at, email, name) FROM stdin;
\.


--
-- Data for Name: shopify_comment_event_attachments; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_comment_event_attachments (id, external_id, created_at, updated_at, file_extension, image_src, name, size, url) FROM stdin;
\.


--
-- Data for Name: shopify_comment_events; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_comment_events (id, external_id, created_at, updated_at, action, app_title, attribute_to_app, attribute_to_user, can_delete, can_edit, critical_alert, edited, raw_message) FROM stdin;
\.


--
-- Data for Name: shopify_comments; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_comments (id, external_id, created_at, updated_at, body, ip, is_published, published_at, status, user_agent) FROM stdin;
\.


--
-- Data for Name: shopify_company_addresss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_company_addresss (id, external_id, created_at, updated_at, address1, address2, city, company_name, country, country_code, first_name, formatted_address, formatted_area, last_name, phone, province, recipient, zip, zone_code) FROM stdin;
\.


--
-- Data for Name: shopify_company_contact_role_assignments; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_company_contact_role_assignments (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_company_contact_roles; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_company_contact_roles (id, external_id, created_at, updated_at, name, note) FROM stdin;
\.


--
-- Data for Name: shopify_company_contacts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_company_contacts (id, external_id, created_at, updated_at, is_main_contact, lifetime_duration, locale, title) FROM stdin;
\.


--
-- Data for Name: shopify_company_location_catalogs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_company_location_catalogs (id, external_id, created_at, updated_at, status, title) FROM stdin;
\.


--
-- Data for Name: shopify_company_location_staff_member_assignments; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_company_location_staff_member_assignments (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_company_locations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_company_locations (id, external_id, created_at, updated_at, currency, default_cursor, has_timeline_comment, in_catalog, locale, name, note, order_count, phone, tax_exemptions, tax_registration_id) FROM stdin;
\.


--
-- Data for Name: shopify_companys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_companys (id, external_id, created_at, updated_at, contact_count, customer_since, default_cursor, has_timeline_comment, lifetime_duration, name, note) FROM stdin;
\.


--
-- Data for Name: shopify_countries_in_shipping_zoness; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_countries_in_shipping_zoness (id, external_id, created_at, updated_at, country_codes, include_rest_of_world) FROM stdin;
\.


--
-- Data for Name: shopify_country_harmonized_system_codes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_country_harmonized_system_codes (id, external_id, created_at, updated_at, country_code, harmonized_system_code) FROM stdin;
\.


--
-- Data for Name: shopify_counts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_counts (id, external_id, created_at, updated_at, count, "precision") FROM stdin;
\.


--
-- Data for Name: shopify_currency_formatss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_currency_formatss (id, external_id, created_at, updated_at, money_in_emails_format, money_with_currency_in_emails_format) FROM stdin;
\.


--
-- Data for Name: shopify_currency_settings; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_currency_settings (id, external_id, created_at, updated_at, currency_code, currency_name, enabled, rate_updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_customer_account_app_extension_pages; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_account_app_extension_pages (id, external_id, created_at, updated_at, app_extension_uuid, default_cursor, handle, title) FROM stdin;
\.


--
-- Data for Name: shopify_customer_account_native_pages; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_account_native_pages (id, external_id, created_at, updated_at, default_cursor, handle, page_type, title) FROM stdin;
\.


--
-- Data for Name: shopify_customer_accounts_v2s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_accounts_v2s (id, external_id, created_at, updated_at, customer_accounts_version, login_links_visible_on_storefront_and_checkout, login_required_at_checkout, url) FROM stdin;
\.


--
-- Data for Name: shopify_customer_credit_card_billing_addresss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_credit_card_billing_addresss (id, external_id, created_at, updated_at, address1, city, country, country_code, first_name, last_name, province, province_code, zip) FROM stdin;
\.


--
-- Data for Name: shopify_customer_credit_cards; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_credit_cards (id, external_id, created_at, updated_at, brand, expires_soon, expiry_month, expiry_year, first_digits, is_revocable, last_digits, masked_number, name, source, virtual_last_digits) FROM stdin;
\.


--
-- Data for Name: shopify_customer_email_addresss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_email_addresss (id, external_id, created_at, updated_at, email_address, marketing_state, marketing_unsubscribe_url, open_tracking_level, open_tracking_url) FROM stdin;
\.


--
-- Data for Name: shopify_customer_email_marketing_consent_states; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_email_marketing_consent_states (id, external_id, created_at, updated_at, consent_updated_at, marketing_opt_in_level, marketing_state) FROM stdin;
\.


--
-- Data for Name: shopify_customer_journey_summarys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_journey_summarys (id, external_id, created_at, updated_at, customer_order_index, days_to_conversion, ready) FROM stdin;
\.


--
-- Data for Name: shopify_customer_journeys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_journeys (id, external_id, created_at, updated_at, customer_order_index, days_to_conversion) FROM stdin;
\.


--
-- Data for Name: shopify_customer_merge_errors; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_merge_errors (id, external_id, created_at, updated_at, error_fields, message) FROM stdin;
\.


--
-- Data for Name: shopify_customer_merge_preview_alternate_fieldss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_merge_preview_alternate_fieldss (id, external_id, created_at, updated_at, first_name, last_name) FROM stdin;
\.


--
-- Data for Name: shopify_customer_merge_preview_blocking_fieldss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_merge_preview_blocking_fieldss (id, external_id, created_at, updated_at, note, tags) FROM stdin;
\.


--
-- Data for Name: shopify_customer_merge_preview_default_fieldss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_merge_preview_default_fieldss (id, external_id, created_at, updated_at, display_name, first_name, last_name, note, tags) FROM stdin;
\.


--
-- Data for Name: shopify_customer_merge_previews; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_merge_previews (id, external_id, created_at, updated_at, resulting_customer_id) FROM stdin;
\.


--
-- Data for Name: shopify_customer_merge_requests; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_merge_requests (id, external_id, created_at, updated_at, job_id, resulting_customer_id, status) FROM stdin;
\.


--
-- Data for Name: shopify_customer_mergeables; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_mergeables (id, external_id, created_at, updated_at, error_fields, is_mergeable, reason) FROM stdin;
\.


--
-- Data for Name: shopify_customer_payment_instrument_billing_addres_b82bds; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_payment_instrument_billing_addres_b82bds (id, external_id, created_at, updated_at, address1, city, country, country_code, name, province, province_code, zip) FROM stdin;
\.


--
-- Data for Name: shopify_customer_payment_methods; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_payment_methods (id, external_id, created_at, updated_at, revoked_at, revoked_reason) FROM stdin;
\.


--
-- Data for Name: shopify_customer_paypal_billing_agreements; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_paypal_billing_agreements (id, external_id, created_at, updated_at, inactive, is_revocable, paypal_account_email) FROM stdin;
\.


--
-- Data for Name: shopify_customer_phone_numbers; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_phone_numbers (id, external_id, created_at, updated_at, marketing_state, phone_number) FROM stdin;
\.


--
-- Data for Name: shopify_customer_segment_members; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_segment_members (id, external_id, created_at, updated_at, display_name, first_name, last_name, last_order_id, note) FROM stdin;
\.


--
-- Data for Name: shopify_customer_segment_members_querys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_segment_members_querys (id, external_id, created_at, updated_at, current_count, done) FROM stdin;
\.


--
-- Data for Name: shopify_customer_shop_pay_agreements; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_shop_pay_agreements (id, external_id, created_at, updated_at, expires_soon, expiry_month, expiry_year, inactive, is_revocable, last_digits, masked_number, name) FROM stdin;
\.


--
-- Data for Name: shopify_customer_sms_marketing_consent_errors; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_sms_marketing_consent_errors (id, external_id, created_at, updated_at, code, field, message) FROM stdin;
\.


--
-- Data for Name: shopify_customer_sms_marketing_consent_states; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_sms_marketing_consent_states (id, external_id, created_at, updated_at, consent_collected_from, consent_updated_at, marketing_opt_in_level, marketing_state) FROM stdin;
\.


--
-- Data for Name: shopify_customer_statisticss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_statisticss (id, external_id, created_at, updated_at, predicted_spend_tier) FROM stdin;
\.


--
-- Data for Name: shopify_customer_visit_product_infos; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_visit_product_infos (id, store_id, external_id, created_at, updated_at, quantity, product_id, variant_id) FROM stdin;
\.


--
-- Data for Name: shopify_customer_visits; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customer_visits (id, external_id, created_at, updated_at, landing_page, occurred_at, referral_code, referrer_url, source, source_description, source_type) FROM stdin;
\.


--
-- Data for Name: shopify_customers; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_customers (id, external_id, created_at, updated_at, can_delete, data_sale_opt_out, display_name, email, first_name, has_timeline_comment, image_src, last_name, lifetime_duration, locale, multipass_identifier, note, phone, product_subscriber_status, state, tags, tax_exempt, tax_exemptions, unsubscribe_url, valid_email_address, verified_email) FROM stdin;
\.


--
-- Data for Name: shopify_delegate_access_tokens; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delegate_access_tokens (id, external_id, created_at, updated_at, access_scopes, access_token) FROM stdin;
\.


--
-- Data for Name: shopify_deletion_events; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_deletion_events (id, external_id, created_at, updated_at, occurred_at, subject_id, subject_type) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_available_services; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_available_services (id, external_id, created_at, updated_at, name) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_branded_promises; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_branded_promises (id, external_id, created_at, updated_at, handle, name) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_carrier_service_and_locationss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_carrier_service_and_locationss (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_carrier_services; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_carrier_services (id, external_id, created_at, updated_at, active, callback_url, formatted_name, icon_src, name, supports_service_discovery) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_conditions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_conditions (id, external_id, created_at, updated_at, field, operator) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_country_and_zones; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_country_and_zones (id, external_id, created_at, updated_at, zone) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_country_code_or_rest_of_worlds; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_country_code_or_rest_of_worlds (id, external_id, created_at, updated_at, country_code, rest_of_world) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_country_codes_or_rest_of_worlds; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_country_codes_or_rest_of_worlds (id, external_id, created_at, updated_at, country_codes, rest_of_world) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_countrys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_countrys (id, external_id, created_at, updated_at, name, translated_name) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_customization_errors; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_customization_errors (id, external_id, created_at, updated_at, code, field, message) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_customizations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_customizations (id, external_id, created_at, updated_at, enabled, function_id, title) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_legacy_mode_blockeds; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_legacy_mode_blockeds (id, external_id, created_at, updated_at, blocked, reasons) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_local_pickup_settingss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_local_pickup_settingss (id, external_id, created_at, updated_at, instructions, pickup_time) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_location_group_zones; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_location_group_zones (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_location_groups; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_location_groups (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_location_local_pickup_settings_er_57b7bs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_location_local_pickup_settings_er_57b7bs (id, external_id, created_at, updated_at, code, field, message) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_method_additional_informations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_method_additional_informations (id, external_id, created_at, updated_at, instructions, phone) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_method_definition_countss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_method_definition_countss (id, external_id, created_at, updated_at, participant_definitions_count, rate_definitions_count) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_method_definitions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_method_definitions (id, external_id, created_at, updated_at, active, description, name) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_methods; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_methods (id, external_id, created_at, updated_at, max_delivery_date_time, method_type, min_delivery_date_time, presented_name, service_code, source_reference) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_participant_services; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_participant_services (id, external_id, created_at, updated_at, active, name) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_participants; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_participants (id, external_id, created_at, updated_at, adapt_to_new_services_flag, percentage_of_rate_fee) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_product_variants_counts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_product_variants_counts (id, external_id, created_at, updated_at, capped, count) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_profile_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_profile_items (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_profile_location_groups; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_profile_location_groups (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_profiles; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_profiles (id, external_id, created_at, updated_at, active_method_definitions_count, "default", legacy_mode, locations_without_rates_count, name, origin_location_count, zone_country_count) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_promise_providers; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_promise_providers (id, external_id, created_at, updated_at, active, fulfillment_delay, time_zone) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_provinces; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_provinces (id, external_id, created_at, updated_at, code, name, translated_name) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_rate_definitions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_rate_definitions (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_settings; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_settings (id, external_id, created_at, updated_at, legacy_mode_profiles) FROM stdin;
\.


--
-- Data for Name: shopify_delivery_zones; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_delivery_zones (id, external_id, created_at, updated_at, name) FROM stdin;
\.


--
-- Data for Name: shopify_deposit_percentages; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_deposit_percentages (id, external_id, created_at, updated_at, percentage) FROM stdin;
\.


--
-- Data for Name: shopify_discount_allocations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_allocations (id, store_id, external_id, created_at, updated_at, allocated_amount_id, allocated_amount_set_id) FROM stdin;
\.


--
-- Data for Name: shopify_discount_amounts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_amounts (id, external_id, created_at, updated_at, applies_on_each_item) FROM stdin;
\.


--
-- Data for Name: shopify_discount_automatic_apps; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_automatic_apps (id, external_id, created_at, updated_at, applies_on_subscription, async_usage_count, discount_class, discount_id, ends_at, recurring_cycle_limit, starts_at, status, title) FROM stdin;
\.


--
-- Data for Name: shopify_discount_automatic_basics; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_automatic_basics (id, external_id, created_at, updated_at, async_usage_count, discount_class, ends_at, recurring_cycle_limit, short_summary, starts_at, status, summary, title, usage_count) FROM stdin;
\.


--
-- Data for Name: shopify_discount_automatic_bxgys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_automatic_bxgys (id, external_id, created_at, updated_at, async_usage_count, discount_class, ends_at, starts_at, status, summary, title, usage_count, uses_per_order_limit) FROM stdin;
\.


--
-- Data for Name: shopify_discount_automatic_free_shippings; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_automatic_free_shippings (id, external_id, created_at, updated_at, applies_on_one_time_purchase, applies_on_subscription, async_usage_count, discount_class, ends_at, has_timeline_comment, recurring_cycle_limit, short_summary, starts_at, status, summary, title) FROM stdin;
\.


--
-- Data for Name: shopify_discount_automatic_nodes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_automatic_nodes (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_discount_code_applications; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_code_applications (id, external_id, created_at, updated_at, allocation_method, code, index, target_selection, target_type) FROM stdin;
\.


--
-- Data for Name: shopify_discount_code_apps; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_code_apps (id, external_id, created_at, updated_at, applies_once_per_customer, async_usage_count, discount_class, discount_id, ends_at, has_timeline_comment, recurring_cycle_limit, starts_at, status, title, usage_limit) FROM stdin;
\.


--
-- Data for Name: shopify_discount_code_basics; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_code_basics (id, external_id, created_at, updated_at, title, summary, code, usage_count) FROM stdin;
\.


--
-- Data for Name: shopify_discount_code_bxgys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_code_bxgys (id, external_id, created_at, updated_at, applies_once_per_customer, async_usage_count, discount_class, ends_at, has_timeline_comment, starts_at, status, summary, title, usage_limit, uses_per_order_limit) FROM stdin;
\.


--
-- Data for Name: shopify_discount_code_free_shippings; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_code_free_shippings (id, external_id, created_at, updated_at, applies_on_one_time_purchase, applies_on_subscription, applies_once_per_customer, async_usage_count, discount_class, ends_at, has_timeline_comment, recurring_cycle_limit, short_summary, starts_at, status, summary, title, usage_limit) FROM stdin;
\.


--
-- Data for Name: shopify_discount_code_nodes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_code_nodes (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_discount_collectionss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_collectionss (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_discount_combines_withs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_combines_withs (id, external_id, created_at, updated_at, order_discounts, product_discounts, shipping_discounts) FROM stdin;
\.


--
-- Data for Name: shopify_discount_countriess; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_countriess (id, external_id, created_at, updated_at, countries, include_rest_of_world) FROM stdin;
\.


--
-- Data for Name: shopify_discount_country_alls; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_country_alls (id, external_id, created_at, updated_at, all_countries) FROM stdin;
\.


--
-- Data for Name: shopify_discount_customer_alls; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_customer_alls (id, external_id, created_at, updated_at, all_customers) FROM stdin;
\.


--
-- Data for Name: shopify_discount_customer_buyss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_customer_buyss (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_discount_customer_getss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_customer_getss (id, external_id, created_at, updated_at, applies_on_one_time_purchase, applies_on_subscription) FROM stdin;
\.


--
-- Data for Name: shopify_discount_customer_segmentss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_customer_segmentss (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_discount_customerss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_customerss (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_discount_minimum_quantitys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_minimum_quantitys (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_discount_minimum_subtotals; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_minimum_subtotals (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_discount_nodes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_nodes (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_discount_on_quantitys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_on_quantitys (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_discount_percentages; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_percentages (id, external_id, created_at, updated_at, percentage) FROM stdin;
\.


--
-- Data for Name: shopify_discount_productss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_productss (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_discount_purchase_amounts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_purchase_amounts (id, external_id, created_at, updated_at, amount) FROM stdin;
\.


--
-- Data for Name: shopify_discount_quantitys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_quantitys (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_discount_redeem_code_bulk_creation_codes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_redeem_code_bulk_creation_codes (id, external_id, created_at, updated_at, code) FROM stdin;
\.


--
-- Data for Name: shopify_discount_redeem_code_bulk_creations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_redeem_code_bulk_creations (id, external_id, created_at, updated_at, codes_count, done, failed_count, imported_count) FROM stdin;
\.


--
-- Data for Name: shopify_discount_redeem_codes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_redeem_codes (id, external_id, created_at, updated_at, async_usage_count, code) FROM stdin;
\.


--
-- Data for Name: shopify_discount_shareable_urls; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_discount_shareable_urls (id, external_id, created_at, updated_at, target_item_image_src, target_type, title, url) FROM stdin;
\.


--
-- Data for Name: shopify_domain_localizations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_domain_localizations (id, external_id, created_at, updated_at, alternate_locales, country, default_locale) FROM stdin;
\.


--
-- Data for Name: shopify_domains; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_domains (id, external_id, created_at, updated_at, host, ssl_enabled, url) FROM stdin;
\.


--
-- Data for Name: shopify_draft_order_applied_discounts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_draft_order_applied_discounts (id, external_id, created_at, updated_at, description, title, value, value_type) FROM stdin;
\.


--
-- Data for Name: shopify_draft_order_bundle_added_warnings; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_draft_order_bundle_added_warnings (id, external_id, created_at, updated_at, error_code, field, message) FROM stdin;
\.


--
-- Data for Name: shopify_draft_order_discount_not_applied_warnings; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_draft_order_discount_not_applied_warnings (id, external_id, created_at, updated_at, discount_code, discount_title, error_code, field, message) FROM stdin;
\.


--
-- Data for Name: shopify_draft_order_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_draft_order_line_items (id, external_id, created_at, updated_at, custom, grams, image_src, is_gift_card, name, quantity, requires_shipping, sku, taxable, title, uuid, variant_title, vendor) FROM stdin;
\.


--
-- Data for Name: shopify_draft_order_platform_discount_allocations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_draft_order_platform_discount_allocations (id, external_id, created_at, updated_at, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_draft_order_platform_discounts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_draft_order_platform_discounts (id, external_id, created_at, updated_at, automatic_discount, bxgy_discount, code, discount_class, presentation_level, short_summary, summary, title) FROM stdin;
\.


--
-- Data for Name: shopify_draft_order_tags; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_draft_order_tags (id, external_id, created_at, updated_at, handle, title) FROM stdin;
\.


--
-- Data for Name: shopify_draft_orders; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_draft_orders (id, external_id, created_at, updated_at, accept_automatic_discounts, allow_discount_codes_in_checkout, billing_address_matches_shipping_address, completed_at, currency_code, default_cursor, discount_codes, email, has_timeline_comment, invoice_email_template_subject, invoice_sent_at, invoice_url, market_name, market_region_country_code, name, note2, phone, po_number, presentment_currency_code, ready, reserve_inventory_until, status, tags, tax_exempt, taxes_included, total_quantity_of_line_items, transformer_fingerprint, visible_to_customer) FROM stdin;
\.


--
-- Data for Name: shopify_duty_sales; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_duty_sales (id, external_id, created_at, updated_at, action_type, line_type, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_dutys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_dutys (id, external_id, created_at, updated_at, country_code_of_origin, harmonized_system_code) FROM stdin;
\.


--
-- Data for Name: shopify_editable_propertys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_editable_propertys (id, external_id, created_at, updated_at, locked) FROM stdin;
\.


--
-- Data for Name: shopify_exchange_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_exchange_line_items (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_exchange_v2_additionss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_exchange_v2_additionss (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_exchange_v2_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_exchange_v2_line_items (id, external_id, created_at, updated_at, gift_card, is_gift_card, name, quantity, requires_shipping, sku, taxable, title, variant_title, vendor) FROM stdin;
\.


--
-- Data for Name: shopify_exchange_v2_returnss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_exchange_v2_returnss (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_exchange_v2s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_exchange_v2s (id, external_id, created_at, updated_at, completed_at, note) FROM stdin;
\.


--
-- Data for Name: shopify_external_videos; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_external_videos (id, external_id, created_at, updated_at, alt, embed_url, embedded_url, file_status, host, media_content_type, origin_url, status) FROM stdin;
\.


--
-- Data for Name: shopify_failed_requirements; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_failed_requirements (id, external_id, created_at, updated_at, message) FROM stdin;
\.


--
-- Data for Name: shopify_fee_sales; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fee_sales (id, external_id, created_at, updated_at, action_type, line_type, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_file_errors; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_file_errors (id, external_id, created_at, updated_at, code, details, message) FROM stdin;
\.


--
-- Data for Name: shopify_filter_options; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_filter_options (id, external_id, created_at, updated_at, label, value) FROM stdin;
\.


--
-- Data for Name: shopify_financial_summary_discount_allocations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_financial_summary_discount_allocations (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_financial_summary_discount_applications; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_financial_summary_discount_applications (id, external_id, created_at, updated_at, allocation_method, target_selection, target_type) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_constraint_rules; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_constraint_rules (id, external_id, created_at, updated_at, delivery_method_types) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_events; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_events (id, external_id, created_at, updated_at, address1, city, country, estimated_delivery_at, happened_at, latitude, longitude, message, province, status, zip) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_holds; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_holds (id, external_id, created_at, updated_at, display_reason, held_by, held_by_requesting_app, reason, reason_notes) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_line_items (id, external_id, created_at, updated_at, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_order_assigned_locations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_order_assigned_locations (id, external_id, created_at, updated_at, address1, address2, city, country_code, name, phone, province, zip) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_order_destinations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_order_destinations (id, external_id, created_at, updated_at, address1, address2, city, company, country_code, email, first_name, last_name, phone, province, zip) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_order_international_dutiess; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_order_international_dutiess (id, external_id, created_at, updated_at, incoterm) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_order_line_item_financial_summ_e222fs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_order_line_item_financial_summ_e222fs (id, external_id, created_at, updated_at, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_order_line_item_warnings; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_order_line_item_warnings (id, external_id, created_at, updated_at, description, title) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_order_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_order_line_items (id, external_id, created_at, updated_at, image_src, inventory_item_id, product_title, remaining_quantity, requires_shipping, sku, total_quantity, variant_title, vendor) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_order_location_for_moves; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_order_location_for_moves (id, external_id, created_at, updated_at, message, movable) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_order_merchant_requests; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_order_merchant_requests (id, external_id, created_at, updated_at, kind, message, sent_at) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_order_merge_results; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_order_merge_results (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_order_split_results; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_order_split_results (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_order_supported_actions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_order_supported_actions (id, external_id, created_at, updated_at, action, external_url) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_orders; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_orders (id, external_id, created_at, updated_at, channel_id, fulfill_at, fulfill_by, order_id, order_name, order_processed_at, request_status, status) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_origin_addresss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_origin_addresss (id, external_id, created_at, updated_at, address1, address2, city, country_code, province_code, zip) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_services; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_services (id, external_id, created_at, updated_at, callback_url, fulfillment_orders_opt_in, handle, inventory_management, permits_sku_sharing, service_name, tracking_support, type) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillment_tracking_infos; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillment_tracking_infos (id, external_id, created_at, updated_at, company, number, url) FROM stdin;
\.


--
-- Data for Name: shopify_fulfillments; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_fulfillments (id, external_id, created_at, updated_at, delivered_at, display_status, estimated_delivery_at, in_transit_at, name, requires_shipping, status, total_quantity) FROM stdin;
\.


--
-- Data for Name: shopify_functions_app_bridges; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_functions_app_bridges (id, external_id, created_at, updated_at, create_path, details_path) FROM stdin;
\.


--
-- Data for Name: shopify_functions_error_historys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_functions_error_historys (id, external_id, created_at, updated_at, errors_first_occurred_at, first_occurred_at, has_been_shared_since_last_error, has_shared_recent_errors) FROM stdin;
\.


--
-- Data for Name: shopify_generic_files; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_generic_files (id, external_id, created_at, updated_at, alt, file_status, mime_type, original_file_size, url) FROM stdin;
\.


--
-- Data for Name: shopify_gift_card_credit_transactions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_gift_card_credit_transactions (id, external_id, created_at, updated_at, note, processed_at) FROM stdin;
\.


--
-- Data for Name: shopify_gift_card_debit_transactions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_gift_card_debit_transactions (id, external_id, created_at, updated_at, note, processed_at) FROM stdin;
\.


--
-- Data for Name: shopify_gift_card_recipients; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_gift_card_recipients (id, external_id, created_at, updated_at, message, preferred_name, send_notification_at) FROM stdin;
\.


--
-- Data for Name: shopify_gift_card_sales; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_gift_card_sales (id, external_id, created_at, updated_at, action_type, line_type, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_gift_cards; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_gift_cards (id, external_id, created_at, updated_at, deactivated_at, enabled, last_characters, masked_code, note, template_suffix) FROM stdin;
\.


--
-- Data for Name: shopify_image_upload_parameters; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_image_upload_parameters (id, external_id, created_at, updated_at, name, value) FROM stdin;
\.


--
-- Data for Name: shopify_images; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_images (id, store_id, external_id, created_at, updated_at, alt_text, height, original_src, src, transformed_src, url, width, metafield_id, private_metafield_id) FROM stdin;
\.


--
-- Data for Name: shopify_integrations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_integrations (id, store_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_inventory_adjustment_groups; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_inventory_adjustment_groups (id, external_id, created_at, updated_at, reason, reference_document_uri) FROM stdin;
\.


--
-- Data for Name: shopify_inventory_changes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_inventory_changes (id, external_id, created_at, updated_at, delta, ledger_document_uri, name, quantity_after_change) FROM stdin;
\.


--
-- Data for Name: shopify_inventory_item_measurements; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_inventory_item_measurements (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_inventory_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_inventory_items (id, external_id, created_at, updated_at, country_code_of_origin, duplicate_sku_count, harmonized_system_code, inventory_history_url, province_code_of_origin, requires_shipping, sku, tracked) FROM stdin;
\.


--
-- Data for Name: shopify_inventory_levels; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_inventory_levels (id, external_id, created_at, updated_at, can_deactivate, deactivation_alert) FROM stdin;
\.


--
-- Data for Name: shopify_inventory_propertiess; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_inventory_propertiess (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_inventory_quantity_names; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_inventory_quantity_names (id, external_id, created_at, updated_at, belongs_to, comprises, display_name, is_in_use, name) FROM stdin;
\.


--
-- Data for Name: shopify_inventory_quantitys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_inventory_quantitys (id, external_id, created_at, updated_at, name, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_inventory_scheduled_changes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_inventory_scheduled_changes (id, external_id, created_at, updated_at, expected_at, from_name, ledger_document_uri, quantity, to_name) FROM stdin;
\.


--
-- Data for Name: shopify_jobs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_jobs (id, external_id, created_at, updated_at, done) FROM stdin;
\.


--
-- Data for Name: shopify_limited_pending_order_counts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_limited_pending_order_counts (id, external_id, created_at, updated_at, at_max, count) FROM stdin;
\.


--
-- Data for Name: shopify_line_item_groups; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_line_item_groups (id, external_id, created_at, updated_at, quantity, title, variant_id, variant_sku) FROM stdin;
\.


--
-- Data for Name: shopify_line_item_selling_plans; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_line_item_selling_plans (id, external_id, created_at, updated_at, name, selling_plan_id) FROM stdin;
\.


--
-- Data for Name: shopify_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_line_items (id, external_id, created_at, updated_at, can_restock, current_quantity, fulfillable_quantity, fulfillment_status, image_src, is_gift_card, merchant_editable, name, non_fulfillable_quantity, quantity, refundable_quantity, requires_shipping, restockable, sku, taxable, title, unfulfilled_quantity, variant_title, vendor) FROM stdin;
\.


--
-- Data for Name: shopify_linked_metafields; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_linked_metafields (id, external_id, created_at, updated_at, key, namespace) FROM stdin;
\.


--
-- Data for Name: shopify_links; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_links (id, store_id, external_id, created_at, updated_at, label, url, translations_id) FROM stdin;
\.


--
-- Data for Name: shopify_local_payment_methods_payment_detailss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_local_payment_methods_payment_detailss (id, external_id, created_at, updated_at, payment_descriptor, payment_method_name) FROM stdin;
\.


--
-- Data for Name: shopify_locales; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_locales (id, external_id, created_at, updated_at, iso_code, name) FROM stdin;
\.


--
-- Data for Name: shopify_localization_extensions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_localization_extensions (id, external_id, created_at, updated_at, country_code, key, purpose, title, value) FROM stdin;
\.


--
-- Data for Name: shopify_location_addresss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_location_addresss (id, external_id, created_at, updated_at, address1, address2, city, country, country_code, formatted, latitude, longitude, phone, province, province_code, zip) FROM stdin;
\.


--
-- Data for Name: shopify_location_suggested_addresss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_location_suggested_addresss (id, external_id, created_at, updated_at, address1, address2, city, country, country_code, formatted, province, province_code, zip) FROM stdin;
\.


--
-- Data for Name: shopify_locations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_locations (id, external_id, created_at, updated_at, activatable, address_verified, deactivatable, deactivated_at, deletable, fulfills_online_orders, has_active_inventory, has_unfulfilled_orders, is_active, is_fulfillment_service, is_primary, name, ships_inventory) FROM stdin;
\.


--
-- Data for Name: shopify_mailing_addresss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_mailing_addresss (id, store_id, external_id, created_at, updated_at, address1, address2, city, company, coordinates_validated, country, country_code, country_code_v2, first_name, formatted, formatted_area, last_name, latitude, longitude, name, phone, province, province_code, time_zone, validation_result_summary, zip) FROM stdin;
\.


--
-- Data for Name: shopify_manual_discount_applications; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_manual_discount_applications (id, external_id, created_at, updated_at, allocation_method, description, index, target_selection, target_type, title) FROM stdin;
\.


--
-- Data for Name: shopify_market_catalogs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_market_catalogs (id, external_id, created_at, updated_at, status, title) FROM stdin;
\.


--
-- Data for Name: shopify_market_currency_settingss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_market_currency_settingss (id, external_id, created_at, updated_at, local_currencies) FROM stdin;
\.


--
-- Data for Name: shopify_market_localizable_contents; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_market_localizable_contents (id, external_id, created_at, updated_at, digest, key, value) FROM stdin;
\.


--
-- Data for Name: shopify_market_localizable_resources; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_market_localizable_resources (id, external_id, created_at, updated_at, resource_id) FROM stdin;
\.


--
-- Data for Name: shopify_market_localizations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_market_localizations (id, external_id, created_at, updated_at, key, outdated, value) FROM stdin;
\.


--
-- Data for Name: shopify_market_region_countrys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_market_region_countrys (id, external_id, created_at, updated_at, code, name) FROM stdin;
\.


--
-- Data for Name: shopify_market_web_presence_root_urls; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_market_web_presence_root_urls (id, external_id, created_at, updated_at, locale, url) FROM stdin;
\.


--
-- Data for Name: shopify_market_web_presences; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_market_web_presences (id, external_id, created_at, updated_at, subfolder_suffix) FROM stdin;
\.


--
-- Data for Name: shopify_marketing_activity_extension_app_errorss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_marketing_activity_extension_app_errorss (id, external_id, created_at, updated_at, code) FROM stdin;
\.


--
-- Data for Name: shopify_marketing_activitys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_marketing_activitys (id, external_id, created_at, updated_at, activity_list_url, form_data, hierarchy_level, in_main_workflow_version, is_external, marketing_channel, marketing_channel_type, parent_activity_id, parent_remote_id, source_and_medium, status, status_badge_type, status_badge_type_v2, status_label, status_transitioned_at, tactic, target_status, title, url_parameter_value) FROM stdin;
\.


--
-- Data for Name: shopify_marketing_budgets; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_marketing_budgets (id, external_id, created_at, updated_at, budget_type) FROM stdin;
\.


--
-- Data for Name: shopify_marketing_engagements; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_marketing_engagements (id, external_id, created_at, updated_at, channel_handle, clicks_count, comments_count, complaints_count, fails_count, favorites_count, first_time_customers, impressions_count, is_cumulative, orders, returning_customers, sends_count, sessions_count, shares_count, unique_clicks_count, unique_views_count, unsubscribes_count, views_count) FROM stdin;
\.


--
-- Data for Name: shopify_marketing_events; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_marketing_events (id, external_id, created_at, updated_at, channel, channel_handle, description, ended_at, manage_url, marketing_channel_type, preview_url, remote_id, scheduled_to_end_at, source_and_medium, started_at, target_type_display_text, type, utm_campaign, utm_medium, utm_source) FROM stdin;
\.


--
-- Data for Name: shopify_markets; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_markets (id, external_id, created_at, updated_at, enabled, handle, name, "primary") FROM stdin;
\.


--
-- Data for Name: shopify_media_errors; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_media_errors (id, external_id, created_at, updated_at, code, details, message) FROM stdin;
\.


--
-- Data for Name: shopify_media_image_original_sources; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_media_image_original_sources (id, external_id, created_at, updated_at, file_size, url) FROM stdin;
\.


--
-- Data for Name: shopify_media_images; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_media_images (id, external_id, created_at, updated_at, alt, file_status, image_src, media_content_type, mime_type, status) FROM stdin;
\.


--
-- Data for Name: shopify_media_preview_images; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_media_preview_images (id, external_id, created_at, updated_at, image_src, status) FROM stdin;
\.


--
-- Data for Name: shopify_media_warnings; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_media_warnings (id, external_id, created_at, updated_at, code, message) FROM stdin;
\.


--
-- Data for Name: shopify_menu_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_menu_items (id, external_id, created_at, updated_at, resource_id, tags, title, type, url) FROM stdin;
\.


--
-- Data for Name: shopify_menus; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_menus (id, external_id, created_at, updated_at, handle, is_default, title) FROM stdin;
\.


--
-- Data for Name: shopify_merchant_approval_signalss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_merchant_approval_signalss (id, external_id, created_at, updated_at, identity_verified, verified_by_shopify, verified_by_shopify_tier) FROM stdin;
\.


--
-- Data for Name: shopify_metafield_access_grants; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metafield_access_grants (id, external_id, created_at, updated_at, access, grantee) FROM stdin;
\.


--
-- Data for Name: shopify_metafield_accesss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metafield_accesss (id, external_id, created_at, updated_at, admin, customer_account, storefront) FROM stdin;
\.


--
-- Data for Name: shopify_metafield_capabilitiess; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metafield_capabilitiess (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_metafield_capability_admin_filterables; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metafield_capability_admin_filterables (id, external_id, created_at, updated_at, eligible, enabled, status) FROM stdin;
\.


--
-- Data for Name: shopify_metafield_capability_smart_collection_cond_9416fs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metafield_capability_smart_collection_cond_9416fs (id, external_id, created_at, updated_at, eligible, enabled) FROM stdin;
\.


--
-- Data for Name: shopify_metafield_definition_constraint_values; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metafield_definition_constraint_values (id, external_id, created_at, updated_at, value) FROM stdin;
\.


--
-- Data for Name: shopify_metafield_definition_constraintss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metafield_definition_constraintss (id, external_id, created_at, updated_at, key) FROM stdin;
\.


--
-- Data for Name: shopify_metafield_definition_supported_validations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metafield_definition_supported_validations (id, external_id, created_at, updated_at, name, type) FROM stdin;
\.


--
-- Data for Name: shopify_metafield_definition_types; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metafield_definition_types (id, external_id, created_at, updated_at, category, name, supports_definition_migrations, value_type) FROM stdin;
\.


--
-- Data for Name: shopify_metafield_definition_validations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metafield_definition_validations (id, external_id, created_at, updated_at, name, type, value) FROM stdin;
\.


--
-- Data for Name: shopify_metafield_definitions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metafield_definitions (id, external_id, created_at, updated_at, description, key, metafields_count, name, namespace, owner_type, pinned_position, use_as_collection_condition, validation_status, visible_to_storefront_api) FROM stdin;
\.


--
-- Data for Name: shopify_metafield_identifiers; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metafield_identifiers (id, external_id, created_at, updated_at, key, namespace, owner_id) FROM stdin;
\.


--
-- Data for Name: shopify_metafield_relations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metafield_relations (id, external_id, created_at, updated_at, key, name, namespace) FROM stdin;
\.


--
-- Data for Name: shopify_metafield_storefront_visibilitys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metafield_storefront_visibilitys (id, external_id, created_at, updated_at, key, namespace, owner_type) FROM stdin;
\.


--
-- Data for Name: shopify_metafields; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metafields (id, external_id, created_at, updated_at, compare_digest, description, key, namespace, owner_type, type, value) FROM stdin;
\.


--
-- Data for Name: shopify_metaobject_accesss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metaobject_accesss (id, external_id, created_at, updated_at, admin, storefront) FROM stdin;
\.


--
-- Data for Name: shopify_metaobject_capabilities_online_stores; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metaobject_capabilities_online_stores (id, external_id, created_at, updated_at, enabled) FROM stdin;
\.


--
-- Data for Name: shopify_metaobject_capabilities_publishables; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metaobject_capabilities_publishables (id, external_id, created_at, updated_at, enabled) FROM stdin;
\.


--
-- Data for Name: shopify_metaobject_capabilities_renderables; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metaobject_capabilities_renderables (id, external_id, created_at, updated_at, enabled) FROM stdin;
\.


--
-- Data for Name: shopify_metaobject_capabilities_translatables; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metaobject_capabilities_translatables (id, external_id, created_at, updated_at, enabled) FROM stdin;
\.


--
-- Data for Name: shopify_metaobject_capabilitiess; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metaobject_capabilitiess (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_metaobject_capability_data_online_stores; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metaobject_capability_data_online_stores (id, external_id, created_at, updated_at, template_suffix) FROM stdin;
\.


--
-- Data for Name: shopify_metaobject_capability_data_publishables; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metaobject_capability_data_publishables (id, external_id, created_at, updated_at, status) FROM stdin;
\.


--
-- Data for Name: shopify_metaobject_capability_datas; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metaobject_capability_datas (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_metaobject_capability_definition_data_onli_f6f7es; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metaobject_capability_definition_data_onli_f6f7es (id, external_id, created_at, updated_at, can_create_redirects, url_handle) FROM stdin;
\.


--
-- Data for Name: shopify_metaobject_capability_definition_data_rend_33c52s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metaobject_capability_definition_data_rend_33c52s (id, external_id, created_at, updated_at, meta_description_key, meta_title_key) FROM stdin;
\.


--
-- Data for Name: shopify_metaobject_definitions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metaobject_definitions (id, external_id, created_at, updated_at, description, display_name_key, has_thumbnail_field, metaobjects_count, name, type) FROM stdin;
\.


--
-- Data for Name: shopify_metaobject_field_definitions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metaobject_field_definitions (id, external_id, created_at, updated_at, description, key, name, required) FROM stdin;
\.


--
-- Data for Name: shopify_metaobject_fields; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metaobject_fields (id, external_id, created_at, updated_at, key, type, value) FROM stdin;
\.


--
-- Data for Name: shopify_metaobject_thumbnails; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metaobject_thumbnails (id, external_id, created_at, updated_at, hex) FROM stdin;
\.


--
-- Data for Name: shopify_metaobjects; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_metaobjects (id, external_id, created_at, updated_at, display_name, handle, type) FROM stdin;
\.


--
-- Data for Name: shopify_model3d_bounding_boxs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_model3d_bounding_boxs (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_model3d_sources; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_model3d_sources (id, external_id, created_at, updated_at, filesize, format, mime_type, url) FROM stdin;
\.


--
-- Data for Name: shopify_model3ds; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_model3ds (id, external_id, created_at, updated_at, alt, file_status, filename, media_content_type, status) FROM stdin;
\.


--
-- Data for Name: shopify_money_bags; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_money_bags (id, store_id, external_id, created_at, updated_at, presentment_money_id, shop_money_id) FROM stdin;
\.


--
-- Data for Name: shopify_money_v2s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_money_v2s (id, store_id, external_id, created_at, updated_at, amount, currency_code) FROM stdin;
\.


--
-- Data for Name: shopify_mutations_staged_upload_target_generate_up_2fbc2s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_mutations_staged_upload_target_generate_up_2fbc2s (id, external_id, created_at, updated_at, name, value) FROM stdin;
\.


--
-- Data for Name: shopify_navigation_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_navigation_items (id, external_id, created_at, updated_at, title, url) FROM stdin;
\.


--
-- Data for Name: shopify_online_store_password_protections; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_online_store_password_protections (id, external_id, created_at, updated_at, enabled) FROM stdin;
\.


--
-- Data for Name: shopify_online_store_theme_file_body_base64s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_online_store_theme_file_body_base64s (id, external_id, created_at, updated_at, content_base64) FROM stdin;
\.


--
-- Data for Name: shopify_online_store_theme_file_body_texts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_online_store_theme_file_body_texts (id, external_id, created_at, updated_at, content) FROM stdin;
\.


--
-- Data for Name: shopify_online_store_theme_file_body_urls; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_online_store_theme_file_body_urls (id, external_id, created_at, updated_at, url) FROM stdin;
\.


--
-- Data for Name: shopify_online_store_theme_file_operation_results; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_online_store_theme_file_operation_results (id, external_id, created_at, updated_at, filename) FROM stdin;
\.


--
-- Data for Name: shopify_online_store_theme_file_read_results; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_online_store_theme_file_read_results (id, external_id, created_at, updated_at, code, filename) FROM stdin;
\.


--
-- Data for Name: shopify_online_store_theme_files; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_online_store_theme_files (id, external_id, created_at, updated_at, checksum_md5, content_type, filename) FROM stdin;
\.


--
-- Data for Name: shopify_online_store_theme_files_user_errorss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_online_store_theme_files_user_errorss (id, external_id, created_at, updated_at, code, field, filename, message) FROM stdin;
\.


--
-- Data for Name: shopify_online_store_themes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_online_store_themes (id, external_id, created_at, updated_at, name, prefix, processing, processing_failed, role, theme_store_id) FROM stdin;
\.


--
-- Data for Name: shopify_online_stores; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_online_stores (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_order_adjustments; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_adjustments (id, external_id, created_at, updated_at, reason) FROM stdin;
\.


--
-- Data for Name: shopify_order_agreements; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_agreements (id, external_id, created_at, updated_at, happened_at, reason) FROM stdin;
\.


--
-- Data for Name: shopify_order_apps; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_apps (id, external_id, created_at, updated_at, icon_src, name) FROM stdin;
\.


--
-- Data for Name: shopify_order_cancellations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_cancellations (id, external_id, created_at, updated_at, staff_note) FROM stdin;
\.


--
-- Data for Name: shopify_order_dispute_summarys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_dispute_summarys (id, external_id, created_at, updated_at, initiated_as, status) FROM stdin;
\.


--
-- Data for Name: shopify_order_edit_agreements; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_edit_agreements (id, external_id, created_at, updated_at, happened_at, reason) FROM stdin;
\.


--
-- Data for Name: shopify_order_payment_collection_detailss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_payment_collection_detailss (id, external_id, created_at, updated_at, additional_payment_collection_url) FROM stdin;
\.


--
-- Data for Name: shopify_order_payment_statuss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_payment_statuss (id, external_id, created_at, updated_at, error_message, payment_reference_id, status, translated_error_message) FROM stdin;
\.


--
-- Data for Name: shopify_order_risk_assessments; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_risk_assessments (id, external_id, created_at, updated_at, risk_level) FROM stdin;
\.


--
-- Data for Name: shopify_order_risk_summarys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_risk_summarys (id, external_id, created_at, updated_at, recommendation) FROM stdin;
\.


--
-- Data for Name: shopify_order_risks; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_risks (id, external_id, created_at, updated_at, display, level, message) FROM stdin;
\.


--
-- Data for Name: shopify_order_staged_change_add_custom_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_staged_change_add_custom_items (id, external_id, created_at, updated_at, quantity, title) FROM stdin;
\.


--
-- Data for Name: shopify_order_staged_change_add_line_item_discounts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_staged_change_add_line_item_discounts (id, external_id, created_at, updated_at, description) FROM stdin;
\.


--
-- Data for Name: shopify_order_staged_change_add_shipping_lines; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_staged_change_add_shipping_lines (id, external_id, created_at, updated_at, phone, presentment_title, title) FROM stdin;
\.


--
-- Data for Name: shopify_order_staged_change_add_variants; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_staged_change_add_variants (id, external_id, created_at, updated_at, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_order_staged_change_decrement_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_staged_change_decrement_items (id, external_id, created_at, updated_at, delta, restock) FROM stdin;
\.


--
-- Data for Name: shopify_order_staged_change_increment_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_staged_change_increment_items (id, external_id, created_at, updated_at, delta) FROM stdin;
\.


--
-- Data for Name: shopify_order_staged_change_remove_shipping_lines; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_staged_change_remove_shipping_lines (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_order_transactions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_order_transactions (id, external_id, created_at, updated_at, account_number, authorization_code, authorization_expires_at, error_code, formatted_gateway, gateway, kind, manually_capturable, multi_capturable, payment_icon_src, payment_id, payment_method, processed_at, settlement_currency, settlement_currency_rate, status, test) FROM stdin;
\.


--
-- Data for Name: shopify_orders; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_orders (id, external_id, created_at, updated_at, billing_address_matches_shipping_address, can_mark_as_paid, can_notify_customer, cancel_reason, cancelled_at, capturable, client_ip, closed, closed_at, confirmation_number, confirmed, currency_code, current_subtotal_line_items_quantity, customer_accepts_marketing, customer_locale, discount_code, discount_codes, display_financial_status, display_fulfillment_status, duties_included, edited, email, estimated_taxes, fulfillable, fully_paid, has_timeline_comment, landing_page_display_text, landing_page_url, merchant_editable, merchant_editable_errors, name, note, payment_gateway_names, phone, po_number, presentment_currency_code, processed_at, referral_code, referrer_display_text, referrer_url, refundable, registered_source_url, requires_shipping, restockable, return_status, risk_level, source_identifier, source_name, status_page_url, subtotal_line_items_quantity, tags, tax_exempt, taxes_included, test, unpaid) FROM stdin;
\.


--
-- Data for Name: shopify_page_infos; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_page_infos (id, store_id, external_id, created_at, updated_at, end_cursor, has_next_page, has_previous_page, start_cursor) FROM stdin;
\.


--
-- Data for Name: shopify_pages; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_pages (id, external_id, created_at, updated_at, body_summary, default_cursor, handle, is_published, published_at, template_suffix, title) FROM stdin;
\.


--
-- Data for Name: shopify_payment_customization_errors; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_payment_customization_errors (id, external_id, created_at, updated_at, code, field, message) FROM stdin;
\.


--
-- Data for Name: shopify_payment_customizations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_payment_customizations (id, external_id, created_at, updated_at, enabled, function_id, title) FROM stdin;
\.


--
-- Data for Name: shopify_payment_mandates; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_payment_mandates (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_payment_schedules; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_payment_schedules (id, external_id, created_at, updated_at, completed_at, due_at, issued_at) FROM stdin;
\.


--
-- Data for Name: shopify_payment_settingss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_payment_settingss (id, external_id, created_at, updated_at, supported_digital_wallets) FROM stdin;
\.


--
-- Data for Name: shopify_payment_terms_templates; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_payment_terms_templates (id, external_id, created_at, updated_at, description, due_in_days, name, payment_terms_type, translated_name) FROM stdin;
\.


--
-- Data for Name: shopify_payment_termss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_payment_termss (id, external_id, created_at, updated_at, due_in_days, overdue, payment_terms_name, payment_terms_type, translated_name) FROM stdin;
\.


--
-- Data for Name: shopify_price_list_adjustment_settingss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_list_adjustment_settingss (id, external_id, created_at, updated_at, compare_at_mode) FROM stdin;
\.


--
-- Data for Name: shopify_price_list_adjustments; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_list_adjustments (id, external_id, created_at, updated_at, type, value) FROM stdin;
\.


--
-- Data for Name: shopify_price_list_parents; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_list_parents (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_price_list_prices; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_list_prices (id, external_id, created_at, updated_at, origin_type) FROM stdin;
\.


--
-- Data for Name: shopify_price_lists; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_lists (id, external_id, created_at, updated_at, currency, fixed_prices_count, name) FROM stdin;
\.


--
-- Data for Name: shopify_price_rule_customer_selections; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_rule_customer_selections (id, external_id, created_at, updated_at, for_all_customers) FROM stdin;
\.


--
-- Data for Name: shopify_price_rule_discount_codes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_rule_discount_codes (id, external_id, created_at, updated_at, code, usage_count) FROM stdin;
\.


--
-- Data for Name: shopify_price_rule_entitlement_to_prerequisite_qua_242abs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_rule_entitlement_to_prerequisite_qua_242abs (id, external_id, created_at, updated_at, entitlement_quantity, prerequisite_quantity) FROM stdin;
\.


--
-- Data for Name: shopify_price_rule_fixed_amount_values; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_rule_fixed_amount_values (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_price_rule_item_entitlementss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_rule_item_entitlementss (id, external_id, created_at, updated_at, target_all_line_items) FROM stdin;
\.


--
-- Data for Name: shopify_price_rule_line_item_prerequisitess; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_rule_line_item_prerequisitess (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_price_rule_money_ranges; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_rule_money_ranges (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_price_rule_percent_values; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_rule_percent_values (id, external_id, created_at, updated_at, percentage) FROM stdin;
\.


--
-- Data for Name: shopify_price_rule_prerequisite_to_entitlement_qua_13b24s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_rule_prerequisite_to_entitlement_qua_13b24s (id, external_id, created_at, updated_at, entitlement_quantity, prerequisite_quantity) FROM stdin;
\.


--
-- Data for Name: shopify_price_rule_quantity_ranges; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_rule_quantity_ranges (id, external_id, created_at, updated_at, greater_than, greater_than_or_equal_to, less_than, less_than_or_equal_to) FROM stdin;
\.


--
-- Data for Name: shopify_price_rule_shareable_urls; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_rule_shareable_urls (id, external_id, created_at, updated_at, target_item_image_src, target_type, title, url) FROM stdin;
\.


--
-- Data for Name: shopify_price_rule_shipping_line_entitlementss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_rule_shipping_line_entitlementss (id, external_id, created_at, updated_at, country_codes, include_rest_of_world, target_all_shipping_lines) FROM stdin;
\.


--
-- Data for Name: shopify_price_rule_validity_periods; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_rule_validity_periods (id, external_id, created_at, updated_at, "end", start) FROM stdin;
\.


--
-- Data for Name: shopify_price_rules; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_price_rules (id, external_id, created_at, updated_at, allocation_limit, allocation_method, discount_class, ends_at, features, has_timeline_comment, once_per_customer, starts_at, status, summary, target, title, traits, usage_count, usage_limit) FROM stdin;
\.


--
-- Data for Name: shopify_pricing_percentage_values; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_pricing_percentage_values (id, external_id, created_at, updated_at, percentage) FROM stdin;
\.


--
-- Data for Name: shopify_private_metafields; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_private_metafields (id, external_id, created_at, updated_at, key, namespace, value, value_type) FROM stdin;
\.


--
-- Data for Name: shopify_product_bundle_component_option_selection__dc101s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_bundle_component_option_selection__dc101s (id, external_id, created_at, updated_at, selection_status, value) FROM stdin;
\.


--
-- Data for Name: shopify_product_bundle_component_option_selections; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_bundle_component_option_selections (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_product_bundle_component_quantity_option_v_88df4s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_bundle_component_quantity_option_v_88df4s (id, external_id, created_at, updated_at, name, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_product_bundle_component_quantity_options; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_bundle_component_quantity_options (id, external_id, created_at, updated_at, name) FROM stdin;
\.


--
-- Data for Name: shopify_product_bundle_components; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_bundle_components (id, external_id, created_at, updated_at, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_product_bundle_operations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_bundle_operations (id, external_id, created_at, updated_at, status) FROM stdin;
\.


--
-- Data for Name: shopify_product_categorys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_categorys (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_product_compare_at_price_ranges; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_compare_at_price_ranges (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_product_contextual_pricings; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_contextual_pricings (id, external_id, created_at, updated_at, fixed_quantity_rules_count) FROM stdin;
\.


--
-- Data for Name: shopify_product_delete_operations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_delete_operations (id, external_id, created_at, updated_at, deleted_product_id, status) FROM stdin;
\.


--
-- Data for Name: shopify_product_duplicate_jobs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_duplicate_jobs (id, external_id, created_at, updated_at, done) FROM stdin;
\.


--
-- Data for Name: shopify_product_duplicate_operations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_duplicate_operations (id, external_id, created_at, updated_at, status) FROM stdin;
\.


--
-- Data for Name: shopify_product_feeds; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_feeds (id, external_id, created_at, updated_at, country, language, status) FROM stdin;
\.


--
-- Data for Name: shopify_product_option_value_swatchs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_option_value_swatchs (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_product_option_values; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_option_values (id, external_id, created_at, updated_at, has_variants, linked_metafield_value, name) FROM stdin;
\.


--
-- Data for Name: shopify_product_options; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_options (id, external_id, created_at, updated_at, name, "position", "values") FROM stdin;
\.


--
-- Data for Name: shopify_product_price_range_v2s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_price_range_v2s (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_product_price_ranges; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_price_ranges (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_product_publications; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_publications (id, external_id, created_at, updated_at, is_published, publish_date) FROM stdin;
\.


--
-- Data for Name: shopify_product_resource_feedbacks; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_resource_feedbacks (id, external_id, created_at, updated_at, feedback_generated_at, messages, product_id, product_updated_at, state) FROM stdin;
\.


--
-- Data for Name: shopify_product_sales; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_sales (id, external_id, created_at, updated_at, action_type, line_type, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_product_set_operations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_set_operations (id, external_id, created_at, updated_at, status) FROM stdin;
\.


--
-- Data for Name: shopify_product_taxonomy_nodes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_taxonomy_nodes (id, external_id, created_at, updated_at, full_name, is_leaf, is_root, name) FROM stdin;
\.


--
-- Data for Name: shopify_product_variant_components; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_variant_components (id, external_id, created_at, updated_at, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_product_variant_contextual_pricings; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_variant_contextual_pricings (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_product_variant_price_pairs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_variant_price_pairs (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_product_variants; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_product_variants (id, external_id, created_at, updated_at, available_for_sale, barcode, default_cursor, display_name, image_src, inventory_policy, inventory_quantity, "position", requires_components, sellable_online_quantity, selling_plan_group_count, sku, tax_code, taxable, title) FROM stdin;
\.


--
-- Data for Name: shopify_products; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_products (id, external_id, created_at, updated_at, body_html, combined_listing_role, custom_product_type, default_cursor, description, description_plain_summary, featured_image_src, gift_card_template_suffix, handle, has_only_default_variant, has_out_of_stock_variants, has_variants_that_requires_components, in_collection, is_gift_card, online_store_preview_url, online_store_url, product_type, publication_count, published_at, published_in_context, published_on_channel, published_on_current_channel, published_on_current_publication, published_on_publication, requires_selling_plan, selling_plan_group_count, status, tags, template_suffix, title, total_inventory, total_variants, tracks_inventory, vendor) FROM stdin;
\.


--
-- Data for Name: shopify_publication_resource_operations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_publication_resource_operations (id, external_id, created_at, updated_at, processed_row_count, status) FROM stdin;
\.


--
-- Data for Name: shopify_publications; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_publications (id, external_id, created_at, updated_at, auto_publish, has_collection, name, supports_future_publishing) FROM stdin;
\.


--
-- Data for Name: shopify_purchasing_companys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_purchasing_companys (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_quantity_price_breaks; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_quantity_price_breaks (id, external_id, created_at, updated_at, minimum_quantity) FROM stdin;
\.


--
-- Data for Name: shopify_quantity_rules; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_quantity_rules (id, external_id, created_at, updated_at, increment, is_default, maximum, minimum, origin_type) FROM stdin;
\.


--
-- Data for Name: shopify_refund_agreements; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_refund_agreements (id, external_id, created_at, updated_at, happened_at, reason) FROM stdin;
\.


--
-- Data for Name: shopify_refund_dutys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_refund_dutys (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_refund_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_refund_line_items (id, external_id, created_at, updated_at, quantity, restock_type, restocked) FROM stdin;
\.


--
-- Data for Name: shopify_refund_shipping_lines; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_refund_shipping_lines (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_refunds; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_refunds (id, external_id, created_at, updated_at, note) FROM stdin;
\.


--
-- Data for Name: shopify_resource_alert_actions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_resource_alert_actions (id, external_id, created_at, updated_at, "primary", show, title, url) FROM stdin;
\.


--
-- Data for Name: shopify_resource_alerts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_resource_alerts (id, external_id, created_at, updated_at, dismissible_handle, icon, severity, title) FROM stdin;
\.


--
-- Data for Name: shopify_resource_feedbacks; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_resource_feedbacks (id, external_id, created_at, updated_at, summary) FROM stdin;
\.


--
-- Data for Name: shopify_resource_publication_v2s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_resource_publication_v2s (id, external_id, created_at, updated_at, is_published, publish_date) FROM stdin;
\.


--
-- Data for Name: shopify_resource_publications; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_resource_publications (id, external_id, created_at, updated_at, is_published, publish_date) FROM stdin;
\.


--
-- Data for Name: shopify_restocking_fees; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_restocking_fees (id, external_id, created_at, updated_at, percentage) FROM stdin;
\.


--
-- Data for Name: shopify_restricted_for_resources; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_restricted_for_resources (id, external_id, created_at, updated_at, restricted, restricted_reason) FROM stdin;
\.


--
-- Data for Name: shopify_return_agreements; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_return_agreements (id, external_id, created_at, updated_at, happened_at, reason) FROM stdin;
\.


--
-- Data for Name: shopify_return_declines; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_return_declines (id, external_id, created_at, updated_at, note, reason) FROM stdin;
\.


--
-- Data for Name: shopify_return_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_return_line_items (id, external_id, created_at, updated_at, customer_note, quantity, refundable_quantity, refunded_quantity, return_reason, return_reason_note) FROM stdin;
\.


--
-- Data for Name: shopify_return_objs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_return_objs (id, external_id, created_at, updated_at, name, status, total_quantity) FROM stdin;
\.


--
-- Data for Name: shopify_return_shipping_fees; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_return_shipping_fees (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_returnable_fulfillment_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_returnable_fulfillment_line_items (id, external_id, created_at, updated_at, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_returnable_fulfillments; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_returnable_fulfillments (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_reverse_delivery_label_v2s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_reverse_delivery_label_v2s (id, external_id, created_at, updated_at, public_file_url) FROM stdin;
\.


--
-- Data for Name: shopify_reverse_delivery_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_reverse_delivery_line_items (id, external_id, created_at, updated_at, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_reverse_delivery_shipping_deliverables; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_reverse_delivery_shipping_deliverables (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_reverse_delivery_tracking_v2s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_reverse_delivery_tracking_v2s (id, external_id, created_at, updated_at, carrier_name, number, url) FROM stdin;
\.


--
-- Data for Name: shopify_reverse_deliverys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_reverse_deliverys (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_reverse_fulfillment_order_dispositions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_reverse_fulfillment_order_dispositions (id, external_id, created_at, updated_at, quantity, type) FROM stdin;
\.


--
-- Data for Name: shopify_reverse_fulfillment_order_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_reverse_fulfillment_order_line_items (id, external_id, created_at, updated_at, total_quantity) FROM stdin;
\.


--
-- Data for Name: shopify_reverse_fulfillment_order_third_party_conf_d7032s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_reverse_fulfillment_order_third_party_conf_d7032s (id, external_id, created_at, updated_at, status) FROM stdin;
\.


--
-- Data for Name: shopify_reverse_fulfillment_orders; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_reverse_fulfillment_orders (id, external_id, created_at, updated_at, status) FROM stdin;
\.


--
-- Data for Name: shopify_risk_facts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_risk_facts (id, external_id, created_at, updated_at, description, sentiment) FROM stdin;
\.


--
-- Data for Name: shopify_row_counts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_row_counts (id, store_id, external_id, created_at, updated_at, count, exceeds_max) FROM stdin;
\.


--
-- Data for Name: shopify_sale_additional_fees; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_sale_additional_fees (id, external_id, created_at, updated_at, name) FROM stdin;
\.


--
-- Data for Name: shopify_sale_taxs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_sale_taxs (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_saved_searchs; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_saved_searchs (id, external_id, created_at, updated_at, name, query, resource_type, search_terms) FROM stdin;
\.


--
-- Data for Name: shopify_script_discount_applications; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_script_discount_applications (id, external_id, created_at, updated_at, allocation_method, description, index, target_selection, target_type, title) FROM stdin;
\.


--
-- Data for Name: shopify_script_tags; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_script_tags (id, external_id, created_at, updated_at, cache, display_scope, src) FROM stdin;
\.


--
-- Data for Name: shopify_search_filter_optionss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_search_filter_optionss (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_search_filters; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_search_filters (id, store_id, external_id, created_at, updated_at, key, value) FROM stdin;
\.


--
-- Data for Name: shopify_search_results; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_search_results (id, external_id, created_at, updated_at, description, image_src, title, url) FROM stdin;
\.


--
-- Data for Name: shopify_segment_association_filters; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_segment_association_filters (id, store_id, external_id, created_at, updated_at, localized_name, multi_value, query_name) FROM stdin;
\.


--
-- Data for Name: shopify_segment_attribute_statisticss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_segment_attribute_statisticss (id, external_id, created_at, updated_at, average, sum) FROM stdin;
\.


--
-- Data for Name: shopify_segment_boolean_filters; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_segment_boolean_filters (id, store_id, external_id, created_at, updated_at, localized_name, multi_value, query_name) FROM stdin;
\.


--
-- Data for Name: shopify_segment_date_filters; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_segment_date_filters (id, store_id, external_id, created_at, updated_at, localized_name, multi_value, query_name) FROM stdin;
\.


--
-- Data for Name: shopify_segment_enum_filters; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_segment_enum_filters (id, store_id, external_id, created_at, updated_at, localized_name, multi_value, query_name) FROM stdin;
\.


--
-- Data for Name: shopify_segment_event_filter_parameters; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_segment_event_filter_parameters (id, external_id, created_at, updated_at, accepts_multiple_values, localized_description, localized_name, optional, parameter_type, query_name) FROM stdin;
\.


--
-- Data for Name: shopify_segment_event_filters; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_segment_event_filters (id, store_id, external_id, created_at, updated_at, localized_name, multi_value, query_name, return_value_type, parameters_id) FROM stdin;
\.


--
-- Data for Name: shopify_segment_float_filters; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_segment_float_filters (id, store_id, external_id, created_at, updated_at, localized_name, multi_value, query_name) FROM stdin;
\.


--
-- Data for Name: shopify_segment_integer_filters; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_segment_integer_filters (id, store_id, external_id, created_at, updated_at, localized_name, multi_value, query_name) FROM stdin;
\.


--
-- Data for Name: shopify_segment_membership_responses; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_segment_membership_responses (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_segment_memberships; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_segment_memberships (id, external_id, created_at, updated_at, is_member, segment_id) FROM stdin;
\.


--
-- Data for Name: shopify_segment_migrations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_segment_migrations (id, external_id, created_at, updated_at, saved_search_id, segment_id) FROM stdin;
\.


--
-- Data for Name: shopify_segment_statisticss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_segment_statisticss (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_segment_string_filters; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_segment_string_filters (id, store_id, external_id, created_at, updated_at, localized_name, multi_value, query_name) FROM stdin;
\.


--
-- Data for Name: shopify_segment_values; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_segment_values (id, external_id, created_at, updated_at, localized_value, query_name) FROM stdin;
\.


--
-- Data for Name: shopify_segments; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_segments (id, external_id, created_at, updated_at, creation_date, last_edit_date, name, query) FROM stdin;
\.


--
-- Data for Name: shopify_selected_options; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_selected_options (id, external_id, created_at, updated_at, name, value) FROM stdin;
\.


--
-- Data for Name: shopify_selling_plan_anchors; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_selling_plan_anchors (id, external_id, created_at, updated_at, cutoff_day, day, month, type) FROM stdin;
\.


--
-- Data for Name: shopify_selling_plan_checkout_charge_percentage_va_8339as; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_selling_plan_checkout_charge_percentage_va_8339as (id, external_id, created_at, updated_at, percentage) FROM stdin;
\.


--
-- Data for Name: shopify_selling_plan_checkout_charges; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_selling_plan_checkout_charges (id, external_id, created_at, updated_at, type) FROM stdin;
\.


--
-- Data for Name: shopify_selling_plan_fixed_billing_policys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_selling_plan_fixed_billing_policys (id, external_id, created_at, updated_at, remaining_balance_charge_exact_time, remaining_balance_charge_time_after_checkout, remaining_balance_charge_trigger) FROM stdin;
\.


--
-- Data for Name: shopify_selling_plan_fixed_delivery_policys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_selling_plan_fixed_delivery_policys (id, external_id, created_at, updated_at, cutoff, fulfillment_exact_time, fulfillment_trigger, intent, pre_anchor_behavior) FROM stdin;
\.


--
-- Data for Name: shopify_selling_plan_fixed_pricing_policys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_selling_plan_fixed_pricing_policys (id, external_id, created_at, updated_at, adjustment_type) FROM stdin;
\.


--
-- Data for Name: shopify_selling_plan_groups; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_selling_plan_groups (id, external_id, created_at, updated_at, app_id, applies_to_product, applies_to_product_variant, applies_to_product_variants, description, merchant_code, name, options, "position", summary) FROM stdin;
\.


--
-- Data for Name: shopify_selling_plan_inventory_policys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_selling_plan_inventory_policys (id, external_id, created_at, updated_at, reserve) FROM stdin;
\.


--
-- Data for Name: shopify_selling_plan_pricing_policy_percentage_val_89796s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_selling_plan_pricing_policy_percentage_val_89796s (id, external_id, created_at, updated_at, percentage) FROM stdin;
\.


--
-- Data for Name: shopify_selling_plan_recurring_billing_policys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_selling_plan_recurring_billing_policys (id, external_id, created_at, updated_at, "interval", interval_count, max_cycles, min_cycles) FROM stdin;
\.


--
-- Data for Name: shopify_selling_plan_recurring_delivery_policys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_selling_plan_recurring_delivery_policys (id, external_id, created_at, updated_at, cutoff, intent, "interval", interval_count, pre_anchor_behavior) FROM stdin;
\.


--
-- Data for Name: shopify_selling_plan_recurring_pricing_policys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_selling_plan_recurring_pricing_policys (id, external_id, created_at, updated_at, adjustment_type, after_cycle) FROM stdin;
\.


--
-- Data for Name: shopify_selling_plans; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_selling_plans (id, external_id, created_at, updated_at, category, description, name, options, "position") FROM stdin;
\.


--
-- Data for Name: shopify_seos; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_seos (id, external_id, created_at, updated_at, description, title) FROM stdin;
\.


--
-- Data for Name: shopify_server_pixels; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_server_pixels (id, external_id, created_at, updated_at, status, webhook_endpoint_address) FROM stdin;
\.


--
-- Data for Name: shopify_shipping_line_sales; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shipping_line_sales (id, external_id, created_at, updated_at, action_type, line_type, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_shipping_lines; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shipping_lines (id, external_id, created_at, updated_at, carrier_identifier, code, custom, delivery_category, is_removed, phone, shipping_rate_handle, source, title) FROM stdin;
\.


--
-- Data for Name: shopify_shipping_rates; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shipping_rates (id, external_id, created_at, updated_at, handle, title) FROM stdin;
\.


--
-- Data for Name: shopify_shipping_refunds; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shipping_refunds (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_shop_addresss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shop_addresss (id, external_id, created_at, updated_at, address1, address2, city, company, coordinates_validated, country, country_code, country_code_v2, first_name, formatted, formatted_area, last_name, latitude, longitude, name, phone, province, province_code, zip) FROM stdin;
\.


--
-- Data for Name: shopify_shop_alert_actions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shop_alert_actions (id, external_id, created_at, updated_at, title, url) FROM stdin;
\.


--
-- Data for Name: shopify_shop_alerts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shop_alerts (id, external_id, created_at, updated_at, description) FROM stdin;
\.


--
-- Data for Name: shopify_shop_billing_preferencess; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shop_billing_preferencess (id, external_id, created_at, updated_at, currency) FROM stdin;
\.


--
-- Data for Name: shopify_shop_featuress; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shop_featuress (id, external_id, created_at, updated_at, avalara_avatax, branding, captcha, captcha_external_domains, delivery_profiles, dynamic_remarketing, eligible_for_subscription_migration, eligible_for_subscriptions, gift_cards, harmonized_system_code, international_domains, international_price_overrides, international_price_rules, legacy_subscription_gateway_enabled, live_view, multi_location, onboarding_visual, paypal_express_subscription_gateway_status, reports, sells_subscriptions, shopify_plus, show_metrics, storefront, using_shopify_balance) FROM stdin;
\.


--
-- Data for Name: shopify_shop_locales; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shop_locales (id, external_id, created_at, updated_at, locale, name, "primary", published) FROM stdin;
\.


--
-- Data for Name: shopify_shop_pay_installments_payment_detailss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shop_pay_installments_payment_detailss (id, external_id, created_at, updated_at, payment_method_name) FROM stdin;
\.


--
-- Data for Name: shopify_shop_plans; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shop_plans (id, store_id, external_id, created_at, updated_at, display_name, partner_development, shopify_plus) FROM stdin;
\.


--
-- Data for Name: shopify_shop_policys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shop_policys (id, external_id, created_at, updated_at, title, type, url) FROM stdin;
\.


--
-- Data for Name: shopify_shop_resource_limitss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shop_resource_limitss (id, external_id, created_at, updated_at, location_limit, max_product_options, max_product_variants, redirect_limit_reached) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_functions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_functions (id, external_id, created_at, updated_at, api_type, api_version, app_key, description, input_query, title, use_creation_ui) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_accounts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_accounts (id, external_id, created_at, updated_at, activated, charge_statement_descriptor, country, default_currency, onboardable, payout_statement_descriptor) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_adjustment_orders; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_adjustment_orders (id, external_id, created_at, updated_at, link, name, order_transaction_id) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_associated_orders; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_associated_orders (id, external_id, created_at, updated_at, name) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_balance_transaction_assoc_13997s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_balance_transaction_assoc_13997s (id, external_id, created_at, updated_at, status) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_balance_transactions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_balance_transactions (id, external_id, created_at, updated_at, adjustment_reason, source_id, source_order_transaction_id, source_type, test, transaction_date, type) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_bank_accounts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_bank_accounts (id, external_id, created_at, updated_at, account_number, account_number_last_digits, bank_name, country, currency, routing_number, status) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_default_charge_statement__c00d2s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_default_charge_statement__c00d2s (id, external_id, created_at, updated_at, "default", prefix) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_dispute_evidences; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_dispute_evidences (id, external_id, created_at, updated_at, access_activity_log, cancellation_policy_disclosure, cancellation_rebuttal, customer_email_address, customer_first_name, customer_last_name, customer_purchase_ip, product_description, refund_policy_disclosure, refund_refusal_explanation, submitted, uncategorized_text) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_dispute_file_uploads; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_dispute_file_uploads (id, external_id, created_at, updated_at, dispute_evidence_type, file_size, file_type, original_file_name, url) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_dispute_fulfillments; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_dispute_fulfillments (id, external_id, created_at, updated_at, shipping_carrier, shipping_tracking_number) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_dispute_reason_detailss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_dispute_reason_detailss (id, external_id, created_at, updated_at, network_reason_code, reason) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_disputes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_disputes (id, external_id, created_at, updated_at, initiated_at, status, type) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_extended_authorizations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_extended_authorizations (id, external_id, created_at, updated_at, extended_authorization_expires_at, standard_authorization_expires_at) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_jp_charge_statement_descr_b9a66s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_jp_charge_statement_descr_b9a66s (id, external_id, created_at, updated_at, "default", kana, kanji, prefix) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_payout_schedules; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_payout_schedules (id, external_id, created_at, updated_at, "interval", monthly_anchor, weekly_anchor) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_payout_summarys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_payout_summarys (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_payouts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_payouts (id, external_id, created_at, updated_at, issued_at, status, transaction_type) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_refund_sets; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_refund_sets (id, external_id, created_at, updated_at, acquirer_reference_number) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_tooling_provider_payouts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_tooling_provider_payouts (id, external_id, created_at, updated_at, arrival_date, currency, remote_id) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_payments_transaction_sets; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_payments_transaction_sets (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_protect_order_eligibilitys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_protect_order_eligibilitys (id, external_id, created_at, updated_at, status) FROM stdin;
\.


--
-- Data for Name: shopify_shopify_protect_order_summarys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shopify_protect_order_summarys (id, external_id, created_at, updated_at, status) FROM stdin;
\.


--
-- Data for Name: shopify_shops; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_shops (id, external_id, created_at, updated_at, analytics_token, checkout_api_supported, contact_email, currency_code, customer_accounts, description, email, enabled_presentment_currencies, iana_timezone, marketing_sms_consent_enabled_at_checkout, myshopify_domain, name, order_number_format_prefix, order_number_format_suffix, plan_display_name, publication_count, rich_text_editor_url, setup_required, ships_to_countries, shop_owner_name, storefront_url, tax_shipping, taxes_included, timezone_abbreviation, timezone_offset, timezone_offset_minutes, transactional_sms_disabled, unit_system, uploaded_images_by_ids_src, url, weight_unit) FROM stdin;
\.


--
-- Data for Name: shopify_staff_member_private_datas; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_staff_member_private_datas (id, external_id, created_at, updated_at, account_settings_url, permissions) FROM stdin;
\.


--
-- Data for Name: shopify_staff_members; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_staff_members (id, external_id, created_at, updated_at, account_type, active, avatar_src, email, "exists", first_name, initials, is_shop_owner, last_name, locale, name, phone) FROM stdin;
\.


--
-- Data for Name: shopify_staged_media_upload_targets; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_staged_media_upload_targets (id, external_id, created_at, updated_at, resource_url, url) FROM stdin;
\.


--
-- Data for Name: shopify_staged_upload_parameters; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_staged_upload_parameters (id, external_id, created_at, updated_at, name, value) FROM stdin;
\.


--
-- Data for Name: shopify_staged_upload_targets; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_staged_upload_targets (id, external_id, created_at, updated_at, url) FROM stdin;
\.


--
-- Data for Name: shopify_standard_metafield_definition_templates; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_standard_metafield_definition_templates (id, external_id, created_at, updated_at, description, key, name, namespace, owner_types, visible_to_storefront_api) FROM stdin;
\.


--
-- Data for Name: shopify_standardized_product_types; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_standardized_product_types (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_store_credit_account_credit_transactions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_store_credit_account_credit_transactions (id, external_id, created_at, updated_at, expires_at) FROM stdin;
\.


--
-- Data for Name: shopify_store_credit_account_debit_revert_transact_22361s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_store_credit_account_debit_revert_transact_22361s (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_store_credit_account_debit_transactions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_store_credit_account_debit_transactions (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_store_credit_account_expiration_transactio_11160s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_store_credit_account_expiration_transactio_11160s (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_store_credit_accounts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_store_credit_accounts (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_storefront_access_tokens; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_storefront_access_tokens (id, external_id, created_at, updated_at, access_token, title) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_applied_code_discounts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_applied_code_discounts (id, external_id, created_at, updated_at, redeem_code, rejection_reason) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_billing_attempts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_billing_attempts (id, external_id, created_at, updated_at, completed_at, error_code, error_message, idempotency_key, next_action_url, origin_time, payment_group_id, payment_session_id, ready) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_billing_cycle_edited_contracts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_billing_cycle_edited_contracts (id, external_id, created_at, updated_at, app_admin_url, currency_code, line_count, note) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_billing_cycles; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_billing_cycles (id, external_id, created_at, updated_at, billing_attempt_expected_date, cycle_end_at, cycle_index, cycle_start_at, edited, skipped, status) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_billing_policys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_billing_policys (id, external_id, created_at, updated_at, "interval", interval_count, max_cycles, min_cycles) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_contracts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_contracts (id, external_id, created_at, updated_at, app_admin_url, currency_code, last_billing_attempt_error_type, last_payment_status, line_count, next_billing_date, note, status) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_cycle_price_adjustments; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_cycle_price_adjustments (id, external_id, created_at, updated_at, adjustment_type, after_cycle) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_delivery_method_local_deliver_62924s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_delivery_method_local_deliver_62924s (id, external_id, created_at, updated_at, code, description, instructions, phone, presentment_title, title) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_delivery_method_local_deliver_9f7c1s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_delivery_method_local_deliver_9f7c1s (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_delivery_method_pickup_options; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_delivery_method_pickup_options (id, external_id, created_at, updated_at, code, description, presentment_title, title) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_delivery_method_pickups; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_delivery_method_pickups (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_delivery_method_shipping_opti_a8dd4s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_delivery_method_shipping_opti_a8dd4s (id, external_id, created_at, updated_at, code, description, presentment_title, title) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_delivery_method_shippings; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_delivery_method_shippings (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_delivery_option_result_failur_181d4s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_delivery_option_result_failur_181d4s (id, external_id, created_at, updated_at, message) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_delivery_option_result_succes_d8302s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_delivery_option_result_succes_d8302s (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_delivery_policys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_delivery_policys (id, external_id, created_at, updated_at, "interval", interval_count) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_discount_allocations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_discount_allocations (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_discount_entitled_liness; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_discount_entitled_liness (id, external_id, created_at, updated_at, "all") FROM stdin;
\.


--
-- Data for Name: shopify_subscription_discount_fixed_amount_values; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_discount_fixed_amount_values (id, external_id, created_at, updated_at, applies_on_each_item) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_discount_percentage_values; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_discount_percentage_values (id, external_id, created_at, updated_at, percentage) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_drafts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_drafts (id, external_id, created_at, updated_at, currency_code, next_billing_date, note, status) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_lines; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_lines (id, external_id, created_at, updated_at, product_id, quantity, requires_shipping, selling_plan_id, selling_plan_name, sku, taxable, title, variant_id, variant_image_src, variant_title) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_local_delivery_options; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_local_delivery_options (id, external_id, created_at, updated_at, code, description, phone_required, presentment_title, title) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_mailing_addresss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_mailing_addresss (id, external_id, created_at, updated_at, address1, address2, city, company, country, country_code, first_name, last_name, name, phone, province, province_code, zip) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_manual_discounts; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_manual_discounts (id, external_id, created_at, updated_at, recurring_cycle_limit, rejection_reason, target_type, title, type, usage_count) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_pickup_options; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_pickup_options (id, external_id, created_at, updated_at, code, description, phone_required, pickup_time, presentment_title, title) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_pricing_policys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_pricing_policys (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_shipping_option_result_failur_a06a2s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_shipping_option_result_failur_a06a2s (id, external_id, created_at, updated_at, message) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_shipping_option_result_succes_92ddes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_shipping_option_result_succes_92ddes (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_subscription_shipping_options; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_subscription_shipping_options (id, external_id, created_at, updated_at, code, description, phone_required, presentment_title, title) FROM stdin;
\.


--
-- Data for Name: shopify_suggested_order_transactions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_suggested_order_transactions (id, external_id, created_at, updated_at, account_number, formatted_gateway, gateway, kind) FROM stdin;
\.


--
-- Data for Name: shopify_suggested_refunds; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_suggested_refunds (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_suggested_return_refunds; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_suggested_return_refunds (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_tax_app_configurations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_tax_app_configurations (id, external_id, created_at, updated_at, state) FROM stdin;
\.


--
-- Data for Name: shopify_tax_lines; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_tax_lines (id, store_id, external_id, created_at, updated_at, channel_liable, rate, rate_percentage, source, title, price_set_id) FROM stdin;
\.


--
-- Data for Name: shopify_taxonomy_attributes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_taxonomy_attributes (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_taxonomy_categorys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_taxonomy_categorys (id, external_id, created_at, updated_at, ancestor_ids, children_ids, full_name, is_archived, is_leaf, is_root, level, name, parent_id) FROM stdin;
\.


--
-- Data for Name: shopify_taxonomy_choice_list_attributes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_taxonomy_choice_list_attributes (id, external_id, created_at, updated_at, name) FROM stdin;
\.


--
-- Data for Name: shopify_taxonomy_measurement_attributes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_taxonomy_measurement_attributes (id, external_id, created_at, updated_at, name) FROM stdin;
\.


--
-- Data for Name: shopify_taxonomy_values; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_taxonomy_values (id, external_id, created_at, updated_at, name) FROM stdin;
\.


--
-- Data for Name: shopify_taxonomys; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_taxonomys (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_tender_transaction_credit_card_detailss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_tender_transaction_credit_card_detailss (id, external_id, created_at, updated_at, credit_card_company, credit_card_number) FROM stdin;
\.


--
-- Data for Name: shopify_tender_transactions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_tender_transactions (id, external_id, created_at, updated_at, payment_method, processed_at, remote_reference, test) FROM stdin;
\.


--
-- Data for Name: shopify_tip_sales; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_tip_sales (id, external_id, created_at, updated_at, action_type, line_type, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_transaction_fees; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_transaction_fees (id, external_id, created_at, updated_at, flat_fee_name, rate, rate_name, type) FROM stdin;
\.


--
-- Data for Name: shopify_translatable_contents; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_translatable_contents (id, external_id, created_at, updated_at, digest, key, locale, type, value) FROM stdin;
\.


--
-- Data for Name: shopify_translatable_resources; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_translatable_resources (id, external_id, created_at, updated_at, resource_id) FROM stdin;
\.


--
-- Data for Name: shopify_translations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_translations (id, external_id, created_at, updated_at, key, locale, outdated, value) FROM stdin;
\.


--
-- Data for Name: shopify_typed_attributes; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_typed_attributes (id, external_id, created_at, updated_at, key, value) FROM stdin;
\.


--
-- Data for Name: shopify_unit_price_measurements; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_unit_price_measurements (id, external_id, created_at, updated_at, measured_type, quantity_unit, quantity_value, reference_unit, reference_value) FROM stdin;
\.


--
-- Data for Name: shopify_unknown_sales; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_unknown_sales (id, external_id, created_at, updated_at, action_type, line_type, quantity) FROM stdin;
\.


--
-- Data for Name: shopify_unverified_return_line_items; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_unverified_return_line_items (id, external_id, created_at, updated_at, customer_note, quantity, refundable_quantity, refunded_quantity, return_reason, return_reason_note) FROM stdin;
\.


--
-- Data for Name: shopify_url_redirect_import_previews; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_url_redirect_import_previews (id, external_id, created_at, updated_at, path, target) FROM stdin;
\.


--
-- Data for Name: shopify_url_redirect_imports; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_url_redirect_imports (id, external_id, created_at, updated_at, count, created_count, failed_count, finished, finished_at, updated_count) FROM stdin;
\.


--
-- Data for Name: shopify_url_redirects; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_url_redirects (id, external_id, created_at, updated_at, path, target) FROM stdin;
\.


--
-- Data for Name: shopify_utm_parameterss; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_utm_parameterss (id, external_id, created_at, updated_at, campaign, content, medium, source, term) FROM stdin;
\.


--
-- Data for Name: shopify_validations; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_validations (id, external_id, created_at, updated_at, block_on_failure, enabled, title) FROM stdin;
\.


--
-- Data for Name: shopify_vault_credit_cards; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_vault_credit_cards (id, external_id, created_at, updated_at, brand, expired, expiry_month, expiry_year, last_digits, name) FROM stdin;
\.


--
-- Data for Name: shopify_vault_paypal_billing_agreements; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_vault_paypal_billing_agreements (id, external_id, created_at, updated_at, inactive, name, paypal_account_email) FROM stdin;
\.


--
-- Data for Name: shopify_vector3s; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_vector3s (id, external_id, created_at, updated_at, x, y, z) FROM stdin;
\.


--
-- Data for Name: shopify_video_sources; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_video_sources (id, external_id, created_at, updated_at, file_size, format, height, mime_type, url, width) FROM stdin;
\.


--
-- Data for Name: shopify_videos; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_videos (id, external_id, created_at, updated_at, alt, duration, file_status, filename, media_content_type, status) FROM stdin;
\.


--
-- Data for Name: shopify_web_pixels; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_web_pixels (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_webhook_event_bridge_endpoints; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_webhook_event_bridge_endpoints (id, external_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: shopify_webhook_http_endpoints; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_webhook_http_endpoints (id, external_id, created_at, updated_at, callback_url) FROM stdin;
\.


--
-- Data for Name: shopify_webhook_pub_sub_endpoints; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_webhook_pub_sub_endpoints (id, external_id, created_at, updated_at, pub_sub_project, pub_sub_topic) FROM stdin;
\.


--
-- Data for Name: shopify_webhook_subscriptions; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_webhook_subscriptions (id, external_id, created_at, updated_at, callback_url, filter, format, include_fields, metafield_namespaces, private_metafield_namespaces, topic) FROM stdin;
\.


--
-- Data for Name: shopify_weights; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.shopify_weights (id, external_id, created_at, updated_at, unit, value) FROM stdin;
\.


--
-- Data for Name: store_analytics_snapshots; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.store_analytics_snapshots (id, store_id, date, total_sales, total_orders, average_order_value, new_customers, conversion_rate, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: store_sales; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.store_sales (id, store_id, product_id, variant_id, sale_date, quantity_sold, revenue, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: stores; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.stores (id, name, platform, api_key, api_secret_key, admin_access_token, storefront_access_token, shop_domain, shop_id, shop_name, is_active, last_sync, created_at, updated_at, owner_id) FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.users (id, email, hashed_password, full_name, is_active, created_at, updated_at) FROM stdin;
\.


--
-- Name: forecasts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.forecasts_id_seq', 1, false);


--
-- Name: holidays_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.holidays_id_seq', 1, false);


--
-- Name: product_performance_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.product_performance_id_seq', 1, false);


--
-- Name: shopify_abandoned_checkout_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_abandoned_checkout_line_items_id_seq', 1, false);


--
-- Name: shopify_abandoned_checkouts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_abandoned_checkouts_id_seq', 1, false);


--
-- Name: shopify_abandonments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_abandonments_id_seq', 1, false);


--
-- Name: shopify_access_scopes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_access_scopes_id_seq', 1, false);


--
-- Name: shopify_add_all_products_operations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_add_all_products_operations_id_seq', 1, false);


--
-- Name: shopify_additional_fee_sales_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_additional_fee_sales_id_seq', 1, false);


--
-- Name: shopify_additional_fees_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_additional_fees_id_seq', 1, false);


--
-- Name: shopify_adjustment_sales_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_adjustment_sales_id_seq', 1, false);


--
-- Name: shopify_all_discount_itemss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_all_discount_itemss_id_seq', 1, false);


--
-- Name: shopify_android_applications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_android_applications_id_seq', 1, false);


--
-- Name: shopify_api_versions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_api_versions_id_seq', 1, false);


--
-- Name: shopify_app_catalogs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_catalogs_id_seq', 1, false);


--
-- Name: shopify_app_credits_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_credits_id_seq', 1, false);


--
-- Name: shopify_app_discount_types_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_discount_types_id_seq', 1, false);


--
-- Name: shopify_app_feedbacks_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_feedbacks_id_seq', 1, false);


--
-- Name: shopify_app_installations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_installations_id_seq', 1, false);


--
-- Name: shopify_app_plan_v2s_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_plan_v2s_id_seq', 1, false);


--
-- Name: shopify_app_purchase_one_times_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_purchase_one_times_id_seq', 1, false);


--
-- Name: shopify_app_recurring_pricings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_recurring_pricings_id_seq', 1, false);


--
-- Name: shopify_app_revenue_attribution_records_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_revenue_attribution_records_id_seq', 1, false);


--
-- Name: shopify_app_revoke_access_scopes_app_revoke_scope__6977b_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_revoke_access_scopes_app_revoke_scope__6977b_id_seq', 1, false);


--
-- Name: shopify_app_subscription_discount_amounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_subscription_discount_amounts_id_seq', 1, false);


--
-- Name: shopify_app_subscription_discount_percentages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_subscription_discount_percentages_id_seq', 1, false);


--
-- Name: shopify_app_subscription_discounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_subscription_discounts_id_seq', 1, false);


--
-- Name: shopify_app_subscription_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_subscription_line_items_id_seq', 1, false);


--
-- Name: shopify_app_subscriptions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_subscriptions_id_seq', 1, false);


--
-- Name: shopify_app_usage_pricings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_usage_pricings_id_seq', 1, false);


--
-- Name: shopify_app_usage_records_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_app_usage_records_id_seq', 1, false);


--
-- Name: shopify_apple_applications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_apple_applications_id_seq', 1, false);


--
-- Name: shopify_apps_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_apps_id_seq', 1, false);


--
-- Name: shopify_article_authors_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_article_authors_id_seq', 1, false);


--
-- Name: shopify_articles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_articles_id_seq', 1, false);


--
-- Name: shopify_attributes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_attributes_id_seq', 1, false);


--
-- Name: shopify_automatic_discount_applications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_automatic_discount_applications_id_seq', 1, false);


--
-- Name: shopify_available_channel_definitions_by_channels_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_available_channel_definitions_by_channels_id_seq', 1, false);


--
-- Name: shopify_basic_events_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_basic_events_id_seq', 1, false);


--
-- Name: shopify_blog_feeds_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_blog_feeds_id_seq', 1, false);


--
-- Name: shopify_blogs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_blogs_id_seq', 1, false);


--
-- Name: shopify_bulk_operations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_bulk_operations_id_seq', 1, false);


--
-- Name: shopify_bundles_features_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_bundles_features_id_seq', 1, false);


--
-- Name: shopify_business_entity_addresss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_business_entity_addresss_id_seq', 1, false);


--
-- Name: shopify_business_entitys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_business_entitys_id_seq', 1, false);


--
-- Name: shopify_buyer_experience_configurations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_buyer_experience_configurations_id_seq', 1, false);


--
-- Name: shopify_calculated_automatic_discount_applications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_calculated_automatic_discount_applications_id_seq', 1, false);


--
-- Name: shopify_calculated_discount_allocations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_calculated_discount_allocations_id_seq', 1, false);


--
-- Name: shopify_calculated_discount_code_applications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_calculated_discount_code_applications_id_seq', 1, false);


--
-- Name: shopify_calculated_draft_order_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_calculated_draft_order_line_items_id_seq', 1, false);


--
-- Name: shopify_calculated_draft_orders_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_calculated_draft_orders_id_seq', 1, false);


--
-- Name: shopify_calculated_exchange_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_calculated_exchange_line_items_id_seq', 1, false);


--
-- Name: shopify_calculated_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_calculated_line_items_id_seq', 1, false);


--
-- Name: shopify_calculated_manual_discount_applications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_calculated_manual_discount_applications_id_seq', 1, false);


--
-- Name: shopify_calculated_orders_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_calculated_orders_id_seq', 1, false);


--
-- Name: shopify_calculated_restocking_fees_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_calculated_restocking_fees_id_seq', 1, false);


--
-- Name: shopify_calculated_return_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_calculated_return_line_items_id_seq', 1, false);


--
-- Name: shopify_calculated_return_shipping_fees_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_calculated_return_shipping_fees_id_seq', 1, false);


--
-- Name: shopify_calculated_returns_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_calculated_returns_id_seq', 1, false);


--
-- Name: shopify_calculated_script_discount_applications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_calculated_script_discount_applications_id_seq', 1, false);


--
-- Name: shopify_calculated_shipping_lines_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_calculated_shipping_lines_id_seq', 1, false);


--
-- Name: shopify_card_payment_detailss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_card_payment_detailss_id_seq', 1, false);


--
-- Name: shopify_cart_transform_eligible_operationss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_cart_transform_eligible_operationss_id_seq', 1, false);


--
-- Name: shopify_cart_transform_features_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_cart_transform_features_id_seq', 1, false);


--
-- Name: shopify_cart_transforms_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_cart_transforms_id_seq', 1, false);


--
-- Name: shopify_cash_rounding_adjustments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_cash_rounding_adjustments_id_seq', 1, false);


--
-- Name: shopify_cash_tracking_adjustments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_cash_tracking_adjustments_id_seq', 1, false);


--
-- Name: shopify_cash_tracking_sessions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_cash_tracking_sessions_id_seq', 1, false);


--
-- Name: shopify_catalog_csv_operations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_catalog_csv_operations_id_seq', 1, false);


--
-- Name: shopify_channel_definitions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_channel_definitions_id_seq', 1, false);


--
-- Name: shopify_channel_informations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_channel_informations_id_seq', 1, false);


--
-- Name: shopify_channels_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_channels_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_button_color_roless_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_button_color_roless_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_buttons_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_buttons_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_buyer_journeys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_buyer_journeys_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_cart_links_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_cart_links_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_checkboxs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_checkboxs_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_choice_list_groups_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_choice_list_groups_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_choice_lists_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_choice_lists_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_color_globals_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_color_globals_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_color_roless_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_color_roless_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_color_schemes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_color_schemes_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_color_schemess_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_color_schemess_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_colorss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_colorss_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_container_dividers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_container_dividers_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_contents_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_contents_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_control_color_roless_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_control_color_roless_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_controls_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_controls_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_corner_radius_variabless_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_corner_radius_variabless_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_custom_fonts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_custom_fonts_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_customizationss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_customizationss_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_design_systems_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_design_systems_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_divider_styles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_divider_styles_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_express_checkout_buttons_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_express_checkout_buttons_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_express_checkouts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_express_checkouts_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_font_groups_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_font_groups_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_font_sizes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_font_sizes_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_footer_contents_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_footer_contents_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_footers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_footers_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_globals_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_globals_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_header_cart_links_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_header_cart_links_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_headers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_headers_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_heading_levels_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_heading_levels_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_images_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_images_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_logos_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_logos_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_main_sections_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_main_sections_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_mains_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_mains_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_merchandise_thumbnails_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_merchandise_thumbnails_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_order_summary_sections_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_order_summary_sections_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_order_summarys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_order_summarys_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_selects_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_selects_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_shopify_fonts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_shopify_fonts_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_text_fields_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_text_fields_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_typography_style_globals_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_typography_style_globals_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_typography_styles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_typography_styles_id_seq', 1, false);


--
-- Name: shopify_checkout_branding_typographys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_branding_typographys_id_seq', 1, false);


--
-- Name: shopify_checkout_brandings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_brandings_id_seq', 1, false);


--
-- Name: shopify_checkout_profiles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_checkout_profiles_id_seq', 1, false);


--
-- Name: shopify_collection_publications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_collection_publications_id_seq', 1, false);


--
-- Name: shopify_collection_rule_category_conditions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_collection_rule_category_conditions_id_seq', 1, false);


--
-- Name: shopify_collection_rule_conditionss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_collection_rule_conditionss_id_seq', 1, false);


--
-- Name: shopify_collection_rule_metafield_conditions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_collection_rule_metafield_conditions_id_seq', 1, false);


--
-- Name: shopify_collection_rule_product_category_conditions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_collection_rule_product_category_conditions_id_seq', 1, false);


--
-- Name: shopify_collection_rule_sets_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_collection_rule_sets_id_seq', 1, false);


--
-- Name: shopify_collection_rule_text_conditions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_collection_rule_text_conditions_id_seq', 1, false);


--
-- Name: shopify_collection_rules_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_collection_rules_id_seq', 1, false);


--
-- Name: shopify_collections_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_collections_id_seq', 1, false);


--
-- Name: shopify_combined_listing_childs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_combined_listing_childs_id_seq', 1, false);


--
-- Name: shopify_combined_listings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_combined_listings_id_seq', 1, false);


--
-- Name: shopify_comment_authors_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_comment_authors_id_seq', 1, false);


--
-- Name: shopify_comment_event_attachments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_comment_event_attachments_id_seq', 1, false);


--
-- Name: shopify_comment_events_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_comment_events_id_seq', 1, false);


--
-- Name: shopify_comments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_comments_id_seq', 1, false);


--
-- Name: shopify_company_addresss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_company_addresss_id_seq', 1, false);


--
-- Name: shopify_company_contact_role_assignments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_company_contact_role_assignments_id_seq', 1, false);


--
-- Name: shopify_company_contact_roles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_company_contact_roles_id_seq', 1, false);


--
-- Name: shopify_company_contacts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_company_contacts_id_seq', 1, false);


--
-- Name: shopify_company_location_catalogs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_company_location_catalogs_id_seq', 1, false);


--
-- Name: shopify_company_location_staff_member_assignments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_company_location_staff_member_assignments_id_seq', 1, false);


--
-- Name: shopify_company_locations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_company_locations_id_seq', 1, false);


--
-- Name: shopify_companys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_companys_id_seq', 1, false);


--
-- Name: shopify_countries_in_shipping_zoness_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_countries_in_shipping_zoness_id_seq', 1, false);


--
-- Name: shopify_country_harmonized_system_codes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_country_harmonized_system_codes_id_seq', 1, false);


--
-- Name: shopify_counts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_counts_id_seq', 1, false);


--
-- Name: shopify_currency_formatss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_currency_formatss_id_seq', 1, false);


--
-- Name: shopify_currency_settings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_currency_settings_id_seq', 1, false);


--
-- Name: shopify_customer_account_app_extension_pages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_account_app_extension_pages_id_seq', 1, false);


--
-- Name: shopify_customer_account_native_pages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_account_native_pages_id_seq', 1, false);


--
-- Name: shopify_customer_accounts_v2s_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_accounts_v2s_id_seq', 1, false);


--
-- Name: shopify_customer_credit_card_billing_addresss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_credit_card_billing_addresss_id_seq', 1, false);


--
-- Name: shopify_customer_credit_cards_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_credit_cards_id_seq', 1, false);


--
-- Name: shopify_customer_email_addresss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_email_addresss_id_seq', 1, false);


--
-- Name: shopify_customer_email_marketing_consent_states_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_email_marketing_consent_states_id_seq', 1, false);


--
-- Name: shopify_customer_journey_summarys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_journey_summarys_id_seq', 1, false);


--
-- Name: shopify_customer_journeys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_journeys_id_seq', 1, false);


--
-- Name: shopify_customer_merge_errors_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_merge_errors_id_seq', 1, false);


--
-- Name: shopify_customer_merge_preview_alternate_fieldss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_merge_preview_alternate_fieldss_id_seq', 1, false);


--
-- Name: shopify_customer_merge_preview_blocking_fieldss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_merge_preview_blocking_fieldss_id_seq', 1, false);


--
-- Name: shopify_customer_merge_preview_default_fieldss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_merge_preview_default_fieldss_id_seq', 1, false);


--
-- Name: shopify_customer_merge_previews_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_merge_previews_id_seq', 1, false);


--
-- Name: shopify_customer_merge_requests_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_merge_requests_id_seq', 1, false);


--
-- Name: shopify_customer_mergeables_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_mergeables_id_seq', 1, false);


--
-- Name: shopify_customer_payment_instrument_billing_addres_b82bd_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_payment_instrument_billing_addres_b82bd_id_seq', 1, false);


--
-- Name: shopify_customer_payment_methods_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_payment_methods_id_seq', 1, false);


--
-- Name: shopify_customer_paypal_billing_agreements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_paypal_billing_agreements_id_seq', 1, false);


--
-- Name: shopify_customer_phone_numbers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_phone_numbers_id_seq', 1, false);


--
-- Name: shopify_customer_segment_members_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_segment_members_id_seq', 1, false);


--
-- Name: shopify_customer_segment_members_querys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_segment_members_querys_id_seq', 1, false);


--
-- Name: shopify_customer_shop_pay_agreements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_shop_pay_agreements_id_seq', 1, false);


--
-- Name: shopify_customer_sms_marketing_consent_errors_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_sms_marketing_consent_errors_id_seq', 1, false);


--
-- Name: shopify_customer_sms_marketing_consent_states_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_sms_marketing_consent_states_id_seq', 1, false);


--
-- Name: shopify_customer_statisticss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_statisticss_id_seq', 1, false);


--
-- Name: shopify_customer_visit_product_infos_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_visit_product_infos_id_seq', 1, false);


--
-- Name: shopify_customer_visits_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customer_visits_id_seq', 1, false);


--
-- Name: shopify_customers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_customers_id_seq', 1, false);


--
-- Name: shopify_delegate_access_tokens_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delegate_access_tokens_id_seq', 1, false);


--
-- Name: shopify_deletion_events_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_deletion_events_id_seq', 1, false);


--
-- Name: shopify_delivery_available_services_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_available_services_id_seq', 1, false);


--
-- Name: shopify_delivery_branded_promises_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_branded_promises_id_seq', 1, false);


--
-- Name: shopify_delivery_carrier_service_and_locationss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_carrier_service_and_locationss_id_seq', 1, false);


--
-- Name: shopify_delivery_carrier_services_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_carrier_services_id_seq', 1, false);


--
-- Name: shopify_delivery_conditions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_conditions_id_seq', 1, false);


--
-- Name: shopify_delivery_country_and_zones_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_country_and_zones_id_seq', 1, false);


--
-- Name: shopify_delivery_country_code_or_rest_of_worlds_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_country_code_or_rest_of_worlds_id_seq', 1, false);


--
-- Name: shopify_delivery_country_codes_or_rest_of_worlds_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_country_codes_or_rest_of_worlds_id_seq', 1, false);


--
-- Name: shopify_delivery_countrys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_countrys_id_seq', 1, false);


--
-- Name: shopify_delivery_customization_errors_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_customization_errors_id_seq', 1, false);


--
-- Name: shopify_delivery_customizations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_customizations_id_seq', 1, false);


--
-- Name: shopify_delivery_legacy_mode_blockeds_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_legacy_mode_blockeds_id_seq', 1, false);


--
-- Name: shopify_delivery_local_pickup_settingss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_local_pickup_settingss_id_seq', 1, false);


--
-- Name: shopify_delivery_location_group_zones_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_location_group_zones_id_seq', 1, false);


--
-- Name: shopify_delivery_location_groups_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_location_groups_id_seq', 1, false);


--
-- Name: shopify_delivery_location_local_pickup_settings_er_57b7b_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_location_local_pickup_settings_er_57b7b_id_seq', 1, false);


--
-- Name: shopify_delivery_method_additional_informations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_method_additional_informations_id_seq', 1, false);


--
-- Name: shopify_delivery_method_definition_countss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_method_definition_countss_id_seq', 1, false);


--
-- Name: shopify_delivery_method_definitions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_method_definitions_id_seq', 1, false);


--
-- Name: shopify_delivery_methods_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_methods_id_seq', 1, false);


--
-- Name: shopify_delivery_participant_services_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_participant_services_id_seq', 1, false);


--
-- Name: shopify_delivery_participants_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_participants_id_seq', 1, false);


--
-- Name: shopify_delivery_product_variants_counts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_product_variants_counts_id_seq', 1, false);


--
-- Name: shopify_delivery_profile_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_profile_items_id_seq', 1, false);


--
-- Name: shopify_delivery_profile_location_groups_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_profile_location_groups_id_seq', 1, false);


--
-- Name: shopify_delivery_profiles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_profiles_id_seq', 1, false);


--
-- Name: shopify_delivery_promise_providers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_promise_providers_id_seq', 1, false);


--
-- Name: shopify_delivery_provinces_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_provinces_id_seq', 1, false);


--
-- Name: shopify_delivery_rate_definitions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_rate_definitions_id_seq', 1, false);


--
-- Name: shopify_delivery_settings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_settings_id_seq', 1, false);


--
-- Name: shopify_delivery_zones_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_delivery_zones_id_seq', 1, false);


--
-- Name: shopify_deposit_percentages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_deposit_percentages_id_seq', 1, false);


--
-- Name: shopify_discount_allocations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_allocations_id_seq', 1, false);


--
-- Name: shopify_discount_amounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_amounts_id_seq', 1, false);


--
-- Name: shopify_discount_automatic_apps_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_automatic_apps_id_seq', 1, false);


--
-- Name: shopify_discount_automatic_basics_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_automatic_basics_id_seq', 1, false);


--
-- Name: shopify_discount_automatic_bxgys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_automatic_bxgys_id_seq', 1, false);


--
-- Name: shopify_discount_automatic_free_shippings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_automatic_free_shippings_id_seq', 1, false);


--
-- Name: shopify_discount_automatic_nodes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_automatic_nodes_id_seq', 1, false);


--
-- Name: shopify_discount_code_applications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_code_applications_id_seq', 1, false);


--
-- Name: shopify_discount_code_apps_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_code_apps_id_seq', 1, false);


--
-- Name: shopify_discount_code_basics_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_code_basics_id_seq', 1, false);


--
-- Name: shopify_discount_code_bxgys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_code_bxgys_id_seq', 1, false);


--
-- Name: shopify_discount_code_free_shippings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_code_free_shippings_id_seq', 1, false);


--
-- Name: shopify_discount_code_nodes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_code_nodes_id_seq', 1, false);


--
-- Name: shopify_discount_collectionss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_collectionss_id_seq', 1, false);


--
-- Name: shopify_discount_combines_withs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_combines_withs_id_seq', 1, false);


--
-- Name: shopify_discount_countriess_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_countriess_id_seq', 1, false);


--
-- Name: shopify_discount_country_alls_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_country_alls_id_seq', 1, false);


--
-- Name: shopify_discount_customer_alls_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_customer_alls_id_seq', 1, false);


--
-- Name: shopify_discount_customer_buyss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_customer_buyss_id_seq', 1, false);


--
-- Name: shopify_discount_customer_getss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_customer_getss_id_seq', 1, false);


--
-- Name: shopify_discount_customer_segmentss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_customer_segmentss_id_seq', 1, false);


--
-- Name: shopify_discount_customerss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_customerss_id_seq', 1, false);


--
-- Name: shopify_discount_minimum_quantitys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_minimum_quantitys_id_seq', 1, false);


--
-- Name: shopify_discount_minimum_subtotals_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_minimum_subtotals_id_seq', 1, false);


--
-- Name: shopify_discount_nodes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_nodes_id_seq', 1, false);


--
-- Name: shopify_discount_on_quantitys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_on_quantitys_id_seq', 1, false);


--
-- Name: shopify_discount_percentages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_percentages_id_seq', 1, false);


--
-- Name: shopify_discount_productss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_productss_id_seq', 1, false);


--
-- Name: shopify_discount_purchase_amounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_purchase_amounts_id_seq', 1, false);


--
-- Name: shopify_discount_quantitys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_quantitys_id_seq', 1, false);


--
-- Name: shopify_discount_redeem_code_bulk_creation_codes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_redeem_code_bulk_creation_codes_id_seq', 1, false);


--
-- Name: shopify_discount_redeem_code_bulk_creations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_redeem_code_bulk_creations_id_seq', 1, false);


--
-- Name: shopify_discount_redeem_codes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_redeem_codes_id_seq', 1, false);


--
-- Name: shopify_discount_shareable_urls_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_discount_shareable_urls_id_seq', 1, false);


--
-- Name: shopify_domain_localizations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_domain_localizations_id_seq', 1, false);


--
-- Name: shopify_domains_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_domains_id_seq', 1, false);


--
-- Name: shopify_draft_order_applied_discounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_draft_order_applied_discounts_id_seq', 1, false);


--
-- Name: shopify_draft_order_bundle_added_warnings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_draft_order_bundle_added_warnings_id_seq', 1, false);


--
-- Name: shopify_draft_order_discount_not_applied_warnings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_draft_order_discount_not_applied_warnings_id_seq', 1, false);


--
-- Name: shopify_draft_order_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_draft_order_line_items_id_seq', 1, false);


--
-- Name: shopify_draft_order_platform_discount_allocations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_draft_order_platform_discount_allocations_id_seq', 1, false);


--
-- Name: shopify_draft_order_platform_discounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_draft_order_platform_discounts_id_seq', 1, false);


--
-- Name: shopify_draft_order_tags_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_draft_order_tags_id_seq', 1, false);


--
-- Name: shopify_draft_orders_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_draft_orders_id_seq', 1, false);


--
-- Name: shopify_duty_sales_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_duty_sales_id_seq', 1, false);


--
-- Name: shopify_dutys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_dutys_id_seq', 1, false);


--
-- Name: shopify_editable_propertys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_editable_propertys_id_seq', 1, false);


--
-- Name: shopify_exchange_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_exchange_line_items_id_seq', 1, false);


--
-- Name: shopify_exchange_v2_additionss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_exchange_v2_additionss_id_seq', 1, false);


--
-- Name: shopify_exchange_v2_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_exchange_v2_line_items_id_seq', 1, false);


--
-- Name: shopify_exchange_v2_returnss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_exchange_v2_returnss_id_seq', 1, false);


--
-- Name: shopify_exchange_v2s_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_exchange_v2s_id_seq', 1, false);


--
-- Name: shopify_external_videos_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_external_videos_id_seq', 1, false);


--
-- Name: shopify_failed_requirements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_failed_requirements_id_seq', 1, false);


--
-- Name: shopify_fee_sales_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fee_sales_id_seq', 1, false);


--
-- Name: shopify_file_errors_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_file_errors_id_seq', 1, false);


--
-- Name: shopify_filter_options_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_filter_options_id_seq', 1, false);


--
-- Name: shopify_financial_summary_discount_allocations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_financial_summary_discount_allocations_id_seq', 1, false);


--
-- Name: shopify_financial_summary_discount_applications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_financial_summary_discount_applications_id_seq', 1, false);


--
-- Name: shopify_fulfillment_constraint_rules_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_constraint_rules_id_seq', 1, false);


--
-- Name: shopify_fulfillment_events_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_events_id_seq', 1, false);


--
-- Name: shopify_fulfillment_holds_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_holds_id_seq', 1, false);


--
-- Name: shopify_fulfillment_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_line_items_id_seq', 1, false);


--
-- Name: shopify_fulfillment_order_assigned_locations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_order_assigned_locations_id_seq', 1, false);


--
-- Name: shopify_fulfillment_order_destinations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_order_destinations_id_seq', 1, false);


--
-- Name: shopify_fulfillment_order_international_dutiess_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_order_international_dutiess_id_seq', 1, false);


--
-- Name: shopify_fulfillment_order_line_item_financial_summ_e222f_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_order_line_item_financial_summ_e222f_id_seq', 1, false);


--
-- Name: shopify_fulfillment_order_line_item_warnings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_order_line_item_warnings_id_seq', 1, false);


--
-- Name: shopify_fulfillment_order_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_order_line_items_id_seq', 1, false);


--
-- Name: shopify_fulfillment_order_location_for_moves_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_order_location_for_moves_id_seq', 1, false);


--
-- Name: shopify_fulfillment_order_merchant_requests_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_order_merchant_requests_id_seq', 1, false);


--
-- Name: shopify_fulfillment_order_merge_results_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_order_merge_results_id_seq', 1, false);


--
-- Name: shopify_fulfillment_order_split_results_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_order_split_results_id_seq', 1, false);


--
-- Name: shopify_fulfillment_order_supported_actions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_order_supported_actions_id_seq', 1, false);


--
-- Name: shopify_fulfillment_orders_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_orders_id_seq', 1, false);


--
-- Name: shopify_fulfillment_origin_addresss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_origin_addresss_id_seq', 1, false);


--
-- Name: shopify_fulfillment_services_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_services_id_seq', 1, false);


--
-- Name: shopify_fulfillment_tracking_infos_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillment_tracking_infos_id_seq', 1, false);


--
-- Name: shopify_fulfillments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_fulfillments_id_seq', 1, false);


--
-- Name: shopify_functions_app_bridges_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_functions_app_bridges_id_seq', 1, false);


--
-- Name: shopify_functions_error_historys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_functions_error_historys_id_seq', 1, false);


--
-- Name: shopify_generic_files_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_generic_files_id_seq', 1, false);


--
-- Name: shopify_gift_card_credit_transactions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_gift_card_credit_transactions_id_seq', 1, false);


--
-- Name: shopify_gift_card_debit_transactions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_gift_card_debit_transactions_id_seq', 1, false);


--
-- Name: shopify_gift_card_recipients_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_gift_card_recipients_id_seq', 1, false);


--
-- Name: shopify_gift_card_sales_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_gift_card_sales_id_seq', 1, false);


--
-- Name: shopify_gift_cards_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_gift_cards_id_seq', 1, false);


--
-- Name: shopify_image_upload_parameters_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_image_upload_parameters_id_seq', 1, false);


--
-- Name: shopify_images_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_images_id_seq', 1, false);


--
-- Name: shopify_integrations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_integrations_id_seq', 1, false);


--
-- Name: shopify_inventory_adjustment_groups_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_inventory_adjustment_groups_id_seq', 1, false);


--
-- Name: shopify_inventory_changes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_inventory_changes_id_seq', 1, false);


--
-- Name: shopify_inventory_item_measurements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_inventory_item_measurements_id_seq', 1, false);


--
-- Name: shopify_inventory_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_inventory_items_id_seq', 1, false);


--
-- Name: shopify_inventory_levels_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_inventory_levels_id_seq', 1, false);


--
-- Name: shopify_inventory_propertiess_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_inventory_propertiess_id_seq', 1, false);


--
-- Name: shopify_inventory_quantity_names_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_inventory_quantity_names_id_seq', 1, false);


--
-- Name: shopify_inventory_quantitys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_inventory_quantitys_id_seq', 1, false);


--
-- Name: shopify_inventory_scheduled_changes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_inventory_scheduled_changes_id_seq', 1, false);


--
-- Name: shopify_jobs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_jobs_id_seq', 1, false);


--
-- Name: shopify_limited_pending_order_counts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_limited_pending_order_counts_id_seq', 1, false);


--
-- Name: shopify_line_item_groups_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_line_item_groups_id_seq', 1, false);


--
-- Name: shopify_line_item_selling_plans_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_line_item_selling_plans_id_seq', 1, false);


--
-- Name: shopify_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_line_items_id_seq', 1, false);


--
-- Name: shopify_linked_metafields_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_linked_metafields_id_seq', 1, false);


--
-- Name: shopify_links_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_links_id_seq', 1, false);


--
-- Name: shopify_local_payment_methods_payment_detailss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_local_payment_methods_payment_detailss_id_seq', 1, false);


--
-- Name: shopify_locales_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_locales_id_seq', 1, false);


--
-- Name: shopify_localization_extensions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_localization_extensions_id_seq', 1, false);


--
-- Name: shopify_location_addresss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_location_addresss_id_seq', 1, false);


--
-- Name: shopify_location_suggested_addresss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_location_suggested_addresss_id_seq', 1, false);


--
-- Name: shopify_locations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_locations_id_seq', 1, false);


--
-- Name: shopify_mailing_addresss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_mailing_addresss_id_seq', 1, false);


--
-- Name: shopify_manual_discount_applications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_manual_discount_applications_id_seq', 1, false);


--
-- Name: shopify_market_catalogs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_market_catalogs_id_seq', 1, false);


--
-- Name: shopify_market_currency_settingss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_market_currency_settingss_id_seq', 1, false);


--
-- Name: shopify_market_localizable_contents_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_market_localizable_contents_id_seq', 1, false);


--
-- Name: shopify_market_localizable_resources_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_market_localizable_resources_id_seq', 1, false);


--
-- Name: shopify_market_localizations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_market_localizations_id_seq', 1, false);


--
-- Name: shopify_market_region_countrys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_market_region_countrys_id_seq', 1, false);


--
-- Name: shopify_market_web_presence_root_urls_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_market_web_presence_root_urls_id_seq', 1, false);


--
-- Name: shopify_market_web_presences_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_market_web_presences_id_seq', 1, false);


--
-- Name: shopify_marketing_activity_extension_app_errorss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_marketing_activity_extension_app_errorss_id_seq', 1, false);


--
-- Name: shopify_marketing_activitys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_marketing_activitys_id_seq', 1, false);


--
-- Name: shopify_marketing_budgets_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_marketing_budgets_id_seq', 1, false);


--
-- Name: shopify_marketing_engagements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_marketing_engagements_id_seq', 1, false);


--
-- Name: shopify_marketing_events_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_marketing_events_id_seq', 1, false);


--
-- Name: shopify_markets_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_markets_id_seq', 1, false);


--
-- Name: shopify_media_errors_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_media_errors_id_seq', 1, false);


--
-- Name: shopify_media_image_original_sources_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_media_image_original_sources_id_seq', 1, false);


--
-- Name: shopify_media_images_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_media_images_id_seq', 1, false);


--
-- Name: shopify_media_preview_images_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_media_preview_images_id_seq', 1, false);


--
-- Name: shopify_media_warnings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_media_warnings_id_seq', 1, false);


--
-- Name: shopify_menu_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_menu_items_id_seq', 1, false);


--
-- Name: shopify_menus_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_menus_id_seq', 1, false);


--
-- Name: shopify_merchant_approval_signalss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_merchant_approval_signalss_id_seq', 1, false);


--
-- Name: shopify_metafield_access_grants_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metafield_access_grants_id_seq', 1, false);


--
-- Name: shopify_metafield_accesss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metafield_accesss_id_seq', 1, false);


--
-- Name: shopify_metafield_capabilitiess_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metafield_capabilitiess_id_seq', 1, false);


--
-- Name: shopify_metafield_capability_admin_filterables_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metafield_capability_admin_filterables_id_seq', 1, false);


--
-- Name: shopify_metafield_capability_smart_collection_cond_9416f_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metafield_capability_smart_collection_cond_9416f_id_seq', 1, false);


--
-- Name: shopify_metafield_definition_constraint_values_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metafield_definition_constraint_values_id_seq', 1, false);


--
-- Name: shopify_metafield_definition_constraintss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metafield_definition_constraintss_id_seq', 1, false);


--
-- Name: shopify_metafield_definition_supported_validations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metafield_definition_supported_validations_id_seq', 1, false);


--
-- Name: shopify_metafield_definition_types_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metafield_definition_types_id_seq', 1, false);


--
-- Name: shopify_metafield_definition_validations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metafield_definition_validations_id_seq', 1, false);


--
-- Name: shopify_metafield_definitions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metafield_definitions_id_seq', 1, false);


--
-- Name: shopify_metafield_identifiers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metafield_identifiers_id_seq', 1, false);


--
-- Name: shopify_metafield_relations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metafield_relations_id_seq', 1, false);


--
-- Name: shopify_metafield_storefront_visibilitys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metafield_storefront_visibilitys_id_seq', 1, false);


--
-- Name: shopify_metafields_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metafields_id_seq', 1, false);


--
-- Name: shopify_metaobject_accesss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metaobject_accesss_id_seq', 1, false);


--
-- Name: shopify_metaobject_capabilities_online_stores_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metaobject_capabilities_online_stores_id_seq', 1, false);


--
-- Name: shopify_metaobject_capabilities_publishables_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metaobject_capabilities_publishables_id_seq', 1, false);


--
-- Name: shopify_metaobject_capabilities_renderables_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metaobject_capabilities_renderables_id_seq', 1, false);


--
-- Name: shopify_metaobject_capabilities_translatables_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metaobject_capabilities_translatables_id_seq', 1, false);


--
-- Name: shopify_metaobject_capabilitiess_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metaobject_capabilitiess_id_seq', 1, false);


--
-- Name: shopify_metaobject_capability_data_online_stores_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metaobject_capability_data_online_stores_id_seq', 1, false);


--
-- Name: shopify_metaobject_capability_data_publishables_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metaobject_capability_data_publishables_id_seq', 1, false);


--
-- Name: shopify_metaobject_capability_datas_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metaobject_capability_datas_id_seq', 1, false);


--
-- Name: shopify_metaobject_capability_definition_data_onli_f6f7e_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metaobject_capability_definition_data_onli_f6f7e_id_seq', 1, false);


--
-- Name: shopify_metaobject_capability_definition_data_rend_33c52_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metaobject_capability_definition_data_rend_33c52_id_seq', 1, false);


--
-- Name: shopify_metaobject_definitions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metaobject_definitions_id_seq', 1, false);


--
-- Name: shopify_metaobject_field_definitions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metaobject_field_definitions_id_seq', 1, false);


--
-- Name: shopify_metaobject_fields_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metaobject_fields_id_seq', 1, false);


--
-- Name: shopify_metaobject_thumbnails_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metaobject_thumbnails_id_seq', 1, false);


--
-- Name: shopify_metaobjects_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_metaobjects_id_seq', 1, false);


--
-- Name: shopify_model3d_bounding_boxs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_model3d_bounding_boxs_id_seq', 1, false);


--
-- Name: shopify_model3d_sources_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_model3d_sources_id_seq', 1, false);


--
-- Name: shopify_model3ds_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_model3ds_id_seq', 1, false);


--
-- Name: shopify_money_bags_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_money_bags_id_seq', 1, false);


--
-- Name: shopify_money_v2s_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_money_v2s_id_seq', 1, false);


--
-- Name: shopify_mutations_staged_upload_target_generate_up_2fbc2_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_mutations_staged_upload_target_generate_up_2fbc2_id_seq', 1, false);


--
-- Name: shopify_navigation_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_navigation_items_id_seq', 1, false);


--
-- Name: shopify_online_store_password_protections_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_online_store_password_protections_id_seq', 1, false);


--
-- Name: shopify_online_store_theme_file_body_base64s_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_online_store_theme_file_body_base64s_id_seq', 1, false);


--
-- Name: shopify_online_store_theme_file_body_texts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_online_store_theme_file_body_texts_id_seq', 1, false);


--
-- Name: shopify_online_store_theme_file_body_urls_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_online_store_theme_file_body_urls_id_seq', 1, false);


--
-- Name: shopify_online_store_theme_file_operation_results_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_online_store_theme_file_operation_results_id_seq', 1, false);


--
-- Name: shopify_online_store_theme_file_read_results_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_online_store_theme_file_read_results_id_seq', 1, false);


--
-- Name: shopify_online_store_theme_files_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_online_store_theme_files_id_seq', 1, false);


--
-- Name: shopify_online_store_theme_files_user_errorss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_online_store_theme_files_user_errorss_id_seq', 1, false);


--
-- Name: shopify_online_store_themes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_online_store_themes_id_seq', 1, false);


--
-- Name: shopify_online_stores_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_online_stores_id_seq', 1, false);


--
-- Name: shopify_order_adjustments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_adjustments_id_seq', 1, false);


--
-- Name: shopify_order_agreements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_agreements_id_seq', 1, false);


--
-- Name: shopify_order_apps_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_apps_id_seq', 1, false);


--
-- Name: shopify_order_cancellations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_cancellations_id_seq', 1, false);


--
-- Name: shopify_order_dispute_summarys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_dispute_summarys_id_seq', 1, false);


--
-- Name: shopify_order_edit_agreements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_edit_agreements_id_seq', 1, false);


--
-- Name: shopify_order_payment_collection_detailss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_payment_collection_detailss_id_seq', 1, false);


--
-- Name: shopify_order_payment_statuss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_payment_statuss_id_seq', 1, false);


--
-- Name: shopify_order_risk_assessments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_risk_assessments_id_seq', 1, false);


--
-- Name: shopify_order_risk_summarys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_risk_summarys_id_seq', 1, false);


--
-- Name: shopify_order_risks_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_risks_id_seq', 1, false);


--
-- Name: shopify_order_staged_change_add_custom_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_staged_change_add_custom_items_id_seq', 1, false);


--
-- Name: shopify_order_staged_change_add_line_item_discounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_staged_change_add_line_item_discounts_id_seq', 1, false);


--
-- Name: shopify_order_staged_change_add_shipping_lines_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_staged_change_add_shipping_lines_id_seq', 1, false);


--
-- Name: shopify_order_staged_change_add_variants_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_staged_change_add_variants_id_seq', 1, false);


--
-- Name: shopify_order_staged_change_decrement_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_staged_change_decrement_items_id_seq', 1, false);


--
-- Name: shopify_order_staged_change_increment_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_staged_change_increment_items_id_seq', 1, false);


--
-- Name: shopify_order_staged_change_remove_shipping_lines_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_staged_change_remove_shipping_lines_id_seq', 1, false);


--
-- Name: shopify_order_transactions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_order_transactions_id_seq', 1, false);


--
-- Name: shopify_orders_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_orders_id_seq', 1, false);


--
-- Name: shopify_page_infos_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_page_infos_id_seq', 1, false);


--
-- Name: shopify_pages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_pages_id_seq', 1, false);


--
-- Name: shopify_payment_customization_errors_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_payment_customization_errors_id_seq', 1, false);


--
-- Name: shopify_payment_customizations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_payment_customizations_id_seq', 1, false);


--
-- Name: shopify_payment_mandates_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_payment_mandates_id_seq', 1, false);


--
-- Name: shopify_payment_schedules_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_payment_schedules_id_seq', 1, false);


--
-- Name: shopify_payment_settingss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_payment_settingss_id_seq', 1, false);


--
-- Name: shopify_payment_terms_templates_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_payment_terms_templates_id_seq', 1, false);


--
-- Name: shopify_payment_termss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_payment_termss_id_seq', 1, false);


--
-- Name: shopify_price_list_adjustment_settingss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_list_adjustment_settingss_id_seq', 1, false);


--
-- Name: shopify_price_list_adjustments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_list_adjustments_id_seq', 1, false);


--
-- Name: shopify_price_list_parents_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_list_parents_id_seq', 1, false);


--
-- Name: shopify_price_list_prices_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_list_prices_id_seq', 1, false);


--
-- Name: shopify_price_lists_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_lists_id_seq', 1, false);


--
-- Name: shopify_price_rule_customer_selections_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_rule_customer_selections_id_seq', 1, false);


--
-- Name: shopify_price_rule_discount_codes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_rule_discount_codes_id_seq', 1, false);


--
-- Name: shopify_price_rule_entitlement_to_prerequisite_qua_242ab_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_rule_entitlement_to_prerequisite_qua_242ab_id_seq', 1, false);


--
-- Name: shopify_price_rule_fixed_amount_values_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_rule_fixed_amount_values_id_seq', 1, false);


--
-- Name: shopify_price_rule_item_entitlementss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_rule_item_entitlementss_id_seq', 1, false);


--
-- Name: shopify_price_rule_line_item_prerequisitess_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_rule_line_item_prerequisitess_id_seq', 1, false);


--
-- Name: shopify_price_rule_money_ranges_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_rule_money_ranges_id_seq', 1, false);


--
-- Name: shopify_price_rule_percent_values_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_rule_percent_values_id_seq', 1, false);


--
-- Name: shopify_price_rule_prerequisite_to_entitlement_qua_13b24_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_rule_prerequisite_to_entitlement_qua_13b24_id_seq', 1, false);


--
-- Name: shopify_price_rule_quantity_ranges_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_rule_quantity_ranges_id_seq', 1, false);


--
-- Name: shopify_price_rule_shareable_urls_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_rule_shareable_urls_id_seq', 1, false);


--
-- Name: shopify_price_rule_shipping_line_entitlementss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_rule_shipping_line_entitlementss_id_seq', 1, false);


--
-- Name: shopify_price_rule_validity_periods_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_rule_validity_periods_id_seq', 1, false);


--
-- Name: shopify_price_rules_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_price_rules_id_seq', 1, false);


--
-- Name: shopify_pricing_percentage_values_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_pricing_percentage_values_id_seq', 1, false);


--
-- Name: shopify_private_metafields_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_private_metafields_id_seq', 1, false);


--
-- Name: shopify_product_bundle_component_option_selection__dc101_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_bundle_component_option_selection__dc101_id_seq', 1, false);


--
-- Name: shopify_product_bundle_component_option_selections_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_bundle_component_option_selections_id_seq', 1, false);


--
-- Name: shopify_product_bundle_component_quantity_option_v_88df4_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_bundle_component_quantity_option_v_88df4_id_seq', 1, false);


--
-- Name: shopify_product_bundle_component_quantity_options_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_bundle_component_quantity_options_id_seq', 1, false);


--
-- Name: shopify_product_bundle_components_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_bundle_components_id_seq', 1, false);


--
-- Name: shopify_product_bundle_operations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_bundle_operations_id_seq', 1, false);


--
-- Name: shopify_product_categorys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_categorys_id_seq', 1, false);


--
-- Name: shopify_product_compare_at_price_ranges_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_compare_at_price_ranges_id_seq', 1, false);


--
-- Name: shopify_product_contextual_pricings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_contextual_pricings_id_seq', 1, false);


--
-- Name: shopify_product_delete_operations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_delete_operations_id_seq', 1, false);


--
-- Name: shopify_product_duplicate_jobs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_duplicate_jobs_id_seq', 1, false);


--
-- Name: shopify_product_duplicate_operations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_duplicate_operations_id_seq', 1, false);


--
-- Name: shopify_product_feeds_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_feeds_id_seq', 1, false);


--
-- Name: shopify_product_option_value_swatchs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_option_value_swatchs_id_seq', 1, false);


--
-- Name: shopify_product_option_values_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_option_values_id_seq', 1, false);


--
-- Name: shopify_product_options_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_options_id_seq', 1, false);


--
-- Name: shopify_product_price_range_v2s_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_price_range_v2s_id_seq', 1, false);


--
-- Name: shopify_product_price_ranges_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_price_ranges_id_seq', 1, false);


--
-- Name: shopify_product_publications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_publications_id_seq', 1, false);


--
-- Name: shopify_product_resource_feedbacks_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_resource_feedbacks_id_seq', 1, false);


--
-- Name: shopify_product_sales_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_sales_id_seq', 1, false);


--
-- Name: shopify_product_set_operations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_set_operations_id_seq', 1, false);


--
-- Name: shopify_product_taxonomy_nodes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_taxonomy_nodes_id_seq', 1, false);


--
-- Name: shopify_product_variant_components_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_variant_components_id_seq', 1, false);


--
-- Name: shopify_product_variant_contextual_pricings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_variant_contextual_pricings_id_seq', 1, false);


--
-- Name: shopify_product_variant_price_pairs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_variant_price_pairs_id_seq', 1, false);


--
-- Name: shopify_product_variants_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_product_variants_id_seq', 1, false);


--
-- Name: shopify_products_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_products_id_seq', 1, false);


--
-- Name: shopify_publication_resource_operations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_publication_resource_operations_id_seq', 1, false);


--
-- Name: shopify_publications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_publications_id_seq', 1, false);


--
-- Name: shopify_purchasing_companys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_purchasing_companys_id_seq', 1, false);


--
-- Name: shopify_quantity_price_breaks_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_quantity_price_breaks_id_seq', 1, false);


--
-- Name: shopify_quantity_rules_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_quantity_rules_id_seq', 1, false);


--
-- Name: shopify_refund_agreements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_refund_agreements_id_seq', 1, false);


--
-- Name: shopify_refund_dutys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_refund_dutys_id_seq', 1, false);


--
-- Name: shopify_refund_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_refund_line_items_id_seq', 1, false);


--
-- Name: shopify_refund_shipping_lines_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_refund_shipping_lines_id_seq', 1, false);


--
-- Name: shopify_refunds_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_refunds_id_seq', 1, false);


--
-- Name: shopify_resource_alert_actions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_resource_alert_actions_id_seq', 1, false);


--
-- Name: shopify_resource_alerts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_resource_alerts_id_seq', 1, false);


--
-- Name: shopify_resource_feedbacks_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_resource_feedbacks_id_seq', 1, false);


--
-- Name: shopify_resource_publication_v2s_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_resource_publication_v2s_id_seq', 1, false);


--
-- Name: shopify_resource_publications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_resource_publications_id_seq', 1, false);


--
-- Name: shopify_restocking_fees_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_restocking_fees_id_seq', 1, false);


--
-- Name: shopify_restricted_for_resources_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_restricted_for_resources_id_seq', 1, false);


--
-- Name: shopify_return_agreements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_return_agreements_id_seq', 1, false);


--
-- Name: shopify_return_declines_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_return_declines_id_seq', 1, false);


--
-- Name: shopify_return_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_return_line_items_id_seq', 1, false);


--
-- Name: shopify_return_objs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_return_objs_id_seq', 1, false);


--
-- Name: shopify_return_shipping_fees_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_return_shipping_fees_id_seq', 1, false);


--
-- Name: shopify_returnable_fulfillment_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_returnable_fulfillment_line_items_id_seq', 1, false);


--
-- Name: shopify_returnable_fulfillments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_returnable_fulfillments_id_seq', 1, false);


--
-- Name: shopify_reverse_delivery_label_v2s_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_reverse_delivery_label_v2s_id_seq', 1, false);


--
-- Name: shopify_reverse_delivery_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_reverse_delivery_line_items_id_seq', 1, false);


--
-- Name: shopify_reverse_delivery_shipping_deliverables_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_reverse_delivery_shipping_deliverables_id_seq', 1, false);


--
-- Name: shopify_reverse_delivery_tracking_v2s_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_reverse_delivery_tracking_v2s_id_seq', 1, false);


--
-- Name: shopify_reverse_deliverys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_reverse_deliverys_id_seq', 1, false);


--
-- Name: shopify_reverse_fulfillment_order_dispositions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_reverse_fulfillment_order_dispositions_id_seq', 1, false);


--
-- Name: shopify_reverse_fulfillment_order_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_reverse_fulfillment_order_line_items_id_seq', 1, false);


--
-- Name: shopify_reverse_fulfillment_order_third_party_conf_d7032_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_reverse_fulfillment_order_third_party_conf_d7032_id_seq', 1, false);


--
-- Name: shopify_reverse_fulfillment_orders_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_reverse_fulfillment_orders_id_seq', 1, false);


--
-- Name: shopify_risk_facts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_risk_facts_id_seq', 1, false);


--
-- Name: shopify_row_counts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_row_counts_id_seq', 1, false);


--
-- Name: shopify_sale_additional_fees_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_sale_additional_fees_id_seq', 1, false);


--
-- Name: shopify_sale_taxs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_sale_taxs_id_seq', 1, false);


--
-- Name: shopify_saved_searchs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_saved_searchs_id_seq', 1, false);


--
-- Name: shopify_script_discount_applications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_script_discount_applications_id_seq', 1, false);


--
-- Name: shopify_script_tags_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_script_tags_id_seq', 1, false);


--
-- Name: shopify_search_filter_optionss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_search_filter_optionss_id_seq', 1, false);


--
-- Name: shopify_search_filters_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_search_filters_id_seq', 1, false);


--
-- Name: shopify_search_results_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_search_results_id_seq', 1, false);


--
-- Name: shopify_segment_association_filters_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_segment_association_filters_id_seq', 1, false);


--
-- Name: shopify_segment_attribute_statisticss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_segment_attribute_statisticss_id_seq', 1, false);


--
-- Name: shopify_segment_boolean_filters_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_segment_boolean_filters_id_seq', 1, false);


--
-- Name: shopify_segment_date_filters_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_segment_date_filters_id_seq', 1, false);


--
-- Name: shopify_segment_enum_filters_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_segment_enum_filters_id_seq', 1, false);


--
-- Name: shopify_segment_event_filter_parameters_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_segment_event_filter_parameters_id_seq', 1, false);


--
-- Name: shopify_segment_event_filters_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_segment_event_filters_id_seq', 1, false);


--
-- Name: shopify_segment_float_filters_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_segment_float_filters_id_seq', 1, false);


--
-- Name: shopify_segment_integer_filters_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_segment_integer_filters_id_seq', 1, false);


--
-- Name: shopify_segment_membership_responses_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_segment_membership_responses_id_seq', 1, false);


--
-- Name: shopify_segment_memberships_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_segment_memberships_id_seq', 1, false);


--
-- Name: shopify_segment_migrations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_segment_migrations_id_seq', 1, false);


--
-- Name: shopify_segment_statisticss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_segment_statisticss_id_seq', 1, false);


--
-- Name: shopify_segment_string_filters_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_segment_string_filters_id_seq', 1, false);


--
-- Name: shopify_segment_values_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_segment_values_id_seq', 1, false);


--
-- Name: shopify_segments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_segments_id_seq', 1, false);


--
-- Name: shopify_selected_options_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_selected_options_id_seq', 1, false);


--
-- Name: shopify_selling_plan_anchors_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_selling_plan_anchors_id_seq', 1, false);


--
-- Name: shopify_selling_plan_checkout_charge_percentage_va_8339a_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_selling_plan_checkout_charge_percentage_va_8339a_id_seq', 1, false);


--
-- Name: shopify_selling_plan_checkout_charges_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_selling_plan_checkout_charges_id_seq', 1, false);


--
-- Name: shopify_selling_plan_fixed_billing_policys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_selling_plan_fixed_billing_policys_id_seq', 1, false);


--
-- Name: shopify_selling_plan_fixed_delivery_policys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_selling_plan_fixed_delivery_policys_id_seq', 1, false);


--
-- Name: shopify_selling_plan_fixed_pricing_policys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_selling_plan_fixed_pricing_policys_id_seq', 1, false);


--
-- Name: shopify_selling_plan_groups_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_selling_plan_groups_id_seq', 1, false);


--
-- Name: shopify_selling_plan_inventory_policys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_selling_plan_inventory_policys_id_seq', 1, false);


--
-- Name: shopify_selling_plan_pricing_policy_percentage_val_89796_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_selling_plan_pricing_policy_percentage_val_89796_id_seq', 1, false);


--
-- Name: shopify_selling_plan_recurring_billing_policys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_selling_plan_recurring_billing_policys_id_seq', 1, false);


--
-- Name: shopify_selling_plan_recurring_delivery_policys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_selling_plan_recurring_delivery_policys_id_seq', 1, false);


--
-- Name: shopify_selling_plan_recurring_pricing_policys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_selling_plan_recurring_pricing_policys_id_seq', 1, false);


--
-- Name: shopify_selling_plans_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_selling_plans_id_seq', 1, false);


--
-- Name: shopify_seos_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_seos_id_seq', 1, false);


--
-- Name: shopify_server_pixels_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_server_pixels_id_seq', 1, false);


--
-- Name: shopify_shipping_line_sales_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shipping_line_sales_id_seq', 1, false);


--
-- Name: shopify_shipping_lines_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shipping_lines_id_seq', 1, false);


--
-- Name: shopify_shipping_rates_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shipping_rates_id_seq', 1, false);


--
-- Name: shopify_shipping_refunds_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shipping_refunds_id_seq', 1, false);


--
-- Name: shopify_shop_addresss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shop_addresss_id_seq', 1, false);


--
-- Name: shopify_shop_alert_actions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shop_alert_actions_id_seq', 1, false);


--
-- Name: shopify_shop_alerts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shop_alerts_id_seq', 1, false);


--
-- Name: shopify_shop_billing_preferencess_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shop_billing_preferencess_id_seq', 1, false);


--
-- Name: shopify_shop_featuress_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shop_featuress_id_seq', 1, false);


--
-- Name: shopify_shop_locales_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shop_locales_id_seq', 1, false);


--
-- Name: shopify_shop_pay_installments_payment_detailss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shop_pay_installments_payment_detailss_id_seq', 1, false);


--
-- Name: shopify_shop_plans_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shop_plans_id_seq', 1, false);


--
-- Name: shopify_shop_policys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shop_policys_id_seq', 1, false);


--
-- Name: shopify_shop_resource_limitss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shop_resource_limitss_id_seq', 1, false);


--
-- Name: shopify_shopify_functions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_functions_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_accounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_accounts_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_adjustment_orders_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_adjustment_orders_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_associated_orders_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_associated_orders_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_balance_transaction_assoc_13997_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_balance_transaction_assoc_13997_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_balance_transactions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_balance_transactions_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_bank_accounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_bank_accounts_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_default_charge_statement__c00d2_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_default_charge_statement__c00d2_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_dispute_evidences_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_dispute_evidences_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_dispute_file_uploads_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_dispute_file_uploads_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_dispute_fulfillments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_dispute_fulfillments_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_dispute_reason_detailss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_dispute_reason_detailss_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_disputes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_disputes_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_extended_authorizations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_extended_authorizations_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_jp_charge_statement_descr_b9a66_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_jp_charge_statement_descr_b9a66_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_payout_schedules_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_payout_schedules_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_payout_summarys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_payout_summarys_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_payouts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_payouts_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_refund_sets_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_refund_sets_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_tooling_provider_payouts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_tooling_provider_payouts_id_seq', 1, false);


--
-- Name: shopify_shopify_payments_transaction_sets_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_payments_transaction_sets_id_seq', 1, false);


--
-- Name: shopify_shopify_protect_order_eligibilitys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_protect_order_eligibilitys_id_seq', 1, false);


--
-- Name: shopify_shopify_protect_order_summarys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shopify_protect_order_summarys_id_seq', 1, false);


--
-- Name: shopify_shops_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_shops_id_seq', 1, false);


--
-- Name: shopify_staff_member_private_datas_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_staff_member_private_datas_id_seq', 1, false);


--
-- Name: shopify_staff_members_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_staff_members_id_seq', 1, false);


--
-- Name: shopify_staged_media_upload_targets_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_staged_media_upload_targets_id_seq', 1, false);


--
-- Name: shopify_staged_upload_parameters_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_staged_upload_parameters_id_seq', 1, false);


--
-- Name: shopify_staged_upload_targets_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_staged_upload_targets_id_seq', 1, false);


--
-- Name: shopify_standard_metafield_definition_templates_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_standard_metafield_definition_templates_id_seq', 1, false);


--
-- Name: shopify_standardized_product_types_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_standardized_product_types_id_seq', 1, false);


--
-- Name: shopify_store_credit_account_credit_transactions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_store_credit_account_credit_transactions_id_seq', 1, false);


--
-- Name: shopify_store_credit_account_debit_revert_transact_22361_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_store_credit_account_debit_revert_transact_22361_id_seq', 1, false);


--
-- Name: shopify_store_credit_account_debit_transactions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_store_credit_account_debit_transactions_id_seq', 1, false);


--
-- Name: shopify_store_credit_account_expiration_transactio_11160_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_store_credit_account_expiration_transactio_11160_id_seq', 1, false);


--
-- Name: shopify_store_credit_accounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_store_credit_accounts_id_seq', 1, false);


--
-- Name: shopify_storefront_access_tokens_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_storefront_access_tokens_id_seq', 1, false);


--
-- Name: shopify_subscription_applied_code_discounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_applied_code_discounts_id_seq', 1, false);


--
-- Name: shopify_subscription_billing_attempts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_billing_attempts_id_seq', 1, false);


--
-- Name: shopify_subscription_billing_cycle_edited_contracts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_billing_cycle_edited_contracts_id_seq', 1, false);


--
-- Name: shopify_subscription_billing_cycles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_billing_cycles_id_seq', 1, false);


--
-- Name: shopify_subscription_billing_policys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_billing_policys_id_seq', 1, false);


--
-- Name: shopify_subscription_contracts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_contracts_id_seq', 1, false);


--
-- Name: shopify_subscription_cycle_price_adjustments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_cycle_price_adjustments_id_seq', 1, false);


--
-- Name: shopify_subscription_delivery_method_local_deliver_62924_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_delivery_method_local_deliver_62924_id_seq', 1, false);


--
-- Name: shopify_subscription_delivery_method_local_deliver_9f7c1_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_delivery_method_local_deliver_9f7c1_id_seq', 1, false);


--
-- Name: shopify_subscription_delivery_method_pickup_options_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_delivery_method_pickup_options_id_seq', 1, false);


--
-- Name: shopify_subscription_delivery_method_pickups_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_delivery_method_pickups_id_seq', 1, false);


--
-- Name: shopify_subscription_delivery_method_shipping_opti_a8dd4_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_delivery_method_shipping_opti_a8dd4_id_seq', 1, false);


--
-- Name: shopify_subscription_delivery_method_shippings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_delivery_method_shippings_id_seq', 1, false);


--
-- Name: shopify_subscription_delivery_option_result_failur_181d4_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_delivery_option_result_failur_181d4_id_seq', 1, false);


--
-- Name: shopify_subscription_delivery_option_result_succes_d8302_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_delivery_option_result_succes_d8302_id_seq', 1, false);


--
-- Name: shopify_subscription_delivery_policys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_delivery_policys_id_seq', 1, false);


--
-- Name: shopify_subscription_discount_allocations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_discount_allocations_id_seq', 1, false);


--
-- Name: shopify_subscription_discount_entitled_liness_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_discount_entitled_liness_id_seq', 1, false);


--
-- Name: shopify_subscription_discount_fixed_amount_values_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_discount_fixed_amount_values_id_seq', 1, false);


--
-- Name: shopify_subscription_discount_percentage_values_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_discount_percentage_values_id_seq', 1, false);


--
-- Name: shopify_subscription_drafts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_drafts_id_seq', 1, false);


--
-- Name: shopify_subscription_lines_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_lines_id_seq', 1, false);


--
-- Name: shopify_subscription_local_delivery_options_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_local_delivery_options_id_seq', 1, false);


--
-- Name: shopify_subscription_mailing_addresss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_mailing_addresss_id_seq', 1, false);


--
-- Name: shopify_subscription_manual_discounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_manual_discounts_id_seq', 1, false);


--
-- Name: shopify_subscription_pickup_options_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_pickup_options_id_seq', 1, false);


--
-- Name: shopify_subscription_pricing_policys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_pricing_policys_id_seq', 1, false);


--
-- Name: shopify_subscription_shipping_option_result_failur_a06a2_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_shipping_option_result_failur_a06a2_id_seq', 1, false);


--
-- Name: shopify_subscription_shipping_option_result_succes_92dde_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_shipping_option_result_succes_92dde_id_seq', 1, false);


--
-- Name: shopify_subscription_shipping_options_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_subscription_shipping_options_id_seq', 1, false);


--
-- Name: shopify_suggested_order_transactions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_suggested_order_transactions_id_seq', 1, false);


--
-- Name: shopify_suggested_refunds_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_suggested_refunds_id_seq', 1, false);


--
-- Name: shopify_suggested_return_refunds_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_suggested_return_refunds_id_seq', 1, false);


--
-- Name: shopify_tax_app_configurations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_tax_app_configurations_id_seq', 1, false);


--
-- Name: shopify_tax_lines_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_tax_lines_id_seq', 1, false);


--
-- Name: shopify_taxonomy_attributes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_taxonomy_attributes_id_seq', 1, false);


--
-- Name: shopify_taxonomy_categorys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_taxonomy_categorys_id_seq', 1, false);


--
-- Name: shopify_taxonomy_choice_list_attributes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_taxonomy_choice_list_attributes_id_seq', 1, false);


--
-- Name: shopify_taxonomy_measurement_attributes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_taxonomy_measurement_attributes_id_seq', 1, false);


--
-- Name: shopify_taxonomy_values_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_taxonomy_values_id_seq', 1, false);


--
-- Name: shopify_taxonomys_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_taxonomys_id_seq', 1, false);


--
-- Name: shopify_tender_transaction_credit_card_detailss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_tender_transaction_credit_card_detailss_id_seq', 1, false);


--
-- Name: shopify_tender_transactions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_tender_transactions_id_seq', 1, false);


--
-- Name: shopify_tip_sales_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_tip_sales_id_seq', 1, false);


--
-- Name: shopify_transaction_fees_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_transaction_fees_id_seq', 1, false);


--
-- Name: shopify_translatable_contents_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_translatable_contents_id_seq', 1, false);


--
-- Name: shopify_translatable_resources_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_translatable_resources_id_seq', 1, false);


--
-- Name: shopify_translations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_translations_id_seq', 1, false);


--
-- Name: shopify_typed_attributes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_typed_attributes_id_seq', 1, false);


--
-- Name: shopify_unit_price_measurements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_unit_price_measurements_id_seq', 1, false);


--
-- Name: shopify_unknown_sales_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_unknown_sales_id_seq', 1, false);


--
-- Name: shopify_unverified_return_line_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_unverified_return_line_items_id_seq', 1, false);


--
-- Name: shopify_url_redirect_import_previews_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_url_redirect_import_previews_id_seq', 1, false);


--
-- Name: shopify_url_redirect_imports_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_url_redirect_imports_id_seq', 1, false);


--
-- Name: shopify_url_redirects_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_url_redirects_id_seq', 1, false);


--
-- Name: shopify_utm_parameterss_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_utm_parameterss_id_seq', 1, false);


--
-- Name: shopify_validations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_validations_id_seq', 1, false);


--
-- Name: shopify_vault_credit_cards_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_vault_credit_cards_id_seq', 1, false);


--
-- Name: shopify_vault_paypal_billing_agreements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_vault_paypal_billing_agreements_id_seq', 1, false);


--
-- Name: shopify_vector3s_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_vector3s_id_seq', 1, false);


--
-- Name: shopify_video_sources_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_video_sources_id_seq', 1, false);


--
-- Name: shopify_videos_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_videos_id_seq', 1, false);


--
-- Name: shopify_web_pixels_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_web_pixels_id_seq', 1, false);


--
-- Name: shopify_webhook_event_bridge_endpoints_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_webhook_event_bridge_endpoints_id_seq', 1, false);


--
-- Name: shopify_webhook_http_endpoints_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_webhook_http_endpoints_id_seq', 1, false);


--
-- Name: shopify_webhook_pub_sub_endpoints_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_webhook_pub_sub_endpoints_id_seq', 1, false);


--
-- Name: shopify_webhook_subscriptions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_webhook_subscriptions_id_seq', 1, false);


--
-- Name: shopify_weights_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.shopify_weights_id_seq', 1, false);


--
-- Name: store_analytics_snapshots_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.store_analytics_snapshots_id_seq', 1, false);


--
-- Name: store_sales_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.store_sales_id_seq', 1, false);


--
-- Name: stores_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.stores_id_seq', 1, false);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.users_id_seq', 1, false);


--
-- PostgreSQL database dump complete
--

