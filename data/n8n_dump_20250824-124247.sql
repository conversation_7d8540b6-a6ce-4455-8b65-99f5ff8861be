--
-- PostgreSQL database dump
--

-- Dumped from database version 15.14 (Debian 15.14-1.pgdg12+1)
-- Dumped by pg_dump version 17.5 (Ubuntu 17.5-0ubuntu0.25.04.1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: annotation_tag_entity; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.annotation_tag_entity (
    id character varying(16) NOT NULL,
    name character varying(24) NOT NULL,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL
);


ALTER TABLE public.annotation_tag_entity OWNER TO "user";

--
-- Name: auth_identity; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.auth_identity (
    "userId" uuid,
    "providerId" character varying(64) NOT NULL,
    "providerType" character varying(32) NOT NULL,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL
);


ALTER TABLE public.auth_identity OWNER TO "user";

--
-- Name: auth_provider_sync_history; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.auth_provider_sync_history (
    id integer NOT NULL,
    "providerType" character varying(32) NOT NULL,
    "runMode" text NOT NULL,
    status text NOT NULL,
    "startedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "endedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    scanned integer NOT NULL,
    created integer NOT NULL,
    updated integer NOT NULL,
    disabled integer NOT NULL,
    error text
);


ALTER TABLE public.auth_provider_sync_history OWNER TO "user";

--
-- Name: auth_provider_sync_history_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.auth_provider_sync_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.auth_provider_sync_history_id_seq OWNER TO "user";

--
-- Name: auth_provider_sync_history_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.auth_provider_sync_history_id_seq OWNED BY public.auth_provider_sync_history.id;


--
-- Name: credentials_entity; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.credentials_entity (
    name character varying(128) NOT NULL,
    data text NOT NULL,
    type character varying(128) NOT NULL,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    id character varying(36) NOT NULL,
    "isManaged" boolean DEFAULT false NOT NULL
);


ALTER TABLE public.credentials_entity OWNER TO "user";

--
-- Name: event_destinations; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.event_destinations (
    id uuid NOT NULL,
    destination jsonb NOT NULL,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL
);


ALTER TABLE public.event_destinations OWNER TO "user";

--
-- Name: execution_annotation_tags; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.execution_annotation_tags (
    "annotationId" integer NOT NULL,
    "tagId" character varying(24) NOT NULL
);


ALTER TABLE public.execution_annotation_tags OWNER TO "user";

--
-- Name: execution_annotations; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.execution_annotations (
    id integer NOT NULL,
    "executionId" integer NOT NULL,
    vote character varying(6),
    note text,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL
);


ALTER TABLE public.execution_annotations OWNER TO "user";

--
-- Name: execution_annotations_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.execution_annotations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.execution_annotations_id_seq OWNER TO "user";

--
-- Name: execution_annotations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.execution_annotations_id_seq OWNED BY public.execution_annotations.id;


--
-- Name: execution_data; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.execution_data (
    "executionId" integer NOT NULL,
    "workflowData" json NOT NULL,
    data text NOT NULL
);


ALTER TABLE public.execution_data OWNER TO "user";

--
-- Name: execution_entity; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.execution_entity (
    id integer NOT NULL,
    finished boolean NOT NULL,
    mode character varying NOT NULL,
    "retryOf" character varying,
    "retrySuccessId" character varying,
    "startedAt" timestamp(3) with time zone,
    "stoppedAt" timestamp(3) with time zone,
    "waitTill" timestamp(3) with time zone,
    status character varying NOT NULL,
    "workflowId" character varying(36) NOT NULL,
    "deletedAt" timestamp(3) with time zone,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL
);


ALTER TABLE public.execution_entity OWNER TO "user";

--
-- Name: execution_entity_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.execution_entity_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.execution_entity_id_seq OWNER TO "user";

--
-- Name: execution_entity_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.execution_entity_id_seq OWNED BY public.execution_entity.id;


--
-- Name: execution_metadata; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.execution_metadata (
    id integer NOT NULL,
    "executionId" integer NOT NULL,
    key character varying(255) NOT NULL,
    value text NOT NULL
);


ALTER TABLE public.execution_metadata OWNER TO "user";

--
-- Name: execution_metadata_temp_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.execution_metadata_temp_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.execution_metadata_temp_id_seq OWNER TO "user";

--
-- Name: execution_metadata_temp_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.execution_metadata_temp_id_seq OWNED BY public.execution_metadata.id;


--
-- Name: folder; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.folder (
    id character varying(36) NOT NULL,
    name character varying(128) NOT NULL,
    "parentFolderId" character varying(36),
    "projectId" character varying(36) NOT NULL,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL
);


ALTER TABLE public.folder OWNER TO "user";

--
-- Name: folder_tag; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.folder_tag (
    "folderId" character varying(36) NOT NULL,
    "tagId" character varying(36) NOT NULL
);


ALTER TABLE public.folder_tag OWNER TO "user";

--
-- Name: insights_by_period; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.insights_by_period (
    id integer NOT NULL,
    "metaId" integer NOT NULL,
    type integer NOT NULL,
    value integer NOT NULL,
    "periodUnit" integer NOT NULL,
    "periodStart" timestamp(0) with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.insights_by_period OWNER TO "user";

--
-- Name: COLUMN insights_by_period.type; Type: COMMENT; Schema: public; Owner: user
--

COMMENT ON COLUMN public.insights_by_period.type IS '0: time_saved_minutes, 1: runtime_milliseconds, 2: success, 3: failure';


--
-- Name: COLUMN insights_by_period."periodUnit"; Type: COMMENT; Schema: public; Owner: user
--

COMMENT ON COLUMN public.insights_by_period."periodUnit" IS '0: hour, 1: day, 2: week';


--
-- Name: insights_by_period_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.insights_by_period ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.insights_by_period_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: insights_metadata; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.insights_metadata (
    "metaId" integer NOT NULL,
    "workflowId" character varying(16),
    "projectId" character varying(36),
    "workflowName" character varying(128) NOT NULL,
    "projectName" character varying(255) NOT NULL
);


ALTER TABLE public.insights_metadata OWNER TO "user";

--
-- Name: insights_metadata_metaId_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.insights_metadata ALTER COLUMN "metaId" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public."insights_metadata_metaId_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: insights_raw; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.insights_raw (
    id integer NOT NULL,
    "metaId" integer NOT NULL,
    type integer NOT NULL,
    value integer NOT NULL,
    "timestamp" timestamp(0) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.insights_raw OWNER TO "user";

--
-- Name: COLUMN insights_raw.type; Type: COMMENT; Schema: public; Owner: user
--

COMMENT ON COLUMN public.insights_raw.type IS '0: time_saved_minutes, 1: runtime_milliseconds, 2: success, 3: failure';


--
-- Name: insights_raw_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

ALTER TABLE public.insights_raw ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.insights_raw_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: installed_nodes; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.installed_nodes (
    name character varying(200) NOT NULL,
    type character varying(200) NOT NULL,
    "latestVersion" integer DEFAULT 1 NOT NULL,
    package character varying(241) NOT NULL
);


ALTER TABLE public.installed_nodes OWNER TO "user";

--
-- Name: installed_packages; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.installed_packages (
    "packageName" character varying(214) NOT NULL,
    "installedVersion" character varying(50) NOT NULL,
    "authorName" character varying(70),
    "authorEmail" character varying(70),
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL
);


ALTER TABLE public.installed_packages OWNER TO "user";

--
-- Name: invalid_auth_token; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.invalid_auth_token (
    token character varying(512) NOT NULL,
    "expiresAt" timestamp(3) with time zone NOT NULL
);


ALTER TABLE public.invalid_auth_token OWNER TO "user";

--
-- Name: migrations; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.migrations (
    id integer NOT NULL,
    "timestamp" bigint NOT NULL,
    name character varying NOT NULL
);


ALTER TABLE public.migrations OWNER TO "user";

--
-- Name: migrations_id_seq; Type: SEQUENCE; Schema: public; Owner: user
--

CREATE SEQUENCE public.migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.migrations_id_seq OWNER TO "user";

--
-- Name: migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: user
--

ALTER SEQUENCE public.migrations_id_seq OWNED BY public.migrations.id;


--
-- Name: processed_data; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.processed_data (
    "workflowId" character varying(36) NOT NULL,
    context character varying(255) NOT NULL,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    value text NOT NULL
);


ALTER TABLE public.processed_data OWNER TO "user";

--
-- Name: project; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.project (
    id character varying(36) NOT NULL,
    name character varying(255) NOT NULL,
    type character varying(36) NOT NULL,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    icon json,
    description character varying(512)
);


ALTER TABLE public.project OWNER TO "user";

--
-- Name: project_relation; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.project_relation (
    "projectId" character varying(36) NOT NULL,
    "userId" uuid NOT NULL,
    role character varying NOT NULL,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL
);


ALTER TABLE public.project_relation OWNER TO "user";

--
-- Name: settings; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.settings (
    key character varying(255) NOT NULL,
    value text NOT NULL,
    "loadOnStartup" boolean DEFAULT false NOT NULL
);


ALTER TABLE public.settings OWNER TO "user";

--
-- Name: shared_credentials; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.shared_credentials (
    "credentialsId" character varying(36) NOT NULL,
    "projectId" character varying(36) NOT NULL,
    role text NOT NULL,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL
);


ALTER TABLE public.shared_credentials OWNER TO "user";

--
-- Name: shared_workflow; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.shared_workflow (
    "workflowId" character varying(36) NOT NULL,
    "projectId" character varying(36) NOT NULL,
    role text NOT NULL,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL
);


ALTER TABLE public.shared_workflow OWNER TO "user";

--
-- Name: tag_entity; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.tag_entity (
    name character varying(24) NOT NULL,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    id character varying(36) NOT NULL
);


ALTER TABLE public.tag_entity OWNER TO "user";

--
-- Name: test_case_execution; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.test_case_execution (
    id character varying(36) NOT NULL,
    "testRunId" character varying(36) NOT NULL,
    "executionId" integer,
    status character varying NOT NULL,
    "runAt" timestamp(3) with time zone,
    "completedAt" timestamp(3) with time zone,
    "errorCode" character varying,
    "errorDetails" json,
    metrics json,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    inputs json,
    outputs json
);


ALTER TABLE public.test_case_execution OWNER TO "user";

--
-- Name: test_run; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.test_run (
    id character varying(36) NOT NULL,
    "workflowId" character varying(36) NOT NULL,
    status character varying NOT NULL,
    "errorCode" character varying,
    "errorDetails" json,
    "runAt" timestamp(3) with time zone,
    "completedAt" timestamp(3) with time zone,
    metrics json,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL
);


ALTER TABLE public.test_run OWNER TO "user";

--
-- Name: user; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public."user" (
    id uuid DEFAULT uuid_in((OVERLAY(OVERLAY(md5((((random())::text || ':'::text) || (clock_timestamp())::text)) PLACING '4'::text FROM 13) PLACING to_hex((floor(((random() * (((11 - 8) + 1))::double precision) + (8)::double precision)))::integer) FROM 17))::cstring) NOT NULL,
    email character varying(255),
    "firstName" character varying(32),
    "lastName" character varying(32),
    password character varying(255),
    "personalizationAnswers" json,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    settings json,
    disabled boolean DEFAULT false NOT NULL,
    "mfaEnabled" boolean DEFAULT false NOT NULL,
    "mfaSecret" text,
    "mfaRecoveryCodes" text,
    role text NOT NULL,
    "lastActiveAt" date
);


ALTER TABLE public."user" OWNER TO "user";

--
-- Name: user_api_keys; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.user_api_keys (
    id character varying(36) NOT NULL,
    "userId" uuid NOT NULL,
    label character varying(100) NOT NULL,
    "apiKey" character varying NOT NULL,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    scopes json
);


ALTER TABLE public.user_api_keys OWNER TO "user";

--
-- Name: variables; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.variables (
    key character varying(50) NOT NULL,
    type character varying(50) DEFAULT 'string'::character varying NOT NULL,
    value character varying(255),
    id character varying(36) NOT NULL
);


ALTER TABLE public.variables OWNER TO "user";

--
-- Name: webhook_entity; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.webhook_entity (
    "webhookPath" character varying NOT NULL,
    method character varying NOT NULL,
    node character varying NOT NULL,
    "webhookId" character varying,
    "pathLength" integer,
    "workflowId" character varying(36) NOT NULL
);


ALTER TABLE public.webhook_entity OWNER TO "user";

--
-- Name: workflow_entity; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.workflow_entity (
    name character varying(128) NOT NULL,
    active boolean NOT NULL,
    nodes json NOT NULL,
    connections json NOT NULL,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    settings json,
    "staticData" json,
    "pinData" json,
    "versionId" character(36),
    "triggerCount" integer DEFAULT 0 NOT NULL,
    id character varying(36) NOT NULL,
    meta json,
    "parentFolderId" character varying(36) DEFAULT NULL::character varying,
    "isArchived" boolean DEFAULT false NOT NULL
);


ALTER TABLE public.workflow_entity OWNER TO "user";

--
-- Name: workflow_history; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.workflow_history (
    "versionId" character varying(36) NOT NULL,
    "workflowId" character varying(36) NOT NULL,
    authors character varying(255) NOT NULL,
    "createdAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    "updatedAt" timestamp(3) with time zone DEFAULT CURRENT_TIMESTAMP(3) NOT NULL,
    nodes json NOT NULL,
    connections json NOT NULL
);


ALTER TABLE public.workflow_history OWNER TO "user";

--
-- Name: workflow_statistics; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.workflow_statistics (
    count integer DEFAULT 0,
    "latestEvent" timestamp(3) with time zone,
    name character varying(128) NOT NULL,
    "workflowId" character varying(36) NOT NULL,
    "rootCount" integer DEFAULT 0
);


ALTER TABLE public.workflow_statistics OWNER TO "user";

--
-- Name: workflows_tags; Type: TABLE; Schema: public; Owner: user
--

CREATE TABLE public.workflows_tags (
    "workflowId" character varying(36) NOT NULL,
    "tagId" character varying(36) NOT NULL
);


ALTER TABLE public.workflows_tags OWNER TO "user";

--
-- Name: auth_provider_sync_history id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_provider_sync_history ALTER COLUMN id SET DEFAULT nextval('public.auth_provider_sync_history_id_seq'::regclass);


--
-- Name: execution_annotations id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.execution_annotations ALTER COLUMN id SET DEFAULT nextval('public.execution_annotations_id_seq'::regclass);


--
-- Name: execution_entity id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.execution_entity ALTER COLUMN id SET DEFAULT nextval('public.execution_entity_id_seq'::regclass);


--
-- Name: execution_metadata id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.execution_metadata ALTER COLUMN id SET DEFAULT nextval('public.execution_metadata_temp_id_seq'::regclass);


--
-- Name: migrations id; Type: DEFAULT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.migrations ALTER COLUMN id SET DEFAULT nextval('public.migrations_id_seq'::regclass);


--
-- Data for Name: annotation_tag_entity; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.annotation_tag_entity (id, name, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: auth_identity; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.auth_identity ("userId", "providerId", "providerType", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: auth_provider_sync_history; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.auth_provider_sync_history (id, "providerType", "runMode", status, "startedAt", "endedAt", scanned, created, updated, disabled, error) FROM stdin;
\.


--
-- Data for Name: credentials_entity; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.credentials_entity (name, data, type, "createdAt", "updatedAt", id, "isManaged") FROM stdin;
OpenAi account	U2FsdGVkX19oEhBd7WyVpwW18PnONdrEoXk9Mnm7hv7alXCJzCPgy3JTvrEnUDBXtjM27rkd4RpygwN3gNPvtl4ZKaPiU5ZHg+eFQHSQGwKMnmX2Viz/5Sjkn3XTjBrEB25/yBli14OVy1nXXhPxGF3bbCl3MxG5D3cEDAeffe1eIiuhRNNcz7ivpTTQ59NRoJQN0/KhSqOjPUGdgqb3eNE8Tchs+a1N9bcL8RjzKzSYG3yn5LH3ZQRKSp2HKn8MrXfaz3A7/98KyVg4UQ4mlg==	openAiApi	2025-08-22 09:13:16.732+00	2025-08-22 09:13:16.732+00	ZOoOxdQLoU9NXU8C	f
Google Gemini(PaLM) Api account	U2FsdGVkX1+xovBCtSBqUw5ymAEBfC9OESIOl8uPR/0eliNCLpIQ57OpLvklqgYWywhQhN+psEfbmfJjdAn+FYRvjGiRZo+V++9tpsOnzrU=	googlePalmApi	2025-08-22 09:23:15.815+00	2025-08-22 09:23:15.813+00	3WkByUE0XIbzCIyP	f
\.


--
-- Data for Name: event_destinations; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.event_destinations (id, destination, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: execution_annotation_tags; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.execution_annotation_tags ("annotationId", "tagId") FROM stdin;
\.


--
-- Data for Name: execution_annotations; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.execution_annotations (id, "executionId", vote, note, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: execution_data; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.execution_data ("executionId", "workflowData", data) FROM stdin;
1	{"id":"pH2xNmi7K8vUn2CU","name":"🤖💬 Conversational AI Chatbot with Google Gemini for Text & Image | Telegram","active":false,"nodes":[{"parameters":{"mode":"manual","duplicateItem":false,"assignments":{"assignments":[{"id":"b3ce66e0-2b6f-4714-a672-59ef6e43ced9","name":"prompt","type":"string","value":"=1. Rules\\n- Communicate formally, clearly, and respectfully at all times.\\n- Be concise and precise, avoiding unnecessary details.\\n- Maintain a positive, professional tone as a reliable and knowledgeable assistant.\\n- Respect user boundaries and refrain from engaging in prohibited or inappropriate topics.\\n- Keep the conversation engaging by encouraging user participation, but when requesting clarification or additional information, ask only one focused question at a time to avoid overwhelming the user. Avoid abrupt endings to the dialogue.\\n- Follow user instructions precisely.\\n- Do not include any additional text or explanations unless explicitly requested.\\n\\n2. Response instructions\\n- Analyze the message and conversation history to maintain context and continuity.\\n- If repetition is requested, return the message exactly as provided. Otherwise, respond clearly and appropriately based on intent and context.\\n- If the message is vague, suggest an interpretation and ask for clarification while keeping the conversation active."}]},"includeOtherFields":false,"options":{}},"id":"3017950d-52a5-4f51-a64d-7f223c01743e","name":"chatPrompt","type":"n8n-nodes-base.set","position":[1600,608],"typeVersion":3.4},{"parameters":{"mode":"manual","duplicateItem":false,"assignments":{"assignments":[{"id":"d78c64d5-3c9e-4ffd-875e-973eb3c4d20a","name":"prompt","type":"string","value":"=1. Rules\\n- Communicate formally, clearly, and respectfully at all times.\\n- Be concise and precise, avoiding unnecessary details.\\n- Maintain a positive, professional tone as a reliable and knowledgeable assistant.\\n- Respect user boundaries and refrain from engaging in prohibited or inappropriate topics.\\n- Keep the conversation engaging by encouraging user participation, but when requesting clarification or additional information, ask only one focused question at a time to avoid overwhelming the user. Avoid abrupt endings to the dialogue.\\n- Follow user instructions precisely.\\n- Do not include any additional text or explanations unless explicitly requested.\\n\\n2. General Instructions\\n- If a message is unclear or lacks details, respond with your best understanding as a suggestion, and politely ask the user to confirm or provide more details if it is not what they meant.\\n- If the request is inappropriate or irrelevant, respond politely and clearly refuse, maintaining respect and professionalism, and invite a valid request.\\n- Always follow the established rules to maintain professionalism and accuracy."}]},"includeOtherFields":false,"options":{}},"id":"975f1ebd-9af9-4bc5-8cec-6c3be3d3a63d","name":"otherPrompt","type":"n8n-nodes-base.set","position":[1600,768],"typeVersion":3.4},{"parameters":{"mode":"manual","duplicateItem":false,"assignments":{"assignments":[{"id":"70c93816-7110-48e3-a105-568dd766bdf4","name":"prompt","type":"string","value":"=1.  General instructions\\nCreate a prompt that generates only one response with no comments and no line breaks. Based on the input provided, generate a detailed visual description of the request in a clear and coherent manner. Avoid using quotation marks, apostrophes, or any other punctuation marks.\\n\\n2. Visual specifications\\n- Composition: maintain a balanced and harmonious layout where all elements (images, shapes, and text) work together seamlessly. Ensure that no part of the image feels overcrowded, and the design directs the viewer’s attention naturally. Use a minimalistic approach, avoiding excessive detail or distractions.\\n\\n- Color palette: choose a color scheme that complements the overall theme and enhances the mood. The colors should be visually pleasing and work together harmoniously. Use no more than 3-4 main colors to maintain simplicity and coherence, ensuring they do not overpower the design.\\n\\n- Visual clarity: the image should be crisp and clear, with all elements legible and easy to interpret. Avoid blurry or pixelated visuals. Maintain sharp contrasts between key elements for emphasis and visual clarity.\\n\\n- Image size and resolution: the image should be sized to fit standard display requirements (e.g., social media, web posts). Ensure the resolution is high enough for clarity on both desktop and mobile devices. The image must maintain visual quality without losing sharpness or details when viewed on different screens.\\n\\n- Aesthetic appeal: aim for a design that is not only functional but also visually appealing. The overall style should be modern, clean, and aesthetically pleasing, with a consistent look that aligns with the intended mood or message. Ensure the design is pleasing to the eye, evoking the intended response from the viewer.\\n\\n- Visual balance: ensure the elements in the image are well-spaced, with no part feeling too heavy or too light. Create a natural flow of focus, guiding the viewer’s eye through the composition without distraction."}]},"includeOtherFields":false,"options":{}},"id":"f297c8ce-7c7c-4011-9c36-e4993dbfd926","name":"generatePrompt","type":"n8n-nodes-base.set","position":[1600,448],"typeVersion":3.4},{"parameters":{"mode":"manual","duplicateItem":false,"assignments":{"assignments":[{"id":"f435e1c3-6ff9-4e68-852c-0a39a5903ebe","name":"prompt","type":"string","value":"={{ $json.prompt }}"}]},"includeOtherFields":true,"include":"all","options":{}},"id":"51eb4b31-0099-4886-9dfd-dc9c02107562","name":"buildPrompt","type":"n8n-nodes-base.set","position":[1728,608],"typeVersion":3.4},{"parameters":{"aiAgentStarterCallout":"","preBuiltAgentsCallout":"","agent":"toolsAgent","promptType":"define","text":"=Input from user: {{ $('sessionData').item.json.Mensaje }}\\n\\n{{ $json.prompt }}\\n\\nRespond only with the exact text requested, strictly following the instructions above. ","hasOutputParser":false,"options":{"systemMessage":"=You are a professional enterprise chatbot designed to assist users with clear, respectful, and accurate communication. You provide informative text responses and generate images only upon explicit user requests. Utilize available conversation history to maintain context and coherence. Adhere strictly to company rules to ensure appropriate and secure interactions.\\n\\nYou can chat with me to get answers and create custom images based on your instructions.\\n\\nPrioritize the most recent relevant message if multiple prior references exist. Always respond in Spanish when providing text-based chat replies. For image generation requests, respond in English. Keep responses as short as possible without compromising clarity or natural interaction. Do not force interaction with the user beyond what is necessary to respond clearly."},"credentials":""},"id":"d9a263b3-9078-47e9-8146-abf62100af21","name":"ChatCore","type":"@n8n/n8n-nodes-langchain.agent","position":[1872,608],"typeVersion":1.9},{"parameters":{"notice":"","modelName":"models/gemini-2.0-flash-001","options":{}},"id":"67e27375-a9f9-45d7-8ea9-aad5d18996de","name":"GeminiModel","type":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","position":[1888,768],"typeVersion":1,"credentials":{"googlePalmApi":{"id":"3WkByUE0XIbzCIyP","name":"Google Gemini(PaLM) Api account"}}},{"parameters":{"notice":"","promptType":"define","text":"=1. Context\\nYou are an intelligent assistant designed to analyze user requests accurately by considering both the current user input and prior conversation history (if available). Use this context to improve classification and response relevance. Only classify as \\"generate\\" if the request clearly meets all criteria. Never assume without certainty.\\n\\n2. General instructions\\n- Use prior conversation history, if available, to interpret references, refinements, or corrections in the user’s message. \\n- Apply updates directly when a previous request is being modified (e.g., changing part of a generated image).\\n- Maintain context continuity unless the user explicitly shifts the topic.\\n- When evaluating intention, prioritize the most recent relevant message to avoid using outdated context.\\n- If the message is vague, nonsensical, or overly brief (e.g., a single word), do not classify it as \\"generate\\". Even if image requests occurred earlier. Only proceed if the current input clearly meets all criteria. Default to \\"other\\" unless the message includes clear direction.\\n\\n3. User Request: \\"{{ $('sessionData').item.json.Mensaje }}\\"\\n\\n4. Conversation history:\\n{{ $json.messages}}\\n\\n5. Intentions\\nClassify the user's intention as exactly one of the following:\\n- \\"generate\\": user requests the direct creation of new visual content (e.g., images or art) to be delivered. This includes implicit requests for physical or tangible objects (e.g., \\"give me a pet\\"), but only if the message contains clear intent or is supported by prior context. Single-word or vague inputs without clear directive or context should not be treated as generate.\\n- \\"chat\\": user engages in meaningful conversational interaction, including requests to generate textual prompts, explanations, or dialogue that have clear context and intent.\\n- \\"other\\": request does not fit into the above categories, or involves content that is restricted, inappropriate, irrelevant, nonsensical, or lacks clear context or intent distinct from conversational engagement.\\n\\n6. Data types\\nWhen analyzing the user’s message, determine whether a request for a physical or tangible object should be interpreted as a visual content request. In general, treat requests for tangible items as image generation unless the context explicitly suggests otherwise. For intangible content, respond with text unless the user clearly asks for an image.\\n\\nBased on the intention and content of the request, select exactly one data type to return:\\n\\n- \\"text\\": a textual response such as an answer, explanation, dialogue, or prompt text.\\n- \\"image\\": a visual response explicitly or implicitly requested as image generation.\\n\\nAssign \\"image\\" as typeData only if the intention is \\"generate\\". In all other cases, including when the intention is \\"other\\", assign \\"text\\".\\n\\n7. Output format\\nRespond only with a JSON object in this exact format, with no additional text or explanation: {\\"intention\\": \\"value\\", \\"typeData\\": \\"value\\"}","hasOutputParser":true,"needsFallback":false,"messages":{}},"id":"58ab707f-00e3-4b9f-b598-af89cebf99ce","name":"inputProcessor","type":"@n8n/n8n-nodes-langchain.chainLlm","position":[1136,608],"typeVersion":1.6,"alwaysOutputData":false},{"parameters":{"notice":"","modelName":"models/gemini-2.0-flash","options":{}},"id":"0123eef4-eb57-485a-ad01-66dc47b332f8","name":"GeminiModel1","type":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","position":[1152,752],"typeVersion":1,"credentials":{"googlePalmApi":{"id":"3WkByUE0XIbzCIyP","name":"Google Gemini(PaLM) Api account"}}},{"parameters":{"notice":"","schemaType":"fromJson","jsonSchemaExample":"{\\n\\t\\"typeData\\": \\"text\\",\\n\\t\\"intention\\": \\"generate\\"\\n}","autoFix":false},"id":"8853d1da-5ab6-44cf-8054-7267392c9eae","name":"structuredOutput","type":"@n8n/n8n-nodes-langchain.outputParserStructured","position":[1296,752],"typeVersion":1.2},{"parameters":{"mode":"rules","rules":{"values":[{"conditions":{"options":{"version":2,"leftValue":"","caseSensitive":true,"typeValidation":"strict"},"combinator":"and","conditions":[{"id":"24ab5811-2b6d-4f2f-8620-8697dadc2d4d","operator":{"type":"string","operation":"contains"},"leftValue":"={{ $('intentHandler').item.json.output.typeData }}","rightValue":"image"}]},"renameOutput":true,"outputKey":"=Imagen"},{"conditions":{"options":{"version":2,"leftValue":"","caseSensitive":true,"typeValidation":"strict"},"combinator":"and","conditions":[{"id":"********-2f6a-4bb3-a9bf-************","operator":{"type":"string","operation":"contains"},"leftValue":"={{ $('intentHandler').item.json.output.typeData }}","rightValue":"text"}]},"renameOutput":true,"outputKey":"Texto"}]},"looseTypeValidation":false,"options":{}},"id":"b679111d-c640-4f59-8046-c37fa7f977e9","name":"contentType","type":"n8n-nodes-base.switch","position":[2176,688],"typeVersion":3.2},{"parameters":{"mode":"runOnceForAllItems","language":"javaScript","jsCode":"let texto = $input.first().json.prompt.replace(/[\\\\n\\\\r\\\\t]/g, \\" \\")  // Reemplaza saltos de línea, retorno de carro y tabulaciones por espacio\\n  .replace(/['\\"\\\\\\\\]/g, \\"\\")      // Elimina comillas simples, dobles y barras invertidas\\n  .replace(/\\\\s+/g, \\" \\")        // Reemplaza múltiples espacios por un solo espacio\\n  .trim();                     // Elimina los espacios en blanco al inicio y al final\\n\\nreturn {\\n  text: texto\\n}","notice":""},"id":"8abd3463-8ecc-4086-9057-e7494b8b2d9d","name":"textCleaner","type":"n8n-nodes-base.code","position":[2416,512],"typeVersion":2},{"parameters":{"preBuiltAgentsCalloutHttpRequest":"","curlImport":"","method":"POST","url":"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp-image-generation:generateContent","authentication":"predefinedCredentialType","nodeCredentialType":"googlePalmApi","provideSslCertificates":false,"sendQuery":false,"sendHeaders":false,"sendBody":true,"contentType":"json","specifyBody":"json","jsonBody":"={\\n  \\"contents\\": [\\n    {\\n      \\"role\\": \\"user\\",\\n      \\"parts\\": [\\n        {\\n          \\"text\\": \\"{{ $json.text }}\\"\\n        }\\n      ]\\n    }\\n  ],\\n  \\"generationConfig\\": {\\n    \\"responseModalities\\": [\\"Text\\", \\"Image\\"]\\n  }\\n}","options":{},"infoMessage":""},"id":"a11a2afc-71c4-4b3f-81ec-fb8cacc1d9f2","name":"imageGeneration","type":"n8n-nodes-base.httpRequest","position":[2528,512],"typeVersion":4.2},{"parameters":{"operation":"toBinary","sourceProperty":"candidates[0].content.parts[1].inlineData.data","binaryPropertyName":"data","options":{"fileName":"generated_image.png"}},"id":"ccce85bc-79f7-43ed-b649-8b1533d774a5","name":"imageBuilder","type":"n8n-nodes-base.convertToFile","position":[2656,512],"typeVersion":1.1},{"parameters":{"preBuiltAgentsCalloutTelegram":"","resource":"message","operation":"sendPhoto","chatId":"={{ $('sessionData').item.json.sessionId }}","binaryData":true,"binaryPropertyName":"data","replyMarkup":"none","additionalFields":{"fileName":"=generated_image.png"}},"id":"4dbbf4c4-4d78-42d3-bdbc-a35a584643ff","name":"sendImage","type":"n8n-nodes-base.telegram","position":[2768,512],"webhookId":"511272cc-d9e3-4384-9407-e5d4296db621","typeVersion":1},{"parameters":{"preBuiltAgentsCalloutTelegram":"","resource":"message","operation":"sendMessage","chatId":"={{ $('sessionData').item.json.sessionId }}","text":"={{ $('ChatCore').item.json.output }}","replyMarkup":"none","additionalFields":{"appendAttribution":false,"parse_mode":"HTML"}},"id":"8e525ff1-f403-4f7a-a281-36fb18824168","name":"sendTextMessage","type":"n8n-nodes-base.telegram","position":[2768,704],"webhookId":"1d1eee00-2306-4ac1-870f-0988e305f0d0","typeVersion":1},{"parameters":{"keepOnlySet":true,"values":{"string":[{"name":"Mensaje","value":"={{ $('userInput').item.json.message.text }}"},{"name":"sessionId","value":"={{ $('userInput').item.json.message.chat.id }}"},{"name":"Lenguaje","value":"={{ $('userInput').item.json.message.from.language_code }}"},{"name":"Username","value":"={{ $('userInput').item.json.message.chat.username }}"}]},"options":{}},"id":"4e0fcbad-8b6b-4c39-80ac-6aed0f8cf0fd","name":"sessionData","type":"n8n-nodes-base.set","position":[608,608],"typeVersion":2},{"parameters":{"mode":"load","simplifyOutput":true,"options":{"groupMessages":true}},"id":"ea84ebc4-2932-4fd9-8771-b13a929b6ea5","name":"conversationStore","type":"@n8n/n8n-nodes-langchain.memoryManager","position":[736,608],"typeVersion":1.1},{"parameters":{"notice":"","sessionIdType":"customKey","sessionKey":"={{ $('sessionData').item.json.sessionId }}","contextWindowLength":2},"id":"03420024-1474-4b55-a98a-e80e0826ccc7","name":"memoryRetriever","type":"@n8n/n8n-nodes-langchain.memoryBufferWindow","position":[768,752],"typeVersion":1.3},{"parameters":{"mode":"runOnceForAllItems","language":"javaScript","jsCode":"const allItems = $input.all();\\nconst lastItem = allItems[allItems.length - 1];\\n\\nif (lastItem && Array.isArray(lastItem.json.messages)) {\\n  const messages = lastItem.json.messages;\\n  const count = messages.length;\\n\\n  if (count === 0) return [{ json: { message: \\"\\" } }];\\n\\n  const extractFirstLine = (text) => {\\n    if (!text) return \\"\\";\\n    return text.split('\\\\n')[0].replace(/^Input from user:\\\\s*/, '');\\n  };\\n\\n  const trimEndNewline = (text) => {\\n    if (!text) return \\"\\";\\n    return text.replace(/\\\\n+$/, '');\\n  };\\n\\n  // Tomar los últimos dos o menos mensajes\\n  const selectedMessages = (count === 1) ? [messages[0]] : messages.slice(-1);\\n\\n  // Construir el texto concatenado\\n  const combinedMessage = selectedMessages.map((msg, idx) => {\\n    return `Message ${idx + 1}:\\\\nhuman: ${extractFirstLine(msg.human)}\\\\nai: ${trimEndNewline(msg.ai)}`;\\n  }).join('\\\\n\\\\n');\\n\\n  return [{ json: { messages: combinedMessage } }];\\n}\\n\\nreturn [{ json: { messages: \\"\\" } }];","notice":""},"id":"28f6a639-1c50-4754-a66c-772cc3a05083","name":"latestContext","type":"n8n-nodes-base.code","position":[1008,608],"typeVersion":2},{"parameters":{"mode":"rules","rules":{"values":[{"conditions":{"options":{"version":2,"leftValue":"","caseSensitive":true,"typeValidation":"strict"},"combinator":"and","conditions":[{"id":"24ab5811-2b6d-4f2f-8620-8697dadc2d4d","operator":{"type":"string","operation":"contains"},"leftValue":"={{ $json.output.intention }}","rightValue":"generate"}]},"renameOutput":true,"outputKey":"generate"},{"conditions":{"options":{"version":2,"leftValue":"","caseSensitive":true,"typeValidation":"strict"},"combinator":"and","conditions":[{"id":"********-2f6a-4bb3-a9bf-************","operator":{"type":"string","operation":"contains"},"leftValue":"={{ $json.output.intention }}","rightValue":"chat"}]},"renameOutput":true,"outputKey":"chat"},{"conditions":{"options":{"version":2,"leftValue":"","caseSensitive":true,"typeValidation":"strict"},"combinator":"and","conditions":[{"id":"de4b6e87-950e-461c-869f-d27c73f8d763","operator":{"type":"string","operation":"contains"},"leftValue":"={{ $json.output.intention }}","rightValue":"other"}]},"renameOutput":true,"outputKey":"other"}]},"looseTypeValidation":false,"options":{}},"id":"14bae45a-aafa-407f-9761-62954acabbbd","name":"intentHandler","type":"n8n-nodes-base.switch","position":[1408,608],"typeVersion":3.2},{"parameters":{"notice":"","sessionIdType":"customKey","sessionKey":"={{ $('sessionData').item.json.sessionId }}","contextWindowLength":10},"id":"357a258b-32a7-47c4-bcf1-f32cec025cba","name":"conversationMemory","type":"@n8n/n8n-nodes-langchain.memoryBufferWindow","position":[2016,784],"typeVersion":1.3},{"parameters":{"mode":"manual","duplicateItem":false,"assignments":{"assignments":[{"id":"d11831d7-533b-4132-9849-612e093d6b32","name":"prompt","type":"string","value":"={{ $('ChatCore').item.json.output }} It is mandatory to add a description of the image you are going to make."}]},"includeOtherFields":false,"options":{}},"id":"f1dad098-8b9f-4160-91ab-1eb7185d51a2","name":"errorPreprocessor","type":"n8n-nodes-base.set","position":[2288,512],"typeVersion":3.4},{"parameters":{"content":"### 2. Memory and Conversational Context\\n\\nRetrieves the necessary context to properly infer intentions.\\n\\n- conversationStore: stores the entire conversation history.\\n\\n- memoryRetriever: extracts relevant information according to the current need.\\n\\n- latestContext: formats and prepares the context to be useful in response generation.\\n","height":680,"width":380,"color":4},"id":"804fcbd3-cbc1-41d9-bbeb-40469545012f","name":"Sticky Note","type":"n8n-nodes-base.stickyNote","position":[736,240],"typeVersion":1},{"parameters":{"content":"### 1. Input and Session Management\\n\\nReceives messages from Telegram and manages the session to maintain context.\\n\\n- userInput: captures the user's message.\\n\\n- sessionData: saves and updates the session state.\\n","height":680,"width":340,"color":5},"id":"4b4e8510-6b97-4b1d-afdb-916581aa539d","name":"Sticky Note1","type":"n8n-nodes-base.stickyNote","position":[368,240],"typeVersion":1},{"parameters":{"content":"### 3. Intent Processing and Prompt Generation\\nAnalyzes the intention and selects appropriate prompts according to the user's intention.\\n\\n- inputProcessor: detects intention and type of content to send.\\n\\n- intentHandler: redirects the flow based on the intention.\\n\\n- generatePrompt, chatPrompt, otherPrompt, buildPrompt: select the prompts for the response.","height":680,"width":700,"color":6},"id":"6b14b58f-2e17-4cfb-baf4-3318b3fd9533","name":"Sticky Note2","type":"n8n-nodes-base.stickyNote","position":[1136,240],"typeVersion":1},{"parameters":{"content":"### 4. Core of Generation and Conversation Management\\nGenerates responses using Google Gemini, integrating context for coherence.\\n\\n- ChatCore: orchestrates the generation and management of the conversation.\\n\\n- GeminiModel: creates the final response based on prompts.\\n\\n- conversationMemory: stores information to maintain coherence.","height":680,"width":300,"color":3},"id":"fa4cfc3f-cdaf-4184-b183-ca31b1aa1d86","name":"Sticky Note3","type":"n8n-nodes-base.stickyNote","position":[1856,240],"typeVersion":1},{"parameters":{"content":"### 5. Content Classification and User Delivery\\n\\nDetermines the type of content and manages its delivery via Telegram.\\n\\n- contentType: defines the output format (text, image, etc.).\\n\\n- errorPreprocessor, textCleaner: clean and validate the content.\\n\\n- imageGeneration, imageBuilder: create visual content when necessary.\\n\\n- sendImage, sendTextMessage: send the content to the user.","height":680,"width":800,"color":7},"id":"ce598ece-492b-4294-9aa4-ea0c6757edf2","name":"Sticky Note4","type":"n8n-nodes-base.stickyNote","position":[2176,240],"typeVersion":1}],"connections":{"ChatCore":{"main":[[{"node":"contentType","type":"main","index":0}]]},"chatPrompt":{"main":[[{"node":"buildPrompt","type":"main","index":0}]]},"GeminiModel":{"ai_languageModel":[[{"node":"ChatCore","type":"ai_languageModel","index":0}]]},"buildPrompt":{"main":[[{"node":"ChatCore","type":"main","index":0}]]},"contentType":{"main":[[{"node":"errorPreprocessor","type":"main","index":0}],[{"node":"sendTextMessage","type":"main","index":0}]]},"otherPrompt":{"main":[[{"node":"buildPrompt","type":"main","index":0}]]},"sessionData":{"main":[[{"node":"conversationStore","type":"main","index":0}]]},"textCleaner":{"main":[[{"node":"imageGeneration","type":"main","index":0}]]},"GeminiModel1":{"ai_languageModel":[[{"node":"inputProcessor","type":"ai_languageModel","index":0}]]},"imageBuilder":{"main":[[{"node":"sendImage","type":"main","index":0}]]},"intentHandler":{"main":[[{"node":"generatePrompt","type":"main","index":0}],[{"node":"chatPrompt","type":"main","index":0}],[{"node":"otherPrompt","type":"main","index":0}]]},"latestContext":{"main":[[{"node":"inputProcessor","type":"main","index":0}]]},"generatePrompt":{"main":[[{"node":"buildPrompt","type":"main","index":0}]]},"inputProcessor":{"main":[[{"node":"intentHandler","type":"main","index":0}]]},"imageGeneration":{"main":[[{"node":"imageBuilder","type":"main","index":0}]]},"memoryRetriever":{"ai_memory":[[{"node":"conversationStore","type":"ai_memory","index":0}]]},"sendTextMessage":{"main":[[]]},"structuredOutput":{"ai_outputParser":[[{"node":"inputProcessor","type":"ai_outputParser","index":0}]]},"conversationStore":{"main":[[{"node":"latestContext","type":"main","index":0}]]},"errorPreprocessor":{"main":[[{"node":"textCleaner","type":"main","index":0}]]},"conversationMemory":{"ai_memory":[[{"node":"ChatCore","type":"ai_memory","index":0}]]}},"settings":{"executionOrder":"v1"},"pinData":{}}	[{"startData":"1","resultData":"2","executionData":"3"},{"destinationNode":"4","runNodeFilter":"5"},{"runData":"6","pinData":"7","lastNodeExecuted":"4","error":"8"},{"contextData":"9","nodeExecutionStack":"10","metadata":"11","waitingExecution":"12","waitingExecutionSource":"13"},"sessionData",["4"],{"sessionData":"14"},{},{"cause":"15","level":"16","tags":"17","timestamp":1755854641526,"context":"18","functionality":"19","name":"20","message":"21","stack":"22"},{},["23"],{},{},{},["24"],["25","26","27","28"],"warning",{},{"runIndex":0,"itemIndex":0,"nodeCause":"29","descriptionKey":"30","parameter":"31"},"regular","ExpressionError","Referenced node doesn't exist","ExpressionError: Referenced node doesn't exist\\n    at createExpressionError (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-workflow@file+packages+workflow/node_modules/n8n-workflow/src/workflow-data-proxy.ts:775:11)\\n    at Proxy.$ (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-workflow@file+packages+workflow/node_modules/n8n-workflow/src/workflow-data-proxy.ts:1056:12)\\n    at Proxy.eval (eval at getFunction (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/@n8n+tournament@1.0.6/node_modules/@n8n/tournament/src/FunctionEvaluator.ts:13:16), <anonymous>:6:42)\\n    at FunctionEvaluator.evaluate (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/@n8n+tournament@1.0.6/node_modules/@n8n/tournament/src/FunctionEvaluator.ts:20:13)\\n    at Tournament.execute (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/@n8n+tournament@1.0.6/node_modules/@n8n/tournament/src/index.ts:45:25)\\n    at evaluateExpression (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-workflow@file+packages+workflow/node_modules/n8n-workflow/src/expression-evaluator-proxy.ts:20:9)\\n    at Expression.renderExpression (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-workflow@file+packages+workflow/node_modules/n8n-workflow/src/expression.ts:332:29)\\n    at Expression.resolveSimpleParameterValue (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-workflow@file+packages+workflow/node_modules/n8n-workflow/src/expression.ts:311:28)\\n    at resolveParameterValue (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-workflow@file+packages+workflow/node_modules/n8n-workflow/src/expression.ts:503:16)\\n    at Expression.getParameterValue (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-workflow@file+packages+workflow/node_modules/n8n-workflow/src/expression.ts:560:22)",{"node":"32","data":"33","source":null},{"startTime":1755854641522,"executionIndex":0,"source":"34","hints":"35","executionTime":14,"executionStatus":"36","error":"37"},{"name":"38","value":"39"},{"name":"40","value":"41"},{"name":"42","value":"43"},{"name":"44","value":"45"},"userInput","nodeNotFound","values.string",{"parameters":"46","id":"47","name":"4","type":"48","position":"49","typeVersion":2},{"main":"50"},[],[],"error",{"cause":"15","level":"16","tags":"17","timestamp":1755854641526,"context":"18","functionality":"19","name":"20","message":"21","stack":"22"},"Mensaje","={{ $('userInput').item.json.message.text }}","sessionId","={{ $('userInput').item.json.message.chat.id }}","Lenguaje","={{ $('userInput').item.json.message.from.language_code }}","Username","={{ $('userInput').item.json.message.chat.username }}",{"keepOnlySet":true,"values":"51","options":"52"},"4e0fcbad-8b6b-4c39-80ac-6aed0f8cf0fd","n8n-nodes-base.set",[608,608],["53"],{"string":"15"},{},["54"],{"json":"55","pairedItem":"56"},{},{"item":0}]
\.


--
-- Data for Name: execution_entity; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.execution_entity (id, finished, mode, "retryOf", "retrySuccessId", "startedAt", "stoppedAt", "waitTill", status, "workflowId", "deletedAt", "createdAt") FROM stdin;
1	f	manual	\N	\N	2025-08-22 09:24:01.516+00	2025-08-22 09:24:01.536+00	\N	error	pH2xNmi7K8vUn2CU	\N	2025-08-22 09:24:01.498+00
\.


--
-- Data for Name: execution_metadata; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.execution_metadata (id, "executionId", key, value) FROM stdin;
\.


--
-- Data for Name: folder; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.folder (id, name, "parentFolderId", "projectId", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: folder_tag; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.folder_tag ("folderId", "tagId") FROM stdin;
\.


--
-- Data for Name: insights_by_period; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.insights_by_period (id, "metaId", type, value, "periodUnit", "periodStart") FROM stdin;
\.


--
-- Data for Name: insights_metadata; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.insights_metadata ("metaId", "workflowId", "projectId", "workflowName", "projectName") FROM stdin;
\.


--
-- Data for Name: insights_raw; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.insights_raw (id, "metaId", type, value, "timestamp") FROM stdin;
\.


--
-- Data for Name: installed_nodes; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.installed_nodes (name, type, "latestVersion", package) FROM stdin;
\.


--
-- Data for Name: installed_packages; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.installed_packages ("packageName", "installedVersion", "authorName", "authorEmail", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: invalid_auth_token; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.invalid_auth_token (token, "expiresAt") FROM stdin;
\.


--
-- Data for Name: migrations; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.migrations (id, "timestamp", name) FROM stdin;
1	1587669153312	InitialMigration1587669153312
2	1589476000887	WebhookModel1589476000887
3	1594828256133	CreateIndexStoppedAt1594828256133
4	1607431743768	MakeStoppedAtNullable1607431743768
5	1611144599516	AddWebhookId1611144599516
6	1617270242566	CreateTagEntity1617270242566
7	1620824779533	UniqueWorkflowNames1620824779533
8	1626176912946	AddwaitTill1626176912946
9	1630419189837	UpdateWorkflowCredentials1630419189837
10	1644422880309	AddExecutionEntityIndexes1644422880309
11	1646834195327	IncreaseTypeVarcharLimit1646834195327
12	1646992772331	CreateUserManagement1646992772331
13	1648740597343	LowerCaseUserEmail1648740597343
14	1652254514002	CommunityNodes1652254514002
15	1652367743993	AddUserSettings1652367743993
16	1652905585850	AddAPIKeyColumn1652905585850
17	1654090467022	IntroducePinData1654090467022
18	1658932090381	AddNodeIds1658932090381
19	1659902242948	AddJsonKeyPinData1659902242948
20	1660062385367	CreateCredentialsUserRole1660062385367
21	1663755770893	CreateWorkflowsEditorRole1663755770893
22	1664196174001	WorkflowStatistics1664196174001
23	1665484192212	CreateCredentialUsageTable1665484192212
24	1665754637025	RemoveCredentialUsageTable1665754637025
25	1669739707126	AddWorkflowVersionIdColumn1669739707126
26	1669823906995	AddTriggerCountColumn1669823906995
27	1671535397530	MessageEventBusDestinations1671535397530
28	1671726148421	RemoveWorkflowDataLoadedFlag1671726148421
29	1673268682475	DeleteExecutionsWithWorkflows1673268682475
30	1674138566000	AddStatusToExecutions1674138566000
31	1674509946020	CreateLdapEntities1674509946020
32	1675940580449	PurgeInvalidWorkflowConnections1675940580449
33	1676996103000	MigrateExecutionStatus1676996103000
34	1677236854063	UpdateRunningExecutionStatus1677236854063
35	1677501636754	CreateVariables1677501636754
36	1679416281778	CreateExecutionMetadataTable1679416281778
37	1681134145996	AddUserActivatedProperty1681134145996
38	1681134145997	RemoveSkipOwnerSetup1681134145997
39	1690000000000	MigrateIntegerKeysToString1690000000000
40	1690000000020	SeparateExecutionData1690000000020
41	1690000000030	RemoveResetPasswordColumns1690000000030
42	1690000000030	AddMfaColumns1690000000030
43	1690787606731	AddMissingPrimaryKeyOnExecutionData1690787606731
44	1691088862123	CreateWorkflowNameIndex1691088862123
45	1692967111175	CreateWorkflowHistoryTable1692967111175
46	1693491613982	ExecutionSoftDelete1693491613982
47	1693554410387	DisallowOrphanExecutions1693554410387
48	1694091729095	MigrateToTimestampTz1694091729095
49	1695128658538	AddWorkflowMetadata1695128658538
50	1695829275184	ModifyWorkflowHistoryNodesAndConnections1695829275184
51	1700571993961	AddGlobalAdminRole1700571993961
52	1705429061930	DropRoleMapping1705429061930
53	1711018413374	RemoveFailedExecutionStatus1711018413374
54	1711390882123	MoveSshKeysToDatabase1711390882123
55	1712044305787	RemoveNodesAccess1712044305787
56	1714133768519	CreateProject1714133768519
57	1714133768521	MakeExecutionStatusNonNullable1714133768521
58	1717498465931	AddActivatedAtUserSetting1717498465931
59	1720101653148	AddConstraintToExecutionMetadata1720101653148
60	1721377157740	FixExecutionMetadataSequence1721377157740
61	1723627610222	CreateInvalidAuthTokenTable1723627610222
62	1723796243146	RefactorExecutionIndices1723796243146
63	1724753530828	CreateAnnotationTables1724753530828
64	1724951148974	AddApiKeysTable1724951148974
65	1726606152711	CreateProcessedDataTable1726606152711
66	1727427440136	SeparateExecutionCreationFromStart1727427440136
67	1728659839644	AddMissingPrimaryKeyOnAnnotationTagMapping1728659839644
68	1729607673464	UpdateProcessedDataValueColumnToText1729607673464
69	1729607673469	AddProjectIcons1729607673469
70	1730386903556	CreateTestDefinitionTable1730386903556
71	1731404028106	AddDescriptionToTestDefinition1731404028106
72	1731582748663	MigrateTestDefinitionKeyToString1731582748663
73	1732271325258	CreateTestMetricTable1732271325258
74	1732549866705	CreateTestRun1732549866705
75	1733133775640	AddMockedNodesColumnToTestDefinition1733133775640
76	1734479635324	AddManagedColumnToCredentialsTable1734479635324
77	1736172058779	AddStatsColumnsToTestRun1736172058779
78	1736947513045	CreateTestCaseExecutionTable1736947513045
79	1737715421462	AddErrorColumnsToTestRuns1737715421462
80	1738709609940	CreateFolderTable1738709609940
81	1739549398681	CreateAnalyticsTables1739549398681
82	1740445074052	UpdateParentFolderIdColumn1740445074052
83	1741167584277	RenameAnalyticsToInsights1741167584277
84	1742918400000	AddScopesColumnToApiKeys1742918400000
85	1745322634000	ClearEvaluation1745322634000
86	1745587087521	AddWorkflowStatisticsRootCount1745587087521
87	1745934666076	AddWorkflowArchivedColumn1745934666076
88	1745934666077	DropRoleTable1745934666077
89	1747824239000	AddProjectDescriptionColumn1747824239000
90	1750252139166	AddLastActiveAtColumnToUser1750252139166
91	1752669793000	AddInputsOutputsToTestCaseExecution1752669793000
\.


--
-- Data for Name: processed_data; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.processed_data ("workflowId", context, "createdAt", "updatedAt", value) FROM stdin;
\.


--
-- Data for Name: project; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.project (id, name, type, "createdAt", "updatedAt", icon, description) FROM stdin;
WZoc1IM6EDKRYBHv	Pankaj Kumar <<EMAIL>>	personal	2025-08-20 22:14:28.717+00	2025-08-22 08:41:16.57+00	\N	\N
\.


--
-- Data for Name: project_relation; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.project_relation ("projectId", "userId", role, "createdAt", "updatedAt") FROM stdin;
WZoc1IM6EDKRYBHv	d7a441a9-6f91-44b8-81cd-c5225bffba5d	project:personalOwner	2025-08-20 22:14:28.717+00	2025-08-20 22:14:28.717+00
\.


--
-- Data for Name: settings; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.settings (key, value, "loadOnStartup") FROM stdin;
ui.banners.dismissed	["V1"]	t
features.ldap	{"loginEnabled":false,"loginLabel":"","connectionUrl":"","allowUnauthorizedCerts":false,"connectionSecurity":"none","connectionPort":389,"baseDn":"","bindingAdminDn":"","bindingAdminPassword":"","firstNameAttribute":"","lastNameAttribute":"","emailAttribute":"","loginIdAttribute":"","ldapIdAttribute":"","userFilter":"","synchronizationEnabled":false,"synchronizationInterval":60,"searchPageSize":0,"searchTimeout":60}	t
features.oidc	{"clientId":"","clientSecret":"","discoveryEndpoint":"","loginEnabled":false}	t
userManagement.authenticationMethod	email	t
features.sourceControl.sshKeys	{"encryptedPrivateKey":"U2FsdGVkX1+ZSaS73rLtFlJOmh838IrKrstNPZa0OzdVLgTZl5y+hd31gidBqg220fbRVT3Jro+Bb+7Sm93W9v6tMtpytw0gOamDDNPoLIF/Szy/wjAEQJoKHMJLTJxPrjYRhJm8YZrx3g14xxLaB+Ci1C7M/d7pMuG4QudSlkCNAye6Zs3zJoCDUCLU9wcqkI4mA55rPb2FFiKltJYlq2WwzZTQwM8sapbGFuYsm4ahs0TD69Ttc6q68GW3mwFU0LUxgxviRWoeWf0mJcgMzWP1h6VXy/PcAbf+GHaVade/sqVxVwuxU1073/ew99o8vHBSrMAsgq1LbMkPSZiJLh4ezncPBgsN92qGC/yhXADdMNzSynvA8RVhxUTfLwfmQThq1v+nBu19uDRQm/oPq3tg55a70Qo297tmqpn891nv4g/zihldfWOnYQbTaVc3LQXUlzm/ZYyFoy3ADzElOk0RnIt9PT5ozd8dWjx7soj5VgIR6l06ecE3oYFg1lvl27XPku6oq/XjNlR2sJRmUTERHI4266ZsnDG1I62T8gmc9S0sNruonu9Z7MWsviVn","publicKey":"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIIRSVE5rLiCbnHAGxUVS0jq77LSlpy4BKXCv70+4Y7G3 n8n deploy key"}	t
features.sourceControl	{"branchName":"main","keyGeneratorType":"ed25519"}	t
userManagement.isInstanceOwnerSetUp	true	t
\.


--
-- Data for Name: shared_credentials; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.shared_credentials ("credentialsId", "projectId", role, "createdAt", "updatedAt") FROM stdin;
ZOoOxdQLoU9NXU8C	WZoc1IM6EDKRYBHv	credential:owner	2025-08-22 09:13:16.732+00	2025-08-22 09:13:16.732+00
3WkByUE0XIbzCIyP	WZoc1IM6EDKRYBHv	credential:owner	2025-08-22 09:23:15.815+00	2025-08-22 09:23:15.815+00
\.


--
-- Data for Name: shared_workflow; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.shared_workflow ("workflowId", "projectId", role, "createdAt", "updatedAt") FROM stdin;
iN2nBcNE8vEr6waZ	WZoc1IM6EDKRYBHv	workflow:owner	2025-08-22 08:54:54.384+00	2025-08-22 08:54:54.384+00
pH2xNmi7K8vUn2CU	WZoc1IM6EDKRYBHv	workflow:owner	2025-08-22 09:11:18.749+00	2025-08-22 09:11:18.749+00
kLkyDkNvB687bGo6	WZoc1IM6EDKRYBHv	workflow:owner	2025-08-22 09:13:28.831+00	2025-08-22 09:13:28.831+00
\.


--
-- Data for Name: tag_entity; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.tag_entity (name, "createdAt", "updatedAt", id) FROM stdin;
\.


--
-- Data for Name: test_case_execution; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.test_case_execution (id, "testRunId", "executionId", status, "runAt", "completedAt", "errorCode", "errorDetails", metrics, "createdAt", "updatedAt", inputs, outputs) FROM stdin;
\.


--
-- Data for Name: test_run; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.test_run (id, "workflowId", status, "errorCode", "errorDetails", "runAt", "completedAt", metrics, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: user; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public."user" (id, email, "firstName", "lastName", password, "personalizationAnswers", "createdAt", "updatedAt", settings, disabled, "mfaEnabled", "mfaSecret", "mfaRecoveryCodes", role, "lastActiveAt") FROM stdin;
d7a441a9-6f91-44b8-81cd-c5225bffba5d	<EMAIL>	Pankaj	Kumar	$2a$10$D6Qs3bWrxaTiXsUNQddpI.lit8GzjVuL/0r2c/ggTuSBa/ohnuQQq	{"version":"v4","personalization_survey_submitted_at":"2025-08-22T08:41:19.017Z","personalization_survey_n8n_version":"1.107.4"}	2025-08-20 22:14:28.372+00	2025-08-22 08:54:54.806+00	{"userActivated":false,"easyAIWorkflowOnboarded":true}	f	f	\N	\N	global:owner	2025-08-22
\.


--
-- Data for Name: user_api_keys; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.user_api_keys (id, "userId", label, "apiKey", "createdAt", "updatedAt", scopes) FROM stdin;
\.


--
-- Data for Name: variables; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.variables (key, type, value, id) FROM stdin;
\.


--
-- Data for Name: webhook_entity; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.webhook_entity ("webhookPath", method, node, "webhookId", "pathLength", "workflowId") FROM stdin;
\.


--
-- Data for Name: workflow_entity; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.workflow_entity (name, active, nodes, connections, "createdAt", "updatedAt", settings, "staticData", "pinData", "versionId", "triggerCount", id, meta, "parentFolderId", "isArchived") FROM stdin;
Conversational Telegram Bot with GPT-4o for Text and Voice Messages	f	[{"parameters":{"updates":["message","*"],"additionalFields":{}},"id":"a33e3f45-f16e-4107-937e-0ce1d230060f","name":"Telegram Trigger","type":"n8n-nodes-base.telegramTrigger","position":[368,240],"webhookId":"912e007f-19d9-4b5a-a90e-12ba01c9978d","typeVersion":1.2},{"parameters":{"model":{"__rl":true,"mode":"list","value":"gpt-4o","cachedResultName":"gpt-4o"},"options":{}},"id":"e17f452c-4f9a-48f8-91a2-526260159676","name":"OpenAI Chat Model","type":"@n8n/n8n-nodes-langchain.lmChatOpenAi","position":[1552,848],"typeVersion":1.2},{"parameters":{"sessionIdType":"customKey","sessionKey":"={{ $json.sessionId }}","contextWindowLength":10},"id":"9b33ec46-8772-4bfb-a448-fea5a673bbaa","name":"Simple Memory","type":"@n8n/n8n-nodes-langchain.memoryBufferWindow","position":[1776,880],"typeVersion":1.3},{"parameters":{"resource":"file","fileId":"={{ $json.message.voice.file_id }}","additionalFields":{}},"id":"8cdd7ba8-c998-4ea7-b861-8dcaf12febfe","name":"Get Voice","type":"n8n-nodes-base.telegram","position":[1120,432],"webhookId":"1962cd81-9991-4199-a78a-2250bf29631a","typeVersion":1.2},{"parameters":{"rules":{"values":[{"conditions":{"options":{"version":2,"leftValue":"","caseSensitive":true,"typeValidation":"strict"},"combinator":"and","conditions":[{"id":"0a236099-525e-4748-a57d-8b8e2c63c48b","operator":{"type":"string","operation":"exists","singleValue":true},"leftValue":"={{ $json.message.text }}","rightValue":""}]},"renameOutput":true,"outputKey":"Text"},{"conditions":{"options":{"version":2,"leftValue":"","caseSensitive":true,"typeValidation":"strict"},"combinator":"and","conditions":[{"id":"24b7e8b8-6fce-4ddc-94b6-30b852891995","operator":{"type":"string","operation":"exists","singleValue":true},"leftValue":"={{  $json.message.voice.file_id }}","rightValue":""}]},"renameOutput":true,"outputKey":"Voice"}]},"options":{}},"id":"7fa34d0e-5215-44e5-b1d3-1e8abb5771ae","name":"Text Or Voice","type":"n8n-nodes-base.switch","position":[800,240],"typeVersion":3.2},{"parameters":{"resource":"audio","operation":"transcribe","options":{}},"id":"b90a586b-5d90-4f76-8e4e-bd44095f9aac","name":"Transcribe The Voice","type":"@n8n/n8n-nodes-langchain.openAi","position":[1312,432],"typeVersion":1.8},{"parameters":{"promptType":"define","text":"={{ $json.message.text }}","options":{"systemMessage":"=# Overview\\nYou are a personal assistant that helps user to fulfill their request.\\n\\nWhen you are asked to perform a task on the current date, please use the current time and date: {{$now}}\\n\\n## Output\\nYou should output the result and don't include any link. Just mentioned where you added it to."}},"id":"587adf8a-2e69-4078-a603-84f0322207cb","name":"AI Agent","type":"@n8n/n8n-nodes-langchain.agent","position":[1776,464],"typeVersion":1.9},{"parameters":{"operation":"sendChatAction","chatId":"={{ $json.message.chatId }}"},"id":"2455c7cb-f341-4891-9096-dc62116298c0","name":"Typing Action","type":"n8n-nodes-base.telegram","position":[800,64],"webhookId":"2479f7f8-6d54-4d45-921e-af6dcfae768e","typeVersion":1.2},{"parameters":{"chatId":"={{$('Telegram Trigger').first().json.message.chat.id}}","text":"={{ $json.output }}","additionalFields":{"appendAttribution":false}},"id":"8c2b6195-6101-4652-848c-505d13a3c1d2","name":"Send Response To Telegram","type":"n8n-nodes-base.telegram","position":[2368,512],"webhookId":"76e4b8a4-1086-4d24-b60e-b96942d83bdf","typeVersion":1.2},{"parameters":{"assignments":{"assignments":[{"id":"590b803f-833c-4854-bde7-5241d25b2b32","name":"message.text","type":"string","value":"={{ $json.message.text }}"},{"id":"303e0b5d-3d37-48fc-bbd3-ce62dbe7ef78","name":"sessionId","type":"string","value":"={{ $json.message.chat.id }}"}]},"options":{}},"id":"c8fd047d-8575-484e-939a-46064b81d035","name":"Send Context To AI Agent","type":"n8n-nodes-base.set","position":[1488,224],"typeVersion":3.4},{"parameters":{"assignments":{"assignments":[{"id":"2bb71aef-0ca1-4c26-95e1-c0c83bc82068","name":"message.text","type":"string","value":"={{ $json.text }}"},{"id":"692a2bae-35da-41d3-be8d-32c0f5dfbbef","name":"sessionId","type":"string","value":"={{ $('Telegram Trigger').item.json.message.chat.id }}"}]},"options":{}},"id":"f7e90526-08a5-4a59-956f-d39e6ff472f0","name":"Send Transcribe Context To AI Agent","type":"n8n-nodes-base.set","position":[1488,432],"typeVersion":3.4}]	{"AI Agent":{"main":[[{"node":"Send Response To Telegram","type":"main","index":0}]]},"Get Voice":{"main":[[{"node":"Transcribe The Voice","type":"main","index":0}]]},"Simple Memory":{"ai_memory":[[{"node":"AI Agent","type":"ai_memory","index":0}]]},"Text Or Voice":{"main":[[{"node":"Send Context To AI Agent","type":"main","index":0}],[{"node":"Get Voice","type":"main","index":0}]]},"Typing Action":{"main":[[]]},"Telegram Trigger":{"main":[[{"node":"Text Or Voice","type":"main","index":0},{"node":"Typing Action","type":"main","index":0}]]},"OpenAI Chat Model":{"ai_languageModel":[[{"node":"AI Agent","type":"ai_languageModel","index":0}]]},"Transcribe The Voice":{"main":[[{"node":"Send Transcribe Context To AI Agent","type":"main","index":0}]]},"Send Context To AI Agent":{"main":[[{"node":"AI Agent","type":"main","index":0}]]},"Send Transcribe Context To AI Agent":{"main":[[{"node":"AI Agent","type":"main","index":0}]]}}	2025-08-22 08:54:54.384+00	2025-08-22 08:54:54.384+00	{"executionOrder":"v1"}	\N	{}	ecccb5e0-aa33-4703-9eda-06a584cab4fe	0	iN2nBcNE8vEr6waZ	{"templateId":"4696"}	\N	f
🤖💬 Conversational AI Chatbot with Google Gemini for Text & Image | Telegram	f	[{"parameters":{"assignments":{"assignments":[{"id":"b3ce66e0-2b6f-4714-a672-59ef6e43ced9","name":"prompt","type":"string","value":"=1. Rules\\n- Communicate formally, clearly, and respectfully at all times.\\n- Be concise and precise, avoiding unnecessary details.\\n- Maintain a positive, professional tone as a reliable and knowledgeable assistant.\\n- Respect user boundaries and refrain from engaging in prohibited or inappropriate topics.\\n- Keep the conversation engaging by encouraging user participation, but when requesting clarification or additional information, ask only one focused question at a time to avoid overwhelming the user. Avoid abrupt endings to the dialogue.\\n- Follow user instructions precisely.\\n- Do not include any additional text or explanations unless explicitly requested.\\n\\n2. Response instructions\\n- Analyze the message and conversation history to maintain context and continuity.\\n- If repetition is requested, return the message exactly as provided. Otherwise, respond clearly and appropriately based on intent and context.\\n- If the message is vague, suggest an interpretation and ask for clarification while keeping the conversation active."}]},"options":{}},"id":"3017950d-52a5-4f51-a64d-7f223c01743e","name":"chatPrompt","type":"n8n-nodes-base.set","position":[1600,608],"typeVersion":3.4},{"parameters":{"assignments":{"assignments":[{"id":"d78c64d5-3c9e-4ffd-875e-973eb3c4d20a","name":"prompt","type":"string","value":"=1. Rules\\n- Communicate formally, clearly, and respectfully at all times.\\n- Be concise and precise, avoiding unnecessary details.\\n- Maintain a positive, professional tone as a reliable and knowledgeable assistant.\\n- Respect user boundaries and refrain from engaging in prohibited or inappropriate topics.\\n- Keep the conversation engaging by encouraging user participation, but when requesting clarification or additional information, ask only one focused question at a time to avoid overwhelming the user. Avoid abrupt endings to the dialogue.\\n- Follow user instructions precisely.\\n- Do not include any additional text or explanations unless explicitly requested.\\n\\n2. General Instructions\\n- If a message is unclear or lacks details, respond with your best understanding as a suggestion, and politely ask the user to confirm or provide more details if it is not what they meant.\\n- If the request is inappropriate or irrelevant, respond politely and clearly refuse, maintaining respect and professionalism, and invite a valid request.\\n- Always follow the established rules to maintain professionalism and accuracy."}]},"options":{}},"id":"975f1ebd-9af9-4bc5-8cec-6c3be3d3a63d","name":"otherPrompt","type":"n8n-nodes-base.set","position":[1600,768],"typeVersion":3.4},{"parameters":{"assignments":{"assignments":[{"id":"70c93816-7110-48e3-a105-568dd766bdf4","name":"prompt","type":"string","value":"=1.  General instructions\\nCreate a prompt that generates only one response with no comments and no line breaks. Based on the input provided, generate a detailed visual description of the request in a clear and coherent manner. Avoid using quotation marks, apostrophes, or any other punctuation marks.\\n\\n2. Visual specifications\\n- Composition: maintain a balanced and harmonious layout where all elements (images, shapes, and text) work together seamlessly. Ensure that no part of the image feels overcrowded, and the design directs the viewer’s attention naturally. Use a minimalistic approach, avoiding excessive detail or distractions.\\n\\n- Color palette: choose a color scheme that complements the overall theme and enhances the mood. The colors should be visually pleasing and work together harmoniously. Use no more than 3-4 main colors to maintain simplicity and coherence, ensuring they do not overpower the design.\\n\\n- Visual clarity: the image should be crisp and clear, with all elements legible and easy to interpret. Avoid blurry or pixelated visuals. Maintain sharp contrasts between key elements for emphasis and visual clarity.\\n\\n- Image size and resolution: the image should be sized to fit standard display requirements (e.g., social media, web posts). Ensure the resolution is high enough for clarity on both desktop and mobile devices. The image must maintain visual quality without losing sharpness or details when viewed on different screens.\\n\\n- Aesthetic appeal: aim for a design that is not only functional but also visually appealing. The overall style should be modern, clean, and aesthetically pleasing, with a consistent look that aligns with the intended mood or message. Ensure the design is pleasing to the eye, evoking the intended response from the viewer.\\n\\n- Visual balance: ensure the elements in the image are well-spaced, with no part feeling too heavy or too light. Create a natural flow of focus, guiding the viewer’s eye through the composition without distraction."}]},"options":{}},"id":"f297c8ce-7c7c-4011-9c36-e4993dbfd926","name":"generatePrompt","type":"n8n-nodes-base.set","position":[1600,448],"typeVersion":3.4},{"parameters":{"assignments":{"assignments":[{"id":"f435e1c3-6ff9-4e68-852c-0a39a5903ebe","name":"prompt","type":"string","value":"={{ $json.prompt }}"}]},"includeOtherFields":true,"options":{}},"id":"51eb4b31-0099-4886-9dfd-dc9c02107562","name":"buildPrompt","type":"n8n-nodes-base.set","position":[1728,608],"typeVersion":3.4},{"parameters":{"promptType":"define","text":"=Input from user: {{ $('sessionData').item.json.Mensaje }}\\n\\n{{ $json.prompt }}\\n\\nRespond only with the exact text requested, strictly following the instructions above. ","options":{"systemMessage":"=You are a professional enterprise chatbot designed to assist users with clear, respectful, and accurate communication. You provide informative text responses and generate images only upon explicit user requests. Utilize available conversation history to maintain context and coherence. Adhere strictly to company rules to ensure appropriate and secure interactions.\\n\\nYou can chat with me to get answers and create custom images based on your instructions.\\n\\nPrioritize the most recent relevant message if multiple prior references exist. Always respond in Spanish when providing text-based chat replies. For image generation requests, respond in English. Keep responses as short as possible without compromising clarity or natural interaction. Do not force interaction with the user beyond what is necessary to respond clearly."}},"id":"d9a263b3-9078-47e9-8146-abf62100af21","name":"ChatCore","type":"@n8n/n8n-nodes-langchain.agent","position":[1872,608],"typeVersion":1.9},{"parameters":{"modelName":"models/gemini-2.0-flash-001","options":{}},"id":"67e27375-a9f9-45d7-8ea9-aad5d18996de","name":"GeminiModel","type":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","position":[1888,768],"typeVersion":1},{"parameters":{"promptType":"define","text":"=1. Context\\nYou are an intelligent assistant designed to analyze user requests accurately by considering both the current user input and prior conversation history (if available). Use this context to improve classification and response relevance. Only classify as \\"generate\\" if the request clearly meets all criteria. Never assume without certainty.\\n\\n2. General instructions\\n- Use prior conversation history, if available, to interpret references, refinements, or corrections in the user’s message. \\n- Apply updates directly when a previous request is being modified (e.g., changing part of a generated image).\\n- Maintain context continuity unless the user explicitly shifts the topic.\\n- When evaluating intention, prioritize the most recent relevant message to avoid using outdated context.\\n- If the message is vague, nonsensical, or overly brief (e.g., a single word), do not classify it as \\"generate\\". Even if image requests occurred earlier. Only proceed if the current input clearly meets all criteria. Default to \\"other\\" unless the message includes clear direction.\\n\\n3. User Request: \\"{{ $('sessionData').item.json.Mensaje }}\\"\\n\\n4. Conversation history:\\n{{ $json.messages}}\\n\\n5. Intentions\\nClassify the user's intention as exactly one of the following:\\n- \\"generate\\": user requests the direct creation of new visual content (e.g., images or art) to be delivered. This includes implicit requests for physical or tangible objects (e.g., \\"give me a pet\\"), but only if the message contains clear intent or is supported by prior context. Single-word or vague inputs without clear directive or context should not be treated as generate.\\n- \\"chat\\": user engages in meaningful conversational interaction, including requests to generate textual prompts, explanations, or dialogue that have clear context and intent.\\n- \\"other\\": request does not fit into the above categories, or involves content that is restricted, inappropriate, irrelevant, nonsensical, or lacks clear context or intent distinct from conversational engagement.\\n\\n6. Data types\\nWhen analyzing the user’s message, determine whether a request for a physical or tangible object should be interpreted as a visual content request. In general, treat requests for tangible items as image generation unless the context explicitly suggests otherwise. For intangible content, respond with text unless the user clearly asks for an image.\\n\\nBased on the intention and content of the request, select exactly one data type to return:\\n\\n- \\"text\\": a textual response such as an answer, explanation, dialogue, or prompt text.\\n- \\"image\\": a visual response explicitly or implicitly requested as image generation.\\n\\nAssign \\"image\\" as typeData only if the intention is \\"generate\\". In all other cases, including when the intention is \\"other\\", assign \\"text\\".\\n\\n7. Output format\\nRespond only with a JSON object in this exact format, with no additional text or explanation: {\\"intention\\": \\"value\\", \\"typeData\\": \\"value\\"}","hasOutputParser":true},"id":"58ab707f-00e3-4b9f-b598-af89cebf99ce","name":"inputProcessor","type":"@n8n/n8n-nodes-langchain.chainLlm","position":[1136,608],"typeVersion":1.6,"alwaysOutputData":false},{"parameters":{"modelName":"models/gemini-2.0-flash","options":{}},"id":"0123eef4-eb57-485a-ad01-66dc47b332f8","name":"GeminiModel1","type":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","position":[1152,752],"typeVersion":1},{"parameters":{"jsonSchemaExample":"{\\n\\t\\"typeData\\": \\"text\\",\\n\\t\\"intention\\": \\"generate\\"\\n}"},"id":"8853d1da-5ab6-44cf-8054-7267392c9eae","name":"structuredOutput","type":"@n8n/n8n-nodes-langchain.outputParserStructured","position":[1296,752],"typeVersion":1.2},{"parameters":{"rules":{"values":[{"conditions":{"options":{"version":2,"leftValue":"","caseSensitive":true,"typeValidation":"strict"},"combinator":"and","conditions":[{"id":"24ab5811-2b6d-4f2f-8620-8697dadc2d4d","operator":{"type":"string","operation":"contains"},"leftValue":"={{ $('intentHandler').item.json.output.typeData }}","rightValue":"image"}]},"renameOutput":true,"outputKey":"=Imagen"},{"conditions":{"options":{"version":2,"leftValue":"","caseSensitive":true,"typeValidation":"strict"},"combinator":"and","conditions":[{"id":"********-2f6a-4bb3-a9bf-************","operator":{"type":"string","operation":"contains"},"leftValue":"={{ $('intentHandler').item.json.output.typeData }}","rightValue":"text"}]},"renameOutput":true,"outputKey":"Texto"}]},"options":{}},"id":"b679111d-c640-4f59-8046-c37fa7f977e9","name":"contentType","type":"n8n-nodes-base.switch","position":[2176,688],"typeVersion":3.2},{"parameters":{"jsCode":"let texto = $input.first().json.prompt.replace(/[\\\\n\\\\r\\\\t]/g, \\" \\")  // Reemplaza saltos de línea, retorno de carro y tabulaciones por espacio\\n  .replace(/['\\"\\\\\\\\]/g, \\"\\")      // Elimina comillas simples, dobles y barras invertidas\\n  .replace(/\\\\s+/g, \\" \\")        // Reemplaza múltiples espacios por un solo espacio\\n  .trim();                     // Elimina los espacios en blanco al inicio y al final\\n\\nreturn {\\n  text: texto\\n}"},"id":"8abd3463-8ecc-4086-9057-e7494b8b2d9d","name":"textCleaner","type":"n8n-nodes-base.code","position":[2416,512],"typeVersion":2},{"parameters":{"method":"POST","url":"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp-image-generation:generateContent","authentication":"predefinedCredentialType","nodeCredentialType":"googlePalmApi","sendBody":true,"specifyBody":"json","jsonBody":"={\\n  \\"contents\\": [\\n    {\\n      \\"role\\": \\"user\\",\\n      \\"parts\\": [\\n        {\\n          \\"text\\": \\"{{ $json.text }}\\"\\n        }\\n      ]\\n    }\\n  ],\\n  \\"generationConfig\\": {\\n    \\"responseModalities\\": [\\"Text\\", \\"Image\\"]\\n  }\\n}","options":{}},"id":"a11a2afc-71c4-4b3f-81ec-fb8cacc1d9f2","name":"imageGeneration","type":"n8n-nodes-base.httpRequest","position":[2528,512],"typeVersion":4.2},{"parameters":{"operation":"toBinary","sourceProperty":"candidates[0].content.parts[1].inlineData.data","options":{"fileName":"generated_image.png"}},"id":"ccce85bc-79f7-43ed-b649-8b1533d774a5","name":"imageBuilder","type":"n8n-nodes-base.convertToFile","position":[2656,512],"typeVersion":1.1},{"parameters":{"operation":"sendPhoto","chatId":"={{ $('sessionData').item.json.sessionId }}","binaryData":true,"additionalFields":{"fileName":"=generated_image.png"}},"id":"4dbbf4c4-4d78-42d3-bdbc-a35a584643ff","name":"sendImage","type":"n8n-nodes-base.telegram","position":[2768,512],"webhookId":"511272cc-d9e3-4384-9407-e5d4296db621","typeVersion":1},{"parameters":{"chatId":"={{ $('sessionData').item.json.sessionId }}","text":"={{ $('ChatCore').item.json.output }}","additionalFields":{"appendAttribution":false,"parse_mode":"HTML"}},"id":"8e525ff1-f403-4f7a-a281-36fb18824168","name":"sendTextMessage","type":"n8n-nodes-base.telegram","position":[2768,704],"webhookId":"1d1eee00-2306-4ac1-870f-0988e305f0d0","typeVersion":1},{"parameters":{"keepOnlySet":true,"values":{"string":[{"name":"Mensaje","value":"={{ $('userInput').item.json.message.text }}"},{"name":"sessionId","value":"={{ $('userInput').item.json.message.chat.id }}"},{"name":"Lenguaje","value":"={{ $('userInput').item.json.message.from.language_code }}"},{"name":"Username","value":"={{ $('userInput').item.json.message.chat.username }}"}]},"options":{}},"id":"4e0fcbad-8b6b-4c39-80ac-6aed0f8cf0fd","name":"sessionData","type":"n8n-nodes-base.set","position":[608,608],"typeVersion":2},{"parameters":{"options":{"groupMessages":true}},"id":"ea84ebc4-2932-4fd9-8771-b13a929b6ea5","name":"conversationStore","type":"@n8n/n8n-nodes-langchain.memoryManager","position":[736,608],"typeVersion":1.1},{"parameters":{"sessionIdType":"customKey","sessionKey":"={{ $('sessionData').item.json.sessionId }}","contextWindowLength":2},"id":"03420024-1474-4b55-a98a-e80e0826ccc7","name":"memoryRetriever","type":"@n8n/n8n-nodes-langchain.memoryBufferWindow","position":[768,752],"typeVersion":1.3},{"parameters":{"jsCode":"const allItems = $input.all();\\nconst lastItem = allItems[allItems.length - 1];\\n\\nif (lastItem && Array.isArray(lastItem.json.messages)) {\\n  const messages = lastItem.json.messages;\\n  const count = messages.length;\\n\\n  if (count === 0) return [{ json: { message: \\"\\" } }];\\n\\n  const extractFirstLine = (text) => {\\n    if (!text) return \\"\\";\\n    return text.split('\\\\n')[0].replace(/^Input from user:\\\\s*/, '');\\n  };\\n\\n  const trimEndNewline = (text) => {\\n    if (!text) return \\"\\";\\n    return text.replace(/\\\\n+$/, '');\\n  };\\n\\n  // Tomar los últimos dos o menos mensajes\\n  const selectedMessages = (count === 1) ? [messages[0]] : messages.slice(-1);\\n\\n  // Construir el texto concatenado\\n  const combinedMessage = selectedMessages.map((msg, idx) => {\\n    return `Message ${idx + 1}:\\\\nhuman: ${extractFirstLine(msg.human)}\\\\nai: ${trimEndNewline(msg.ai)}`;\\n  }).join('\\\\n\\\\n');\\n\\n  return [{ json: { messages: combinedMessage } }];\\n}\\n\\nreturn [{ json: { messages: \\"\\" } }];"},"id":"28f6a639-1c50-4754-a66c-772cc3a05083","name":"latestContext","type":"n8n-nodes-base.code","position":[1008,608],"typeVersion":2},{"parameters":{"rules":{"values":[{"conditions":{"options":{"version":2,"leftValue":"","caseSensitive":true,"typeValidation":"strict"},"combinator":"and","conditions":[{"id":"24ab5811-2b6d-4f2f-8620-8697dadc2d4d","operator":{"type":"string","operation":"contains"},"leftValue":"={{ $json.output.intention }}","rightValue":"generate"}]},"renameOutput":true,"outputKey":"generate"},{"conditions":{"options":{"version":2,"leftValue":"","caseSensitive":true,"typeValidation":"strict"},"combinator":"and","conditions":[{"id":"********-2f6a-4bb3-a9bf-************","operator":{"type":"string","operation":"contains"},"leftValue":"={{ $json.output.intention }}","rightValue":"chat"}]},"renameOutput":true,"outputKey":"chat"},{"conditions":{"options":{"version":2,"leftValue":"","caseSensitive":true,"typeValidation":"strict"},"combinator":"and","conditions":[{"id":"de4b6e87-950e-461c-869f-d27c73f8d763","operator":{"type":"string","operation":"contains"},"leftValue":"={{ $json.output.intention }}","rightValue":"other"}]},"renameOutput":true,"outputKey":"other"}]},"options":{}},"id":"14bae45a-aafa-407f-9761-62954acabbbd","name":"intentHandler","type":"n8n-nodes-base.switch","position":[1408,608],"typeVersion":3.2},{"parameters":{"updates":["message"],"additionalFields":{}},"id":"2f3d4931-9549-449b-8ff8-e27bf7b69e1f","name":"userInput","type":"n8n-nodes-base.telegramTrigger","position":[496,608],"webhookId":"ddbf3f08-3f4d-446d-9378-054225c91a06","typeVersion":1},{"parameters":{"sessionIdType":"customKey","sessionKey":"={{ $('sessionData').item.json.sessionId }}","contextWindowLength":10},"id":"357a258b-32a7-47c4-bcf1-f32cec025cba","name":"conversationMemory","type":"@n8n/n8n-nodes-langchain.memoryBufferWindow","position":[2016,784],"typeVersion":1.3},{"parameters":{"assignments":{"assignments":[{"id":"d11831d7-533b-4132-9849-612e093d6b32","name":"prompt","type":"string","value":"={{ $('ChatCore').item.json.output }} It is mandatory to add a description of the image you are going to make."}]},"options":{}},"id":"f1dad098-8b9f-4160-91ab-1eb7185d51a2","name":"errorPreprocessor","type":"n8n-nodes-base.set","position":[2288,512],"typeVersion":3.4},{"parameters":{"content":"### 2. Memory and Conversational Context\\n\\nRetrieves the necessary context to properly infer intentions.\\n\\n- conversationStore: stores the entire conversation history.\\n\\n- memoryRetriever: extracts relevant information according to the current need.\\n\\n- latestContext: formats and prepares the context to be useful in response generation.\\n","height":680,"width":380,"color":4},"id":"804fcbd3-cbc1-41d9-bbeb-40469545012f","name":"Sticky Note","type":"n8n-nodes-base.stickyNote","position":[736,240],"typeVersion":1},{"parameters":{"content":"### 1. Input and Session Management\\n\\nReceives messages from Telegram and manages the session to maintain context.\\n\\n- userInput: captures the user's message.\\n\\n- sessionData: saves and updates the session state.\\n","height":680,"width":340,"color":5},"id":"4b4e8510-6b97-4b1d-afdb-916581aa539d","name":"Sticky Note1","type":"n8n-nodes-base.stickyNote","position":[368,240],"typeVersion":1},{"parameters":{"content":"### 3. Intent Processing and Prompt Generation\\nAnalyzes the intention and selects appropriate prompts according to the user's intention.\\n\\n- inputProcessor: detects intention and type of content to send.\\n\\n- intentHandler: redirects the flow based on the intention.\\n\\n- generatePrompt, chatPrompt, otherPrompt, buildPrompt: select the prompts for the response.","height":680,"width":700,"color":6},"id":"6b14b58f-2e17-4cfb-baf4-3318b3fd9533","name":"Sticky Note2","type":"n8n-nodes-base.stickyNote","position":[1136,240],"typeVersion":1},{"parameters":{"content":"### 4. Core of Generation and Conversation Management\\nGenerates responses using Google Gemini, integrating context for coherence.\\n\\n- ChatCore: orchestrates the generation and management of the conversation.\\n\\n- GeminiModel: creates the final response based on prompts.\\n\\n- conversationMemory: stores information to maintain coherence.","height":680,"width":300,"color":3},"id":"fa4cfc3f-cdaf-4184-b183-ca31b1aa1d86","name":"Sticky Note3","type":"n8n-nodes-base.stickyNote","position":[1856,240],"typeVersion":1},{"parameters":{"content":"### 5. Content Classification and User Delivery\\n\\nDetermines the type of content and manages its delivery via Telegram.\\n\\n- contentType: defines the output format (text, image, etc.).\\n\\n- errorPreprocessor, textCleaner: clean and validate the content.\\n\\n- imageGeneration, imageBuilder: create visual content when necessary.\\n\\n- sendImage, sendTextMessage: send the content to the user.","height":680,"width":800,"color":7},"id":"ce598ece-492b-4294-9aa4-ea0c6757edf2","name":"Sticky Note4","type":"n8n-nodes-base.stickyNote","position":[2176,240],"typeVersion":1}]	{"ChatCore":{"main":[[{"node":"contentType","type":"main","index":0}]]},"userInput":{"main":[[{"node":"sessionData","type":"main","index":0}]]},"chatPrompt":{"main":[[{"node":"buildPrompt","type":"main","index":0}]]},"GeminiModel":{"ai_languageModel":[[{"node":"ChatCore","type":"ai_languageModel","index":0}]]},"buildPrompt":{"main":[[{"node":"ChatCore","type":"main","index":0}]]},"contentType":{"main":[[{"node":"errorPreprocessor","type":"main","index":0}],[{"node":"sendTextMessage","type":"main","index":0}]]},"otherPrompt":{"main":[[{"node":"buildPrompt","type":"main","index":0}]]},"sessionData":{"main":[[{"node":"conversationStore","type":"main","index":0}]]},"textCleaner":{"main":[[{"node":"imageGeneration","type":"main","index":0}]]},"GeminiModel1":{"ai_languageModel":[[{"node":"inputProcessor","type":"ai_languageModel","index":0}]]},"imageBuilder":{"main":[[{"node":"sendImage","type":"main","index":0}]]},"intentHandler":{"main":[[{"node":"generatePrompt","type":"main","index":0}],[{"node":"chatPrompt","type":"main","index":0}],[{"node":"otherPrompt","type":"main","index":0}]]},"latestContext":{"main":[[{"node":"inputProcessor","type":"main","index":0}]]},"generatePrompt":{"main":[[{"node":"buildPrompt","type":"main","index":0}]]},"inputProcessor":{"main":[[{"node":"intentHandler","type":"main","index":0}]]},"imageGeneration":{"main":[[{"node":"imageBuilder","type":"main","index":0}]]},"memoryRetriever":{"ai_memory":[[{"node":"conversationStore","type":"ai_memory","index":0}]]},"sendTextMessage":{"main":[[]]},"structuredOutput":{"ai_outputParser":[[{"node":"inputProcessor","type":"ai_outputParser","index":0}]]},"conversationStore":{"main":[[{"node":"latestContext","type":"main","index":0}]]},"errorPreprocessor":{"main":[[{"node":"textCleaner","type":"main","index":0}]]},"conversationMemory":{"ai_memory":[[{"node":"ChatCore","type":"ai_memory","index":0}]]}}	2025-08-22 09:11:18.749+00	2025-08-22 09:11:18.749+00	{"executionOrder":"v1"}	\N	{}	70f120cf-deb1-4c30-89df-fa699dfb5cbd	0	pH2xNmi7K8vUn2CU	{"templateId":"4365"}	\N	f
Conversational Travel Booker: Automate Flight & Hotel Reservations with GPT-3.5	f	[{"id":"8b53e7e2-cedb-42a9-8918-3d7e8d0a23d1","name":"Webhook Trigger","type":"n8n-nodes-base.webhook","position":[393,380],"webhookId":"booking-webhook","parameters":{"path":"booking-request","options":{},"httpMethod":"POST","responseMode":"responseNode"},"typeVersion":1},{"id":"16a396bd-3200-4be1-80d4-364d8a7a65e6","name":"AI Request Parser","type":"n8n-nodes-base.openAi","position":[613,380],"parameters":{"model":"=gpt-3.5-turbo","prompt":"Hello","options":{"temperature":0.3},"requestOptions":{}},"credentials":{"openAiApi":{"id":"ZOoOxdQLoU9NXU8C","name":"OpenAi account"}},"typeVersion":1},{"id":"d3d569ce-8e9b-4d88-ba84-e39f5cc41bf9","name":"Booking Type Router","type":"n8n-nodes-base.switch","position":[833,359],"parameters":{},"typeVersion":1},{"id":"79041d5f-75e0-43e1-92be-486dcfb30847","name":"Flight Data Processor","type":"n8n-nodes-base.code","position":[1053,280],"parameters":{"jsCode":"// Parse AI response and prepare flight search\\nconst aiResponse = $input.first().json.choices[0].message.content;\\nlet bookingData;\\n\\ntry {\\n  // Try to parse as JSON first\\n  bookingData = JSON.parse(aiResponse);\\n} catch {\\n  // If not JSON, extract info from text\\n  bookingData = {\\n    type: 'flight',\\n    origin: extractValue(aiResponse, ['from', 'origin', 'departure']),\\n    destination: extractValue(aiResponse, ['to', 'destination', 'arrival']),\\n    departDate: extractValue(aiResponse, ['depart', 'departure date', 'leaving']),\\n    passengers: extractValue(aiResponse, ['passenger', 'traveler', 'people']) || 1\\n  };\\n}\\n\\nfunction extractValue(text, keywords) {\\n  for (const keyword of keywords) {\\n    const regex = new RegExp(`${keyword}[:\\\\s]+([^\\\\n,]+)`, 'i');\\n    const match = text.match(regex);\\n    if (match) return match[1].trim();\\n  }\\n  return null;\\n}\\n\\n// Prepare flight search parameters\\nconst flightSearch = {\\n  origin: bookingData.origin || 'NYC',\\n  destination: bookingData.destination || 'LAX',\\n  departureDate: bookingData.departDate || new Date().toISOString().split('T')[0],\\n  passengers: parseInt(bookingData.passengers) || 1,\\n  class: bookingData.class || 'economy'\\n};\\n\\nreturn { json: { bookingData, flightSearch, originalRequest: $input.first().json } };"},"typeVersion":2},{"id":"ac45ba1d-40a7-4f4e-98f6-ce3a17c94cbe","name":"Hotel Data Processor","type":"n8n-nodes-base.code","position":[1053,480],"parameters":{"jsCode":"// Parse AI response and prepare hotel search\\nconst aiResponse = $input.first().json.choices[0].message.content;\\nlet bookingData;\\n\\ntry {\\n  bookingData = JSON.parse(aiResponse);\\n} catch {\\n  bookingData = {\\n    type: 'hotel',\\n    destination: extractValue(aiResponse, ['destination', 'city', 'location']),\\n    checkIn: extractValue(aiResponse, ['check in', 'arrival', 'checkin']),\\n    checkOut: extractValue(aiResponse, ['check out', 'departure', 'checkout']),\\n    guests: extractValue(aiResponse, ['guest', 'people', 'traveler']) || 2,\\n    rooms: extractValue(aiResponse, ['room']) || 1\\n  };\\n}\\n\\nfunction extractValue(text, keywords) {\\n  for (const keyword of keywords) {\\n    const regex = new RegExp(`${keyword}[:\\\\s]+([^\\\\n,]+)`, 'i');\\n    const match = text.match(regex);\\n    if (match) return match[1].trim();\\n  }\\n  return null;\\n}\\n\\n// Prepare hotel search parameters\\nconst hotelSearch = {\\n  destination: bookingData.destination || 'New York',\\n  checkIn: bookingData.checkIn || new Date().toISOString().split('T')[0],\\n  checkOut: bookingData.checkOut || new Date(Date.now() + 86400000).toISOString().split('T')[0],\\n  guests: parseInt(bookingData.guests) || 2,\\n  rooms: parseInt(bookingData.rooms) || 1\\n};\\n\\nreturn { json: { bookingData, hotelSearch, originalRequest: $input.first().json } };"},"typeVersion":2},{"id":"e9c34a77-e19b-4cf6-86ed-736b815c353f","name":"Flight Search API","type":"n8n-nodes-base.httpRequest","position":[1273,280],"parameters":{"url":"https://api.aviationstack.com/v1/flights","options":{},"sendQuery":true,"authentication":"predefinedCredentialType","queryParameters":{"parameters":[{"name":"dep_iata","value":"={{ $json.flightSearch.origin }}"},{"name":"arr_iata","value":"={{ $json.flightSearch.destination }}"},{"name":"limit","value":"5"}]},"nodeCredentialType":"aviationStackApi"},"typeVersion":4.1},{"id":"d49ce324-9a2b-4bb2-81db-a55f3977aa0d","name":"Hotel Search API","type":"n8n-nodes-base.httpRequest","position":[1273,480],"parameters":{"url":"https://api.booking.com/v1/hotels/search","options":{},"sendQuery":true,"authentication":"predefinedCredentialType","queryParameters":{"parameters":[{"name":"dest_id","value":"={{ $json.hotelSearch.destination }}"},{"name":"checkin_date","value":"={{ $json.hotelSearch.checkIn }}"},{"name":"checkout_date","value":"={{ $json.hotelSearch.checkOut }}"},{"name":"adults_number","value":"={{ $json.hotelSearch.guests }}"},{"name":"room_number","value":"={{ $json.hotelSearch.rooms }}"}]},"nodeCredentialType":"bookingComApi"},"typeVersion":4.1},{"id":"0840d661-f296-4a66-94cc-4ee03dd30300","name":"Flight Booking Processor","type":"n8n-nodes-base.code","position":[1493,280],"parameters":{"jsCode":"// Process flight search results and prepare booking\\nconst searchResults = $input.first().json;\\nconst bookingData = $input.first().json;\\n\\n// Mock flight results if API fails\\nconst flights = searchResults.data || [\\n  {\\n    flight_number: 'AA123',\\n    airline: 'American Airlines',\\n    departure: { airport: bookingData.flightSearch?.origin || 'NYC', time: '09:00' },\\n    arrival: { airport: bookingData.flightSearch?.destination || 'LAX', time: '12:00' },\\n    price: 299,\\n    duration: '5h 30m'\\n  },\\n  {\\n    flight_number: 'DL456',\\n    airline: 'Delta Airlines', \\n    departure: { airport: bookingData.flightSearch?.origin || 'NYC', time: '14:00' },\\n    arrival: { airport: bookingData.flightSearch?.destination || 'LAX', time: '17:30' },\\n    price: 325,\\n    duration: '5h 30m'\\n  }\\n];\\n\\n// Select best flight (lowest price)\\nconst selectedFlight = flights.sort((a, b) => (a.price || 299) - (b.price || 325))[0];\\n\\n// Prepare booking confirmation\\nconst booking = {\\n  type: 'flight',\\n  status: 'confirmed',\\n  confirmation: `FL${Date.now()}`,\\n  details: selectedFlight,\\n  passenger: {\\n    name: 'John Doe', // In real workflow, get from user data\\n    email: '<EMAIL>'\\n  },\\n  total: selectedFlight.price || 299\\n};\\n\\nreturn { json: { booking, searchResults: flights } };"},"typeVersion":2},{"id":"eb5d528a-6bd8-4546-8744-9ce2f0416ea4","name":"Hotel Booking Processor","type":"n8n-nodes-base.code","position":[1493,480],"parameters":{"jsCode":"// Process hotel search results and prepare booking\\nconst searchResults = $input.first().json;\\nconst bookingData = $input.first().json;\\n\\n// Mock hotel results if API fails\\nconst hotels = searchResults.hotels || [\\n  {\\n    name: 'Grand Plaza Hotel',\\n    location: bookingData.hotelSearch?.destination || 'New York',\\n    rating: 4.5,\\n    price_per_night: 150,\\n    amenities: ['WiFi', 'Pool', 'Gym'],\\n    room_type: 'Deluxe Room'\\n  },\\n  {\\n    name: 'City Center Inn',\\n    location: bookingData.hotelSearch?.destination || 'New York',\\n    rating: 4.0,\\n    price_per_night: 120,\\n    amenities: ['WiFi', 'Breakfast'],\\n    room_type: 'Standard Room'\\n  }\\n];\\n\\n// Select best hotel (highest rating)\\nconst selectedHotel = hotels.sort((a, b) => (b.rating || 4) - (a.rating || 3.5))[0];\\n\\n// Calculate total nights\\nconst checkIn = new Date(bookingData.hotelSearch?.checkIn || new Date());\\nconst checkOut = new Date(bookingData.hotelSearch?.checkOut || new Date(Date.now() + 86400000));\\nconst nights = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24)) || 1;\\n\\n// Prepare booking confirmation\\nconst booking = {\\n  type: 'hotel',\\n  status: 'confirmed',\\n  confirmation: `HT${Date.now()}`,\\n  details: selectedHotel,\\n  guest: {\\n    name: 'John Doe',\\n    email: '<EMAIL>'\\n  },\\n  nights: nights,\\n  total: (selectedHotel.price_per_night || 120) * nights\\n};\\n\\nreturn { json: { booking, searchResults: hotels } };"},"typeVersion":2},{"id":"ee741a63-77c7-4348-b62d-32150bdc4e4b","name":"Confirmation Message Generator","type":"n8n-nodes-base.openAi","position":[1713,380],"parameters":{"model":"=gpt-3.5-turbo","prompt":"{{json.data}}","options":{"temperature":0.7},"requestOptions":{}},"credentials":{"openAiApi":{"id":"ZOoOxdQLoU9NXU8C","name":"OpenAi account"}},"typeVersion":1},{"id":"45c5475b-31d2-4de8-b4b0-19acc57ef344","name":"Send Confirmation Email","type":"n8n-nodes-base.emailSend","position":[1933,380],"webhookId":"f80f3193-3a16-47c4-b8b3-23ac37cb8dfd","parameters":{"text":"{{json.data}}","options":{"allowUnauthorizedCerts":true},"subject":"🎉 Your Travel Booking is Confirmed!","toEmail":"<EMAIL>","fromEmail":"<EMAIL>"},"typeVersion":2},{"id":"1b362532-fb06-497b-9858-609d9ad6fa9b","name":"Send Response","type":"n8n-nodes-base.respondToWebhook","position":[2153,380],"parameters":{"options":{},"respondWith":"json","responseBody":{"status":"success","booking":"{{ $('flight-booking-processor').item.json.booking || $('hotel-booking-processor').item.json.booking }}","message":"{{ $('confirmation-generator').item.json.choices[0].message.content }}","confirmation_code":"{{ $('flight-booking-processor').item.json.booking?.confirmation || $('hotel-booking-processor').item.json.booking?.confirmation }}"}},"typeVersion":1},{"id":"6b6f2ebf-fda1-4a32-b8a3-5cb9525b4534","name":"Sticky Note","type":"n8n-nodes-base.stickyNote","position":[368,240],"parameters":{"color":5,"width":150,"height":300,"content":"Accepts booking requests via HTTP POST"},"typeVersion":1},{"id":"3a6ac642-06a1-4c10-93ec-a8ef60ed7f1f","name":"Sticky Note1","type":"n8n-nodes-base.stickyNote","position":[1248,140],"parameters":{"color":4,"width":150,"height":520,"content":" Searches for flights/hotels (with mock fallbacks)"},"typeVersion":1},{"id":"4c698f05-39a6-4369-88a4-8e92f43b1b18","name":"Sticky Note2","type":"n8n-nodes-base.stickyNote","position":[1028,140],"parameters":{"color":6,"width":150,"height":520,"content":" Extracts and structures booking parameters"},"typeVersion":1},{"id":"8721b991-ce05-4244-84bf-237312498fbc","name":"Sticky Note3","type":"n8n-nodes-base.stickyNote","position":[813,240],"parameters":{"width":150,"height":300,"content":"Automatically determines if it's a flight or hotel booking"},"typeVersion":1},{"id":"b1d232d9-31f0-4f31-9a0b-9ea14ee31ab5","name":"Sticky Note4","type":"n8n-nodes-base.stickyNote","position":[588,240],"parameters":{"color":3,"width":150,"height":300,"content":" Uses OpenAI to understand natural language booking requests"},"typeVersion":1},{"id":"c001363a-c58c-406f-9cef-d7ad668fb071","name":"Sticky Note5","type":"n8n-nodes-base.stickyNote","position":[1908,240],"parameters":{"color":5,"width":150,"height":300,"content":"Sends booking confirmations via email"},"typeVersion":1},{"id":"5d23d45e-23ab-45c8-8658-3a93ae8ec0c1","name":"Sticky Note6","type":"n8n-nodes-base.stickyNote","position":[1688,240],"parameters":{"color":2,"width":150,"height":300,"content":"Creates personalized confirmation messages"},"typeVersion":1},{"id":"44b36b14-4353-45a1-894a-42596a2641fc","name":"Sticky Note10","type":"n8n-nodes-base.stickyNote","position":[1468,140],"parameters":{"color":3,"width":150,"height":520,"content":"Processes and confirms bookings"},"typeVersion":1},{"id":"7588a845-8d92-4e8f-aa07-b454126f61f1","name":"Sticky Note11","type":"n8n-nodes-base.stickyNote","position":[2128,240],"parameters":{"color":4,"width":150,"height":300,"content":"Returns structured booking data"},"typeVersion":1}]	{"Webhook Trigger":{"main":[[{"node":"AI Request Parser","type":"main","index":0}]]},"Hotel Search API":{"main":[[{"node":"Hotel Booking Processor","type":"main","index":0}]]},"AI Request Parser":{"main":[[{"node":"Booking Type Router","type":"main","index":0}]]},"Flight Search API":{"main":[[{"node":"Flight Booking Processor","type":"main","index":0}]]},"Booking Type Router":{"main":[[{"node":"Flight Data Processor","type":"main","index":0}],[{"node":"Hotel Data Processor","type":"main","index":0}]]},"Hotel Data Processor":{"main":[[{"node":"Hotel Search API","type":"main","index":0}]]},"Flight Data Processor":{"main":[[{"node":"Flight Search API","type":"main","index":0}]]},"Hotel Booking Processor":{"main":[[{"node":"Confirmation Message Generator","type":"main","index":0}]]},"Send Confirmation Email":{"main":[[{"node":"Send Response","type":"main","index":0}]]},"Flight Booking Processor":{"main":[[{"node":"Confirmation Message Generator","type":"main","index":0}]]},"Confirmation Message Generator":{"main":[[{"node":"Send Confirmation Email","type":"main","index":0}]]}}	2025-08-22 09:13:28.831+00	2025-08-22 09:13:28.831+00	\N	\N	\N	e1775b5f-8028-4bb5-a787-4bd17525dca4	0	kLkyDkNvB687bGo6	{"templateId":"6262"}	\N	f
\.


--
-- Data for Name: workflow_history; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.workflow_history ("versionId", "workflowId", authors, "createdAt", "updatedAt", nodes, connections) FROM stdin;
\.


--
-- Data for Name: workflow_statistics; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.workflow_statistics (count, "latestEvent", name, "workflowId", "rootCount") FROM stdin;
1	2025-08-22 09:24:01.551+00	manual_error	pH2xNmi7K8vUn2CU	0
\.


--
-- Data for Name: workflows_tags; Type: TABLE DATA; Schema: public; Owner: user
--

COPY public.workflows_tags ("workflowId", "tagId") FROM stdin;
\.


--
-- Name: auth_provider_sync_history_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.auth_provider_sync_history_id_seq', 1, false);


--
-- Name: execution_annotations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.execution_annotations_id_seq', 1, false);


--
-- Name: execution_entity_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.execution_entity_id_seq', 1, true);


--
-- Name: execution_metadata_temp_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.execution_metadata_temp_id_seq', 1, false);


--
-- Name: insights_by_period_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.insights_by_period_id_seq', 1, false);


--
-- Name: insights_metadata_metaId_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public."insights_metadata_metaId_seq"', 1, false);


--
-- Name: insights_raw_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.insights_raw_id_seq', 1, false);


--
-- Name: migrations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: user
--

SELECT pg_catalog.setval('public.migrations_id_seq', 91, true);


--
-- Name: test_run PK_011c050f566e9db509a0fadb9b9; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.test_run
    ADD CONSTRAINT "PK_011c050f566e9db509a0fadb9b9" PRIMARY KEY (id);


--
-- Name: installed_packages PK_08cc9197c39b028c1e9beca225940576fd1a5804; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.installed_packages
    ADD CONSTRAINT "PK_08cc9197c39b028c1e9beca225940576fd1a5804" PRIMARY KEY ("packageName");


--
-- Name: execution_metadata PK_17a0b6284f8d626aae88e1c16e4; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.execution_metadata
    ADD CONSTRAINT "PK_17a0b6284f8d626aae88e1c16e4" PRIMARY KEY (id);


--
-- Name: project_relation PK_1caaa312a5d7184a003be0f0cb6; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.project_relation
    ADD CONSTRAINT "PK_1caaa312a5d7184a003be0f0cb6" PRIMARY KEY ("projectId", "userId");


--
-- Name: folder_tag PK_27e4e00852f6b06a925a4d83a3e; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.folder_tag
    ADD CONSTRAINT "PK_27e4e00852f6b06a925a4d83a3e" PRIMARY KEY ("folderId", "tagId");


--
-- Name: project PK_4d68b1358bb5b766d3e78f32f57; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.project
    ADD CONSTRAINT "PK_4d68b1358bb5b766d3e78f32f57" PRIMARY KEY (id);


--
-- Name: invalid_auth_token PK_5779069b7235b256d91f7af1a15; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.invalid_auth_token
    ADD CONSTRAINT "PK_5779069b7235b256d91f7af1a15" PRIMARY KEY (token);


--
-- Name: shared_workflow PK_5ba87620386b847201c9531c58f; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.shared_workflow
    ADD CONSTRAINT "PK_5ba87620386b847201c9531c58f" PRIMARY KEY ("workflowId", "projectId");


--
-- Name: folder PK_6278a41a706740c94c02e288df8; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.folder
    ADD CONSTRAINT "PK_6278a41a706740c94c02e288df8" PRIMARY KEY (id);


--
-- Name: annotation_tag_entity PK_69dfa041592c30bbc0d4b84aa00; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.annotation_tag_entity
    ADD CONSTRAINT "PK_69dfa041592c30bbc0d4b84aa00" PRIMARY KEY (id);


--
-- Name: execution_annotations PK_7afcf93ffa20c4252869a7c6a23; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.execution_annotations
    ADD CONSTRAINT "PK_7afcf93ffa20c4252869a7c6a23" PRIMARY KEY (id);


--
-- Name: migrations PK_8c82d7f526340ab734260ea46be; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.migrations
    ADD CONSTRAINT "PK_8c82d7f526340ab734260ea46be" PRIMARY KEY (id);


--
-- Name: installed_nodes PK_8ebd28194e4f792f96b5933423fc439df97d9689; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.installed_nodes
    ADD CONSTRAINT "PK_8ebd28194e4f792f96b5933423fc439df97d9689" PRIMARY KEY (name);


--
-- Name: shared_credentials PK_8ef3a59796a228913f251779cff; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.shared_credentials
    ADD CONSTRAINT "PK_8ef3a59796a228913f251779cff" PRIMARY KEY ("credentialsId", "projectId");


--
-- Name: test_case_execution PK_90c121f77a78a6580e94b794bce; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.test_case_execution
    ADD CONSTRAINT "PK_90c121f77a78a6580e94b794bce" PRIMARY KEY (id);


--
-- Name: user_api_keys PK_978fa5caa3468f463dac9d92e69; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.user_api_keys
    ADD CONSTRAINT "PK_978fa5caa3468f463dac9d92e69" PRIMARY KEY (id);


--
-- Name: execution_annotation_tags PK_979ec03d31294cca484be65d11f; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.execution_annotation_tags
    ADD CONSTRAINT "PK_979ec03d31294cca484be65d11f" PRIMARY KEY ("annotationId", "tagId");


--
-- Name: webhook_entity PK_b21ace2e13596ccd87dc9bf4ea6; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.webhook_entity
    ADD CONSTRAINT "PK_b21ace2e13596ccd87dc9bf4ea6" PRIMARY KEY ("webhookPath", method);


--
-- Name: insights_by_period PK_b606942249b90cc39b0265f0575; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.insights_by_period
    ADD CONSTRAINT "PK_b606942249b90cc39b0265f0575" PRIMARY KEY (id);


--
-- Name: workflow_history PK_b6572dd6173e4cd06fe79937b58; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.workflow_history
    ADD CONSTRAINT "PK_b6572dd6173e4cd06fe79937b58" PRIMARY KEY ("versionId");


--
-- Name: processed_data PK_ca04b9d8dc72de268fe07a65773; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.processed_data
    ADD CONSTRAINT "PK_ca04b9d8dc72de268fe07a65773" PRIMARY KEY ("workflowId", context);


--
-- Name: settings PK_dc0fe14e6d9943f268e7b119f69ab8bd; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.settings
    ADD CONSTRAINT "PK_dc0fe14e6d9943f268e7b119f69ab8bd" PRIMARY KEY (key);


--
-- Name: user PK_ea8f538c94b6e352418254ed6474a81f; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public."user"
    ADD CONSTRAINT "PK_ea8f538c94b6e352418254ed6474a81f" PRIMARY KEY (id);


--
-- Name: insights_raw PK_ec15125755151e3a7e00e00014f; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.insights_raw
    ADD CONSTRAINT "PK_ec15125755151e3a7e00e00014f" PRIMARY KEY (id);


--
-- Name: insights_metadata PK_f448a94c35218b6208ce20cf5a1; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.insights_metadata
    ADD CONSTRAINT "PK_f448a94c35218b6208ce20cf5a1" PRIMARY KEY ("metaId");


--
-- Name: user UQ_e12875dfb3b1d92d7d7c5377e2; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public."user"
    ADD CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e2" UNIQUE (email);


--
-- Name: auth_identity auth_identity_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_identity
    ADD CONSTRAINT auth_identity_pkey PRIMARY KEY ("providerId", "providerType");


--
-- Name: auth_provider_sync_history auth_provider_sync_history_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_provider_sync_history
    ADD CONSTRAINT auth_provider_sync_history_pkey PRIMARY KEY (id);


--
-- Name: credentials_entity credentials_entity_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.credentials_entity
    ADD CONSTRAINT credentials_entity_pkey PRIMARY KEY (id);


--
-- Name: event_destinations event_destinations_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.event_destinations
    ADD CONSTRAINT event_destinations_pkey PRIMARY KEY (id);


--
-- Name: execution_data execution_data_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.execution_data
    ADD CONSTRAINT execution_data_pkey PRIMARY KEY ("executionId");


--
-- Name: execution_entity pk_e3e63bbf986767844bbe1166d4e; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.execution_entity
    ADD CONSTRAINT pk_e3e63bbf986767844bbe1166d4e PRIMARY KEY (id);


--
-- Name: workflow_statistics pk_workflow_statistics; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.workflow_statistics
    ADD CONSTRAINT pk_workflow_statistics PRIMARY KEY ("workflowId", name);


--
-- Name: workflows_tags pk_workflows_tags; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.workflows_tags
    ADD CONSTRAINT pk_workflows_tags PRIMARY KEY ("workflowId", "tagId");


--
-- Name: tag_entity tag_entity_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.tag_entity
    ADD CONSTRAINT tag_entity_pkey PRIMARY KEY (id);


--
-- Name: variables variables_key_key; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.variables
    ADD CONSTRAINT variables_key_key UNIQUE (key);


--
-- Name: variables variables_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.variables
    ADD CONSTRAINT variables_pkey PRIMARY KEY (id);


--
-- Name: workflow_entity workflow_entity_pkey; Type: CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.workflow_entity
    ADD CONSTRAINT workflow_entity_pkey PRIMARY KEY (id);


--
-- Name: IDX_14f68deffaf858465715995508; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX "IDX_14f68deffaf858465715995508" ON public.folder USING btree ("projectId", id);


--
-- Name: IDX_1d8ab99d5861c9388d2dc1cf73; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX "IDX_1d8ab99d5861c9388d2dc1cf73" ON public.insights_metadata USING btree ("workflowId");


--
-- Name: IDX_1e31657f5fe46816c34be7c1b4; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX "IDX_1e31657f5fe46816c34be7c1b4" ON public.workflow_history USING btree ("workflowId");


--
-- Name: IDX_1ef35bac35d20bdae979d917a3; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX "IDX_1ef35bac35d20bdae979d917a3" ON public.user_api_keys USING btree ("apiKey");


--
-- Name: IDX_5f0643f6717905a05164090dde; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX "IDX_5f0643f6717905a05164090dde" ON public.project_relation USING btree ("userId");


--
-- Name: IDX_60b6a84299eeb3f671dfec7693; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX "IDX_60b6a84299eeb3f671dfec7693" ON public.insights_by_period USING btree ("periodStart", type, "periodUnit", "metaId");


--
-- Name: IDX_61448d56d61802b5dfde5cdb00; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX "IDX_61448d56d61802b5dfde5cdb00" ON public.project_relation USING btree ("projectId");


--
-- Name: IDX_63d7bbae72c767cf162d459fcc; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX "IDX_63d7bbae72c767cf162d459fcc" ON public.user_api_keys USING btree ("userId", label);


--
-- Name: IDX_8e4b4774db42f1e6dda3452b2a; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX "IDX_8e4b4774db42f1e6dda3452b2a" ON public.test_case_execution USING btree ("testRunId");


--
-- Name: IDX_97f863fa83c4786f1956508496; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX "IDX_97f863fa83c4786f1956508496" ON public.execution_annotations USING btree ("executionId");


--
-- Name: IDX_a3697779b366e131b2bbdae297; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX "IDX_a3697779b366e131b2bbdae297" ON public.execution_annotation_tags USING btree ("tagId");


--
-- Name: IDX_ae51b54c4bb430cf92f48b623f; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX "IDX_ae51b54c4bb430cf92f48b623f" ON public.annotation_tag_entity USING btree (name);


--
-- Name: IDX_c1519757391996eb06064f0e7c; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX "IDX_c1519757391996eb06064f0e7c" ON public.execution_annotation_tags USING btree ("annotationId");


--
-- Name: IDX_cec8eea3bf49551482ccb4933e; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX "IDX_cec8eea3bf49551482ccb4933e" ON public.execution_metadata USING btree ("executionId", key);


--
-- Name: IDX_d6870d3b6e4c185d33926f423c; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX "IDX_d6870d3b6e4c185d33926f423c" ON public.test_run USING btree ("workflowId");


--
-- Name: IDX_execution_entity_deletedAt; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX "IDX_execution_entity_deletedAt" ON public.execution_entity USING btree ("deletedAt");


--
-- Name: IDX_workflow_entity_name; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX "IDX_workflow_entity_name" ON public.workflow_entity USING btree (name);


--
-- Name: idx_07fde106c0b471d8cc80a64fc8; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX idx_07fde106c0b471d8cc80a64fc8 ON public.credentials_entity USING btree (type);


--
-- Name: idx_16f4436789e804e3e1c9eeb240; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX idx_16f4436789e804e3e1c9eeb240 ON public.webhook_entity USING btree ("webhookId", method, "pathLength");


--
-- Name: idx_812eb05f7451ca757fb98444ce; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX idx_812eb05f7451ca757fb98444ce ON public.tag_entity USING btree (name);


--
-- Name: idx_execution_entity_stopped_at_status_deleted_at; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX idx_execution_entity_stopped_at_status_deleted_at ON public.execution_entity USING btree ("stoppedAt", status, "deletedAt") WHERE (("stoppedAt" IS NOT NULL) AND ("deletedAt" IS NULL));


--
-- Name: idx_execution_entity_wait_till_status_deleted_at; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX idx_execution_entity_wait_till_status_deleted_at ON public.execution_entity USING btree ("waitTill", status, "deletedAt") WHERE (("waitTill" IS NOT NULL) AND ("deletedAt" IS NULL));


--
-- Name: idx_execution_entity_workflow_id_started_at; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX idx_execution_entity_workflow_id_started_at ON public.execution_entity USING btree ("workflowId", "startedAt") WHERE (("startedAt" IS NOT NULL) AND ("deletedAt" IS NULL));


--
-- Name: idx_workflows_tags_workflow_id; Type: INDEX; Schema: public; Owner: user
--

CREATE INDEX idx_workflows_tags_workflow_id ON public.workflows_tags USING btree ("workflowId");


--
-- Name: pk_credentials_entity_id; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX pk_credentials_entity_id ON public.credentials_entity USING btree (id);


--
-- Name: pk_tag_entity_id; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX pk_tag_entity_id ON public.tag_entity USING btree (id);


--
-- Name: pk_variables_id; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX pk_variables_id ON public.variables USING btree (id);


--
-- Name: pk_workflow_entity_id; Type: INDEX; Schema: public; Owner: user
--

CREATE UNIQUE INDEX pk_workflow_entity_id ON public.workflow_entity USING btree (id);


--
-- Name: processed_data FK_06a69a7032c97a763c2c7599464; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.processed_data
    ADD CONSTRAINT "FK_06a69a7032c97a763c2c7599464" FOREIGN KEY ("workflowId") REFERENCES public.workflow_entity(id) ON DELETE CASCADE;


--
-- Name: insights_metadata FK_1d8ab99d5861c9388d2dc1cf733; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.insights_metadata
    ADD CONSTRAINT "FK_1d8ab99d5861c9388d2dc1cf733" FOREIGN KEY ("workflowId") REFERENCES public.workflow_entity(id) ON DELETE SET NULL;


--
-- Name: workflow_history FK_1e31657f5fe46816c34be7c1b4b; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.workflow_history
    ADD CONSTRAINT "FK_1e31657f5fe46816c34be7c1b4b" FOREIGN KEY ("workflowId") REFERENCES public.workflow_entity(id) ON DELETE CASCADE;


--
-- Name: insights_metadata FK_2375a1eda085adb16b24615b69c; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.insights_metadata
    ADD CONSTRAINT "FK_2375a1eda085adb16b24615b69c" FOREIGN KEY ("projectId") REFERENCES public.project(id) ON DELETE SET NULL;


--
-- Name: execution_metadata FK_31d0b4c93fb85ced26f6005cda3; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.execution_metadata
    ADD CONSTRAINT "FK_31d0b4c93fb85ced26f6005cda3" FOREIGN KEY ("executionId") REFERENCES public.execution_entity(id) ON DELETE CASCADE;


--
-- Name: shared_credentials FK_416f66fc846c7c442970c094ccf; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.shared_credentials
    ADD CONSTRAINT "FK_416f66fc846c7c442970c094ccf" FOREIGN KEY ("credentialsId") REFERENCES public.credentials_entity(id) ON DELETE CASCADE;


--
-- Name: project_relation FK_5f0643f6717905a05164090dde7; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.project_relation
    ADD CONSTRAINT "FK_5f0643f6717905a05164090dde7" FOREIGN KEY ("userId") REFERENCES public."user"(id) ON DELETE CASCADE;


--
-- Name: project_relation FK_61448d56d61802b5dfde5cdb002; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.project_relation
    ADD CONSTRAINT "FK_61448d56d61802b5dfde5cdb002" FOREIGN KEY ("projectId") REFERENCES public.project(id) ON DELETE CASCADE;


--
-- Name: insights_by_period FK_6414cfed98daabbfdd61a1cfbc0; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.insights_by_period
    ADD CONSTRAINT "FK_6414cfed98daabbfdd61a1cfbc0" FOREIGN KEY ("metaId") REFERENCES public.insights_metadata("metaId") ON DELETE CASCADE;


--
-- Name: insights_raw FK_6e2e33741adef2a7c5d66befa4e; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.insights_raw
    ADD CONSTRAINT "FK_6e2e33741adef2a7c5d66befa4e" FOREIGN KEY ("metaId") REFERENCES public.insights_metadata("metaId") ON DELETE CASCADE;


--
-- Name: installed_nodes FK_73f857fc5dce682cef8a99c11dbddbc969618951; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.installed_nodes
    ADD CONSTRAINT "FK_73f857fc5dce682cef8a99c11dbddbc969618951" FOREIGN KEY (package) REFERENCES public.installed_packages("packageName") ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: folder FK_804ea52f6729e3940498bd54d78; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.folder
    ADD CONSTRAINT "FK_804ea52f6729e3940498bd54d78" FOREIGN KEY ("parentFolderId") REFERENCES public.folder(id) ON DELETE CASCADE;


--
-- Name: shared_credentials FK_812c2852270da1247756e77f5a4; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.shared_credentials
    ADD CONSTRAINT "FK_812c2852270da1247756e77f5a4" FOREIGN KEY ("projectId") REFERENCES public.project(id) ON DELETE CASCADE;


--
-- Name: test_case_execution FK_8e4b4774db42f1e6dda3452b2af; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.test_case_execution
    ADD CONSTRAINT "FK_8e4b4774db42f1e6dda3452b2af" FOREIGN KEY ("testRunId") REFERENCES public.test_run(id) ON DELETE CASCADE;


--
-- Name: folder_tag FK_94a60854e06f2897b2e0d39edba; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.folder_tag
    ADD CONSTRAINT "FK_94a60854e06f2897b2e0d39edba" FOREIGN KEY ("folderId") REFERENCES public.folder(id) ON DELETE CASCADE;


--
-- Name: execution_annotations FK_97f863fa83c4786f19565084960; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.execution_annotations
    ADD CONSTRAINT "FK_97f863fa83c4786f19565084960" FOREIGN KEY ("executionId") REFERENCES public.execution_entity(id) ON DELETE CASCADE;


--
-- Name: execution_annotation_tags FK_a3697779b366e131b2bbdae2976; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.execution_annotation_tags
    ADD CONSTRAINT "FK_a3697779b366e131b2bbdae2976" FOREIGN KEY ("tagId") REFERENCES public.annotation_tag_entity(id) ON DELETE CASCADE;


--
-- Name: shared_workflow FK_a45ea5f27bcfdc21af9b4188560; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.shared_workflow
    ADD CONSTRAINT "FK_a45ea5f27bcfdc21af9b4188560" FOREIGN KEY ("projectId") REFERENCES public.project(id) ON DELETE CASCADE;


--
-- Name: folder FK_a8260b0b36939c6247f385b8221; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.folder
    ADD CONSTRAINT "FK_a8260b0b36939c6247f385b8221" FOREIGN KEY ("projectId") REFERENCES public.project(id) ON DELETE CASCADE;


--
-- Name: execution_annotation_tags FK_c1519757391996eb06064f0e7c8; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.execution_annotation_tags
    ADD CONSTRAINT "FK_c1519757391996eb06064f0e7c8" FOREIGN KEY ("annotationId") REFERENCES public.execution_annotations(id) ON DELETE CASCADE;


--
-- Name: test_run FK_d6870d3b6e4c185d33926f423c8; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.test_run
    ADD CONSTRAINT "FK_d6870d3b6e4c185d33926f423c8" FOREIGN KEY ("workflowId") REFERENCES public.workflow_entity(id) ON DELETE CASCADE;


--
-- Name: shared_workflow FK_daa206a04983d47d0a9c34649ce; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.shared_workflow
    ADD CONSTRAINT "FK_daa206a04983d47d0a9c34649ce" FOREIGN KEY ("workflowId") REFERENCES public.workflow_entity(id) ON DELETE CASCADE;


--
-- Name: folder_tag FK_dc88164176283de80af47621746; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.folder_tag
    ADD CONSTRAINT "FK_dc88164176283de80af47621746" FOREIGN KEY ("tagId") REFERENCES public.tag_entity(id) ON DELETE CASCADE;


--
-- Name: user_api_keys FK_e131705cbbc8fb589889b02d457; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.user_api_keys
    ADD CONSTRAINT "FK_e131705cbbc8fb589889b02d457" FOREIGN KEY ("userId") REFERENCES public."user"(id) ON DELETE CASCADE;


--
-- Name: test_case_execution FK_e48965fac35d0f5b9e7f51d8c44; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.test_case_execution
    ADD CONSTRAINT "FK_e48965fac35d0f5b9e7f51d8c44" FOREIGN KEY ("executionId") REFERENCES public.execution_entity(id) ON DELETE SET NULL;


--
-- Name: auth_identity auth_identity_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.auth_identity
    ADD CONSTRAINT "auth_identity_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."user"(id);


--
-- Name: execution_data execution_data_fk; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.execution_data
    ADD CONSTRAINT execution_data_fk FOREIGN KEY ("executionId") REFERENCES public.execution_entity(id) ON DELETE CASCADE;


--
-- Name: execution_entity fk_execution_entity_workflow_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.execution_entity
    ADD CONSTRAINT fk_execution_entity_workflow_id FOREIGN KEY ("workflowId") REFERENCES public.workflow_entity(id) ON DELETE CASCADE;


--
-- Name: webhook_entity fk_webhook_entity_workflow_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.webhook_entity
    ADD CONSTRAINT fk_webhook_entity_workflow_id FOREIGN KEY ("workflowId") REFERENCES public.workflow_entity(id) ON DELETE CASCADE;


--
-- Name: workflow_entity fk_workflow_parent_folder; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.workflow_entity
    ADD CONSTRAINT fk_workflow_parent_folder FOREIGN KEY ("parentFolderId") REFERENCES public.folder(id) ON DELETE CASCADE;


--
-- Name: workflow_statistics fk_workflow_statistics_workflow_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.workflow_statistics
    ADD CONSTRAINT fk_workflow_statistics_workflow_id FOREIGN KEY ("workflowId") REFERENCES public.workflow_entity(id) ON DELETE CASCADE;


--
-- Name: workflows_tags fk_workflows_tags_tag_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.workflows_tags
    ADD CONSTRAINT fk_workflows_tags_tag_id FOREIGN KEY ("tagId") REFERENCES public.tag_entity(id) ON DELETE CASCADE;


--
-- Name: workflows_tags fk_workflows_tags_workflow_id; Type: FK CONSTRAINT; Schema: public; Owner: user
--

ALTER TABLE ONLY public.workflows_tags
    ADD CONSTRAINT fk_workflows_tags_workflow_id FOREIGN KEY ("workflowId") REFERENCES public.workflow_entity(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

