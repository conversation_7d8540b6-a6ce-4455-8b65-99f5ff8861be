# Architecture

This document outlines the architecture of the e-commerce integration platform, with a focus on modularity and extensibility.

## Backend

The backend is built with FastAPI and SQLAlchemy, and is designed to support multiple e-commerce platforms.

### Database

The database schema is designed to be modular, with a core set of abstract models and platform-specific implementations.

-   **`backend/database/core.py`**: Defines abstract SQLAlchemy models for core e-commerce concepts like `BaseStore`, `BaseProduct`, `BaseOrder`, and `BaseCustomer`.
-   **`backend/database/shopify_models.py`**: Defines the concrete Shopify models, which inherit from the abstract core models.

### Services

The services layer is responsible for all business logic, including interacting with e-commerce platform APIs and syncing data to the local database.

-   **`backend/services/shopify_service.py`**: Handles all interactions with the Shopify API.
-   **`backend/services/shopify_sync_service.py`**: Handles the detailed synchronization of Shopify data to the local database.

### Routers

The routers layer is responsible for exposing the API endpoints.

-   **`backend/routers/stores.py`**: Handles all store-related API endpoints.
-   **`backend/routers/products.py`**: Handles all product-related API endpoints.
-   **`backend/routers/orders.py`**: Handles all order-related API endpoints.
-   **`backend/routers/consumers.py`**: Handles all consumer-related API endpoints.
-   **`backend/routers/webhooks.py`**: Handles all incoming webhooks.

## Frontend

The frontend is built with React and TypeScript, and is designed to be a single-page application (SPA).

### Services

The services layer is responsible for all communication with the backend API.

-   **`frontend/src/services/storeService.ts`**: Handles all store-related API calls.
-   **`frontend/src/services/productService.ts`**: Handles all product-related API calls.
-   **`frontend/src/services/orderService.ts`**: Handles all order-related API calls.
-   **`frontend/src/services/customerService.ts`**: Handles all customer-related API calls.
-   **`frontend/src/services/dashboardService.ts`**: Handles all dashboard-related API calls.

### Pages

The pages layer is responsible for rendering the different views of the application.

-   **`frontend/src/pages/Stores.tsx`**: Displays a list of all connected stores.
-   **`frontend/src/pages/Store.tsx`**: Displays a detailed view of a single store, with a tabbed interface for different views.
-   **`frontend/src/pages/NewStore.tsx`**: Displays a form to add a new store.

### Components

The components layer is responsible for rendering the reusable UI components.

-   **`frontend/src/components/ProductsTable.tsx`**: Displays a table of products.
-   **`frontend/src/components/OrdersTable.tsx`**: Displays a table of orders.
-   **`frontend/src/components/CustomersTable.tsx`**: Displays a table of customers.
-   **`frontend/src/components/SyncSettings.tsx`**: Displays a form to configure the sync settings for a store.
-   **`frontend/src/components/WebhookMonitor.tsx`**: Displays a real-time log of all incoming webhooks.

## Adding a New E-commerce Platform

To add support for a new e-commerce platform, you will need to:

1.  **Create a new set of database models** in `backend/database` that inherit from the abstract core models.
2.  **Create a new set of Pydantic schemas** in `backend/schemas` for the new platform.
3.  **Create a new service** in `backend/services` that handles all interactions with the new platform's API.
4.  **Create a new sync service** in `backend/services` that handles the detailed synchronization of the new platform's data to the local database.
5.  **Create a new set of routers** in `backend/routers` to expose the new platform's API endpoints.
6.  **Create a new set of services** in `frontend/src/services` to handle all communication with the new platform's API endpoints.
7.  **Create a new set of pages and components** in `frontend/src/pages` and `frontend/src/components` to display the new platform's data.
