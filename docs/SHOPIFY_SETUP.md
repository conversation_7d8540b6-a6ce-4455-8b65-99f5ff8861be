# Shopify Store Setup Guide

This guide outlines the steps to connect a Shopify store to the application.

## 1. Create a Custom App in Shopify

1.  **Log in to your Shopify store admin.**
2.  Go to **Apps**.
3.  Click **Develop apps for your store**.
4.  Click **Create an app**.
5.  Give your app a name (e.g., "LeanChain Sync") and select an App developer.
6.  Click **Create app**.

## 2. Configure API Scopes

Once the app is created, you need to grant it the necessary permissions.

1.  Go to the **Configuration** tab of your new app.
2.  In the **Admin API integration** section, click **Configure**.
3.  Select the following scopes. These are the minimum required for the current features:
    -   `read_products`, `write_products`
    -   `read_orders`, `write_orders`
    -   `read_customers`, `write_customers`
    -   `read_checkouts`, `write_checkouts`
    -   `read_marketing_events`, `write_marketing_events`
    -   `read_discounts`, `write_discounts`
    -   `read_product_listings`
    -   `read_inventory`, `write_inventory`
    -   `read_locations`
    -   `read_fulfillments`, `write_fulfillments`
    -   `read_shipping`, `write_shipping`
    -   `read_analytics`
    -   `read_reports`, `write_reports`
    -   `read_price_rules`, `write_price_rules`
    -   `read_script_tags`, `write_script_tags`
    -   `read_themes`, `write_themes`
4.  Click **Save**.

## 3. Get API Credentials

1.  Go to the **API credentials** tab.
2.  Click **Install app**.
3.  After installation, you will see your **Admin API access token**. **Reveal the token once and copy it immediately.** You will not be able to see it again.
4.  You will also see the **API key** and **API secret key**.

## 4. Add Credentials to LeanChain

1.  In the LeanChain application, navigate to **Stores -> Add New Store**.
2.  Fill in the following details:
    -   **Store Name**: A friendly name for your store.
    -   **Platform**: Select "Shopify".
    -   **Shop Domain**: Your `myshopify.com` domain (e.g., `your-store-name.myshopify.com`).
    -   **Admin API Access Token**: The token you copied in the previous step.
    -   **API Key**: The API key from your Shopify app.
    -   **API Secret Key**: The API secret key from your Shopify app.
3.  Click **Save and Test Connection**.

## 5. Configure Webhooks

For real-time synchronization, you need to set up webhooks in your Shopify app.

1.  In your Shopify app configuration, go to the **Webhooks** tab.
2.  The application will attempt to create these automatically, but you can also create them manually.
3.  The webhook URL will be `https://your-app-domain.com/api/shopify/webhook/{store_id}`.
4.  Create webhooks for the following topics with the latest stable API version:
    -   `products/create`
    -   `products/update`
    -   `products/delete`
    -   `orders/create`
    -   `orders/updated`
    -   `orders/delete`
    -   `customers/create`
    -   `customers/update`
    -   `customers/delete`
    -   `checkouts/create`
    -   `checkouts/update`
    -   `checkouts/delete`
    -   `marketing_activities/create`
    -   `marketing_activities/update`
    -   `marketing_activities/delete`
    -   `code_discount/create`
    -   `code_discount/update`
    -   `code_discount/delete`
    -   `carts/create`
    -   `carts/update`
    -   `fulfillments/create`
    -   `fulfillments/update`
    -   `themes/publish`
    -   `app/uninstalled`

This setup will enable both bulk synchronization and real-time updates from your Shopify store.
