# Home Service Management Platform - Frontend

A professional, AI-powered platform for home service companies with multi-modal agents, booking management, and comprehensive analytics. Built with Next.js 15, TypeScript, and modern UI components.

## 🌟 Features

### 🤖 AI Agent Management

- **Professional Agent Builder**: Drag-and-drop interface for single/multi-node agents
- **Multi-Modal Support**: Voice (Twilio→STT→LLM→TTS), WebSocket, and chat paths
- **Agent Templates**: Pre-built templates for various home service scenarios
- **Real-Time Testing**: Integrated testing interface with live debugging
- **Agent Transfer**: Seamless handoff between different agent types

### 🏢 Company Management

- **Multi-Company Support**: Manage multiple home service companies
- **Knowledge Base Integration**: Company-specific knowledge management
- **Phone Number Mapping**: Route calls to appropriate agents
- **Configuration Management**: Self-serve configuration for all aspects

### 📞 Communication Features

- **Call Handling**: Professional call management interface
- **Conversation Logging**: Complete conversation history and analytics
- **Chat Plugin**: Embeddable chat widget for websites
- **Web Forms**: Typeform-like forms with prefill capabilities

### 📅 Booking & Scheduling

- **Google Calendar Integration**: Seamless appointment scheduling
- **Service Management**: Comprehensive service booking system
- **Customer Management**: Complete customer relationship management
- **Appointment Tracking**: Real-time booking status and notifications

### 📊 Analytics & Reporting

- **Real-Time Dashboard**: Comprehensive KPI tracking and visualization
- **Performance Monitoring**: Agent performance and quality metrics
- **Revenue Analytics**: Financial tracking and reporting
- **Customer Satisfaction**: Rating and feedback management

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm
- Backend API running (see backend README)

### Installation

1. **Clone and install dependencies:**

```bash
cd frontend
npm install
```

2. **Set up environment variables:**

```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:

```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

3. **Start the development server:**

```bash
npm run dev
```

4. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🏗️ Architecture

### Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: Shadcn/ui with professional enhancements
- **Animations**: Framer Motion for smooth interactions
- **State Management**: React Context + React Query
- **Forms**: React Hook Form with Zod validation
- **Charts**: Recharts for data visualization
- **Icons**: Lucide React

### Project Structure

```
frontend/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── (app)/             # Protected app routes
│   │   │   ├── dashboard/     # Main dashboard
│   │   │   ├── agents/        # AI agent management
│   │   │   ├── companies/     # Company management
│   │   │   ├── conversations/ # Chat & call logs
│   │   │   ├── bookings/      # Appointment management
│   │   │   └── analytics/     # Reports & analytics
│   │   ├── (auth)/           # Authentication pages
│   │   └── (marketing)/      # Public pages
│   ├── components/
│   │   ├── ui/               # Base UI components
│   │   ├── layout/           # Layout components
│   │   ├── features/         # Feature-specific components
│   │   └── common/           # Shared components
│   ├── contexts/             # React contexts
│   ├── hooks/                # Custom hooks
│   ├── lib/                  # Utilities and helpers
│   ├── services/             # API services
│   └── types/                # TypeScript definitions
├── public/                   # Static assets
└── docs/                     # Documentation
```

## 🎨 Design System

### Theme & Colors

The platform uses a professional design system with:

- **Primary**: Professional blue (#3B82F6) for main actions
- **Success**: Green (#22C55E) for positive states
- **Warning**: Amber (#F59E0B) for caution states
- **Destructive**: Red for error states
- **Accent**: Purple (#8B5CF6) for highlights

### Components

All UI components are built with:

- **Consistent Spacing**: 4px grid system
- **Smooth Animations**: 200-300ms transitions
- **Accessibility**: WCAG 2.1 AA compliant
- **Responsive Design**: Mobile-first approach
- **Dark Mode**: Full dark mode support

### Typography

- **Font**: Inter for optimal readability
- **Scale**: Modular scale for consistent hierarchy
- **Weights**: 300-800 for proper emphasis

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server

# Code Quality
npm run lint         # Run ESLint
npm run format       # Format with Prettier
npm run typecheck    # TypeScript type checking

# Testing (when implemented)
npm run test         # Run tests
npm run test:watch   # Run tests in watch mode
```

### Environment Variables

Create a `.env.local` file with:

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
NEXT_PUBLIC_APP_URL=http://localhost:3000

# External Services (Optional)
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_key
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn
```

### Code Style

- **ESLint**: Enforced code quality rules
- **Prettier**: Consistent code formatting
- **TypeScript**: Strict type checking
- **Conventional Commits**: Standardized commit messages

## 📱 Features Deep Dive

### Agent Builder

The agent builder provides a professional interface for creating and managing AI agents:

- **Visual Editor**: Drag-and-drop node-based workflow editor
- **Template Library**: Pre-built templates for common scenarios
- **Multi-Modal Configuration**: Support for voice, chat, and phone
- **Real-Time Testing**: Test agents directly in the builder
- **Version Control**: Track changes and rollback capabilities

### Dashboard Analytics

Comprehensive analytics dashboard featuring:

- **Real-Time Metrics**: Live KPI tracking
- **Interactive Charts**: Recharts-powered visualizations
- **Performance Monitoring**: Agent and system performance
- **Custom Reports**: Configurable reporting system
- **Export Capabilities**: PDF and CSV export options

### Company Management

Multi-tenant company management system:

- **Company Profiles**: Complete company information management
- **User Roles**: Role-based access control
- **Knowledge Base**: Company-specific knowledge management
- **Phone Number Routing**: Intelligent call routing
- **Branding**: Custom branding per company

## 🔌 API Integration

### Service Layer

The frontend uses a comprehensive service layer:

```typescript
// Agent Management
import { agentService } from '@/services/agents';

// Create agent
const agent = await agentService.createAgent(agentData);

// Update agent
await agentService.updateAgent(agentId, updates);

// Test agent
const testResult = await agentService.testAgent(agentId, testConfig);
```

### WebSocket Integration

Real-time communication using WebSockets:

```typescript
// Call handling
import { callService } from '@/services/calls';

// Start voice call
const call = await callService.startVoiceCall(agentId, config);

// Handle messages
call.onMessage((message) => {
  // Handle real-time messages
});
```

## 🧪 Testing

### Component Testing

The platform includes comprehensive testing setup:

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Agent Testing

Built-in agent testing capabilities:

- **Live Testing**: Test agents in real-time
- **Scenario Testing**: Pre-defined test scenarios
- **Performance Testing**: Load and performance testing
- **A/B Testing**: Compare different agent configurations

## 🚀 Deployment

### Production Build

```bash
# Build for production
npm run build

# Start production server
npm run start
```

### Docker Deployment

```bash
# Build Docker image
docker build -t homeservice-frontend .

# Run container
docker run -p 3000:3000 homeservice-frontend
```

### Environment Setup

For production deployment:

1. **Set production environment variables**
2. **Configure CDN for static assets**
3. **Set up monitoring and logging**
4. **Configure SSL certificates**
5. **Set up CI/CD pipeline**

## 📚 Documentation

### Component Documentation

All components are documented with:

- **Props interface**: TypeScript definitions
- **Usage examples**: Code examples
- **Accessibility notes**: A11y considerations
- **Design guidelines**: Visual specifications

### API Documentation

Service layer documentation includes:

- **Method signatures**: Complete API reference
- **Error handling**: Error codes and messages
- **Rate limiting**: API usage limits
- **Authentication**: Auth requirements

## 🤝 Contributing

### Development Workflow

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes**: Follow code style guidelines
4. **Add tests**: Ensure good test coverage
5. **Commit changes**: Use conventional commits
6. **Push to branch**: `git push origin feature/amazing-feature`
7. **Open a Pull Request**: Describe your changes

### Code Standards

- **TypeScript**: Strict mode enabled
- **ESLint**: No warnings allowed
- **Prettier**: Auto-formatting on save
- **Tests**: Minimum 80% coverage
- **Documentation**: Update docs for new features

## 🔒 Security

### Security Measures

- **Authentication**: JWT-based authentication
- **Authorization**: Role-based access control
- **Input Validation**: Zod schema validation
- **XSS Protection**: Content Security Policy
- **CSRF Protection**: CSRF tokens
- **Rate Limiting**: API rate limiting

### Best Practices

- **Environment Variables**: Secure secret management
- **HTTPS Only**: Force HTTPS in production
- **Security Headers**: Comprehensive security headers
- **Regular Updates**: Keep dependencies updated
- **Security Audits**: Regular security reviews

## 📈 Performance

### Optimization Features

- **Code Splitting**: Automatic route-based splitting
- **Image Optimization**: Next.js Image component
- **Lazy Loading**: Component lazy loading
- **Caching**: Intelligent caching strategies
- **Bundle Analysis**: Bundle size monitoring

### Performance Metrics

- **Lighthouse Score**: 95+ target
- **Core Web Vitals**: Optimized for all metrics
- **Load Time**: <2s initial load
- **Time to Interactive**: <3s TTI

## 🆘 Troubleshooting

### Common Issues

**Build Errors:**

```bash
# Clear Next.js cache
rm -rf .next

# Clear node_modules
rm -rf node_modules package-lock.json
npm install
```

**TypeScript Errors:**

```bash
# Check TypeScript configuration
npm run typecheck

# Restart TypeScript server in VS Code
Cmd/Ctrl + Shift + P > "TypeScript: Restart TS Server"
```

**Styling Issues:**

```bash
# Rebuild Tailwind CSS
npm run build:css
```

### Getting Help

- **GitHub Issues**: Report bugs and feature requests
- **Documentation**: Check the docs folder
- **Community**: Join our Discord community
- **Support**: Contact <EMAIL>

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Next.js Team**: For the amazing framework
- **Vercel**: For hosting and deployment
- **Tailwind CSS**: For the utility-first CSS framework
- **Radix UI**: For accessible component primitives
- **Lucide**: For beautiful icons

---

**Built with ❤️ for home service companies worldwide**
