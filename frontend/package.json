{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/vite": "^4.1.10", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "chart.js": "^4.5.0", "lucide-react": "^0.518.0", "postcss": "^8.5.6", "react": "18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "18.3.1", "react-router-dom": "^7.6.2", "tailwindcss": "^4.1.10", "@tanstack/react-table": "8.21.3", "@tremor/react": "3.18.7"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "18.3.1", "@types/react-dom": "18.3.1", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}