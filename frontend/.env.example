# Platform Configuration
# Single business category (only one supported at a time)
# Options: home_services, professional_services, health_wellness
NEXT_PUBLIC_BUSINESS_CATEGORY=home_services

# Examples:
# NEXT_PUBLIC_BUSINESS_CATEGORY=home_services
# NEXT_PUBLIC_BUSINESS_CATEGORY=professional_services
# NEXT_PUBLIC_BUSINESS_CATEGORY=health_wellness

# API Configuration
# NEXT_PUBLIC_API_URL=https://skilled-lemur-yearly.ngrok-free.app
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_URL=http://localhost:3000

PORT=3000
CHOKIDAR_USEPOLLING=true

# Authentication
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3000

# External Services
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=your-google-analytics-id

# Environment
NEXT_PUBLIC_ENVIRONMENT=dev