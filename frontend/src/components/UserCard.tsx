import React from "react";
import { User as UserIcon, Mail } from "lucide-react";

interface User {
  id: number;
  full_name: string;
  email: string;
  is_active: boolean;
}

interface UserCardProps {
  user: User;
  onClick?: (user: User) => void;
  viewMode?: "grid" | "list";
}

const UserCard: React.FC<UserCardProps> = ({
  user,
  onClick,
  viewMode = "grid",
}) => {
  const handleCardClick = () => {
    if (onClick) {
      onClick(user);
    }
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800";
  };

  if (viewMode === "list") {
    return (
      <div
        className="bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer flex items-center p-4"
        onClick={handleCardClick}
      >
        <div className="relative w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
          <UserIcon className="text-gray-400" size={32} />
        </div>
        <div className="ml-4 flex-grow">
          <h3 className="font-semibold text-lg text-gray-900 line-clamp-1">
            {user.full_name}
          </h3>
          <div className="flex items-center text-sm text-gray-500 mt-1">
            <Mail size={12} className="mr-1" />
            {user.email}
          </div>
        </div>
        <div className="w-32 text-center">
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
              user.is_active
            )}`}
          >
            {user.is_active ? "Active" : "Inactive"}
          </span>
        </div>
        <div className="w-24 text-center">
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleCardClick();
            }}
            className="text-blue-600 hover:text-blue-800"
          >
            Details
          </button>
        </div>
      </div>
    );
  }

  // Grid view
  return (
    <div
      className="bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:-translate-y-1 flex flex-col items-center text-center p-6"
      onClick={handleCardClick}
      style={{ minHeight: 280 }}
    >
      <div className="relative w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <UserIcon className="text-gray-400" size={48} />
      </div>
      <h3 className="font-semibold text-xl text-gray-900 line-clamp-1">
        {user.full_name}
      </h3>
      <p className="text-sm text-gray-600 mb-4">{user.email}</p>
      <div
        className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
          user.is_active
        )}`}
      >
        {user.is_active ? "Active" : "Inactive"}
      </div>
    </div>
  );
};

export default UserCard;
