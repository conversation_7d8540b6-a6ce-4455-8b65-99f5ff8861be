import React from 'react';
import Navbar from './Navbar';

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-accent-50 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 opacity-40">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-100 to-accent-100 opacity-20"></div>
        <div
          className="absolute inset-0"
          style={{
            background:
              "radial-gradient(circle at 50% 50%, rgba(59,130,246,0.05), transparent 50%)",
          }}
        ></div>
      </div>

      <div className="relative z-10">
        <Navbar />
        <main className="container mx-auto px-8 py-12 space-y-8">
          {children}
        </main>
      </div>
    </div>
  );
};

export default MainLayout;
