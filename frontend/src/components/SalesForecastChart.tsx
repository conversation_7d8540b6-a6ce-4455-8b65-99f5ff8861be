import React, { useRef } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  Filler,
} from "chart.js";
import type { ChartOptions, ChartData } from "chart.js";
import { Line } from "react-chartjs-2";
import {
  TrendingUp,
  Calendar,
  Target,
  AlertCircle,
  BarChart3,
  Info,
} from "lucide-react";
import {
  Card,
  Title,
  Text,
  Flex,
  Metric,
  Callout,
  Button,
  Grid,
  Col,
} from "@tremor/react";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ChartJSTitle,
  Tooltip,
  Legend,
  Filler
);

interface ForecastData {
  date: string;
  predicted_sales: number;
  lower_bound: number;
  upper_bound: number;
}

interface ModelForecast {
  model_name: string;
  forecast_data: ForecastData[];
  model_info: string;
  smape?: number; // Symmetric Mean Absolute Percentage Error
}

interface AnomalyData {
  date: string;
  value: number;
  detector: string;
  type: string;
}

interface ProductInfo {
  id: number;
  title: string;
  sku?: string;
  current_inventory: number;
}

interface HistoricalData {
  date: string;
  actual_sales: number;
  revenue: number;
  type: string;
}

interface SalesForecastChartProps {
  forecastData?: ForecastData[]; // Legacy single model support
  forecasts?: Record<string, ModelForecast>; // Multi-model forecasts
  ensembleForecast?: ForecastData[]; // Ensemble forecast
  anomalies?: AnomalyData[]; // Anomaly detection results
  historicalData?: HistoricalData[];
  productInfo: ProductInfo;
  isLoading?: boolean;
  error?: string;
  onRefresh?: () => void;
  onPopulateSalesData?: () => void;
  forecastPeriod?: string;
  availableModels?: string[];
}

const SalesForecastChart: React.FC<SalesForecastChartProps> = ({
  forecastData = [],
  forecasts = {},
  ensembleForecast = [],
  anomalies = [],
  historicalData = [],
  productInfo,
  isLoading = false,
  error,
  onRefresh,
  onPopulateSalesData,
  forecastPeriod = "1M",
}) => {
  const chartRef = useRef<ChartJS<"line">>(null);

  // State for controlling which models are visible
  const [visibleModels, setVisibleModels] = React.useState<
    Record<string, boolean>
  >(() => {
    const initial: Record<string, boolean> = {};
    Object.keys(forecasts).forEach((model) => {
      initial[model] = true; // Show all models by default
    });
    initial["ensemble"] = true; // Show ensemble by default
    initial["anomalies"] = true; // Show anomalies by default
    return initial;
  });

  // Define consistent colors for specific models
  const modelColorMap: { [key: string]: string } = {
    ARIMA: "rgb(59, 130, 246)", // Blue
    Prophet: "rgb(16, 185, 129)", // Green
    RNN: "rgb(139, 92, 246)", // Purple
    "N-BEATS": "rgb(245, 158, 11)", // Yellow/Amber
    TFT: "rgb(236, 72, 153)", // Pink
    DeepAR: "rgb(14, 165, 233)", // Sky
    ensemble: "rgb(0, 0, 0)", // Black
    anomalies: "rgb(239, 68, 68)", // Red for anomalies
  };

  // Fallback colors for any unknown models
  const fallbackColors = [
    "rgb(34, 197, 94)", // Emerald
    "rgb(168, 85, 247)", // Violet
    "rgb(251, 146, 60)", // Orange
  ];

  // Calculate confidence scores from MAPE values
  const calculateConfidenceScore = (mape: number): number => {
    // Convert MAPE to confidence score (0-1 scale)
    // Lower MAPE = higher confidence
    // Use exponential decay: confidence = e^(-mape/100)
    // Cap at reasonable values: MAPE > 500% = very low confidence
    const normalizedMape = Math.min(mape, 500); // Cap at 500%
    const confidence = Math.exp(-normalizedMape / 100);
    return Math.max(0, Math.min(1, confidence)); // Ensure 0-1 range
  };

  // Get confidence level description
  const getConfidenceLevel = (score: number): string => {
    if (score >= 0.7) return "High";
    if (score >= 0.4) return "Medium";
    if (score >= 0.2) return "Low";
    return "Very Low";
  };

  // Get confidence color
  const getConfidenceColor = (score: number): string => {
    if (score >= 0.7) return "text-green-600";
    if (score >= 0.4) return "text-yellow-600";
    if (score >= 0.2) return "text-orange-600";
    return "text-red-600";
  };

  // Update visible models when forecasts change
  React.useEffect(() => {
    setVisibleModels((prev) => {
      const updated = { ...prev };
      Object.keys(forecasts).forEach((model) => {
        if (!(model in updated)) {
          updated[model] = true;
        }
      });
      return updated;
    });
  }, [forecasts]);

  const { allFullDates, chartData } = React.useMemo(() => {
    // Create a comprehensive date-based data structure
    const dateMap = new Map<
      string,
      { date: Date; formattedDate: string; data: Record<string, number | null> }
    >();
    const datasets: any[] = [];

    // Helper function to add data point to dateMap
    const addDataPoint = (
      dateStr: string,
      key: string,
      value: number | null
    ) => {
      const date = new Date(dateStr);
      const formattedDate = date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });

      if (!dateMap.has(dateStr)) {
        dateMap.set(dateStr, {
          date,
          formattedDate,
          data: {},
        });
      }

      const entry = dateMap.get(dateStr)!;
      entry.data[key] = value;
    };

    // Add historical data
    historicalData.forEach((item) => {
      addDataPoint(item.date, "historical", item.actual_sales);
    });

    // Add forecast data for each model
    Object.entries(forecasts).forEach(([modelName, modelData]) => {
      if (!visibleModels[modelName]) return;
      modelData.forecast_data.forEach((item) => {
        addDataPoint(item.date, `forecast_${modelName}`, item.predicted_sales);
      });
    });

    // Add ensemble forecast data
    if (ensembleForecast.length > 0 && visibleModels["ensemble"]) {
      ensembleForecast.forEach((item) => {
        addDataPoint(item.date, "ensemble", item.predicted_sales);
      });
    }

    // Add anomaly data
    if (anomalies.length > 0 && visibleModels["anomalies"]) {
      anomalies.forEach((anomaly) => {
        addDataPoint(anomaly.date, "anomaly", anomaly.value);
      });
    }

    // Sort dates chronologically and build final arrays
    const sortedEntries = Array.from(dateMap.entries()).sort(
      ([a], [b]) => new Date(a).getTime() - new Date(b).getTime()
    );

    const allDates: string[] = [];
    const allFullDates: Date[] = [];
    const historicalSales: (number | null)[] = [];

    // Initialize data arrays for each model
    const modelDataArrays: Record<string, (number | null)[]> = {};
    Object.keys(forecasts).forEach((modelName) => {
      if (visibleModels[modelName]) {
        modelDataArrays[`forecast_${modelName}`] = [];
      }
    });
    const ensembleData: (number | null)[] = [];
    const anomalyData: (number | null)[] = [];

    // Populate arrays in chronological order
    sortedEntries.forEach(([, entry]) => {
      allDates.push(entry.formattedDate);
      allFullDates.push(entry.date);
      historicalSales.push(entry.data["historical"] || null);

      // Add model forecast data
      Object.keys(modelDataArrays).forEach((key) => {
        modelDataArrays[key].push(entry.data[key] || null);
      });

      ensembleData.push(entry.data["ensemble"] || null);
      anomalyData.push(entry.data["anomaly"] || null);
    });

    // Add historical dataset
    if (historicalData.length > 0) {
      datasets.push({
        label: "Historical Sales",
        data: historicalSales,
        borderColor: "rgb(107, 114, 128)",
        backgroundColor: "rgba(107, 114, 128, 0.1)",
        borderWidth: 2,
        fill: false,
        tension: 0.4,
        pointRadius: 2,
        pointHoverRadius: 6,
        pointBackgroundColor: "rgb(107, 114, 128)",
        pointBorderColor: "#ffffff",
        pointBorderWidth: 1,
      });
    }

    let fallbackColorIndex = 0;

    // Add forecast datasets for each model
    Object.entries(forecasts).forEach(([modelName]) => {
      if (!visibleModels[modelName]) return;

      const modelForecastData = modelDataArrays[`forecast_${modelName}`];
      // Use consistent color mapping or fallback
      const color =
        modelColorMap[modelName] ||
        fallbackColors[fallbackColorIndex % fallbackColors.length];
      if (!modelColorMap[modelName]) {
        fallbackColorIndex++;
      }

      datasets.push({
        label: `${modelName
          .replace(/_/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase())}`,
        data: modelForecastData,
        borderColor: color,
        backgroundColor: color.replace("rgb", "rgba").replace(")", ", 0.1)"),
        borderWidth: 2,
        fill: false,
        tension: 0.4,
        pointRadius: 3,
        pointHoverRadius: 6,
        pointBackgroundColor: color,
        pointBorderColor: "#ffffff",
        pointBorderWidth: 1,
      });
    });

    // Add ensemble forecast if available and visible
    if (ensembleForecast.length > 0 && visibleModels["ensemble"]) {
      datasets.push({
        label: "Ensemble (Average)",
        data: ensembleData,
        borderColor: "rgb(0, 0, 0)",
        backgroundColor: "rgba(0, 0, 0, 0.1)",
        borderWidth: 3,
        fill: false,
        tension: 0.4,
        pointRadius: 4,
        pointHoverRadius: 8,
        pointBackgroundColor: "rgb(0, 0, 0)",
        pointBorderColor: "#ffffff",
        pointBorderWidth: 2,
        borderDash: [5, 5], // Dashed line for ensemble
      });
    }

    // Add anomalies if available and visible
    if (anomalies.length > 0 && visibleModels["anomalies"]) {
      datasets.push({
        label: "Anomalies",
        data: anomalyData,
        borderColor: "rgb(239, 68, 68)",
        backgroundColor: "rgba(239, 68, 68, 0.8)",
        borderWidth: 0,
        fill: false,
        pointRadius: 6,
        pointHoverRadius: 8,
        pointBackgroundColor: "rgb(239, 68, 68)",
        pointBorderColor: "#ffffff",
        pointBorderWidth: 2,
        showLine: false, // Only show points, no line
      });
    }

    const chartData: ChartData<"line"> = {
      labels: allDates,
      datasets: datasets,
    };

    return { allDates, allFullDates, chartData };
  }, [historicalData, forecasts, ensembleForecast, anomalies, visibleModels]);

  // Chart options for interactivity
  const options: ChartOptions<"line"> = React.useMemo(
    () => ({
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        mode: "index",
        intersect: false,
      },
      plugins: {
        title: {
          display: true,
          text: `Sales Forecast - ${productInfo.title}`,
          font: {
            size: 18,
            weight: "bold",
          },
          color: "#1f2937",
          padding: 20,
        },
        legend: {
          display: true,
          position: "top",
          labels: {
            usePointStyle: true,
            padding: 20,
            font: {
              size: 12,
            },
            filter: (legendItem) => {
              // Hide the bounds from legend, only show main prediction
              return legendItem.text === "Predicted Sales";
            },
          },
        },
        tooltip: {
          mode: "index",
          intersect: false,
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          titleColor: "#ffffff",
          bodyColor: "#ffffff",
          borderColor: "rgba(59, 130, 246, 0.5)",
          borderWidth: 1,
          cornerRadius: 8,
          padding: 12,
          callbacks: {
            title: (context) => {
              const index = context[0].dataIndex;

              // Use the full date from our allFullDates array
              if (allFullDates && allFullDates[index]) {
                const date = allFullDates[index];
                return date.toLocaleDateString("en-US", {
                  weekday: "long",
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                });
              }

              return "Date unavailable";
            },
            label: (context) => {
              const label = context.dataset.label || "";
              const value = context.parsed.y;

              if (label === "Predicted Sales") {
                return `${label}: ${value.toFixed(1)} units`;
              } else if (label === "Upper Bound" || label === "Lower Bound") {
                const index = context.dataIndex;
                // Safety check to prevent undefined errors
                if (!forecastData || !forecastData[index]) {
                  return `${label}: ${value.toFixed(1)} units`;
                }
                const lowerBound = forecastData[index].lower_bound;
                const upperBound = forecastData[index].upper_bound;
                if (lowerBound !== undefined && upperBound !== undefined) {
                  return `Confidence Range: ${lowerBound.toFixed(
                    1
                  )} - ${upperBound.toFixed(1)} units`;
                }
              }
              return "";
            },
          },
        },
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: "Date",
            font: {
              size: 14,
              weight: "bold",
            },
            color: "#374151",
          },
          grid: {
            color: "rgba(156, 163, 175, 0.2)",
          },
          ticks: {
            color: "#6b7280",
            font: {
              size: 11,
            },
          },
        },
        y: {
          display: true,
          title: {
            display: true,
            text: "Predicted Sales (Units)",
            font: {
              size: 14,
              weight: "bold",
            },
            color: "#374151",
          },
          beginAtZero: true,
          grid: {
            color: "rgba(156, 163, 175, 0.2)",
          },
          ticks: {
            color: "#6b7280",
            font: {
              size: 11,
            },
            callback: function (value) {
              return Math.round(Number(value));
            },
          },
        },
      },
      elements: {
        point: {
          radius: 4,
          hoverRadius: 8,
        },
        line: {
          borderJoinStyle: "round",
        },
      },
      animation: {
        duration: 1000,
        easing: "easeInOutQuart",
      },
    }),
    [productInfo.title, allFullDates, forecastData]
  );

  // Calculate summary statistics using ensemble forecast or first available model
  const getStatsData = () => {
    if (ensembleForecast.length > 0) {
      return ensembleForecast;
    } else if (forecastData.length > 0) {
      return forecastData;
    } else if (Object.keys(forecasts).length > 0) {
      const firstModel = Object.values(forecasts)[0];
      return firstModel.forecast_data;
    }
    return [];
  };

  const statsData = getStatsData();
  const totalPredictedSales = statsData.reduce(
    (sum, item) => sum + item.predicted_sales,
    0
  );
  const avgDailySales =
    statsData.length > 0 ? totalPredictedSales / statsData.length : 0;
  const maxSalesDay =
    statsData.length > 0
      ? statsData.reduce((max, item) =>
          item.predicted_sales > max.predicted_sales ? item : max
        )
      : { predicted_sales: 0, date: new Date().toISOString() };

  // Calculate historical statistics
  const totalHistoricalSales = historicalData.reduce(
    (sum, item) => sum + item.actual_sales,
    0
  );
  const avgHistoricalSales =
    historicalData.length > 0
      ? totalHistoricalSales / historicalData.length
      : 0;

  if (error) {
    const isInsufficientDataError = error.includes(
      "minimum 10 data points required"
    );

    return (
      <Callout
        title="Forecast Error"
        icon={AlertCircle}
        color="rose"
        className="mt-4"
      >
        <Text className="mb-4">{error}</Text>

        {isInsufficientDataError && (
          <Callout
            title="Quick Fix: Insufficient Data"
            icon={Info}
            color="blue"
            className="mt-4"
          >
            <Text className="mb-3">
              This error occurs when there's not enough historical sales data.
              You can populate sales data from your existing orders to enable
              forecasting.
            </Text>
            {onPopulateSalesData && (
              <Button
                onClick={onPopulateSalesData}
                className="btn-primary w-full mt-3"
              >
                Populate Sales Data from Orders
              </Button>
            )}
          </Callout>
        )}

        {onRefresh && (
          <Button onClick={onRefresh} className="btn-secondary w-full mt-4">
            Regenerate Forecast
          </Button>
        )}
      </Callout>
    );
  }

  if (isLoading) {
    return (
      <Card className="card-modern">
        {/* Loading Header */}
        <Flex justifyContent="between" alignItems="center" className="mb-6">
          <Flex alignItems="center" className="space-x-3">
            <div className="relative">
              <div className="w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
            </div>
            <div>
              <Title className="text-lg font-semibold text-gray-900">
                Generating Sales Forecast
              </Title>
              <Text className="text-sm text-gray-600">
                Training multiple AI models for accurate predictions...
              </Text>
            </div>
          </Flex>
          <div className="text-right">
            <Text className="text-sm text-gray-500">Forecast Period</Text>
            <Metric className="text-lg font-semibold text-blue-600">
              {forecastPeriod}
            </Metric>
          </div>
        </Flex>

        {/* Progress Steps */}
        <div className="mb-8">
          <Flex justifyContent="between" className="text-sm text-gray-600 mb-2">
            <Text>Processing historical data</Text>
            <Text>Training models</Text>
            <Text>Generating predictions</Text>
          </Flex>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full animate-pulse"
              style={{ width: "75%" }}
            ></div>
          </div>
        </div>

        {/* Model Cards Loading */}
        <div className="mb-6">
          <Text className="text-sm font-medium text-gray-700 mb-3">
            Training Forecasting Models
          </Text>
          <Flex className="flex-wrap gap-3">
            {["ARIMA", "Prophet", "RNN", "N-BEATS", "TFT", "DeepAR"].map(
              (model, index) => (
                <Card key={model} className="flex-1 p-3">
                  <Flex alignItems="center" className="space-x-2">
                    <div
                      className={`w-3 h-3 rounded-full ${
                        index < 2
                          ? "bg-green-500"
                          : index < 4
                          ? "bg-yellow-500 animate-pulse"
                          : "bg-gray-300"
                      }`}
                    ></div>
                    <Text className="text-xs font-medium text-gray-700">
                      {model}
                    </Text>
                  </Flex>
                  <Text className="mt-2 text-xs text-gray-500">
                    {index < 2
                      ? "Complete"
                      : index < 4
                      ? "Training..."
                      : "Waiting..."}
                  </Text>
                </Card>
              )
            )}
          </Flex>
        </div>

        {/* Chart Skeleton */}
        <Flex
          justifyContent="center"
          alignItems="center"
          className="h-96 mb-6 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200"
        >
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
            <Text className="font-medium">
              Preparing forecast visualization
            </Text>
            <Text className="text-sm text-gray-400 mt-1">
              This may take a few moments...
            </Text>
          </div>
        </Flex>

        {/* Statistics Skeleton */}
        <Grid numItemsSm={1} numItemsMd={4} className="gap-4 mb-4">
          {[
            { label: "Historical Avg", color: "gray" },
            { label: "Total Forecast", color: "blue" },
            { label: "Daily Average", color: "green" },
            { label: "Peak Day", color: "purple" },
          ].map((stat) => (
            <Col key={stat.label}>
              <Card
                className={`bg-${stat.color}-50 border border-${stat.color}-100`}
              >
                <div className="animate-pulse">
                  <Flex justifyContent="between" alignItems="center">
                    <div>
                      <div className="h-3 bg-gray-200 rounded mb-2 w-3/4"></div>
                      <div className="h-6 bg-gray-300 rounded mb-1 w-1/2"></div>
                      <div className="h-2 bg-gray-200 rounded w-2/3"></div>
                    </div>
                    <div className="w-6 h-6 bg-gray-200 rounded"></div>
                  </Flex>
                </div>
              </Card>
            </Col>
          ))}
        </Grid>

        {/* Loading Tips */}
        <Callout
          title="Did you know?"
          icon={Info}
          color="blue"
          className="mt-4"
        >
          <Text className="text-sm">
            We're using 6 different AI models including ARIMA, Prophet, and
            neural networks to provide the most accurate sales forecasts. Each
            model brings unique strengths to capture different patterns in your
            data.
          </Text>
        </Callout>
      </Card>
    );
  }

  return (
    <>
      {/* Model Toggle Buttons */}
      {Object.keys(forecasts).length > 0 && (
        <Card className="mb-6 card-modern">
          <Title className="text-sm font-medium text-gray-700 mb-3">
            Forecasting Models
          </Title>
          <Flex className="flex-wrap gap-2">
            {/* Replaced Toggle with individual buttons */}
            {Object.keys(forecasts).map((modelName) => {
              const modelColor = modelColorMap[modelName] || fallbackColors[0];
              const modelData = forecasts[modelName];
              const smape = modelData?.smape || 0;
              const confidenceScore = calculateConfidenceScore(smape);
              const confidenceLevel = getConfidenceLevel(confidenceScore);

              return (
                <button
                  key={modelName}
                  onClick={() =>
                    setVisibleModels((prev) => ({
                      ...prev,
                      [modelName]: !prev[modelName],
                    }))
                  }
                  className={`px-3 py-2 rounded-lg text-xs font-medium transition-colors border hover:shadow-sm ${
                    visibleModels[modelName]
                      ? `bg-[${modelColor
                          .replace("rgb", "rgba")
                          .replace(
                            ")",
                            ", 0.1)"
                          )}] border-[${modelColor}] text-[${modelColor}]`
                      : "bg-gray-100 text-gray-600 border-gray-300"
                  }`}
                  style={{
                    borderColor: modelColor,
                    color: modelColor,
                    backgroundColor: visibleModels[modelName]
                      ? modelColor.replace("rgb", "rgba").replace(")", ", 0.1)")
                      : "",
                  }}
                  title={`sMAPE: ${smape.toFixed(1)}% | Confidence: ${(
                    confidenceScore * 100
                  ).toFixed(0)}%`}
                >
                  <Flex
                    flexDirection="col"
                    alignItems="center"
                    className="space-y-1"
                  >
                    <Flex alignItems="center" className="space-x-1">
                      <div
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: modelColor }}
                      ></div>
                      <Text className="text-xs font-medium">
                        {modelName
                          .replace(/_/g, " ")
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </Text>
                    </Flex>
                    <Text
                      className={`font-semibold ${getConfidenceColor(
                        confidenceScore
                      )}`}
                    >
                      {confidenceLevel}
                    </Text>
                    <Text className="text-gray-500">
                      {(confidenceScore * 100).toFixed(0)}%
                    </Text>
                  </Flex>
                </button>
              );
            })}
            {ensembleForecast.length > 0 && (
              <button
                onClick={() =>
                  setVisibleModels((prev) => ({
                    ...prev,
                    ensemble: !prev.ensemble,
                  }))
                }
                className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                  visibleModels["ensemble"]
                    ? "bg-black text-white border border-black"
                    : "bg-gray-100 text-gray-600 border border-gray-300"
                }`}
              >
                Ensemble
              </button>
            )}
            {anomalies.length > 0 && (
              <button
                onClick={() =>
                  setVisibleModels((prev) => ({
                    ...prev,
                    anomalies: !prev.anomalies,
                  }))
                }
                className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                  visibleModels["anomalies"]
                    ? "bg-red-100 text-red-800 border border-red-300"
                    : "bg-gray-100 text-gray-600 border border-gray-300"
                }`}
              >
                Anomalies ({anomalies.length})
              </button>
            )}
          </Flex>
        </Card>
      )}

      {/* Interactive Chart - Increased height */}
      <div className="h-96 mb-6">
        <Line ref={chartRef} data={chartData} options={options} />
      </div>

      {/* Summary Statistics */}
      <Grid numItemsSm={1} numItemsMd={4} className="gap-4 mb-4">
        {historicalData.length > 0 && (
          <Col>
            <Card className="bg-gray-50 border border-gray-100 h-full">
              <Flex justifyContent="between" alignItems="center">
                <div>
                  <Text className="text-sm text-gray-600 font-medium">
                    Historical Avg
                  </Text>
                  <Metric className="text-2xl font-bold text-gray-900">
                    {avgHistoricalSales.toFixed(1)} units
                  </Metric>
                  <Text className="text-xs text-gray-500">
                    Past {historicalData.length} days
                  </Text>
                </div>
                <BarChart3 className="text-gray-500" size={24} />
              </Flex>
            </Card>
          </Col>
        )}

        <Col>
          <Card className="bg-blue-50 border border-blue-100 h-full">
            <Flex justifyContent="between" alignItems="center">
              <div>
                <Text className="text-sm text-blue-600 font-medium">
                  Total Forecast ({forecastPeriod})
                </Text>
                <Metric className="text-2xl font-bold text-blue-900">
                  {totalPredictedSales.toFixed(0)} units
                </Metric>
              </div>
              <TrendingUp className="text-blue-500" size={24} />
            </Flex>
          </Card>
        </Col>

        <Col>
          <Card className="bg-green-50 border border-green-100 h-full">
            <Flex justifyContent="between" alignItems="center">
              <div>
                <Text className="text-sm text-green-600 font-medium">
                  Daily Average
                </Text>
                <Metric className="text-2xl font-bold text-green-900">
                  {avgDailySales.toFixed(1)} units
                </Metric>
              </div>
              <Target className="text-green-500" size={24} />
            </Flex>
          </Card>
        </Col>

        <Col>
          <Card className="bg-purple-50 border border-purple-100 h-full">
            <Flex justifyContent="between" alignItems="center">
              <div>
                <Text className="text-sm text-purple-600 font-medium">
                  Peak Day
                </Text>
                <Metric className="text-lg font-bold text-purple-900">
                  {maxSalesDay.predicted_sales.toFixed(0)} units
                </Metric>
                <Text className="text-xs text-purple-600">
                  {new Date(maxSalesDay.date).toLocaleDateString()}
                </Text>
              </div>
              <Calendar className="text-purple-500" size={24} />
            </Flex>
          </Card>
        </Col>
      </Grid>

      {/* Product Info */}
      <Card className="bg-gray-50 rounded-lg p-4 card-modern">
        <Title className="font-semibold text-gray-900 mb-2">
          Product Information
        </Title>
        <Flex className="grid grid-cols-2 gap-4 text-sm">
          <Flex flexDirection="col">
            <Text className="text-gray-600">SKU:</Text>
            <Text className="font-medium">{productInfo.sku || "N/A"}</Text>
          </Flex>
          <Flex flexDirection="col">
            <Text className="text-gray-600">Current Inventory:</Text>
            <Text className="font-medium">
              {productInfo.current_inventory} units
            </Text>
          </Flex>
        </Flex>
      </Card>

      {/* Refresh Button */}
      {onRefresh && (
        <Flex justifyContent="center" className="mt-4">
          <Button onClick={onRefresh} className="btn-secondary text-sm">
            Regenerate Forecast
          </Button>
        </Flex>
      )}
    </>
  );
};

export default React.memo(SalesForecastChart);
