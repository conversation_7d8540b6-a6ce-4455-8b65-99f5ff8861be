'use client';

import React, { useState } from 'react';
import {
  CheckCircle,
  X,
  Send,
  MessageSquare,
  Mail,
  DollarSign,
  Camera,
  FileText,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'react-hot-toast';

interface BookingRequest {
  id: number;
  request_number: string;
  customer_name: string;
  customer_phone?: string;
  customer_email?: string;
  service_type: string;
  service_description: string;
  service_address: string;
  estimated_cost?: number;
}

interface JobCompletionModalProps {
  isOpen: boolean;
  onClose: () => void;
  serviceRequest: BookingRequest | null;
  onComplete: (completionData: JobCompletionData) => void;
}

interface JobCompletionData {
  status: 'completed';
  completion_notes: string;
  final_cost?: number;
  work_performed: string;
  customer_satisfaction?: number;
  send_sms: boolean;
  send_email: boolean;
  photos?: File[];
  invoice_notes?: string;
}

const JobCompletionModal: React.FC<JobCompletionModalProps> = ({
  isOpen,
  onClose,
  serviceRequest,
  onComplete,
}) => {
  const [completionData, setCompletionData] = useState<JobCompletionData>({
    status: 'completed',
    completion_notes: '',
    work_performed: '',
    send_sms: true,
    send_email: true,
  });
  const [loading, setLoading] = useState(false);
  const [photos, setPhotos] = useState<File[]>([]);

  const handleInputChange = (field: keyof JobCompletionData, value: any) => {
    setCompletionData((prev) => ({ ...prev, [field]: value }));
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setPhotos((prev) => [...prev, ...files]);
  };

  const removePhoto = (index: number) => {
    setPhotos((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async () => {
    if (!serviceRequest) return;

    if (!completionData.work_performed.trim()) {
      toast.error('Please describe the work performed');
      return;
    }

    try {
      setLoading(true);

      const finalData = {
        ...completionData,
        photos: photos,
      };

      await onComplete(finalData);

      // Show success message
      const notifications = [];
      if (completionData.send_sms && serviceRequest.customer_phone) {
        notifications.push('SMS');
      }
      if (completionData.send_email && serviceRequest.customer_email) {
        notifications.push('Email');
      }

      const notificationText =
        notifications.length > 0
          ? ` Customer will be notified via ${notifications.join(' and ')}.`
          : '';

      toast.success(`Job marked as completed!${notificationText}`);
      onClose();

      // Reset form
      setCompletionData({
        status: 'completed',
        completion_notes: '',
        work_performed: '',
        send_sms: true,
        send_email: true,
      });
      setPhotos([]);
    } catch (error) {
      console.error('Failed to complete job:', error);
      toast.error('Failed to complete job');
    } finally {
      setLoading(false);
    }
  };

  if (!serviceRequest) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            Complete Job
          </DialogTitle>
          <DialogDescription>Mark this job as completed and notify the customer</DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Job Summary */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Job Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Label className="text-muted-foreground">Job #</Label>
                  <p className="font-medium">{serviceRequest.request_number}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Service Type</Label>
                  <p className="font-medium">{serviceRequest.service_type}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Customer</Label>
                  <p className="font-medium">{serviceRequest.customer_name}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Estimated Cost</Label>
                  <p className="font-medium">
                    {serviceRequest.estimated_cost
                      ? `$${serviceRequest.estimated_cost.toFixed(2)}`
                      : 'Not specified'}
                  </p>
                </div>
              </div>
              <div>
                <Label className="text-muted-foreground">Address</Label>
                <p className="font-medium">{serviceRequest.service_address}</p>
              </div>
            </CardContent>
          </Card>

          {/* Work Performed */}
          <div className="space-y-2">
            <Label htmlFor="work_performed">Work Performed *</Label>
            <Textarea
              id="work_performed"
              value={completionData.work_performed}
              onChange={(e) => handleInputChange('work_performed', e.target.value)}
              placeholder="Describe the work that was completed..."
              rows={4}
              required
            />
          </div>

          {/* Final Cost */}
          <div className="space-y-2">
            <Label htmlFor="final_cost">Final Cost</Label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                id="final_cost"
                type="number"
                step="0.01"
                min="0"
                value={completionData.final_cost || ''}
                onChange={(e) =>
                  handleInputChange('final_cost', parseFloat(e.target.value) || undefined)
                }
                placeholder="0.00"
                className="pl-10"
              />
            </div>
          </div>

          {/* Completion Notes */}
          <div className="space-y-2">
            <Label htmlFor="completion_notes">Additional Notes</Label>
            <Textarea
              id="completion_notes"
              value={completionData.completion_notes}
              onChange={(e) => handleInputChange('completion_notes', e.target.value)}
              placeholder="Any additional notes or observations..."
              rows={3}
            />
          </div>

          {/* Photo Upload */}
          <div className="space-y-2">
            <Label>Before/After Photos</Label>
            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
              <div className="text-center">
                <Camera className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground mb-2">
                  Upload photos of completed work
                </p>
                <Input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handlePhotoUpload}
                  className="max-w-xs"
                />
              </div>

              {photos.length > 0 && (
                <div className="mt-4">
                  <div className="flex flex-wrap gap-2">
                    {photos.map((photo, index) => (
                      <div key={index} className="relative">
                        <Badge variant="secondary" className="pr-6">
                          {photo.name}
                          <button
                            onClick={() => removePhoto(index)}
                            className="absolute right-1 top-1/2 transform -translate-y-1/2"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Customer Notifications */}
          <div className="space-y-4">
            <Label className="text-base font-semibold">Customer Notifications</Label>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <MessageSquare className="w-4 h-4 text-blue-500" />
                  <div>
                    <Label>Send SMS Notification</Label>
                    <p className="text-sm text-muted-foreground">
                      {serviceRequest.customer_phone || 'No phone number on file'}
                    </p>
                  </div>
                </div>
                <Switch
                  checked={completionData.send_sms}
                  onCheckedChange={(checked) => handleInputChange('send_sms', checked)}
                  disabled={!serviceRequest.customer_phone}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-green-500" />
                  <div>
                    <Label>Send Email Notification</Label>
                    <p className="text-sm text-muted-foreground">
                      {serviceRequest.customer_email || 'No email address on file'}
                    </p>
                  </div>
                </div>
                <Switch
                  checked={completionData.send_email}
                  onCheckedChange={(checked) => handleInputChange('send_email', checked)}
                  disabled={!serviceRequest.customer_email}
                />
              </div>
            </div>

            {(completionData.send_sms || completionData.send_email) && (
              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm text-blue-700">
                  <strong>Customer will receive:</strong> Job completion confirmation, work summary,
                  final cost, and photos (if uploaded).
                </p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button variant="outline" onClick={onClose} disabled={loading}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={loading || !completionData.work_performed.trim()}
              loading={loading}
              loadingText="Completing..."
            >
              <CheckCircle className="w-4 h-4 mr-2" />
              Complete Job
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default JobCompletionModal;
