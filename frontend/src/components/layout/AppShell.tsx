'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { useUI } from '@/contexts/UIContext';
import { cn } from '@/lib/utils';
import { useLayout } from '@/contexts/LayoutContext';

interface AppShellProps {
  children: React.ReactNode;
}

export const AppShell: React.FC<AppShellProps> = ({ children }) => {
  const pathname = usePathname();
  const { sidebarOpen, mobileMenuOpen } = useUI();
  const { hideSidebar } = useLayout();

  // Don't show app shell on auth pages
  const isAuthPage =
    pathname?.startsWith('/auth') ||
    pathname?.startsWith('/login') ||
    pathname?.startsWith('/register');
  const isMarketingPage =
    pathname === '/' || pathname?.startsWith('/pricing') || pathname?.startsWith('/about');

  if (isAuthPage || isMarketingPage) {
    return <>{children}</>;
  }

  return (
    <div className="min-h-screen">
      {/* Sidebar */}
      {!hideSidebar && <Sidebar />}

      {/* Mobile overlay */}
      {mobileMenuOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm lg:hidden"
          onClick={() => {}} // Handle mobile menu close
        />
      )}

      {/* Main content */}
      <div
        className={cn(
          'flex flex-col transition-all duration-300 ease-in-out',
          hideSidebar ? 'md:ml-0' : (sidebarOpen ? 'md:ml-64' : 'md:ml-16'),
        )}
      >
        <Header />

        {/* Page content */}
        <main className="flex-1 overflow-hidden">
          <div className="h-full p-6">{children}</div>
        </main>
      </div>
    </div>
  ); // End of AppShell component
