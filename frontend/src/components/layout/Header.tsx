'use client';

import React, { useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import {
  Bell,
  Search,
  Menu,
  Sun,
  Moon,
  Monitor,
  User,
  Settings,
  LogOut,
  ChevronDown,
  ArrowLeft,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { useUI } from '@/contexts/UIContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useCompany } from '@/contexts/CompanyContext';
import { useAuth } from '@/contexts/AuthContext';

const getPageTitle = (pathname: string): string => {
  const segments = pathname?.split('/').filter(Boolean) || [];
  const page = segments[0] || 'dashboard';

  const titles: Record<string, string> = {
    dashboard: 'Dashboard',
    agents: 'AI Agents',
    companies: 'Companies',
    'phone-numbers': 'Phone Numbers',
    conversations: 'Conversations',
    bookings: 'Bookings',
    'knowledge-base': 'Knowledge Base',
    'web-forms': 'Web Forms',
    'chat-plugin': 'Chat Plugin',
    testing: 'Testing',
    analytics: 'Analytics',
    settings: 'Settings',
  };

  return titles[page] || 'Dashboard';
};

export const Header: React.FC = () => {
  const pathname = usePathname();
  const router = useRouter();
  const { toggleMobileMenu, notifications } = useUI();
  const { theme, setTheme } = useTheme();
  const { currentCompany } = useCompany();
  const { logout } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');

  const pageTitle = getPageTitle(pathname || '');
  const unreadNotifications = notifications.filter((n) => !n.action).length;

  const handleThemeChange = () => {
    if (theme === 'light') {
      setTheme('dark');
    } else if (theme === 'dark') {
      setTheme('system');
    } else {
      setTheme('light');
    }
  };

  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return <Sun className="w-4 h-4" />;
      case 'dark':
        return <Moon className="w-4 h-4" />;
      default:
        return <Monitor className="w-4 h-4" />;
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Navigate to a search results page or implement search logic
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const handleMobileSearch = () => {
    const query = prompt('Search for...');
    if (query?.trim()) {
      router.push(`/search?q=${encodeURIComponent(query.trim())}`);
    }
  };

  return (
    <header className="sticky top-0 z-40 bg-background/80 backdrop-blur-lg border-b border-border">
      <div className="flex h-16 items-center justify-between px-4 lg:px-6">
        {/* Left section */}
        <div className="flex items-center space-x-4">
          {/* Back button for specific pages */}
          {pathname === '/web-forms/new' && (
            <Button variant="ghost" size="icon" onClick={() => router.back()}>
              <ArrowLeft className="w-5 h-5" />
            </Button>
          )}

          {/* Mobile menu button */}
          <Button variant="ghost" size="icon" onClick={toggleMobileMenu} className="lg:hidden">
            <Menu className="w-5 h-5" />
          </Button>

          {/* Company branding and page title */}
          <div className="flex items-center space-x-3">
            {currentCompany?.logo && (
              <img
                src={currentCompany.logo}
                alt={`${currentCompany.name} logo`}
                className="w-8 h-8 rounded-full object-cover"
              />
            )}
            <div>
              <h1 className="text-xl font-semibold text-foreground">{pageTitle}</h1>
              {currentCompany && (
                <p className="text-sm text-muted-foreground">{currentCompany.name}</p>
              )}
            </div>
          </div>
        </div>

        {/* Center section - Search */}
        <div className="hidden md:flex flex-1 max-w-md mx-8">
          <form onSubmit={handleSearch} className="relative w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search agents, customers, bookings..."
              className="pl-10 bg-background/50 border-border/50 focus:bg-background"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </form>
        </div>

        {/* Right section */}
        <div className="flex items-center space-x-2">
          {/* Search button for mobile */}
          <Button variant="ghost" size="icon" className="md:hidden" onClick={handleMobileSearch}>
            <Search className="w-5 h-5" />
          </Button>

          {/* Theme toggle */}
          <Button variant="ghost" size="icon" onClick={handleThemeChange}>
            {getThemeIcon()}
          </Button>

          {/* Notifications */}
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="w-5 h-5" />
            {unreadNotifications > 0 && (
              <Badge
                variant="destructive"
                className="absolute -top-1 -right-1 w-5 h-5 p-0 flex items-center justify-center text-xs"
              >
                {unreadNotifications > 9 ? '9+' : unreadNotifications}
              </Badge>
            )}
          </Button>

          {/* User menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-2 px-3">
                <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-primary-foreground" />
                </div>
                <div className="hidden md:block text-left">
                  <p className="text-sm font-medium">Admin User</p>
                  <p className="text-xs text-muted-foreground"><EMAIL></p>
                </div>
                <ChevronDown className="w-4 h-4 text-muted-foreground" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => router.push('/profile')}>
                <User className="mr-2 h-4 w-4" />
                Profile
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push('/settings')}>
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={logout} className="text-destructive">
                <LogOut className="mr-2 h-4 w-4" />
                Log out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};
