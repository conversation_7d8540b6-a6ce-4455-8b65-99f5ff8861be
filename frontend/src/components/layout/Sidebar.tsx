'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import {
  LayoutDashboard,
  Bot,
  Building2,
  Phone,
  MessageSquare,
  Calendar,
  BookOpen,
  Zap,
  BarChart3,
  Settings,
  ChevronLeft,
  ChevronRight,
  Home,
  Users,
  TestTube,
  ChevronDown,
  ChevronUp,
  User,
  LogOut,
  Sun,
  Moon,
  Monitor,
  Puzzle,
  Headphones,
  Database,
  Wrench,
  Bell,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useUI } from '@/contexts/UIContext';
import { useCompany, Company } from '@/contexts/CompanyContext';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { cn } from '@/lib/utils';

// Main navigation structure
const navigation = [{ name: 'Home', href: '/dashboard', icon: Home }];

// Contact Center submenu
const contactCenterItems = [
  { name: 'Conversations', href: '/conversations', icon: MessageSquare },
  { name: 'Contact Agents', href: '/agents', icon: Bot },
  { name: 'Knowledge Base', href: '/knowledge-base', icon: BookOpen },
  { name: 'Tools', href: '/tools', icon: Wrench },
  { name: 'Phone Numbers', href: '/phone-numbers', icon: Phone },
];

// Main menu items
const mainMenuItems = [
  { name: 'Settings', href: '/settings', icon: Settings },
  { name: 'Integrations', href: '/integrations', icon: Puzzle },
];

// Additional menu items (only <NAME_EMAIL>)
const additionalMenuItems = [
  { name: 'Companies', href: '/companies', icon: Building2 },
  { name: 'Bookings', href: '/bookings', icon: Calendar },
  { name: 'Web Forms', href: '/web-forms', icon: Zap },
  { name: 'Chat Plugin', href: '/chat-plugin', icon: MessageSquare },
  { name: 'Testing', href: '/testing', icon: TestTube },
  { name: 'Analytics', href: '/analytics', icon: BarChart3 },
];

const CompanySwitcher: React.FC = () => {
  const { companies, currentCompany, selectCompany } = useCompany();
  const router = useRouter();

  const handleCompanySelect = (company: Company) => {
    selectCompany(company);
    router.push('/dashboard');
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="w-full justify-between text-foreground hover:bg-accent hover:text-foreground"
        >
          <div className="flex items-center space-x-2">
            <Building2 className="w-4 h-4" />
            <span className="truncate">
              {currentCompany ? currentCompany.name : 'Select Company'}
            </span>
          </div>
          <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56">
        <DropdownMenuLabel>Select a Company</DropdownMenuLabel>
        {companies.map((company) => (
          <DropdownMenuItem
            key={company.id}
            onClick={() => handleCompanySelect(company)}
            className="cursor-pointer"
          >
            {company.name}
            {currentCompany?.id === company.id && (
              <span className="ml-auto text-xs text-muted-foreground">(Current)</span>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export const Sidebar: React.FC = () => {
  const pathname = usePathname();
  const router = useRouter();
  const { sidebarOpen, setSidebarOpen, notifications } = useUI();
  const { currentCompany } = useCompany();
  const { user, logout } = useAuth();
  const { theme, setTheme } = useTheme();
  const [contactCenterOpen, setContactCenterOpen] = useState(false);
  const [additionalMenuOpen, setAdditionalMenuOpen] = useState(false);

  // Check if <NAME_EMAIL> to show additional menu
  const isTestUser = user?.email === '<EMAIL>';
  const unreadNotifications = notifications.filter((n) => !n.action).length;

  const handleThemeChange = () => {
    if (theme === 'light') {
      setTheme('dark');
    } else if (theme === 'dark') {
      setTheme('system');
    } else {
      setTheme('light');
    }
  };

  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return <Sun className="w-4 h-4" />;
      case 'dark':
        return <Moon className="w-4 h-4" />;
      default:
        return <Monitor className="w-4 h-4" />;
    }
  };

  return (
    <>
      {/* Mobile Overlay */}
      <div
        className={cn(
          'fixed inset-0 z-40 bg-black/50 backdrop-blur-sm transition-opacity lg:hidden',
          sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none',
        )}
        onClick={() => setSidebarOpen(false)}
      />

      {/* Desktop Sidebar */}
      <div
        className={cn(
          'fixed inset-y-0 left-0 z-50 flex flex-col bg-card border-r border-border transition-all duration-300 ease-in-out',
          'lg:translate-x-0',
          sidebarOpen ? 'w-64 translate-x-0' : 'w-64 -translate-x-full lg:w-16 lg:translate-x-0',
        )}
      >
        {/* Logo/Brand */}
        <div className="flex h-16 items-center justify-between px-4 border-b border-border">
          {sidebarOpen ? (
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <Home className="w-5 h-5 text-primary-foreground" />
              </div>
              <span className="font-semibold text-foreground">HomeService</span>
            </div>
          ) : (
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mx-auto">
              <Home className="w-5 h-5 text-primary-foreground" />
            </div>
          )}

          <Button
            variant="ghost"
            size="icon-sm"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="hidden lg:flex"
          >
            {sidebarOpen ? (
              <ChevronLeft className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </Button>
        </div>

        {/* Company Selector */}
        {sidebarOpen && (
          <div className="px-4 py-3 border-b border-border">
            <CompanySwitcher />
          </div>
        )}

        {/* Navigation */}
        <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
          {/* Home */}
          {navigation.map((item) => {
            const isActive = pathname === item.href || pathname?.startsWith(item.href + '/');
            return (
              <Link key={item.name} href={item.href}>
                <div
                  className={cn(
                    'group flex items-center px-2 py-2 text-sm font-medium rounded-lg transition-all duration-200',
                    isActive
                      ? 'bg-primary text-primary-foreground shadow-sm'
                      : 'text-muted-foreground hover:text-foreground hover:bg-accent',
                    !sidebarOpen && 'justify-center',
                  )}
                >
                  <item.icon
                    className={cn('flex-shrink-0 w-5 h-5', sidebarOpen ? 'mr-3' : 'mr-0')}
                  />
                  {sidebarOpen && <span className="truncate">{item.name}</span>}

                  {/* Tooltip for collapsed state */}
                  {!sidebarOpen && (
                    <div className="absolute left-16 px-2 py-1 ml-1 text-xs font-medium text-white bg-gray-900 rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                      {item.name}
                    </div>
                  )}
                </div>
              </Link>
            );
          })}

          {/* Contact Center Section */}
          <div className="mt-4">
            <Button
              variant="ghost"
              onClick={() => setContactCenterOpen(!contactCenterOpen)}
              className={cn(
                'w-full justify-between text-muted-foreground hover:text-foreground hover:bg-accent px-2 py-2 text-sm font-medium rounded-lg transition-all duration-200',
                !sidebarOpen && 'justify-center',
              )}
            >
              <div className="flex items-center">
                <Headphones
                  className={cn('flex-shrink-0 w-5 h-5', sidebarOpen ? 'mr-3' : 'mr-0')}
                />
                {sidebarOpen && <span className="truncate">Contact Center</span>}
              </div>
              {sidebarOpen &&
                (contactCenterOpen ? (
                  <ChevronUp className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                ))}
            </Button>

            {/* Contact Center Submenu */}
            {sidebarOpen && contactCenterOpen && (
              <div className="ml-4 mt-1 space-y-1">
                {contactCenterItems.map((item) => {
                  const isActive = pathname === item.href || pathname?.startsWith(item.href + '/');
                  return (
                    <Link key={item.name} href={item.href}>
                      <div
                        className={cn(
                          'group flex items-center px-2 py-2 text-sm font-medium rounded-lg transition-all duration-200',
                          isActive
                            ? 'bg-primary text-primary-foreground shadow-sm'
                            : 'text-muted-foreground hover:text-foreground hover:bg-accent',
                        )}
                      >
                        <item.icon className="flex-shrink-0 w-4 h-4 mr-3" />
                        <span className="truncate">{item.name}</span>
                      </div>
                    </Link>
                  );
                })}
              </div>
            )}
          </div>

          {/* Main Menu Items */}
          <div className="mt-4 space-y-1">
            {mainMenuItems.map((item) => {
              const isActive = pathname === item.href || pathname?.startsWith(item.href + '/');
              return (
                <Link key={item.name} href={item.href}>
                  <div
                    className={cn(
                      'group flex items-center px-2 py-2 text-sm font-medium rounded-lg transition-all duration-200',
                      isActive
                        ? 'bg-primary text-primary-foreground shadow-sm'
                        : 'text-muted-foreground hover:text-foreground hover:bg-accent',
                      !sidebarOpen && 'justify-center',
                    )}
                  >
                    <item.icon
                      className={cn('flex-shrink-0 w-5 h-5', sidebarOpen ? 'mr-3' : 'mr-0')}
                    />
                    {sidebarOpen && <span className="truncate">{item.name}</span>}

                    {/* Tooltip for collapsed state */}
                    {!sidebarOpen && (
                      <div className="absolute left-16 px-2 py-1 ml-1 text-xs font-medium text-white bg-gray-900 rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                        {item.name}
                      </div>
                    )}
                  </div>
                </Link>
              );
            })}
          </div>

          {/* Additional Menu (<NAME_EMAIL>) */}
          {isTestUser && (
            <div className="mt-4">
              <Button
                variant="ghost"
                onClick={() => setAdditionalMenuOpen(!additionalMenuOpen)}
                className={cn(
                  'w-full justify-between text-muted-foreground hover:text-foreground hover:bg-accent px-2 py-2 text-sm font-medium rounded-lg transition-all duration-200',
                  !sidebarOpen && 'justify-center',
                )}
              >
                <div className="flex items-center">
                  <Database
                    className={cn('flex-shrink-0 w-5 h-5', sidebarOpen ? 'mr-3' : 'mr-0')}
                  />
                  {sidebarOpen && <span className="truncate">Additional</span>}
                </div>
                {sidebarOpen &&
                  (additionalMenuOpen ? (
                    <ChevronUp className="w-4 h-4" />
                  ) : (
                    <ChevronDown className="w-4 h-4" />
                  ))}
              </Button>

              {/* Additional Menu Submenu */}
              {sidebarOpen && additionalMenuOpen && (
                <div className="ml-4 mt-1 space-y-1">
                  {additionalMenuItems.map((item) => {
                    const isActive =
                      pathname === item.href || pathname?.startsWith(item.href + '/');
                    return (
                      <Link key={item.name} href={item.href}>
                        <div
                          className={cn(
                            'group flex items-center px-2 py-2 text-sm font-medium rounded-lg transition-all duration-200',
                            isActive
                              ? 'bg-primary text-primary-foreground shadow-sm'
                              : 'text-muted-foreground hover:text-foreground hover:bg-accent',
                          )}
                        >
                          <item.icon className="flex-shrink-0 w-4 h-4 mr-3" />
                          <span className="truncate">{item.name}</span>
                        </div>
                      </Link>
                    );
                  })}
                </div>
              )}
            </div>
          )}
        </nav>

        {/* User section with theme toggle, notifications, and logout */}
        <div className="border-t border-border">
          {sidebarOpen ? (
            <div className="p-4 space-y-3">
              {/* User Info */}
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-primary-foreground" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-foreground truncate">
                    {user?.name || 'Admin User'}
                  </p>
                  <p className="text-xs text-muted-foreground truncate">
                    {user?.email || '<EMAIL>'}
                  </p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-between">
                {/* Theme Toggle */}
                <Button variant="ghost" size="icon" onClick={handleThemeChange}>
                  {getThemeIcon()}
                </Button>

                {/* Notifications */}
                <Button variant="ghost" size="icon" className="relative">
                  <Bell className="w-4 h-4" />
                  {unreadNotifications > 0 && (
                    <Badge
                      variant="destructive"
                      className="absolute -top-1 -right-1 w-4 h-4 p-0 flex items-center justify-center text-xs"
                    >
                      {unreadNotifications > 9 ? '9+' : unreadNotifications}
                    </Badge>
                  )}
                </Button>

                {/* Logout */}
                <Button variant="ghost" size="icon" onClick={() => logout()}>
                  <LogOut className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="p-2 space-y-2">
              {/* Collapsed user avatar */}
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center mx-auto">
                <User className="w-4 h-4 text-primary-foreground" />
              </div>

              {/* Collapsed action buttons */}
              <div className="flex flex-col items-center space-y-1">
                <Button variant="ghost" size="icon" onClick={handleThemeChange}>
                  {getThemeIcon()}
                </Button>
                <Button variant="ghost" size="icon" className="relative">
                  <Bell className="w-4 h-4" />
                  {unreadNotifications > 0 && (
                    <Badge
                      variant="destructive"
                      className="absolute -top-1 -right-1 w-3 h-3 p-0 flex items-center justify-center text-xs"
                    >
                      {unreadNotifications > 9 ? '9+' : unreadNotifications}
                    </Badge>
                  )}
                </Button>
                <Button variant="ghost" size="icon" onClick={() => logout()}>
                  <LogOut className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};
