import React, { useState } from "react";
import { Package, Tag } from "lucide-react";

interface Product {
  id: number;
  external_id: string;
  title: string;
  description?: string;
  description_html?: string;
  handle?: string;
  price: number;
  compare_at_price?: number;
  inventory_quantity: number;
  sku?: string;
  barcode?: string;
  weight?: number;
  weight_unit?: string;
  status: string;
  product_type?: string;
  vendor?: string;
  tags?: string[];
  images?: string[];
  variants?: any[];
  store_id: number;
  order_count?: number; // <-- Add order count
}

interface ProductCardProps {
  product: Product;
  onClick?: (product: Product) => void;
  viewMode?: "grid" | "list";
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onClick,
  viewMode = "grid",
}) => {
  const [imageError, setImageError] = useState(false);

  const handleCardClick = () => {
    if (onClick) {
      onClick(product);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800";
      case "draft":
        return "bg-yellow-100 text-yellow-800";
      case "archived":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getInventoryStatus = (quantity: number) => {
    if (quantity === 0) {
      return { color: "text-red-500", label: "Out of Stock" };
    } else if (quantity < 10) {
      return { color: "text-yellow-500", label: "Low Stock" };
    } else {
      return { color: "text-green-500", label: "In Stock" };
    }
  };

  const inventoryStatus = getInventoryStatus(product.inventory_quantity);
  const primaryImage =
    product.images && product.images.length > 0 ? product.images[0] : null;

  if (viewMode === "list") {
    return (
      <div
        className="bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer flex items-center p-4"
        onClick={handleCardClick}
      >
        <div className="relative w-24 h-24 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
          {primaryImage && !imageError ? (
            <img
              src={primaryImage}
              alt={product.title}
              className="w-full h-full object-cover"
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
              <Package className="text-gray-400" size={32} />
            </div>
          )}
        </div>
        <div className="ml-4 flex-grow">
          <h3 className="font-semibold text-lg text-gray-900 line-clamp-1">
            {product.title}
          </h3>
          <p className="text-sm text-gray-600">by {product.vendor}</p>
          <div className="flex items-center text-xs text-gray-500 mt-1">
            <Tag size={12} className="mr-1" />
            SKU: {product.sku || "N/A"}
          </div>
        </div>
        <div className="w-32 text-center">
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
              product.status
            )}`}
          >
            {product.status.charAt(0).toUpperCase() + product.status.slice(1)}
          </span>
        </div>
        <div className="w-32 text-center">
          <div className={`font-medium ${inventoryStatus.color}`}>
            {inventoryStatus.label}
          </div>
          <div className="text-sm text-gray-500">
            {product.inventory_quantity} units
          </div>
        </div>
        <div className="w-24 text-center font-medium">
          {product.order_count ?? 0}
        </div>
        <div className="w-24 text-right font-bold text-lg">
          ${product.price.toFixed(2)}
        </div>
        <div className="w-24 text-center">
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleCardClick();
            }}
            className="text-blue-600 hover:text-blue-800"
          >
            Details
          </button>
        </div>
      </div>
    );
  }

  // Grid view
  return (
    <div
      className="bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:-translate-y-1 flex flex-col"
      onClick={handleCardClick}
      style={{ minHeight: 420 }}
    >
      <div className="relative h-48 bg-gray-100 rounded-t-lg overflow-hidden">
        {primaryImage && !imageError ? (
          <img
            src={primaryImage}
            alt={product.title}
            className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
            <Package className="text-gray-400" size={48} />
          </div>
        )}
        <div className="absolute top-2 left-2">
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
              product.status
            )}`}
          >
            {product.status.charAt(0).toUpperCase() + product.status.slice(1)}
          </span>
        </div>
      </div>
      <div className="p-4 flex flex-col justify-between flex-1">
        <div>
          <h3 className="font-semibold text-lg text-gray-900 line-clamp-2 mb-1">
            {product.title}
          </h3>
          {product.vendor && (
            <p className="text-sm text-gray-600">by {product.vendor}</p>
          )}
        </div>
        <div className="mt-2">
          <div className="flex items-center justify-between mb-2">
            <div className={`font-medium ${inventoryStatus.color}`}>
              {inventoryStatus.label}
            </div>
            <div className="text-sm text-gray-500">
              {product.inventory_quantity} units
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">Orders:</div>
            <div className="text-sm font-bold text-gray-900">
              {product.order_count ?? 0}
            </div>
          </div>
          <div className="text-xl font-bold text-gray-900 mt-2">
            ${product.price.toFixed(2)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
