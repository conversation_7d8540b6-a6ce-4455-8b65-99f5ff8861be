'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useCompany } from '@/contexts/CompanyContext';
import { Building2, MapPin, Phone, Globe, Clock, Users, ArrowRight, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface CompanySetupProps {
  onComplete?: () => void;
}

const CompanySetup: React.FC<CompanySetupProps> = ({ onComplete }) => {
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    address: '',
    phone: '',
    website: '',
    businessHours: '',
    serviceTypes: [] as string[],
  });
  const { createCompany } = useCompany();
  const router = useRouter();

  const serviceOptions = [
    'Plumbing', 'Electrical', 'HVAC', 'Cleaning', 'Landscaping',
    'Handyman', 'Pest Control', 'Roofing', 'Painting', 'Flooring'
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const toggleService = (service: string) => {
    setFormData(prev => ({
      ...prev,
      serviceTypes: prev.serviceTypes.includes(service)
        ? prev.serviceTypes.filter(s => s !== service)
        : [...prev.serviceTypes, service]
    }));
  };

  const handleNext = () => {
    if (step < 3) {
      setStep(step + 1);
    }
  };

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      await createCompany({
        name: formData.name,
        description: formData.description,
        address: formData.address,
        phone: formData.phone,
        website: formData.website,
        business_hours: formData.businessHours,
        service_types: formData.serviceTypes,
      });
      
      if (onComplete) {
        onComplete();
      } else {
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('Failed to create company:', error);
    } finally {
      setLoading(false);
    }
  };

  const isStepValid = () => {
    switch (step) {
      case 1:
        return formData.name.trim() && formData.description.trim();
      case 2:
        return formData.address.trim() && formData.phone.trim();
      case 3:
        return formData.serviceTypes.length > 0;
      default:
        return false;
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center p-4 sm:p-6 lg:p-8 bg-gradient-to-br from-green-50 via-white to-blue-50">
      <Card className="w-full max-w-2xl animate-fade-in hover-lift" variant="elevated">
        <CardHeader className="text-center pb-6">
          <div className="mx-auto w-16 h-16 bg-gradient-to-r from-green-600 to-blue-600 rounded-full flex items-center justify-center mb-4 animate-bounce-in">
            <Building2 className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
            Set Up Your Company
          </CardTitle>
          <CardDescription className="text-muted-foreground text-lg">
            Let's get your home service business configured
          </CardDescription>
          
          {/* Progress indicator */}
          <div className="flex justify-center mt-6">
            <div className="flex items-center space-x-4">
              {[1, 2, 3].map((stepNum) => (
                <div key={stepNum} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 ${
                    stepNum <= step 
                      ? 'bg-primary text-primary-foreground' 
                      : 'bg-muted text-muted-foreground'
                  }`}>
                    {stepNum < step ? <Check className="w-4 h-4" /> : stepNum}
                  </div>
                  {stepNum < 3 && (
                    <div className={`w-12 h-0.5 mx-2 transition-all duration-300 ${
                      stepNum < step ? 'bg-primary' : 'bg-muted'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {/* Step 1: Basic Information */}
          {step === 1 && (
            <div className="space-y-6 animate-slide-up">
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold mb-2">Basic Information</h3>
                <p className="text-muted-foreground">Tell us about your business</p>
              </div>

              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="name" className="text-sm font-medium">Company Name</Label>
                  <div className="relative">
                    <Input
                      id="name"
                      placeholder="Your Company Name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="pl-10 h-12"
                    />
                    <Building2 className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="description" className="text-sm font-medium">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Describe your services and what makes your business special..."
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Contact Information */}
          {step === 2 && (
            <div className="space-y-6 animate-slide-up">
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold mb-2">Contact Information</h3>
                <p className="text-muted-foreground">How can customers reach you?</p>
              </div>

              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="address" className="text-sm font-medium">Business Address</Label>
                  <div className="relative">
                    <Input
                      id="address"
                      placeholder="123 Main St, City, State 12345"
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      className="pl-10 h-12"
                    />
                    <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="phone" className="text-sm font-medium">Phone Number</Label>
                  <div className="relative">
                    <Input
                      id="phone"
                      placeholder="(*************"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="pl-10 h-12"
                    />
                    <Phone className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="website" className="text-sm font-medium">Website (Optional)</Label>
                  <div className="relative">
                    <Input
                      id="website"
                      placeholder="https://yourcompany.com"
                      value={formData.website}
                      onChange={(e) => handleInputChange('website', e.target.value)}
                      className="pl-10 h-12"
                    />
                    <Globe className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="hours" className="text-sm font-medium">Business Hours</Label>
                  <div className="relative">
                    <Input
                      id="hours"
                      placeholder="Mon-Fri 8AM-6PM, Sat 9AM-4PM"
                      value={formData.businessHours}
                      onChange={(e) => handleInputChange('businessHours', e.target.value)}
                      className="pl-10 h-12"
                    />
                    <Clock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Services */}
          {step === 3 && (
            <div className="space-y-6 animate-slide-up">
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold mb-2">Services Offered</h3>
                <p className="text-muted-foreground">Select the services your business provides</p>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {serviceOptions.map((service) => (
                  <Badge
                    key={service}
                    variant={formData.serviceTypes.includes(service) ? "default" : "outline"}
                    className={`cursor-pointer p-3 text-center justify-center transition-all duration-200 hover:scale-105 ${
                      formData.serviceTypes.includes(service) 
                        ? 'bg-primary text-primary-foreground' 
                        : 'hover:bg-muted'
                    }`}
                    onClick={() => toggleService(service)}
                  >
                    {service}
                  </Badge>
                ))}
              </div>

              {formData.serviceTypes.length > 0 && (
                <div className="text-center text-sm text-muted-foreground">
                  Selected {formData.serviceTypes.length} service{formData.serviceTypes.length !== 1 ? 's' : ''}
                </div>
              )}
            </div>
          )}

          {/* Navigation buttons */}
          <div className="flex justify-between mt-8 pt-6 border-t">
            <Button
              variant="outline"
              onClick={handleBack}
              disabled={step === 1}
              className="flex items-center gap-2"
            >
              Back
            </Button>

            {step < 3 ? (
              <Button
                onClick={handleNext}
                disabled={!isStepValid()}
                className="flex items-center gap-2"
              >
                Next
                <ArrowRight className="w-4 h-4" />
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                disabled={!isStepValid() || loading}
                loading={loading}
                loadingText="Creating company..."
                className="flex items-center gap-2"
              >
                Complete Setup
                <Check className="w-4 h-4" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CompanySetup;
