import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Eye, Edit, Save, Share, Download, Settings, ExternalLink } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';

interface WebFormPageHeaderProps {
  title: string;
  description?: string;
  category?: string;
  mode?: 'edit' | 'preview';
  formId?: string;
  onBack?: () => void;
  onSave?: () => void;
  onPreview?: () => void;
  onEdit?: () => void;
  onShare?: () => void;
  onExport?: () => void;
}

export const WebFormPageHeader: React.FC<WebFormPageHeaderProps> = ({
  title,
  description,
  category,
  mode,
  formId,
  onBack,
  onSave,
  onPreview,
  onEdit,
  onShare,
  onExport,
}) => {
  const router = useRouter();

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.push('/web-forms');
    }
  };

  const handlePreview = () => {
    if (onPreview) {
      onPreview();
    } else if (formId) {
      router.push(`/web-forms/${formId}/preview`);
    }
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit();
    } else if (formId) {
      router.push(`/web-forms/${formId}/edit`);
    }
  };

  return (
    <div className="bg-white/80 backdrop-blur-sm border-b">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={handleBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Forms
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-foreground">
                {title}
              </h1>
              {description && (
                <p className="text-muted-foreground text-sm mt-1">{description}</p>
              )}
              <div className="flex items-center gap-2 mt-1">
                {category && <Badge variant="secondary">{category}</Badge>}
                {mode === 'edit' && (
                  <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                    <Settings className="w-3 h-3 mr-1" />
                    Edit Mode
                  </Badge>
                )}
                {mode === 'preview' && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                    <Eye className="w-3 h-3 mr-1" />
                    Preview Mode
                  </Badge>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {mode === 'edit' && (
              <>
                <Button variant="outline" onClick={handlePreview}>
                  <Eye className="w-4 h-4 mr-2" />
                  Preview
                </Button>
                <Button onClick={onSave}>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </Button>
              </>
            )}
            {mode === 'preview' && (
              <>
                <Button variant="outline" size="sm" onClick={onShare}>
                  <Share className="w-4 h-4 mr-2" />
                  Share
                </Button>
                <Button variant="outline" size="sm" onClick={onExport}>
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
                <Button onClick={handleEdit}>
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Form
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
