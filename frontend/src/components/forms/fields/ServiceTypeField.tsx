"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { getCategoryById } from "@/data/services"; // Assuming this utility exists
import { Wrench } from "lucide-react"; // Example icon

interface ServiceTypeFieldProps {
  value: string;
  onChange: (value: string) => void;
  formData: Record<string, any>;
}

export function ServiceTypeField({ value, onChange, formData }: ServiceTypeFieldProps) {
  const categoryId = formData.service_category;
  const category = categoryId ? getCategoryById(categoryId) : null;

  if (!category) {
    return (
      <div className="text-muted-foreground text-sm">
        Please select a service category first.
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {category.subCategories.map((subCategory) => {
        const Icon = subCategory.icon || Wrench; // Fallback icon
        const isSelected = value === subCategory.id;

        return (
          <motion.div
            key={subCategory.id}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            <Card
              className={cn(
                "cursor-pointer transition-all duration-300 hover:shadow-md border-2",
                isSelected
                  ? "border-primary bg-accent shadow-sm"
                  : "border-border hover:border-primary/30"
              )}
              onClick={() => onChange(subCategory.id)}
            >
              <CardContent className="p-6 text-center">
                <div className="p-3 rounded-lg bg-background mx-auto mb-3 w-fit shadow-sm">
                  <Icon className="w-5 h-5 text-muted-foreground" />
                </div>
                <h3 className="font-semibold text-foreground mb-1">
                  {subCategory.name}
                </h3>
                <p className="text-xs text-muted-foreground">
                  {subCategory.options.length > 0
                    ? `${subCategory.options.length} options available`
                    : "Custom service"
                  }
                </p>
              </CardContent>
            </Card>
          </motion.div>
        );
      })}
    </div>
  );
}
