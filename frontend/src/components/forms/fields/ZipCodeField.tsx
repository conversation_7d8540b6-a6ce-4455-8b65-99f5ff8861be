'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MapPin, Truck, Building2 } from 'lucide-react'; // Added Building2 for company logo placeholder
import { cn } from '@/lib/utils';

interface ZipCodeFieldProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  companyName?: string; // New prop for customization
  companyAddress?: string; // New prop for customization
  headerTitle?: string; // New prop for customization
}

export function ZipCodeField({
  value,
  onChange,
  placeholder = 'Enter your zip code or postal code',
  className,
  companyName = 'HomeService Co.', // Default value
  companyAddress = '123 Service Lane, Cityville, ST 12345', // Default value
  headerTitle = 'HomeService Co.', // Default value
}: ZipCodeFieldProps) {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header with Logo and Company Info */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          <Building2 className="w-12 h-12 text-primary mr-2" />
          <h1 className="text-3xl font-bold text-foreground">{companyName}</h1>
        </div>
        <p className="text-muted-foreground text-sm">{companyAddress}</p>
      </div>

      {/* Main Content */}
      <Card className="p-6">
        <CardHeader className="text-center pb-4">
          <CardTitle className="text-2xl font-bold text-foreground">Make it a great day!</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-center gap-3 text-lg font-semibold text-muted-foreground">
            <Truck className="w-6 h-6 text-primary" />
            <span>Where are you?</span>
          </div>

          <p className="text-center text-muted-foreground mb-4">
            Enter your zip code or postal code so we can check if we provide service in your
            location.
          </p>

          <div>
            <Label htmlFor="zip-code-input" className="text-sm font-medium">
              Zip code*
            </Label>
            <div className="relative mt-1">
              <Input
                id="zip-code-input"
                type="text"
                placeholder={placeholder}
                value={value || ''}
                onChange={(e) => onChange(e.target.value)}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                pattern="[0-9]{5}(-[0-9]{4})?"
                maxLength={10}
                className={cn(
                  'w-full pl-10 pr-4 py-2 transition-all duration-200',
                  isFocused ? 'border-primary ring-2 ring-primary/20' : '',
                )}
              />
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            </div>
          </div>

          {/* Additional rich info fields could go here, conditionally rendered based on isFocused or another state */}
          {isFocused && (
            <div className="text-sm text-muted-foreground text-center">
              <p>
                We use your zip code to ensure service availability and provide accurate local
                pricing.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
