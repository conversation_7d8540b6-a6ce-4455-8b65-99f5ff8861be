"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { getCategoryById, getSubCategoryById } from "@/data/services"; // Assuming these utilities exist

interface ServiceOptionsFieldProps {
  value: string;
  onChange: (value: string) => void;
  formData: Record<string, any>;
}

export function ServiceOptionsField({ value, onChange, formData }: ServiceOptionsFieldProps) {
  const categoryId = formData.service_category;
  const subCategoryId = formData.service_type;

  const category = categoryId ? getCategoryById(categoryId) : null;
  const subCategory = subCategoryId ? getSubCategoryById(categoryId, subCategoryId) : null;

  if (!category || !subCategory) {
    return (
      <div className="text-muted-foreground text-sm">
        Please select a service category and type first.
      </div>
    );
  }

  const hasOptions = subCategory.options.length > 0;

  return (
    <div className="space-y-6">
      {hasOptions && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {subCategory.options.map((option) => {
            const isSelected = value === option.id;

            return (
              <motion.div
                key={option.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
              >
                <Button
                  variant={isSelected ? "default" : "outline"}
                  onClick={() => onChange(option.id)}
                  className={cn(
                    "w-full h-auto p-4 text-left justify-start transition-all duration-200",
                    isSelected
                      ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md"
                      : "hover:border-primary/50"
                  )}
                >
                  <div>
                    <div className="font-medium text-sm">
                      {option.name}
                    </div>
                    {option.description && (
                      <div className="text-xs opacity-80 mt-1">
                        {option.description}
                      </div>
                    )}
                  </div>
                </Button>
              </motion.div>
            );
          })}
        </div>
      )}

      <div>
        <label className="block text-sm font-medium text-foreground mb-3">
          {hasOptions
            ? "Additional details (optional)"
            : "Describe your service needs"
          }
        </label>
        <Textarea
          value={formData.description || ""}
          onChange={(e) => onChange(e.target.value)}
          placeholder={hasOptions
            ? "Tell us more about what you need help with..."
            : "Please describe your service requirements in detail..."
          }
          className="min-h-24 resize-none"
          rows={4}
        />
      </div>
    </div>
  );
}
