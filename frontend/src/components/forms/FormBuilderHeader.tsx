import { Button } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON><PERSON>, Setting<PERSON>, Eye } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { type FormTemplate } from '@/data/services';

interface FormBuilderHeaderProps {
  onSave: () => void;
  showPreview: boolean;
  togglePreview: (show: boolean) => void;
}

export const FormBuilderHeader: React.FC<FormBuilderHeaderProps> = ({ onSave, showPreview, togglePreview }) => {
  const router = useRouter();

  return (
    <header className="sticky top-0 z-40 bg-background/80 backdrop-blur-lg border-b border-border py-4">
      <div className="container mx-auto px-4 lg:px-6 flex items-center justify-between">
        {/* Left section: Back button */}
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <h1 className="text-xl font-semibold text-foreground">
            {showPreview ? 'Form Preview' : 'Form Builder'}
          </h1>
        </div>

        {/* Right section: Preview/Back to Builder and Save Form buttons */}
        <div className="flex gap-2">
          {showPreview ? (
            <Button variant="outline" onClick={() => togglePreview(false)}>
              Back to Builder
            </Button>
          ) : (
            <Button variant="outline" onClick={() => togglePreview(true)}>
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>
          )}
          <Button onClick={onSave} variant="gradient">
            <Settings className="w-4 h-4 mr-2" />
            Save Form
          </Button>
        </div>
      </div>
    </header>
  );
};
