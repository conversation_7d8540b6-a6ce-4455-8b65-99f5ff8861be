@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import 'tailwindcss';

@theme {
  /* Colors */
  --color-background: #ffffff;
  --color-foreground: #0f172a;

  --color-card: #ffffff;
  --color-card-foreground: #0f172a;

  --color-popover: #ffffff;
  --color-popover-foreground: #0f172a;

  /* Primary: Professional blue */
  --color-primary: #2563eb;
  --color-primary-foreground: #f8fafc;

  /* Secondary: Neutral gray */
  --color-secondary: #f1f5f9;
  --color-secondary-foreground: #0f172a;

  --color-muted: #f1f5f9;
  --color-muted-foreground: #64748b;

  /* Accent: Vibrant purple */
  --color-accent: #8b5cf6;
  --color-accent-foreground: #f8fafc;

  --color-destructive: #ef4444;
  --color-destructive-foreground: #f8fafc;

  --color-border: #e2e8f0;
  --color-input: #e2e8f0;
  --color-ring: #2563eb;

  /* Chart colors */
  --color-chart-1: #f97316;
  --color-chart-2: #06b6d4;
  --color-chart-3: #8b5cf6;
  --color-chart-4: #eab308;
  --color-chart-5: #ef4444;

  /* Border radius */
  --radius: 0.75rem;
}

@layer theme {
  .dark {
    /* Dark mode colors */
    --color-background: #0f172a;
    --color-foreground: #f8fafc;

    --color-card: #0f172a;
    --color-card-foreground: #f8fafc;

    --color-popover: #0f172a;
    --color-popover-foreground: #f8fafc;

    /* Primary: Brighter blue for dark mode */
    --color-primary: #3b82f6;
    --color-primary-foreground: #0f172a;

    /* Secondary: Darker gray */
    --color-secondary: #1e293b;
    --color-secondary-foreground: #f8fafc;

    --color-muted: #1e293b;
    --color-muted-foreground: #94a3b8;

    /* Accent: Adjusted for dark mode */
    --color-accent: #a855f7;
    --color-accent-foreground: #0f172a;

    --color-destructive: #f87171;
    --color-destructive-foreground: #0f172a;

    --color-border: #334155;
    --color-input: #1e293b;
    --color-ring: #3b82f6;

    /* Chart colors for dark mode */
    --color-chart-1: #fb923c;
    --color-chart-2: #22d3ee;
    --color-chart-3: #a855f7;
    --color-chart-4: #facc15;
    --color-chart-5: #f87171;
  }
}

/* Base styles */
@layer base {
  * {
    border-color: var(--color-border);
  }

  body {
    color: var(--color-foreground);
    background: var(--color-background);
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }
}

/* Custom animations */
@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

.animate-accordion-down {
  animation: accordion-down 0.2s ease-out;
}

.animate-accordion-up {
  animation: accordion-up 0.2s ease-out;
}
