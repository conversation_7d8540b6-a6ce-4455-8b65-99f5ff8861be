'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { FormRenderer } from '@/components/forms/FormRenderer';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { comprehensiveTemplates } from '@/data/comprehensive-templates';
import { FormTemplate } from '@/types/forms';
import { ArrowLeft, Eye, Edit, Copy } from 'lucide-react';
import Link from 'next/link';

const TemplatePreviewPage: React.FC = () => {
  const params = useParams();
  const templateId = params.templateId as string;
  const [template, setTemplate] = useState<FormTemplate | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (templateId) {
      const foundTemplate = comprehensiveTemplates.find(t => t.id === templateId);
      setTemplate(foundTemplate || null);
      setLoading(false);
    }
  }, [templateId]);

  const handleFormSubmit = (data: Record<string, any>) => {
    console.log('Template preview form submitted:', data);
    // This is just a preview, so we don't actually save the data
    alert('This is a template preview. Form submission is disabled.');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <h1 className="text-2xl font-bold text-foreground mb-2">Template Not Found</h1>
            <p className="text-muted-foreground mb-4">
              The template you're looking for doesn't exist.
            </p>
            <Link href="/web-forms/new">
              <Button>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Templates
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <div className="border-b bg-card/50 backdrop-blur-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/web-forms/new">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back
                </Button>
              </Link>
              <div>
                <h1 className="text-xl font-bold text-foreground">{template.name}</h1>
                <p className="text-sm text-muted-foreground">{template.description}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="capitalize">
                {template.category}
              </Badge>
              <Badge variant="secondary">
                <Eye className="w-3 h-3 mr-1" />
                Preview
              </Badge>
              <Link href={`/web-forms/new?template=${template.id}`}>
                <Button size="sm">
                  <Edit className="w-4 h-4 mr-2" />
                  Use Template
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Template Preview */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
              <Eye className="w-4 h-4" />
              <span className="font-medium">Template Preview Mode</span>
            </div>
            <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
              This is a preview of the template. Form submissions are disabled. Click "Use Template" to create a working form.
            </p>
          </div>

          <FormRenderer
            template={template}
            onSubmit={handleFormSubmit}
            disabled={true}
            customProps={{
              zip_code: {
                headerTitle: 'Your Local Service Pro',
              },
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default TemplatePreviewPage;
