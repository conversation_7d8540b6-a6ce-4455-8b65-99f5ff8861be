'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  FileText,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  DollarSign,
  Calendar,
  User,
  Eye,
  Edit,
  Send,
  Download,
  Copy,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  TrendingUp,
  Calculator
} from 'lucide-react';
import { useCompany } from '@/contexts/CompanyContext';
import { toast } from 'react-hot-toast';

interface Quote {
  id: string;
  quote_number: string;
  customer_id: string;
  customer_name: string;
  customer_email?: string;
  customer_phone?: string;
  service_address: string;
  services: Array<{
    id: string;
    name: string;
    description: string;
    quantity: number;
    unit_price: number;
    total: number;
  }>;
  subtotal: number;
  tax_rate: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  status: 'draft' | 'sent' | 'viewed' | 'accepted' | 'rejected' | 'expired';
  valid_until: string;
  created_at: string;
  sent_at?: string;
  viewed_at?: string;
  responded_at?: string;
  notes?: string;
  terms_conditions?: string;
  created_by: string;
}

export default function QuotesPage() {
  const router = useRouter();
  const { currentCompany } = useCompany();
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState('all');

  useEffect(() => {
    if (currentCompany) {
      loadQuotes();
    }
  }, [currentCompany]);

  const loadQuotes = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockQuotes: Quote[] = [
        {
          id: 'quote_001',
          quote_number: 'QUO-2024-001',
          customer_id: 'cust_001',
          customer_name: 'John Smith',
          customer_email: '<EMAIL>',
          customer_phone: '+****************',
          service_address: '123 Main St, Anytown, CA 12345',
          services: [
            {
              id: 'svc_001',
              name: 'Kitchen Sink Repair',
              description: 'Fix leaking kitchen sink and replace faucet',
              quantity: 1,
              unit_price: 185.00,
              total: 185.00
            },
            {
              id: 'svc_002',
              name: 'Garbage Disposal Installation',
              description: 'Install new garbage disposal unit',
              quantity: 1,
              unit_price: 250.00,
              total: 250.00
            }
          ],
          subtotal: 435.00,
          tax_rate: 8.25,
          tax_amount: 35.89,
          discount_amount: 0,
          total_amount: 470.89,
          status: 'accepted',
          valid_until: '2024-02-15',
          created_at: '2024-01-15T10:00:00Z',
          sent_at: '2024-01-15T10:30:00Z',
          viewed_at: '2024-01-15T14:20:00Z',
          responded_at: '2024-01-16T09:15:00Z',
          notes: 'Customer prefers morning appointments',
          created_by: 'Mike Johnson'
        },
        {
          id: 'quote_002',
          quote_number: 'QUO-2024-002',
          customer_id: 'cust_002',
          customer_name: 'Sarah Wilson',
          customer_email: '<EMAIL>',
          customer_phone: '+****************',
          service_address: '456 Oak Ave, Business City, CA 54321',
          services: [
            {
              id: 'svc_003',
              name: 'HVAC System Maintenance',
              description: 'Annual commercial HVAC system inspection and cleaning',
              quantity: 3,
              unit_price: 300.00,
              total: 900.00
            }
          ],
          subtotal: 900.00,
          tax_rate: 8.25,
          tax_amount: 74.25,
          discount_amount: 50.00,
          total_amount: 924.25,
          status: 'sent',
          valid_until: '2024-02-20',
          created_at: '2024-01-20T14:00:00Z',
          sent_at: '2024-01-20T14:30:00Z',
          viewed_at: '2024-01-21T09:45:00Z',
          notes: 'Commercial building - 3 units',
          created_by: 'David Brown'
        },
        {
          id: 'quote_003',
          quote_number: 'QUO-2024-003',
          customer_id: 'cust_003',
          customer_name: 'Mike Johnson',
          customer_email: '<EMAIL>',
          customer_phone: '+****************',
          service_address: '789 Pine St, Hometown, CA 67890',
          services: [
            {
              id: 'svc_004',
              name: 'Electrical Panel Upgrade',
              description: 'Upgrade electrical panel to 200 amp service',
              quantity: 1,
              unit_price: 1500.00,
              total: 1500.00
            }
          ],
          subtotal: 1500.00,
          tax_rate: 8.25,
          tax_amount: 123.75,
          discount_amount: 0,
          total_amount: 1623.75,
          status: 'draft',
          valid_until: '2024-02-25',
          created_at: '2024-01-25T11:00:00Z',
          notes: 'Requires permit - customer will handle',
          created_by: 'Sarah Wilson'
        }
      ];

      setQuotes(mockQuotes);
    } catch (error) {
      console.error('Failed to load quotes:', error);
      toast.error('Failed to load quotes');
    } finally {
      setLoading(false);
    }
  };

  const filteredQuotes = quotes.filter(quote => {
    const matchesSearch = 
      quote.quote_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      quote.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      quote.services.some(service => 
        service.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    
    const matchesTab = selectedTab === 'all' || quote.status === selectedTab;
    
    return matchesSearch && matchesTab;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'sent':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'viewed':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'accepted':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'expired':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Edit className="w-3 h-3" />;
      case 'sent':
        return <Send className="w-3 h-3" />;
      case 'viewed':
        return <Eye className="w-3 h-3" />;
      case 'accepted':
        return <CheckCircle className="w-3 h-3" />;
      case 'rejected':
        return <XCircle className="w-3 h-3" />;
      case 'expired':
        return <AlertCircle className="w-3 h-3" />;
      default:
        return <Clock className="w-3 h-3" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const calculateStats = () => {
    const totalValue = quotes.reduce((sum, quote) => sum + quote.total_amount, 0);
    const acceptedValue = quotes
      .filter(quote => quote.status === 'accepted')
      .reduce((sum, quote) => sum + quote.total_amount, 0);
    const acceptanceRate = quotes.length > 0 
      ? (quotes.filter(quote => quote.status === 'accepted').length / quotes.length) * 100
      : 0;

    return { totalValue, acceptedValue, acceptanceRate };
  };

  const stats = calculateStats();

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Quotes & Estimates</h1>
          <p className="text-gray-600">
            Create and manage professional quotes for your customers
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <TrendingUp className="w-4 h-4 mr-2" />
            Quote Analytics
          </Button>
          <Button onClick={() => router.push('/quotes/new')} variant="gradient">
            <Plus className="w-4 h-4 mr-2" />
            Create Quote
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Quotes</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{quotes.length}</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalValue)}</div>
            <p className="text-xs text-muted-foreground">
              All quotes combined
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Accepted Value</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.acceptedValue)}</div>
            <p className="text-xs text-muted-foreground">
              Won business
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Acceptance Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.acceptanceRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Quote to job conversion
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search quotes by number, customer, or service..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quotes List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Quotes</CardTitle>
            <Tabs value={selectedTab} onValueChange={setSelectedTab}>
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="draft">Draft</TabsTrigger>
                <TabsTrigger value="sent">Sent</TabsTrigger>
                <TabsTrigger value="accepted">Accepted</TabsTrigger>
                <TabsTrigger value="rejected">Rejected</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Quote #</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Services</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Valid Until</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredQuotes.map((quote) => (
                <TableRow 
                  key={quote.id}
                  className="cursor-pointer hover:bg-accent/5 transition-colors"
                  onClick={() => router.push(`/quotes/${quote.id}`)}
                >
                  <TableCell>
                    <div>
                      <p className="font-medium">{quote.quote_number}</p>
                      <p className="text-sm text-gray-600">
                        by {quote.created_by}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">{quote.customer_name}</p>
                      <p className="text-sm text-gray-600">{quote.customer_phone}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="text-sm">
                        {quote.services.length} service{quote.services.length !== 1 ? 's' : ''}
                      </p>
                      <p className="text-sm text-gray-600">
                        {quote.services[0]?.name}
                        {quote.services.length > 1 && ` +${quote.services.length - 1} more`}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium text-lg">
                      {formatCurrency(quote.total_amount)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(quote.status)}>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(quote.status)}
                        <span className="capitalize">{quote.status}</span>
                      </div>
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">
                      {new Date(quote.valid_until).toLocaleDateString()}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-600">
                      {new Date(quote.created_at).toLocaleDateString()}
                    </span>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" onClick={(e) => e.stopPropagation()}>
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-white border shadow-lg">
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/quotes/${quote.id}`);
                        }}>
                          <Eye className="w-4 h-4 mr-2" />
                          View Quote
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/quotes/${quote.id}/edit`);
                        }}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit Quote
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          // TODO: Implement send quote
                        }}>
                          <Send className="w-4 h-4 mr-2" />
                          Send Quote
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          // TODO: Implement download
                        }}>
                          <Download className="w-4 h-4 mr-2" />
                          Download PDF
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          // TODO: Implement duplication
                        }}>
                          <Copy className="w-4 h-4 mr-2" />
                          Duplicate Quote
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
