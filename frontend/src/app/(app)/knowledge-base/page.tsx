'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  BookOpen,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  FileText,
  Folder,
  Tag,
  Clock,
  Eye,
  Edit,
  Upload,
} from 'lucide-react';
import { useCompany } from '@/contexts/CompanyContext';

interface KnowledgeBaseItem {
  id: number;
  title: string;
  content: string;
  category: string;
  tags: string[];
  type: 'article' | 'faq' | 'procedure' | 'policy';
  status: 'published' | 'draft' | 'archived';
  views: number;
  created_at: string;
  updated_at: string;
  author: string;
}

const KnowledgeBasePage: React.FC = () => {
  const { currentCompany } = useCompany();
  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeBaseItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState('all');

  useEffect(() => {
    const fetchKnowledgeBase = async () => {
      if (!currentCompany) return;

      try {
        // TODO: Replace with actual API call
        // const response = await fetch(`/api/knowledge-base?company_id=${currentCompany.id}`);
        // const data = await response.json();

        // Mock data for now
        const mockData: KnowledgeBaseItem[] = [
          {
            id: 1,
            title: 'Emergency Plumbing Response Procedures',
            content: 'Step-by-step guide for handling emergency plumbing calls...',
            category: 'Procedures',
            tags: ['emergency', 'plumbing', 'response'],
            type: 'procedure',
            status: 'published',
            views: 45,
            created_at: '2024-01-15T10:00:00Z',
            updated_at: '2024-01-18T14:30:00Z',
            author: 'John Manager',
          },
          {
            id: 2,
            title: 'Common HVAC Issues and Solutions',
            content: 'Frequently asked questions about HVAC problems...',
            category: 'FAQ',
            tags: ['hvac', 'troubleshooting', 'maintenance'],
            type: 'faq',
            status: 'published',
            views: 78,
            created_at: '2024-01-12T09:15:00Z',
            updated_at: '2024-01-16T11:20:00Z',
            author: 'Sarah Tech',
          },
          {
            id: 3,
            title: 'Pricing Guidelines for Electrical Services',
            content: 'Standard pricing structure for electrical work...',
            category: 'Pricing',
            tags: ['electrical', 'pricing', 'guidelines'],
            type: 'policy',
            status: 'published',
            views: 32,
            created_at: '2024-01-10T16:45:00Z',
            updated_at: '2024-01-14T10:15:00Z',
            author: 'Mike Owner',
          },
          {
            id: 4,
            title: 'Customer Service Best Practices',
            content: 'Guidelines for providing excellent customer service...',
            category: 'Training',
            tags: ['customer-service', 'training', 'best-practices'],
            type: 'article',
            status: 'draft',
            views: 12,
            created_at: '2024-01-20T08:30:00Z',
            updated_at: '2024-01-20T08:30:00Z',
            author: 'Lisa Manager',
          },
        ];

        setKnowledgeItems(mockData);
      } catch (error) {
        console.error('Error fetching knowledge base:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchKnowledgeBase();
  }, [currentCompany]);

  const filteredItems = knowledgeItems.filter((item) => {
    const matchesSearch =
      item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    if (selectedTab === 'all') return matchesSearch;
    return matchesSearch && item.status === selectedTab;
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'article':
        return <FileText className="w-4 h-4 text-blue-600" />;
      case 'faq':
        return <BookOpen className="w-4 h-4 text-green-600" />;
      case 'procedure':
        return <Folder className="w-4 h-4 text-purple-600" />;
      case 'policy':
        return <FileText className="w-4 h-4 text-orange-600" />;
      default:
        return <FileText className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'archived':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-48 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex justify-between items-center animate-slide-down">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Knowledge Base
          </h1>
          <p className="text-muted-foreground mt-1">
            Manage your company's knowledge and training materials
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" className="hover-lift">
            <Upload className="w-4 h-4 mr-2" />
            Import
          </Button>
          <Button className="hover-lift">
            <Plus className="w-4 h-4 mr-2" />
            New Article
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Articles</p>
                <p className="text-2xl font-bold text-gray-900">{knowledgeItems.length}</p>
              </div>
              <div className="p-3 rounded-full bg-blue-100">
                <BookOpen className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Published</p>
                <p className="text-2xl font-bold text-gray-900">
                  {knowledgeItems.filter((item) => item.status === 'published').length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-green-100">
                <FileText className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Views</p>
                <p className="text-2xl font-bold text-gray-900">
                  {knowledgeItems.reduce((sum, item) => sum + item.views, 0)}
                </p>
              </div>
              <div className="p-3 rounded-full bg-purple-100">
                <Eye className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Categories</p>
                <p className="text-2xl font-bold text-gray-900">
                  {new Set(knowledgeItems.map((item) => item.category)).size}
                </p>
              </div>
              <div className="p-3 rounded-full bg-orange-100">
                <Folder className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search articles, FAQs, procedures..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline">
          <Filter className="w-4 h-4 mr-2" />
          Filter
        </Button>
      </div>

      {/* Knowledge Base Items */}
      <Card>
        <CardHeader>
          <CardTitle>Knowledge Base Items</CardTitle>
          <CardDescription>
            All your company's knowledge articles, procedures, and FAQs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList>
              <TabsTrigger value="all">All ({knowledgeItems.length})</TabsTrigger>
              <TabsTrigger value="published">
                Published ({knowledgeItems.filter((item) => item.status === 'published').length})
              </TabsTrigger>
              <TabsTrigger value="draft">
                Drafts ({knowledgeItems.filter((item) => item.status === 'draft').length})
              </TabsTrigger>
              <TabsTrigger value="archived">
                Archived ({knowledgeItems.filter((item) => item.status === 'archived').length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value={selectedTab} className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredItems.map((item) => (
                  <Card key={item.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-2">
                          {getTypeIcon(item.type)}
                          <Badge variant="outline" className="text-xs">
                            {item.type}
                          </Badge>
                        </div>
                        <Badge className={getStatusColor(item.status)}>{item.status}</Badge>
                      </div>
                      <CardTitle className="text-lg line-clamp-2">{item.title}</CardTitle>
                      <CardDescription className="line-clamp-2">{item.content}</CardDescription>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between text-sm text-gray-600">
                          <span>{item.category}</span>
                          <div className="flex items-center space-x-1">
                            <Eye className="w-3 h-3" />
                            <span>{item.views}</span>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-1">
                          {item.tags.slice(0, 3).map((tag) => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {item.tags.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{item.tags.length - 3}
                            </Badge>
                          )}
                        </div>

                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>By {item.author}</span>
                          <span>{new Date(item.updated_at).toLocaleDateString()}</span>
                        </div>

                        <div className="flex space-x-2 pt-2">
                          <Button size="sm" variant="outline" className="flex-1">
                            <Eye className="w-3 h-3 mr-1" />
                            View
                          </Button>
                          <Button size="sm" variant="outline" className="flex-1">
                            <Edit className="w-3 h-3 mr-1" />
                            Edit
                          </Button>
                          <Button size="sm" variant="ghost">
                            <MoreHorizontal className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {filteredItems.length === 0 && (
                <div className="text-center py-12">
                  <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No articles found</h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm
                      ? 'Try adjusting your search terms'
                      : 'Start building your knowledge base by creating your first article'}
                  </p>
                  {!searchTerm && (
                    <Button>
                      <Plus className="w-4 h-4 mr-2" />
                      Create Article
                    </Button>
                  )}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default KnowledgeBasePage;
