'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loading<PERSON>pinner, LoadingCard } from '@/components/ui/loading';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
} from 'recharts';
import {
  Phone,
  Calendar,
  TrendingUp,
  Clock,
  CheckCircle,
  DollarSign,
  Bot,
  MessageSquare,
  BookOpen,
  Zap,
  ArrowUpRight,
  Activity,
} from 'lucide-react';
import { useCompany } from '@/contexts/CompanyContext';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';

const Dashboard: React.FC = () => {
  const { currentCompany } = useCompany();
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!currentCompany) return;

      try {
        // Mock data for now
        const mockData = {
          overview: {
            total_calls: 1234,
            total_bookings: 89,
            total_revenue: 28450,
            customer_satisfaction: 4.8,
            active_agents: 5,
            knowledge_base_items: 156,
            web_forms: 8,
            chat_sessions: 342,
          },
          trends: {
            calls_trend: [
              { name: 'Mon', calls: 24, answered: 20, bookings: 5 },
              { name: 'Tue', calls: 32, answered: 28, bookings: 8 },
              { name: 'Wed', calls: 18, answered: 16, bookings: 3 },
              { name: 'Thu', calls: 45, answered: 40, bookings: 12 },
              { name: 'Fri', calls: 38, answered: 35, bookings: 9 },
              { name: 'Sat', calls: 22, answered: 18, bookings: 4 },
              { name: 'Sun', calls: 15, answered: 12, bookings: 2 },
            ],
            revenue_trend: [
              { name: 'Jan', revenue: 4000 },
              { name: 'Feb', revenue: 3000 },
              { name: 'Mar', revenue: 5000 },
              { name: 'Apr', revenue: 4500 },
              { name: 'May', revenue: 6000 },
              { name: 'Jun', revenue: 5500 },
            ],
          },
          agent_performance: [
            { name: 'AI Agent 1', calls: 156, satisfaction: 4.9, status: 'active' },
            { name: 'AI Agent 2', calls: 134, satisfaction: 4.7, status: 'active' },
            { name: 'AI Agent 3', calls: 98, satisfaction: 4.8, status: 'active' },
          ],
          recent_activities: [
            { type: 'call', customer: 'John Smith', time: '2 minutes ago', status: 'completed' },
            {
              type: 'booking',
              customer: 'Sarah Johnson',
              time: '15 minutes ago',
              status: 'confirmed',
            },
            { type: 'chat', customer: 'Mike Wilson', time: '1 hour ago', status: 'resolved' },
          ],
        };

        setDashboardData(mockData);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [currentCompany]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4 mb-6">
          <LoadingSpinner size="lg" />
          <div className="space-y-2">
            <div className="h-6 bg-muted rounded w-48 animate-pulse"></div>
            <div className="h-4 bg-muted rounded w-32 animate-pulse"></div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} variant="elevated" className="animate-pulse">
              <LoadingCard />
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} variant="elevated" className="animate-pulse">
              <LoadingCard />
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const stats = [
    {
      title: 'Total Calls',
      value: dashboardData?.overview.total_calls?.toLocaleString() || '0',
      change: '+12%',
      icon: Phone,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Active Agents',
      value: dashboardData?.overview.active_agents?.toString() || '0',
      change: '+2',
      icon: Bot,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Bookings',
      value: dashboardData?.overview.total_bookings?.toString() || '0',
      change: '+15%',
      icon: Calendar,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      title: 'Revenue',
      value: `$${dashboardData?.overview.total_revenue?.toLocaleString() || '0'}`,
      change: '+23%',
      icon: DollarSign,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
  ];

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex justify-between items-center animate-slide-down">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Dashboard
          </h1>
          <p className="text-muted-foreground mt-1">
            Welcome back to {currentCompany?.name || 'your company'}! Here's what's happening today.
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            className="hover-lift border-primary/20 text-primary hover:bg-primary/10"
          >
            <Clock className="w-4 h-4 mr-2" />
            Last 7 days
          </Button>
          <Button
            variant="outline"
            className="hover-lift border-primary/20 text-primary hover:bg-primary/10"
          >
            <TrendingUp className="w-4 h-4 mr-2" />
            View Reports
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card
            key={index}
            variant="elevated"
            className={cn(
              'group cursor-pointer transition-all duration-300 hover:scale-105',
              'animate-fade-in',
            )}
            style={{ animationDelay: `${index * 100}ms` }}
            onClick={() => {
              // Navigate based on stat type
              if (stat.title === 'Active Agents') {
                router.push('/agents');
              } else if (stat.title === 'Total Calls') {
                router.push('/conversations');
              } else if (stat.title === 'Bookings') {
                router.push('/bookings');
              } else if (stat.title === 'Revenue') {
                router.push('/analytics');
              }
            }}
          >
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                  <p className="text-3xl font-bold text-foreground tracking-tight">{stat.value}</p>
                  <div className="flex items-center space-x-1">
                    <ArrowUpRight className="w-4 h-4 text-success" />
                    <p className="text-sm text-success font-medium">{stat.change} from last week</p>
                  </div>
                </div>
                <div
                  className={cn(
                    'p-4 rounded-xl transition-all duration-300 group-hover:scale-110',
                    stat.bgColor,
                  )}
                >
                  <stat.icon className={cn('w-7 h-7', stat.color)} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Additional Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          {
            title: 'Knowledge Base',
            value: dashboardData?.overview.knowledge_base_items || 0,
            subtitle: 'Items available',
            icon: BookOpen,
            color: 'text-blue-600',
            bgColor: 'bg-blue-100',
          },
          {
            title: 'Web Forms',
            value: dashboardData?.overview.web_forms || 0,
            subtitle: 'Active forms',
            icon: Zap,
            color: 'text-indigo-600',
            bgColor: 'bg-indigo-100',
          },
          {
            title: 'Chat Sessions',
            value: dashboardData?.overview.chat_sessions || 0,
            subtitle: 'This month',
            icon: MessageSquare,
            color: 'text-pink-600',
            bgColor: 'bg-pink-100',
          },
          {
            title: 'Satisfaction',
            value: `${dashboardData?.overview.customer_satisfaction || 0}/5`,
            subtitle: 'Average rating',
            icon: CheckCircle,
            color: 'text-success',
            bgColor: 'bg-success/10',
          },
        ].map((stat, index) => (
          <Card
            key={stat.title}
            variant="interactive"
            className={cn('group transition-all duration-300 hover:scale-105', 'animate-slide-up')}
            style={{ animationDelay: `${(index + 4) * 100}ms` }}
          >
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                  <p className="text-3xl font-bold text-foreground tracking-tight">{stat.value}</p>
                  <p
                    className="text-sm font-medium"
                    style={{ color: stat.color.replace('text-', '') }}
                  >
                    {stat.subtitle}
                  </p>
                </div>
                <div
                  className={cn(
                    'p-4 rounded-xl transition-all duration-300 group-hover:scale-110 group-hover:rotate-3',
                    stat.bgColor,
                  )}
                >
                  <stat.icon className={cn('w-7 h-7', stat.color)} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Activity Chart */}
        <Card variant="elevated" className="animate-fade-in" style={{ animationDelay: '800ms' }}>
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl font-semibold">Activity Overview</CardTitle>
                <CardDescription className="text-muted-foreground">
                  Daily calls, bookings, and interactions
                </CardDescription>
              </div>
              <div className="p-2 bg-primary/10 rounded-lg">
                <Activity className="w-5 h-5 text-primary" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <ResponsiveContainer width="100%" height={320}>
              <BarChart
                data={dashboardData?.trends.calls_trend || []}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
                <XAxis
                  dataKey="name"
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  }}
                />
                <Bar
                  dataKey="calls"
                  fill="hsl(var(--primary))"
                  name="Calls"
                  radius={[4, 4, 0, 0]}
                />
                <Bar
                  dataKey="answered"
                  fill="hsl(var(--success))"
                  name="Answered"
                  radius={[4, 4, 0, 0]}
                />
                <Bar
                  dataKey="bookings"
                  fill="hsl(var(--warning))"
                  name="Bookings"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Revenue Chart */}
        <Card variant="elevated" className="animate-fade-in" style={{ animationDelay: '900ms' }}>
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl font-semibold">Revenue Trend</CardTitle>
                <CardDescription className="text-muted-foreground">
                  Monthly revenue performance
                </CardDescription>
              </div>
              <div className="p-2 bg-success/10 rounded-lg">
                <TrendingUp className="w-5 h-5 text-success" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <ResponsiveContainer width="100%" height={320}>
              <LineChart
                data={dashboardData?.trends.revenue_trend || []}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
                <XAxis
                  dataKey="name"
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="revenue"
                  stroke="hsl(var(--primary))"
                  strokeWidth={3}
                  dot={{ fill: 'hsl(var(--primary))', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: 'hsl(var(--primary))', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Bottom Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Agent Performance */}
        <Card>
          <CardHeader>
            <CardTitle>AI Agent Performance</CardTitle>
            <CardDescription>Your AI agents' activity and ratings</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData?.agent_performance?.map((agent: any, index: number) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 border border-gray-100 rounded-lg hover:bg-accent/5 cursor-pointer transition-colors"
                  onClick={() => router.push(`/agents/${agent.id || `agent-${index + 1}`}`)}
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <Bot className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{agent.name}</p>
                      <p className="text-sm text-gray-600">{agent.calls} calls handled</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={agent.status === 'active' ? 'default' : 'secondary'}>
                      {agent.status}
                    </Badge>
                    <p className="text-sm text-gray-600 mt-1">{agent.satisfaction}/5 rating</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activities</CardTitle>
            <CardDescription>Latest customer interactions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData?.recent_activities?.map((activity: any, index: number) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/5 cursor-pointer transition-colors"
                  onClick={() => {
                    // Navigate based on activity type
                    if (activity.type === 'call') {
                      router.push('/conversations');
                    } else if (activity.type === 'booking') {
                      router.push('/bookings');
                    } else if (activity.type === 'chat') {
                      router.push('/conversations');
                    }
                  }}
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                      {activity.type === 'call' && <Phone className="w-5 h-5 text-gray-600" />}
                      {activity.type === 'booking' && (
                        <Calendar className="w-5 h-5 text-gray-600" />
                      )}
                      {activity.type === 'chat' && (
                        <MessageSquare className="w-5 h-5 text-gray-600" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{activity.customer}</p>
                      <p className="text-sm text-gray-600 capitalize">{activity.type}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge
                      variant={
                        activity.status === 'completed' ||
                        activity.status === 'confirmed' ||
                        activity.status === 'resolved'
                          ? 'default'
                          : 'secondary'
                      }
                    >
                      {activity.status}
                    </Badge>
                    <p className="text-sm text-gray-600 mt-1">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
