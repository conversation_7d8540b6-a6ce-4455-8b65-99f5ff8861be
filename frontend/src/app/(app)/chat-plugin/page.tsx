'use client';

import React, { useState } from 'react';
import { useCompany } from '@/contexts/CompanyContext';
import { MessageSquare, Code, Palette, Settings, Eye, Copy, Check, Download, Globe, Smartphone, Monitor } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { toast } from 'react-hot-toast';

interface ChatPluginConfig {
  enabled: boolean;
  widget_title: string;
  welcome_message: string;
  placeholder_text: string;
  primary_color: string;
  position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  size: 'small' | 'medium' | 'large';
  show_agent_avatar: boolean;
  show_typing_indicator: boolean;
  enable_file_upload: boolean;
  enable_emoji: boolean;
  business_hours_only: boolean;
  offline_message: string;
}

const ChatPluginPage: React.FC = () => {
  const [config, setConfig] = useState<ChatPluginConfig>({
    enabled: true,
    widget_title: 'Chat with us',
    welcome_message: 'Hi! How can we help you today?',
    placeholder_text: 'Type your message...',
    primary_color: '#3B82F6',
    position: 'bottom-right',
    size: 'medium',
    show_agent_avatar: true,
    show_typing_indicator: true,
    enable_file_upload: true,
    enable_emoji: true,
    business_hours_only: false,
    offline_message: 'We\'re currently offline. Please leave a message and we\'ll get back to you soon!'
  });
  
  const [copied, setCopied] = useState(false);
  const [saving, setSaving] = useState(false);
  const { currentCompany } = useCompany();

  const handleConfigChange = (field: keyof ChatPluginConfig, value: any) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  const generateEmbedCode = () => {
    const companyId = currentCompany?.id || 'YOUR_COMPANY_ID';
    return `<!-- HomeService AI Chat Widget -->
<script>
  window.HomeServiceChat = {
    companyId: '${companyId}',
    config: ${JSON.stringify(config, null, 2)}
  };
</script>
<script src="https://cdn.homeservice.ai/chat-widget.js" async></script>`;
  };

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(generateEmbedCode());
      setCopied(true);
      toast.success('Code copied to clipboard!');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy code');
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      // TODO: Save configuration to API
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      toast.success('Chat plugin configuration saved!');
    } catch (error) {
      toast.error('Failed to save configuration');
    } finally {
      setSaving(false);
    }
  };

  const previewWidget = () => {
    const positionClasses = {
      'bottom-right': 'bottom-4 right-4',
      'bottom-left': 'bottom-4 left-4',
      'top-right': 'top-4 right-4',
      'top-left': 'top-4 left-4'
    };

    const sizeClasses = {
      'small': 'w-80 h-96',
      'medium': 'w-96 h-[500px]',
      'large': 'w-[420px] h-[600px]'
    };

    return (
      <div className={`fixed ${positionClasses[config.position]} z-50 ${sizeClasses[config.size]} bg-white rounded-lg shadow-2xl border`}>
        <div 
          className="p-4 rounded-t-lg text-white flex items-center justify-between"
          style={{ backgroundColor: config.primary_color }}
        >
          <div className="flex items-center gap-3">
            {config.show_agent_avatar && (
              <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                <MessageSquare className="w-4 h-4" />
              </div>
            )}
            <h3 className="font-semibold">{config.widget_title}</h3>
          </div>
          <button className="text-white/80 hover:text-white">×</button>
        </div>
        
        <div className="p-4 h-full flex flex-col">
          <div className="flex-1 space-y-3">
            <div className="bg-gray-100 rounded-lg p-3 max-w-[80%]">
              <p className="text-sm">{config.welcome_message}</p>
            </div>
            {config.show_typing_indicator && (
              <div className="bg-gray-100 rounded-lg p-3 max-w-[80%]">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            )}
          </div>
          
          <div className="border-t pt-3">
            <div className="flex items-center gap-2">
              <input 
                type="text" 
                placeholder={config.placeholder_text}
                className="flex-1 p-2 border rounded-lg text-sm"
                disabled
              />
              <button 
                className="p-2 rounded-lg text-white"
                style={{ backgroundColor: config.primary_color }}
              >
                →
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 animate-slide-down">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Chat Plugin
          </h1>
          <p className="text-muted-foreground">
            Configure and embed a chat widget on your website
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => window.open('/chat-plugin/preview', '_blank')}>
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
          <Button onClick={handleSave} disabled={saving} loading={saving} loadingText="Saving...">
            <Settings className="w-4 h-4 mr-2" />
            Save Configuration
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configuration Panel */}
        <div className="space-y-6">
          <Tabs defaultValue="appearance" className="space-y-6">
            <TabsList className="animate-slide-up stagger-1">
              <TabsTrigger value="appearance" className="flex items-center gap-2">
                <Palette className="w-4 h-4" />
                Appearance
              </TabsTrigger>
              <TabsTrigger value="behavior" className="flex items-center gap-2">
                <Settings className="w-4 h-4" />
                Behavior
              </TabsTrigger>
              <TabsTrigger value="embed" className="flex items-center gap-2">
                <Code className="w-4 h-4" />
                Embed Code
              </TabsTrigger>
            </TabsList>

            {/* Appearance Settings */}
            <TabsContent value="appearance" className="space-y-6">
              <Card className="animate-slide-up stagger-2" variant="elevated">
                <CardHeader>
                  <CardTitle>Widget Appearance</CardTitle>
                  <CardDescription>
                    Customize how your chat widget looks
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">Widget Title</Label>
                      <Input
                        id="title"
                        value={config.widget_title}
                        onChange={(e) => handleConfigChange('widget_title', e.target.value)}
                        placeholder="Chat with us"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="color">Primary Color</Label>
                      <div className="flex gap-2">
                        <Input
                          id="color"
                          type="color"
                          value={config.primary_color}
                          onChange={(e) => handleConfigChange('primary_color', e.target.value)}
                          className="w-16 h-10 p-1"
                        />
                        <Input
                          value={config.primary_color}
                          onChange={(e) => handleConfigChange('primary_color', e.target.value)}
                          placeholder="#3B82F6"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="welcome">Welcome Message</Label>
                    <Textarea
                      id="welcome"
                      value={config.welcome_message}
                      onChange={(e) => handleConfigChange('welcome_message', e.target.value)}
                      placeholder="Hi! How can we help you today?"
                      rows={2}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="placeholder">Input Placeholder</Label>
                    <Input
                      id="placeholder"
                      value={config.placeholder_text}
                      onChange={(e) => handleConfigChange('placeholder_text', e.target.value)}
                      placeholder="Type your message..."
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Position</Label>
                      <Select value={config.position} onValueChange={(value: any) => handleConfigChange('position', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="bottom-right">Bottom Right</SelectItem>
                          <SelectItem value="bottom-left">Bottom Left</SelectItem>
                          <SelectItem value="top-right">Top Right</SelectItem>
                          <SelectItem value="top-left">Top Left</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Size</Label>
                      <Select value={config.size} onValueChange={(value: any) => handleConfigChange('size', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="small">Small</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="large">Large</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Behavior Settings */}
            <TabsContent value="behavior" className="space-y-6">
              <Card className="animate-slide-up stagger-2" variant="elevated">
                <CardHeader>
                  <CardTitle>Widget Behavior</CardTitle>
                  <CardDescription>
                    Configure how your chat widget behaves
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Show Agent Avatar</Label>
                        <p className="text-sm text-muted-foreground">Display agent profile picture in messages</p>
                      </div>
                      <Switch
                        checked={config.show_agent_avatar}
                        onCheckedChange={(checked) => handleConfigChange('show_agent_avatar', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Typing Indicator</Label>
                        <p className="text-sm text-muted-foreground">Show when agent is typing</p>
                      </div>
                      <Switch
                        checked={config.show_typing_indicator}
                        onCheckedChange={(checked) => handleConfigChange('show_typing_indicator', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>File Upload</Label>
                        <p className="text-sm text-muted-foreground">Allow customers to upload files</p>
                      </div>
                      <Switch
                        checked={config.enable_file_upload}
                        onCheckedChange={(checked) => handleConfigChange('enable_file_upload', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Emoji Support</Label>
                        <p className="text-sm text-muted-foreground">Enable emoji picker in chat</p>
                      </div>
                      <Switch
                        checked={config.enable_emoji}
                        onCheckedChange={(checked) => handleConfigChange('enable_emoji', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Business Hours Only</Label>
                        <p className="text-sm text-muted-foreground">Only show widget during business hours</p>
                      </div>
                      <Switch
                        checked={config.business_hours_only}
                        onCheckedChange={(checked) => handleConfigChange('business_hours_only', checked)}
                      />
                    </div>
                  </div>

                  {config.business_hours_only && (
                    <div className="space-y-2">
                      <Label htmlFor="offline">Offline Message</Label>
                      <Textarea
                        id="offline"
                        value={config.offline_message}
                        onChange={(e) => handleConfigChange('offline_message', e.target.value)}
                        placeholder="We're currently offline..."
                        rows={2}
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Embed Code */}
            <TabsContent value="embed" className="space-y-6">
              <Card className="animate-slide-up stagger-2" variant="elevated">
                <CardHeader>
                  <CardTitle>Embed Code</CardTitle>
                  <CardDescription>
                    Copy this code and paste it into your website's HTML
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="relative">
                    <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
                      <code>{generateEmbedCode()}</code>
                    </pre>
                    <Button
                      variant="outline"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={handleCopyCode}
                    >
                      {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                    </Button>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" onClick={handleCopyCode}>
                      <Copy className="w-4 h-4 mr-2" />
                      Copy Code
                    </Button>
                    <Button variant="outline">
                      <Download className="w-4 h-4 mr-2" />
                      Download HTML
                    </Button>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-semibold mb-2">Installation Instructions:</h4>
                    <ol className="text-sm space-y-1 list-decimal list-inside">
                      <li>Copy the embed code above</li>
                      <li>Paste it before the closing &lt;/body&gt; tag in your HTML</li>
                      <li>The chat widget will appear automatically on your website</li>
                      <li>Test the widget to ensure it's working correctly</li>
                    </ol>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Preview Panel */}
        <div className="space-y-6">
          <Card className="animate-slide-up stagger-3" variant="elevated">
            <CardHeader>
              <CardTitle>Live Preview</CardTitle>
              <CardDescription>
                See how your chat widget will look on different devices
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-2">
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Monitor className="w-3 h-3" />
                    Desktop
                  </Badge>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Smartphone className="w-3 h-3" />
                    Mobile
                  </Badge>
                </div>

                <div className="relative bg-gray-100 rounded-lg h-96 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
                    <div className="text-center text-muted-foreground">
                      <Globe className="w-12 h-12 mx-auto mb-2" />
                      <p>Your Website</p>
                    </div>
                  </div>
                  {previewWidget()}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ChatPluginPage;
