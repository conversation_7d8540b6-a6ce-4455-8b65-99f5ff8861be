'use client';

import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  User, 
  Phone, 
  Mail, 
  MapPin, 
  Calendar, 
  DollarSign,
  Star,
  Edit,
  MessageSquare,
  FileText,
  Clock,
  Wrench,
  CreditCard,
  AlertCircle,
  CheckCircle,
  Plus,
  Eye
} from 'lucide-react';
import { useCompany } from '@/contexts/CompanyContext';
import Link from 'next/link';

interface CustomerDetail {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  customer_type: 'residential' | 'commercial';
  status: 'active' | 'inactive' | 'prospect';
  total_spent: number;
  total_jobs: number;
  last_service_date?: string;
  next_service_date?: string;
  preferred_technician?: string;
  notes?: string;
  rating: number;
  referral_source?: string;
  created_at: string;
  updated_at: string;
  service_history: Array<{
    id: string;
    service_type: string;
    description: string;
    date: string;
    technician: string;
    cost: number;
    status: 'completed' | 'cancelled' | 'pending';
    rating?: number;
    notes?: string;
  }>;
  communications: Array<{
    id: string;
    type: 'call' | 'email' | 'sms' | 'visit';
    direction: 'inbound' | 'outbound';
    subject?: string;
    content: string;
    date: string;
    staff_member: string;
  }>;
  invoices: Array<{
    id: string;
    invoice_number: string;
    date: string;
    amount: number;
    status: 'paid' | 'pending' | 'overdue';
    due_date: string;
  }>;
}

export default function CustomerDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { currentCompany } = useCompany();
  const customerId = params.customerId as string;

  const [customer, setCustomer] = useState<CustomerDetail | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (customerId && currentCompany) {
      loadCustomer();
    }
  }, [customerId, currentCompany]);

  const loadCustomer = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockCustomer: CustomerDetail = {
        id: customerId,
        first_name: 'John',
        last_name: 'Smith',
        email: '<EMAIL>',
        phone: '+****************',
        address: '123 Main St',
        city: 'Anytown',
        state: 'CA',
        zip_code: '12345',
        customer_type: 'residential',
        status: 'active',
        total_spent: 2450.00,
        total_jobs: 8,
        last_service_date: '2024-01-10',
        next_service_date: '2024-04-10',
        preferred_technician: 'Mike Johnson',
        notes: 'Prefers morning appointments. Has two dogs. Gate code: 1234',
        rating: 5,
        referral_source: 'Google',
        created_at: '2023-06-15T10:30:00Z',
        updated_at: '2024-01-10T14:20:00Z',
        service_history: [
          {
            id: 'svc_001',
            service_type: 'Plumbing Repair',
            description: 'Fixed kitchen sink leak',
            date: '2024-01-10',
            technician: 'Mike Johnson',
            cost: 185.00,
            status: 'completed',
            rating: 5,
            notes: 'Customer very satisfied. Replaced faucet gasket.'
          },
          {
            id: 'svc_002',
            service_type: 'HVAC Maintenance',
            description: 'Annual system inspection and cleaning',
            date: '2023-12-15',
            technician: 'David Brown',
            cost: 150.00,
            status: 'completed',
            rating: 4,
            notes: 'Recommended filter replacement in 3 months.'
          },
          {
            id: 'svc_003',
            service_type: 'Electrical Repair',
            description: 'Fixed outlet in garage',
            date: '2023-11-20',
            technician: 'Sarah Wilson',
            cost: 95.00,
            status: 'completed',
            rating: 5
          }
        ],
        communications: [
          {
            id: 'comm_001',
            type: 'call',
            direction: 'inbound',
            subject: 'Kitchen sink leak',
            content: 'Customer called about urgent plumbing issue. Scheduled for tomorrow morning.',
            date: '2024-01-09T14:30:00Z',
            staff_member: 'Reception'
          },
          {
            id: 'comm_002',
            type: 'email',
            direction: 'outbound',
            subject: 'Service Reminder',
            content: 'Reminder about upcoming HVAC maintenance appointment.',
            date: '2023-12-14T09:00:00Z',
            staff_member: 'System'
          }
        ],
        invoices: [
          {
            id: 'inv_001',
            invoice_number: 'INV-2024-001',
            date: '2024-01-10',
            amount: 185.00,
            status: 'paid',
            due_date: '2024-01-25'
          },
          {
            id: 'inv_002',
            invoice_number: 'INV-2023-089',
            date: '2023-12-15',
            amount: 150.00,
            status: 'paid',
            due_date: '2023-12-30'
          }
        ]
      };

      setCustomer(mockCustomer);
    } catch (error) {
      console.error('Failed to load customer:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'prospect':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getServiceStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getInvoiceStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const renderStars = (rating?: number) => {
    if (!rating) return <span className="text-gray-400">No rating</span>;
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-4 h-4 ${
              i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">({rating}/5)</span>
      </div>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getCommunicationIcon = (type: string) => {
    switch (type) {
      case 'call':
        return <Phone className="w-4 h-4" />;
      case 'email':
        return <Mail className="w-4 h-4" />;
      case 'sms':
        return <MessageSquare className="w-4 h-4" />;
      case 'visit':
        return <User className="w-4 h-4" />;
      default:
        return <MessageSquare className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-8 bg-gray-200 rounded w-1/4"></div>
        <div className="h-64 bg-gray-200 rounded"></div>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Customer Not Found</h1>
        <p className="text-gray-600 mb-8">The customer you're looking for doesn't exist.</p>
        <Link href="/customers">
          <Button>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Customers
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/customers">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {customer.first_name} {customer.last_name}
            </h1>
            <p className="text-gray-600">Customer Profile</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge className={getStatusColor(customer.status)}>
            {customer.status}
          </Badge>
          <Button variant="outline" size="sm" onClick={() => router.push(`/customers/${customer.id}/edit`)}>
            <Edit className="w-4 h-4 mr-2" />
            Edit
          </Button>
          <Button variant="outline" size="sm" onClick={() => router.push(`/bookings/new?customer=${customer.id}`)}>
            <Calendar className="w-4 h-4 mr-2" />
            Schedule Service
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Customer Information */}
        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Name</label>
                <p className="text-sm">{customer.first_name} {customer.last_name}</p>
              </div>
              {customer.email && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <p className="text-sm flex items-center gap-1">
                    <Mail className="w-3 h-3" />
                    {customer.email}
                  </p>
                </div>
              )}
              {customer.phone && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Phone</label>
                  <p className="text-sm flex items-center gap-1">
                    <Phone className="w-3 h-3" />
                    {customer.phone}
                  </p>
                </div>
              )}
              <div>
                <label className="text-sm font-medium text-gray-500">Address</label>
                <p className="text-sm flex items-start gap-1">
                  <MapPin className="w-3 h-3 mt-0.5" />
                  <span>
                    {customer.address}<br />
                    {customer.city}, {customer.state} {customer.zip_code}
                  </span>
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Customer Type</label>
                <p className="text-sm capitalize">{customer.customer_type}</p>
              </div>
              {customer.preferred_technician && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Preferred Technician</label>
                  <p className="text-sm">{customer.preferred_technician}</p>
                </div>
              )}
              {customer.referral_source && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Referral Source</label>
                  <p className="text-sm">{customer.referral_source}</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Customer Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Total Spent</span>
                <span className="font-medium">{formatCurrency(customer.total_spent)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Total Jobs</span>
                <span className="font-medium">{customer.total_jobs}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Customer Since</span>
                <span className="font-medium">
                  {new Date(customer.created_at).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Last Service</span>
                <span className="font-medium">
                  {customer.last_service_date 
                    ? new Date(customer.last_service_date).toLocaleDateString()
                    : 'Never'
                  }
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Rating</span>
                {renderStars(customer.rating)}
              </div>
            </CardContent>
          </Card>

          {customer.notes && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Notes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-700">{customer.notes}</p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Service History and Communications */}
        <div className="lg:col-span-2">
          <Tabs defaultValue="services" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="services">Service History</TabsTrigger>
              <TabsTrigger value="communications">Communications</TabsTrigger>
              <TabsTrigger value="invoices">Invoices</TabsTrigger>
              <TabsTrigger value="notes">Notes</TabsTrigger>
            </TabsList>

            <TabsContent value="services" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Service History</h3>
                <Button size="sm" onClick={() => router.push(`/bookings/new?customer=${customer.id}`)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Schedule Service
                </Button>
              </div>
              
              <div className="space-y-4">
                {customer.service_history.map((service) => (
                  <Card key={service.id}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="font-medium">{service.service_type}</h4>
                          <p className="text-sm text-gray-600">{service.description}</p>
                        </div>
                        <div className="text-right">
                          <Badge className={getServiceStatusColor(service.status)}>
                            {service.status}
                          </Badge>
                          <p className="text-sm font-medium mt-1">
                            {formatCurrency(service.cost)}
                          </p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Date:</span> {new Date(service.date).toLocaleDateString()}
                        </div>
                        <div>
                          <span className="text-gray-500">Technician:</span> {service.technician}
                        </div>
                      </div>
                      
                      {service.rating && (
                        <div className="mt-2">
                          <span className="text-gray-500 text-sm">Rating:</span>
                          <div className="inline-block ml-2">
                            {renderStars(service.rating)}
                          </div>
                        </div>
                      )}
                      
                      {service.notes && (
                        <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
                          {service.notes}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="communications" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Communication History</h3>
                <Button size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Note
                </Button>
              </div>
              
              <div className="space-y-4">
                {customer.communications.map((comm) => (
                  <Card key={comm.id}>
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className="p-2 bg-gray-100 rounded-full">
                          {getCommunicationIcon(comm.type)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="font-medium capitalize">
                              {comm.type} - {comm.direction}
                            </h4>
                            <span className="text-sm text-gray-500">
                              {new Date(comm.date).toLocaleString()}
                            </span>
                          </div>
                          {comm.subject && (
                            <p className="text-sm font-medium text-gray-700 mb-1">
                              {comm.subject}
                            </p>
                          )}
                          <p className="text-sm text-gray-600">{comm.content}</p>
                          <p className="text-xs text-gray-500 mt-1">
                            By: {comm.staff_member}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="invoices" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Invoices</h3>
                <Button size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Invoice
                </Button>
              </div>
              
              <div className="space-y-4">
                {customer.invoices.map((invoice) => (
                  <Card key={invoice.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{invoice.invoice_number}</h4>
                          <p className="text-sm text-gray-600">
                            Date: {new Date(invoice.date).toLocaleDateString()}
                          </p>
                          <p className="text-sm text-gray-600">
                            Due: {new Date(invoice.due_date).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold">
                            {formatCurrency(invoice.amount)}
                          </p>
                          <Badge className={getInvoiceStatusColor(invoice.status)}>
                            {invoice.status}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="notes" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Customer Notes</h3>
                <Button size="sm">
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Notes
                </Button>
              </div>
              
              <Card>
                <CardContent className="p-4">
                  <p className="text-sm text-gray-700 whitespace-pre-wrap">
                    {customer.notes || 'No notes available for this customer.'}
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
