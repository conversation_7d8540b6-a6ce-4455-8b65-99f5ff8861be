'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  Target,
  BarChart3,
  Pie<PERSON>hart,
  Download,
  Filter,
  RefreshCw,
  ArrowUpRight,
  ArrowDownRight,
  <PERSON>,
  <PERSON>Pointer,
  Eye,
  Phone,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Area,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'recharts';

interface AnalyticsData {
  overview: {
    total_spend: number;
    total_appointments: number;
    total_revenue: number;
    avg_cost_per_appointment: number;
    total_roi: number;
    total_roas: number;
    active_campaigns: number;
    total_impressions: number;
    total_clicks: number;
    avg_ctr: number;
  };
  trends: {
    daily_data: Array<{
      date: string;
      spend: number;
      appointments: number;
      revenue: number;
      impressions: number;
      clicks: number;
    }>;
    monthly_comparison: Array<{
      month: string;
      current_year: number;
      previous_year: number;
    }>;
  };
  campaign_performance: Array<{
    campaign_name: string;
    spend: number;
    appointments: number;
    roi: number;
    status: string;
  }>;
  business_insights: {
    top_performing_services: Array<{
      service: string;
      appointments: number;
      revenue: number;
    }>;
    peak_hours: Array<{
      hour: string;
      bookings: number;
    }>;
    customer_demographics: Array<{
      age_group: string;
      percentage: number;
      color: string;
    }>;
  };
}

const AnalyticsPage: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState('30d');
  const [selectedMetric, setSelectedMetric] = useState('appointments');

  useEffect(() => {
    fetchAnalyticsData();
  }, [dateRange]);

  const fetchAnalyticsData = async () => {
    setLoading(true);
    try {
      // Mock data - in real implementation, fetch from analytics API
      const mockData: AnalyticsData = {
        overview: {
          total_spend: 2750,
          total_appointments: 68,
          total_revenue: 12240,
          avg_cost_per_appointment: 40.44,
          total_roi: 345,
          total_roas: 4.45,
          active_campaigns: 3,
          total_impressions: 45680,
          total_clicks: 892,
          avg_ctr: 1.95,
        },
        trends: {
          daily_data: [
            {
              date: '2024-01-01',
              spend: 85,
              appointments: 2,
              revenue: 360,
              impressions: 1200,
              clicks: 18,
            },
            {
              date: '2024-01-02',
              spend: 92,
              appointments: 3,
              revenue: 540,
              impressions: 1350,
              clicks: 21,
            },
            {
              date: '2024-01-03',
              spend: 78,
              appointments: 1,
              revenue: 180,
              impressions: 980,
              clicks: 15,
            },
            {
              date: '2024-01-04',
              spend: 105,
              appointments: 4,
              revenue: 720,
              impressions: 1450,
              clicks: 23,
            },
            {
              date: '2024-01-05',
              spend: 88,
              appointments: 2,
              revenue: 360,
              impressions: 1100,
              clicks: 17,
            },
            {
              date: '2024-01-06',
              spend: 110,
              appointments: 3,
              revenue: 540,
              impressions: 1600,
              clicks: 25,
            },
            {
              date: '2024-01-07',
              spend: 95,
              appointments: 2,
              revenue: 360,
              impressions: 1200,
              clicks: 19,
            },
          ],
          monthly_comparison: [
            { month: 'Jan', current_year: 68, previous_year: 45 },
            { month: 'Feb', current_year: 72, previous_year: 52 },
            { month: 'Mar', current_year: 85, previous_year: 48 },
          ],
        },
        campaign_performance: [
          {
            campaign_name: 'Summer Wellness Campaign',
            spend: 1200,
            appointments: 32,
            roi: 280,
            status: 'active',
          },
          {
            campaign_name: 'New Patient Acquisition',
            spend: 850,
            appointments: 21,
            roi: 195,
            status: 'paused',
          },
          {
            campaign_name: 'Weekend Special Offers',
            spend: 700,
            appointments: 15,
            roi: 165,
            status: 'active',
          },
        ],
        business_insights: {
          top_performing_services: [
            { service: 'Deep Tissue Massage', appointments: 28, revenue: 5040 },
            { service: 'Swedish Massage', appointments: 22, revenue: 3960 },
            { service: 'Sports Therapy', appointments: 18, revenue: 3240 },
          ],
          peak_hours: [
            { hour: '9:00', bookings: 8 },
            { hour: '10:00', bookings: 12 },
            { hour: '11:00', bookings: 15 },
            { hour: '14:00', bookings: 18 },
            { hour: '15:00', bookings: 22 },
            { hour: '16:00', bookings: 19 },
            { hour: '17:00', bookings: 14 },
          ],
          customer_demographics: [
            { age_group: '25-34', percentage: 35, color: '#3b82f6' },
            { age_group: '35-44', percentage: 28, color: '#10b981' },
            { age_group: '45-54', percentage: 22, color: '#f59e0b' },
            { age_group: '55+', percentage: 15, color: '#8b5cf6' },
          ],
        },
      };

      setAnalyticsData(mockData);
    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => `CHF ${amount.toLocaleString()}`;
  const formatPercentage = (value: number) => `${value}%`;

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading analytics...</p>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">No analytics data available</h2>
        <p className="text-gray-600">Start running campaigns to see your performance metrics.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Analytics & ROI Dashboard
          </h1>
          <p className="text-gray-600 mt-1">
            Track your campaign performance and return on investment
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" onClick={fetchAnalyticsData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>

          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total ROI</p>
                  <p className="text-3xl font-bold text-green-600">
                    {formatPercentage(analyticsData.overview.total_roi)}
                  </p>
                  <div className="flex items-center space-x-1 text-sm">
                    <ArrowUpRight className="w-4 h-4 text-green-600" />
                    <span className="text-green-600">+23% vs last month</span>
                  </div>
                </div>
                <TrendingUp className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                  <p className="text-3xl font-bold">
                    {formatCurrency(analyticsData.overview.total_revenue)}
                  </p>
                  <div className="flex items-center space-x-1 text-sm">
                    <ArrowUpRight className="w-4 h-4 text-green-600" />
                    <span className="text-green-600">+18% vs last month</span>
                  </div>
                </div>
                <DollarSign className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Appointments</p>
                  <p className="text-3xl font-bold">{analyticsData.overview.total_appointments}</p>
                  <div className="flex items-center space-x-1 text-sm">
                    <ArrowUpRight className="w-4 h-4 text-green-600" />
                    <span className="text-green-600">+15% vs last month</span>
                  </div>
                </div>
                <Calendar className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Cost per Appointment</p>
                  <p className="text-3xl font-bold">
                    {formatCurrency(analyticsData.overview.avg_cost_per_appointment)}
                  </p>
                  <div className="flex items-center space-x-1 text-sm">
                    <ArrowDownRight className="w-4 h-4 text-green-600" />
                    <span className="text-green-600">-8% vs last month</span>
                  </div>
                </div>
                <Target className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Secondary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">ROAS</p>
                <p className="text-2xl font-bold">{analyticsData.overview.total_roas}x</p>
              </div>
              <BarChart3 className="w-6 h-6 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Impressions</p>
                <p className="text-2xl font-bold">
                  {analyticsData.overview.total_impressions.toLocaleString()}
                </p>
              </div>
              <Eye className="w-6 h-6 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Clicks</p>
                <p className="text-2xl font-bold">{analyticsData.overview.total_clicks}</p>
              </div>
              <MousePointer className="w-6 h-6 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg CTR</p>
                <p className="text-2xl font-bold">{analyticsData.overview.avg_ctr}%</p>
              </div>
              <TrendingUp className="w-6 h-6 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Trends */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Performance Trends</CardTitle>
                <CardDescription>Daily performance metrics over time</CardDescription>
              </div>
              <Select value={selectedMetric} onValueChange={setSelectedMetric}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="appointments">Appointments</SelectItem>
                  <SelectItem value="revenue">Revenue</SelectItem>
                  <SelectItem value="spend">Spend</SelectItem>
                  <SelectItem value="clicks">Clicks</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={400}>
              <AreaChart data={analyticsData.trends.daily_data}>
                <defs>
                  <linearGradient id="colorMetric" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3} />
                    <stop offset="95%" stopColor="#3b82f6" stopOpacity={0} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="date"
                  tickFormatter={(value) =>
                    new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
                  }
                />
                <YAxis />
                <Tooltip
                  labelFormatter={(value) => new Date(value).toLocaleDateString()}
                  formatter={(value, name) => [
                    selectedMetric === 'revenue' || selectedMetric === 'spend'
                      ? formatCurrency(Number(value))
                      : value,
                    name.charAt(0).toUpperCase() + name.slice(1),
                  ]}
                />
                <Area
                  type="monotone"
                  dataKey={selectedMetric}
                  stroke="#3b82f6"
                  fillOpacity={1}
                  fill="url(#colorMetric)"
                  strokeWidth={2}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Campaign Performance & Business Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Campaign Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Campaign Performance</CardTitle>
            <CardDescription>ROI comparison across active campaigns</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.campaign_performance.map((campaign, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex-1">
                    <h4 className="font-medium">{campaign.campaign_name}</h4>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                      <span>Spend: {formatCurrency(campaign.spend)}</span>
                      <span>Appointments: {campaign.appointments}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div
                      className={`text-lg font-bold ${campaign.roi > 200 ? 'text-green-600' : campaign.roi > 150 ? 'text-yellow-600' : 'text-red-600'}`}
                    >
                      {formatPercentage(campaign.roi)}
                    </div>
                    <div className="text-xs text-gray-500">ROI</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Customer Demographics */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Demographics</CardTitle>
            <CardDescription>Age distribution of your customers</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RechartsPieChart>
                <Pie
                  data={analyticsData.business_insights.customer_demographics}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={120}
                  paddingAngle={5}
                  dataKey="percentage"
                >
                  {analyticsData.business_insights.customer_demographics.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => `${value}%`} />
              </RechartsPieChart>
            </ResponsiveContainer>
            <div className="grid grid-cols-2 gap-2 mt-4">
              {analyticsData.business_insights.customer_demographics.map((item, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: item.color }}
                  ></div>
                  <span className="text-sm">
                    {item.age_group}: {item.percentage}%
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ROI Insights */}
      <Card>
        <CardHeader>
          <CardTitle>ROI Insights & Recommendations</CardTitle>
          <CardDescription>AI-powered insights to optimize your campaigns</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <TrendingUp className="w-5 h-5 text-green-600" />
                <span className="font-medium text-green-800">Excellent ROI</span>
              </div>
              <p className="text-sm text-green-700">
                Your campaigns are generating 3.4x return on ad spend, which is 85% above industry
                average for wellness businesses.
              </p>
            </div>

            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Target className="w-5 h-5 text-blue-600" />
                <span className="font-medium text-blue-800">Optimization Opportunity</span>
              </div>
              <p className="text-sm text-blue-700">
                Increase budget allocation to weekends when your cost per appointment drops by 25%.
              </p>
            </div>

            <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Users className="w-5 h-5 text-purple-600" />
                <span className="font-medium text-purple-800">Audience Insight</span>
              </div>
              <p className="text-sm text-purple-700">
                Women aged 35-50 show 40% higher conversion rates. Consider creating targeted
                campaigns for this demographic.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AnalyticsPage;
