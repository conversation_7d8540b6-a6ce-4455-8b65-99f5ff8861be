'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Target,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  TrendingUp,
  Users,
  Mail,
  Phone,
  MessageSquare,
  Star,
  Eye,
  Edit,
  Send,
  UserPlus,
  DollarSign,
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  Globe,
  Facebook,
  Instagram,
  <PERSON><PERSON><PERSON>,
  Share2
} from 'lucide-react';
import { useCompany } from '@/contexts/CompanyContext';
import { toast } from 'react-hot-toast';

interface Lead {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  source: 'website' | 'google' | 'facebook' | 'referral' | 'phone' | 'other';
  status: 'new' | 'contacted' | 'qualified' | 'proposal' | 'won' | 'lost';
  service_interest: string;
  estimated_value: number;
  priority: 'low' | 'medium' | 'high';
  assigned_to?: string;
  notes?: string;
  created_at: string;
  last_contact?: string;
  conversion_date?: string;
}

interface Campaign {
  id: string;
  name: string;
  type: 'email' | 'sms' | 'social' | 'google_ads' | 'referral';
  status: 'draft' | 'active' | 'paused' | 'completed';
  budget?: number;
  spent?: number;
  leads_generated: number;
  conversions: number;
  roi: number;
  start_date: string;
  end_date?: string;
}

export default function MarketingPage() {
  const router = useRouter();
  const { currentCompany } = useCompany();
  const [leads, setLeads] = useState<Lead[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState('leads');

  useEffect(() => {
    if (currentCompany) {
      loadMarketingData();
    }
  }, [currentCompany]);

  const loadMarketingData = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API calls
      const mockLeads: Lead[] = [
        {
          id: 'lead_001',
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          phone: '+****************',
          source: 'google',
          status: 'qualified',
          service_interest: 'HVAC Installation',
          estimated_value: 3500.00,
          priority: 'high',
          assigned_to: 'Mike Johnson',
          notes: 'Interested in new HVAC system for 2000 sq ft home',
          created_at: '2024-01-20T10:00:00Z',
          last_contact: '2024-01-22T14:30:00Z'
        },
        {
          id: 'lead_002',
          name: 'Robert Davis',
          email: '<EMAIL>',
          phone: '+****************',
          source: 'website',
          status: 'new',
          service_interest: 'Plumbing Repair',
          estimated_value: 250.00,
          priority: 'medium',
          notes: 'Kitchen sink issues, needs quick response',
          created_at: '2024-01-25T09:15:00Z'
        },
        {
          id: 'lead_003',
          name: 'Lisa Wilson',
          phone: '+****************',
          source: 'referral',
          status: 'contacted',
          service_interest: 'Electrical Work',
          estimated_value: 800.00,
          priority: 'medium',
          assigned_to: 'Sarah Wilson',
          notes: 'Referred by John Smith - needs outlet installation',
          created_at: '2024-01-23T16:45:00Z',
          last_contact: '2024-01-24T11:20:00Z'
        }
      ];

      const mockCampaigns: Campaign[] = [
        {
          id: 'camp_001',
          name: 'Winter HVAC Maintenance',
          type: 'email',
          status: 'active',
          budget: 500.00,
          spent: 320.00,
          leads_generated: 15,
          conversions: 8,
          roi: 250.5,
          start_date: '2024-01-01',
          end_date: '2024-02-29'
        },
        {
          id: 'camp_002',
          name: 'Emergency Plumbing Ads',
          type: 'google_ads',
          status: 'active',
          budget: 1000.00,
          spent: 750.00,
          leads_generated: 25,
          conversions: 12,
          roi: 180.3,
          start_date: '2024-01-15'
        },
        {
          id: 'camp_003',
          name: 'Referral Program',
          type: 'referral',
          status: 'active',
          leads_generated: 8,
          conversions: 6,
          roi: 400.0,
          start_date: '2023-12-01'
        }
      ];

      setLeads(mockLeads);
      setCampaigns(mockCampaigns);
    } catch (error) {
      console.error('Failed to load marketing data:', error);
      toast.error('Failed to load marketing data');
    } finally {
      setLoading(false);
    }
  };

  const filteredLeads = leads.filter(lead => {
    const matchesSearch = 
      lead.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.service_interest.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'contacted':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'qualified':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'proposal':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'won':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'lost':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'website':
        return <Globe className="w-4 h-4" />;
      case 'google':
        return <Search className="w-4 h-4" />;
      case 'facebook':
        return <Facebook className="w-4 h-4" />;
      case 'referral':
        return <Users className="w-4 h-4" />;
      case 'phone':
        return <Phone className="w-4 h-4" />;
      default:
        return <Target className="w-4 h-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const calculateStats = () => {
    const totalLeads = leads.length;
    const qualifiedLeads = leads.filter(lead => lead.status === 'qualified' || lead.status === 'proposal').length;
    const wonLeads = leads.filter(lead => lead.status === 'won').length;
    const totalValue = leads.reduce((sum, lead) => sum + lead.estimated_value, 0);
    const conversionRate = totalLeads > 0 ? (wonLeads / totalLeads) * 100 : 0;

    return { totalLeads, qualifiedLeads, wonLeads, totalValue, conversionRate };
  };

  const stats = calculateStats();

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Marketing & Leads</h1>
          <p className="text-gray-600">
            Manage leads, track campaigns, and grow your business
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <BarChart3 className="w-4 h-4 mr-2" />
            Analytics
          </Button>
          <Button onClick={() => router.push('/marketing/campaigns/new')} variant="gradient">
            <Plus className="w-4 h-4 mr-2" />
            New Campaign
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Leads</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalLeads}</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Qualified</CardTitle>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.qualifiedLeads}</div>
            <p className="text-xs text-muted-foreground">
              Ready for proposal
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversions</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.wonLeads}</div>
            <p className="text-xs text-muted-foreground">
              {stats.conversionRate.toFixed(1)}% conversion rate
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pipeline Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalValue)}</div>
            <p className="text-xs text-muted-foreground">
              Potential revenue
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {campaigns.filter(c => c.status === 'active').length}
            </div>
            <p className="text-xs text-muted-foreground">
              Running now
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="leads">Leads</TabsTrigger>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="leads" className="space-y-6">
          {/* Search and Filters */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <Input
                    placeholder="Search leads by name, email, or service..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Button variant="outline">
                  <Filter className="w-4 h-4 mr-2" />
                  Filters
                </Button>
                <Button onClick={() => router.push('/marketing/leads/new')}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Lead
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Leads Table */}
          <Card>
            <CardHeader>
              <CardTitle>Leads</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Lead</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Source</TableHead>
                    <TableHead>Service Interest</TableHead>
                    <TableHead>Value</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Assigned To</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLeads.map((lead) => (
                    <TableRow 
                      key={lead.id}
                      className="cursor-pointer hover:bg-accent/5 transition-colors"
                      onClick={() => router.push(`/marketing/leads/${lead.id}`)}
                    >
                      <TableCell>
                        <div>
                          <p className="font-medium">{lead.name}</p>
                          <p className="text-sm text-gray-600">
                            {new Date(lead.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {lead.email && (
                            <div className="flex items-center text-sm">
                              <Mail className="w-3 h-3 mr-1" />
                              {lead.email}
                            </div>
                          )}
                          {lead.phone && (
                            <div className="flex items-center text-sm">
                              <Phone className="w-3 h-3 mr-1" />
                              {lead.phone}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getSourceIcon(lead.source)}
                          <span className="capitalize text-sm">{lead.source}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">{lead.service_interest}</span>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">
                          {formatCurrency(lead.estimated_value)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge className={getPriorityColor(lead.priority)}>
                          {lead.priority}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(lead.status)}>
                          {lead.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">{lead.assigned_to || 'Unassigned'}</span>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" onClick={(e) => e.stopPropagation()}>
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="bg-white border shadow-lg">
                            <DropdownMenuItem onClick={(e) => {
                              e.stopPropagation();
                              router.push(`/marketing/leads/${lead.id}`);
                            }}>
                              <Eye className="w-4 h-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={(e) => {
                              e.stopPropagation();
                              router.push(`/marketing/leads/${lead.id}/edit`);
                            }}>
                              <Edit className="w-4 h-4 mr-2" />
                              Edit Lead
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={(e) => {
                              e.stopPropagation();
                              // TODO: Implement contact
                            }}>
                              <Phone className="w-4 h-4 mr-2" />
                              Contact Lead
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={(e) => {
                              e.stopPropagation();
                              router.push(`/quotes/new?lead=${lead.id}`);
                            }}>
                              <Send className="w-4 h-4 mr-2" />
                              Create Quote
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Marketing Campaigns</h2>
            <Button onClick={() => router.push('/marketing/campaigns/new')}>
              <Plus className="w-4 h-4 mr-2" />
              New Campaign
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {campaigns.map((campaign) => (
              <Card key={campaign.id} className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => router.push(`/marketing/campaigns/${campaign.id}`)}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{campaign.name}</CardTitle>
                    <Badge className={getStatusColor(campaign.status)}>
                      {campaign.status}
                    </Badge>
                  </div>
                  <CardDescription className="capitalize">
                    {campaign.type.replace('_', ' ')} Campaign
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Leads Generated:</span>
                      <span className="font-medium">{campaign.leads_generated}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Conversions:</span>
                      <span className="font-medium">{campaign.conversions}</span>
                    </div>
                    {campaign.budget && (
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Budget:</span>
                        <span className="font-medium">{formatCurrency(campaign.budget)}</span>
                      </div>
                    )}
                    {campaign.spent && (
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Spent:</span>
                        <span className="font-medium">{formatCurrency(campaign.spent)}</span>
                      </div>
                    )}
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">ROI:</span>
                      <span className="font-medium text-green-600">{campaign.roi.toFixed(1)}%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <h2 className="text-2xl font-bold">Marketing Analytics</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Lead Sources</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {['google', 'website', 'referral', 'facebook', 'phone'].map((source) => {
                    const count = leads.filter(lead => lead.source === source).length;
                    const percentage = leads.length > 0 ? (count / leads.length) * 100 : 0;
                    return (
                      <div key={source} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getSourceIcon(source)}
                          <span className="capitalize">{source}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${percentage}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium w-8">{count}</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Conversion Funnel</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { stage: 'New Leads', count: leads.filter(l => l.status === 'new').length },
                    { stage: 'Contacted', count: leads.filter(l => l.status === 'contacted').length },
                    { stage: 'Qualified', count: leads.filter(l => l.status === 'qualified').length },
                    { stage: 'Proposal', count: leads.filter(l => l.status === 'proposal').length },
                    { stage: 'Won', count: leads.filter(l => l.status === 'won').length }
                  ].map((stage, index) => {
                    const percentage = leads.length > 0 ? (stage.count / leads.length) * 100 : 0;
                    return (
                      <div key={stage.stage} className="flex items-center justify-between">
                        <span>{stage.stage}</span>
                        <div className="flex items-center gap-2">
                          <div className="w-32 bg-gray-200 rounded-full h-3">
                            <div 
                              className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full" 
                              style={{ width: `${percentage}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium w-8">{stage.count}</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
