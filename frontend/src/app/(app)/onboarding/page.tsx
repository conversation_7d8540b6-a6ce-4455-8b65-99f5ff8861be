'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useCompany } from '@/contexts/CompanyContext';
import CompanySetup from '@/components/features/onboarding/CompanySetup';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Building2, Users, Phone, Calendar, BarChart3 } from 'lucide-react';

const OnboardingPage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<'welcome' | 'company-setup' | 'complete'>(
    'welcome',
  );
  const { user } = useAuth();
  const { companies, loading } = useCompany();
  const router = useRouter();

  useEffect(() => {
    // If user already has companies, redirect to dashboard
    if (!loading && companies && companies.length > 0) {
      router.push('/dashboard');
    }
  }, [companies, loading, router]);

  const handleGetStarted = () => {
    setCurrentStep('company-setup');
  };

  const handleCompanySetupComplete = () => {
    setCurrentStep('complete');
    // Redirect to dashboard after a short delay
    setTimeout(() => {
      router.push('/dashboard');
    }, 2000);
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (currentStep === 'company-setup') {
    return <CompanySetup onComplete={handleCompanySetupComplete} />;
  }

  if (currentStep === 'complete') {
    return (
      <div className="flex min-h-screen items-center justify-center p-4 bg-gradient-to-br from-green-50 via-white to-blue-50">
        <Card className="w-full max-w-md text-center animate-scale-in" variant="elevated">
          <CardContent className="pt-8 pb-8">
            <div className="mx-auto w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mb-6 animate-bounce-in">
              <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold mb-2">Welcome Aboard!</h2>
            <p className="text-muted-foreground mb-6">
              Your company has been set up successfully. Redirecting to your dashboard...
            </p>
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center p-4 sm:p-6 lg:p-8 bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="w-full max-w-4xl">
        {/* Welcome Header */}
        <div className="text-center mb-12 animate-fade-in">
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            Welcome to HomeService AI
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Transform your home service business with AI-powered customer interactions, automated
            booking, and comprehensive analytics.
          </p>
        </div>

        {/* Feature Cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          <Card className="animate-slide-up stagger-1 hover-lift" variant="elevated">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
                <Phone className="w-6 h-6 text-blue-600" />
              </div>
              <CardTitle className="text-lg">AI Phone Agents</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <CardDescription className="text-center">
                Handle customer calls 24/7 with intelligent AI agents that understand your business
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="animate-slide-up stagger-2 hover-lift" variant="elevated">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-3">
                <Calendar className="w-6 h-6 text-green-600" />
              </div>
              <CardTitle className="text-lg">Smart Booking</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <CardDescription className="text-center">
                Automated scheduling with Google Calendar integration and customer notifications
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="animate-slide-up stagger-3 hover-lift" variant="elevated">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-3">
                <BarChart3 className="w-6 h-6 text-purple-600" />
              </div>
              <CardTitle className="text-lg">Analytics Dashboard</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <CardDescription className="text-center">
                Track performance, customer satisfaction, and business growth with detailed insights
              </CardDescription>
            </CardContent>
          </Card>
        </div>

        {/* Main CTA Card */}
        <Card className="animate-slide-up stagger-4 hover-lift" variant="elevated">
          <CardHeader className="text-center pb-6">
            <div className="mx-auto w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mb-4">
              <Building2 className="w-8 h-8 text-white" />
            </div>
            <CardTitle className="text-2xl font-bold">Let's Set Up Your Business</CardTitle>
            <CardDescription className="text-lg">
              It only takes a few minutes to get your AI-powered home service platform ready
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <div className="space-y-4 mb-8">
              <div className="flex items-center justify-center space-x-3 text-sm text-muted-foreground">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Company profile and contact information</span>
              </div>
              <div className="flex items-center justify-center space-x-3 text-sm text-muted-foreground">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Service types and business hours</span>
              </div>
              <div className="flex items-center justify-center space-x-3 text-sm text-muted-foreground">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>AI agent configuration</span>
              </div>
            </div>

            <Button
              size="lg"
              className="w-full md:w-auto px-8 py-3 text-lg"
              onClick={handleGetStarted}
            >
              Get Started
            </Button>

            <p className="text-xs text-muted-foreground mt-4">
              Free 14-day trial • No credit card required
            </p>
          </CardContent>
        </Card>

        {/* User Info */}
        {user && (
          <div className="text-center mt-8 animate-fade-in stagger-5">
            <p className="text-sm text-muted-foreground">
              Signed in as <span className="font-medium">{user.email}</span>
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default OnboardingPage;
