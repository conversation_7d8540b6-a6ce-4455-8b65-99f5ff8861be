'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  Plus, 
  TrendingUp, 
  Calendar, 
  DollarSign,
  Target,
  Play,
  Pause,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Bar<PERSON><PERSON>3,
  <PERSON>,
  Clock
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';

interface Campaign {
  id: number;
  name: string;
  status: 'draft' | 'active' | 'paused' | 'completed';
  budget: number;
  spent: number;
  duration_days: number;
  primary_goal: string;
  business_profile: {
    business_name: string;
    business_type: string;
  };
  analytics: {
    impressions: number;
    clicks: number;
    appointments: number;
    cost_per_appointment: number;
    roi_percentage: number;
  };
  created_at: string;
  start_date?: string;
  end_date?: string;
}

const CampaignsPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuth();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    fetchCampaigns();
  }, []);

  const fetchCampaigns = async () => {
    try {
      // Mock data - in real implementation, fetch from campaigns API
      const mockCampaigns: Campaign[] = [
        {
          id: 1,
          name: 'Summer Wellness Campaign',
          status: 'active',
          budget: 1000,
          spent: 450,
          duration_days: 30,
          primary_goal: 'appointments',
          business_profile: {
            business_name: 'Zurich Wellness Center',
            business_type: 'massage_therapy'
          },
          analytics: {
            impressions: 15420,
            clicks: 234,
            appointments: 12,
            cost_per_appointment: 37.50,
            roi_percentage: 180
          },
          created_at: '2024-01-15T10:00:00Z',
          start_date: '2024-01-20T00:00:00Z',
          end_date: '2024-02-20T00:00:00Z'
        },
        {
          id: 2,
          name: 'New Patient Acquisition',
          status: 'paused',
          budget: 750,
          spent: 320,
          duration_days: 21,
          primary_goal: 'leads',
          business_profile: {
            business_name: 'Zurich Wellness Center',
            business_type: 'massage_therapy'
          },
          analytics: {
            impressions: 8900,
            clicks: 156,
            appointments: 8,
            cost_per_appointment: 40,
            roi_percentage: 145
          },
          created_at: '2024-01-01T10:00:00Z',
          start_date: '2024-01-05T00:00:00Z'
        },
        {
          id: 3,
          name: 'Brand Awareness Q1',
          status: 'draft',
          budget: 500,
          spent: 0,
          duration_days: 14,
          primary_goal: 'awareness',
          business_profile: {
            business_name: 'Zurich Wellness Center',
            business_type: 'massage_therapy'
          },
          analytics: {
            impressions: 0,
            clicks: 0,
            appointments: 0,
            cost_per_appointment: 0,
            roi_percentage: 0
          },
          created_at: '2024-01-25T10:00:00Z'
        }
      ];

      setCampaigns(mockCampaigns);
    } catch (error) {
      console.error('Error fetching campaigns:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getGoalIcon = (goal: string) => {
    switch (goal) {
      case 'appointments':
        return Calendar;
      case 'leads':
        return Target;
      case 'awareness':
        return Eye;
      default:
        return Target;
    }
  };

  const filteredCampaigns = campaigns.filter(campaign => {
    if (activeTab === 'all') return true;
    return campaign.status === activeTab;
  });

  const totalStats = campaigns.reduce((acc, campaign) => ({
    totalSpent: acc.totalSpent + campaign.spent,
    totalAppointments: acc.totalAppointments + campaign.analytics.appointments,
    totalImpressions: acc.totalImpressions + campaign.analytics.impressions,
    totalClicks: acc.totalClicks + campaign.analytics.clicks
  }), { totalSpent: 0, totalAppointments: 0, totalImpressions: 0, totalClicks: 0 });

  const avgCostPerAppointment = totalStats.totalAppointments > 0 
    ? totalStats.totalSpent / totalStats.totalAppointments 
    : 0;

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading campaigns...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Ad Campaigns
          </h1>
          <p className="text-gray-600 mt-1">
            Manage your AI-powered advertising campaigns
          </p>
        </div>
        <Button 
          onClick={() => router.push('/campaigns/create')}
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Campaign
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Spent</p>
                <p className="text-2xl font-bold">CHF {totalStats.totalSpent.toLocaleString()}</p>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Appointments</p>
                <p className="text-2xl font-bold">{totalStats.totalAppointments}</p>
              </div>
              <Calendar className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Cost/Appointment</p>
                <p className="text-2xl font-bold">CHF {avgCostPerAppointment.toFixed(0)}</p>
              </div>
              <Target className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Clicks</p>
                <p className="text-2xl font-bold">{totalStats.totalClicks.toLocaleString()}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Campaigns List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Campaigns</CardTitle>
          <CardDescription>
            Monitor and manage your advertising campaigns
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="all">All ({campaigns.length})</TabsTrigger>
              <TabsTrigger value="active">
                Active ({campaigns.filter(c => c.status === 'active').length})
              </TabsTrigger>
              <TabsTrigger value="paused">
                Paused ({campaigns.filter(c => c.status === 'paused').length})
              </TabsTrigger>
              <TabsTrigger value="draft">
                Draft ({campaigns.filter(c => c.status === 'draft').length})
              </TabsTrigger>
              <TabsTrigger value="completed">
                Completed ({campaigns.filter(c => c.status === 'completed').length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-6">
              {filteredCampaigns.length === 0 ? (
                <div className="text-center py-12">
                  <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {activeTab === 'all' ? 'No campaigns yet' : `No ${activeTab} campaigns`}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    {activeTab === 'all' 
                      ? 'Create your first AI-powered campaign to start getting more appointments'
                      : `You don't have any ${activeTab} campaigns at the moment`
                    }
                  </p>
                  {activeTab === 'all' && (
                    <Button 
                      onClick={() => router.push('/campaigns/create')}
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Create Your First Campaign
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredCampaigns.map((campaign) => {
                    const GoalIcon = getGoalIcon(campaign.primary_goal);
                    const progressPercentage = (campaign.spent / campaign.budget) * 100;
                    
                    return (
                      <motion.div
                        key={campaign.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="border rounded-lg p-6 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className="text-lg font-semibold">{campaign.name}</h3>
                              <Badge className={getStatusColor(campaign.status)}>
                                {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                              </Badge>
                              <div className="flex items-center space-x-1 text-sm text-gray-600">
                                <GoalIcon className="w-4 h-4" />
                                <span>{campaign.primary_goal}</span>
                              </div>
                            </div>
                            
                            <p className="text-gray-600 text-sm mb-4">
                              {campaign.business_profile.business_name} • 
                              {campaign.business_profile.business_type.replace('_', ' ')}
                            </p>

                            {/* Campaign Metrics */}
                            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                              <div>
                                <p className="text-xs text-gray-500">Budget</p>
                                <p className="font-semibold">CHF {campaign.budget}</p>
                              </div>
                              <div>
                                <p className="text-xs text-gray-500">Spent</p>
                                <p className="font-semibold">CHF {campaign.spent}</p>
                              </div>
                              <div>
                                <p className="text-xs text-gray-500">Appointments</p>
                                <p className="font-semibold">{campaign.analytics.appointments}</p>
                              </div>
                              <div>
                                <p className="text-xs text-gray-500">Cost/Appointment</p>
                                <p className="font-semibold">CHF {campaign.analytics.cost_per_appointment}</p>
                              </div>
                              <div>
                                <p className="text-xs text-gray-500">ROI</p>
                                <p className="font-semibold text-green-600">
                                  {campaign.analytics.roi_percentage}%
                                </p>
                              </div>
                            </div>

                            {/* Progress Bar */}
                            {campaign.status !== 'draft' && (
                              <div className="mb-4">
                                <div className="flex justify-between text-xs text-gray-600 mb-1">
                                  <span>Budget Progress</span>
                                  <span>{progressPercentage.toFixed(1)}%</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div 
                                    className="bg-gradient-to-r from-blue-600 to-purple-600 h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                                  ></div>
                                </div>
                              </div>
                            )}

                            {/* Campaign Dates */}
                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              <div className="flex items-center space-x-1">
                                <Clock className="w-3 h-3" />
                                <span>Created {new Date(campaign.created_at).toLocaleDateString()}</span>
                              </div>
                              {campaign.start_date && (
                                <div className="flex items-center space-x-1">
                                  <Play className="w-3 h-3" />
                                  <span>Started {new Date(campaign.start_date).toLocaleDateString()}</span>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Actions */}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => router.push(`/campaigns/${campaign.id}`)}>
                                <Eye className="w-4 h-4 mr-2" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => router.push(`/campaigns/${campaign.id}/edit`)}>
                                <Edit className="w-4 h-4 mr-2" />
                                Edit Campaign
                              </DropdownMenuItem>
                              {campaign.status === 'active' ? (
                                <DropdownMenuItem>
                                  <Pause className="w-4 h-4 mr-2" />
                                  Pause Campaign
                                </DropdownMenuItem>
                              ) : campaign.status === 'paused' ? (
                                <DropdownMenuItem>
                                  <Play className="w-4 h-4 mr-2" />
                                  Resume Campaign
                                </DropdownMenuItem>
                              ) : null}
                              <DropdownMenuItem className="text-red-600">
                                <Trash2 className="w-4 h-4 mr-2" />
                                Delete Campaign
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default CampaignsPage;
