'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  ArrowLeft,
  Play,
  Pause,
  Edit,
  TrendingUp,
  TrendingDown,
  Calendar,
  DollarSign,
  Target,
  Eye,
  Users,
  MousePointer,
  BarChart3,
  Settings
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';

interface CampaignDetails {
  id: number;
  name: string;
  status: 'draft' | 'active' | 'paused' | 'completed';
  budget: number;
  spent: number;
  duration_days: number;
  primary_goal: string;
  business_profile: {
    business_name: string;
    business_type: string;
  };
  analytics: {
    impressions: number;
    clicks: number;
    appointments: number;
    cost_per_appointment: number;
    roi_percentage: number;
    ctr: number;
    conversion_rate: number;
  };
  ad_content: {
    selected_ad_copy: string;
    selected_headline: string;
    selected_description: string;
  };
  performance_data: Array<{
    date: string;
    impressions: number;
    clicks: number;
    spend: number;
    appointments: number;
  }>;
  created_at: string;
  start_date?: string;
  end_date?: string;
}

const CampaignDetailsPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const campaignId = params.id as string;
  
  const [campaign, setCampaign] = useState<CampaignDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchCampaignDetails();
  }, [campaignId]);

  const fetchCampaignDetails = async () => {
    try {
      // Mock data - in real implementation, fetch from campaigns API
      const mockCampaign: CampaignDetails = {
        id: parseInt(campaignId),
        name: 'Summer Wellness Campaign',
        status: 'active',
        budget: 1000,
        spent: 450,
        duration_days: 30,
        primary_goal: 'appointments',
        business_profile: {
          business_name: 'Zurich Wellness Center',
          business_type: 'massage_therapy'
        },
        analytics: {
          impressions: 15420,
          clicks: 234,
          appointments: 12,
          cost_per_appointment: 37.50,
          roi_percentage: 180,
          ctr: 1.52,
          conversion_rate: 5.13
        },
        ad_content: {
          selected_ad_copy: "Book your massage therapy appointment in Zurich today! Professional care you can trust.",
          selected_headline: "Massage Therapy Zurich",
          selected_description: "Professional massage therapy services in Zurich. Book your appointment today."
        },
        performance_data: [
          { date: '2024-01-20', impressions: 1200, clicks: 18, spend: 45, appointments: 1 },
          { date: '2024-01-21', impressions: 1350, clicks: 21, spend: 52, appointments: 1 },
          { date: '2024-01-22', impressions: 980, clicks: 15, spend: 38, appointments: 0 },
          { date: '2024-01-23', impressions: 1450, clicks: 23, spend: 58, appointments: 2 },
          { date: '2024-01-24', impressions: 1600, clicks: 25, spend: 62, appointments: 1 },
          { date: '2024-01-25', impressions: 1100, clicks: 17, spend: 42, appointments: 1 },
          { date: '2024-01-26', impressions: 1800, clicks: 28, spend: 70, appointments: 2 }
        ],
        created_at: '2024-01-15T10:00:00Z',
        start_date: '2024-01-20T00:00:00Z',
        end_date: '2024-02-20T00:00:00Z'
      };

      setCampaign(mockCampaign);
    } catch (error) {
      console.error('Error fetching campaign details:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading campaign details...</p>
        </div>
      </div>
    );
  }

  if (!campaign) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">Campaign not found</h2>
        <p className="text-gray-600 mb-6">The campaign you're looking for doesn't exist.</p>
        <Button onClick={() => router.push('/campaigns')}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Campaigns
        </Button>
      </div>
    );
  }

  const progressPercentage = (campaign.spent / campaign.budget) * 100;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            variant="ghost" 
            onClick={() => router.push('/campaigns')}
            className="p-2"
          >
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <div>
            <div className="flex items-center space-x-3">
              <h1 className="text-3xl font-bold">{campaign.name}</h1>
              <Badge className={getStatusColor(campaign.status)}>
                {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
              </Badge>
            </div>
            <p className="text-gray-600 mt-1">
              {campaign.business_profile.business_name} • 
              {campaign.business_profile.business_type.replace('_', ' ')}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {campaign.status === 'active' ? (
            <Button variant="outline">
              <Pause className="w-4 h-4 mr-2" />
              Pause Campaign
            </Button>
          ) : campaign.status === 'paused' ? (
            <Button variant="outline">
              <Play className="w-4 h-4 mr-2" />
              Resume Campaign
            </Button>
          ) : null}
          
          <Button 
            variant="outline"
            onClick={() => router.push(`/campaigns/${campaign.id}/edit`)}
          >
            <Edit className="w-4 h-4 mr-2" />
            Edit
          </Button>
          
          <Button>
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Spent</p>
                <p className="text-2xl font-bold">CHF {campaign.spent}</p>
                <p className="text-xs text-gray-500">of CHF {campaign.budget} budget</p>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-500 mt-1">{progressPercentage.toFixed(1)}% used</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Appointments</p>
                <p className="text-2xl font-bold">{campaign.analytics.appointments}</p>
                <div className="flex items-center space-x-1 text-xs">
                  <TrendingUp className="w-3 h-3 text-green-600" />
                  <span className="text-green-600">+15% vs last week</span>
                </div>
              </div>
              <Calendar className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Cost per Appointment</p>
                <p className="text-2xl font-bold">CHF {campaign.analytics.cost_per_appointment}</p>
                <div className="flex items-center space-x-1 text-xs">
                  <TrendingDown className="w-3 h-3 text-green-600" />
                  <span className="text-green-600">-8% vs target</span>
                </div>
              </div>
              <Target className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">ROI</p>
                <p className="text-2xl font-bold text-green-600">{campaign.analytics.roi_percentage}%</p>
                <div className="flex items-center space-x-1 text-xs">
                  <TrendingUp className="w-3 h-3 text-green-600" />
                  <span className="text-green-600">Excellent performance</span>
                </div>
              </div>
              <BarChart3 className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Card>
        <CardHeader>
          <CardTitle>Campaign Analytics</CardTitle>
          <CardDescription>
            Detailed performance metrics and trends
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="content">Ad Content</TabsTrigger>
              <TabsTrigger value="targeting">Targeting</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Performance Chart */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Daily Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={campaign.performance_data}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis 
                          dataKey="date" 
                          tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                        />
                        <YAxis />
                        <Tooltip 
                          labelFormatter={(value) => new Date(value).toLocaleDateString()}
                          formatter={(value, name) => [
                            name === 'spend' ? `CHF ${value}` : value,
                            name.charAt(0).toUpperCase() + name.slice(1)
                          ]}
                        />
                        <Line type="monotone" dataKey="impressions" stroke="#3b82f6" strokeWidth={2} />
                        <Line type="monotone" dataKey="clicks" stroke="#10b981" strokeWidth={2} />
                        <Line type="monotone" dataKey="appointments" stroke="#8b5cf6" strokeWidth={2} />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Key Metrics */}
                <div className="space-y-4">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-semibold">Engagement Metrics</h4>
                        <Eye className="w-5 h-5 text-gray-400" />
                      </div>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Impressions</span>
                          <span className="font-medium">{campaign.analytics.impressions.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Clicks</span>
                          <span className="font-medium">{campaign.analytics.clicks}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">CTR</span>
                          <span className="font-medium">{campaign.analytics.ctr}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Conversion Rate</span>
                          <span className="font-medium text-green-600">{campaign.analytics.conversion_rate}%</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-semibold">Campaign Timeline</h4>
                        <Calendar className="w-5 h-5 text-gray-400" />
                      </div>
                      <div className="space-y-3 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Created</span>
                          <span>{new Date(campaign.created_at).toLocaleDateString()}</span>
                        </div>
                        {campaign.start_date && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">Started</span>
                            <span>{new Date(campaign.start_date).toLocaleDateString()}</span>
                          </div>
                        )}
                        {campaign.end_date && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">Ends</span>
                            <span>{new Date(campaign.end_date).toLocaleDateString()}</span>
                          </div>
                        )}
                        <div className="flex justify-between">
                          <span className="text-gray-600">Duration</span>
                          <span>{campaign.duration_days} days</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="performance" className="mt-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Spend Chart */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Daily Spend</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={campaign.performance_data}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis 
                          dataKey="date" 
                          tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                        />
                        <YAxis />
                        <Tooltip 
                          labelFormatter={(value) => new Date(value).toLocaleDateString()}
                          formatter={(value) => [`CHF ${value}`, 'Spend']}
                        />
                        <Bar dataKey="spend" fill="#3b82f6" />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Performance Insights */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Performance Insights</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center space-x-2 mb-2">
                          <TrendingUp className="w-4 h-4 text-green-600" />
                          <span className="font-medium text-green-800">Strong Performance</span>
                        </div>
                        <p className="text-sm text-green-700">
                          Your campaign is performing 25% better than industry average for massage therapy businesses.
                        </p>
                      </div>

                      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center space-x-2 mb-2">
                          <Target className="w-4 h-4 text-blue-600" />
                          <span className="font-medium text-blue-800">Optimization Opportunity</span>
                        </div>
                        <p className="text-sm text-blue-700">
                          Consider increasing budget on weekends when conversion rates are 40% higher.
                        </p>
                      </div>

                      <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                        <div className="flex items-center space-x-2 mb-2">
                          <Users className="w-4 h-4 text-purple-600" />
                          <span className="font-medium text-purple-800">Audience Insight</span>
                        </div>
                        <p className="text-sm text-purple-700">
                          Women aged 35-50 show the highest engagement with your ads.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="content" className="mt-6">
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Active Ad Content</CardTitle>
                    <CardDescription>
                      The AI-generated content currently being used in your campaign
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Headline</Label>
                      <p className="mt-1 p-3 bg-gray-50 rounded-lg font-medium">
                        {campaign.ad_content.selected_headline}
                      </p>
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-gray-600">Ad Copy</Label>
                      <p className="mt-1 p-3 bg-gray-50 rounded-lg">
                        {campaign.ad_content.selected_ad_copy}
                      </p>
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-gray-600">Description</Label>
                      <p className="mt-1 p-3 bg-gray-50 rounded-lg">
                        {campaign.ad_content.selected_description}
                      </p>
                    </div>

                    <div className="pt-4">
                      <Button variant="outline">
                        <Edit className="w-4 h-4 mr-2" />
                        Edit Ad Content
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="targeting" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Targeting Settings</CardTitle>
                  <CardDescription>
                    Current audience targeting configuration
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-3">Geographic Targeting</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Country</span>
                          <span>Switzerland</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Region</span>
                          <span>Zurich</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Radius</span>
                          <span>25 km</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-3">Demographics</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Age Range</span>
                          <span>25-65 years</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Languages</span>
                          <span>German, English</span>
                        </div>
                      </div>
                    </div>

                    <div className="md:col-span-2">
                      <h4 className="font-medium mb-3">Interests</h4>
                      <div className="flex flex-wrap gap-2">
                        {['Massage therapy', 'Wellness', 'Health and fitness', 'Stress relief', 'Self-care'].map((interest) => (
                          <Badge key={interest} variant="secondary">
                            {interest}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="pt-6">
                    <Button variant="outline">
                      <Settings className="w-4 h-4 mr-2" />
                      Edit Targeting
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default CampaignDetailsPage;
