'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Sparkles,
  Target,
  DollarSign,
  Calendar,
  ArrowRight,
  ArrowLeft,
  Zap,
  Eye,
  CheckCircle,
  Loader2,
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/contexts/AuthContext';

// Form validation schema
const campaignSchema = z.object({
  name: z.string().min(3, 'Campaign name must be at least 3 characters'),
  business_profile_id: z.number().min(1, 'Please select a business profile'),
  budget: z.number().min(50, 'Minimum budget is CHF 50'),
  duration_days: z
    .number()
    .min(1, 'Duration must be at least 1 day')
    .max(90, 'Maximum duration is 90 days'),
  primary_goal: z.string().min(1, 'Please select a primary goal'),
  target_appointments: z.number().optional(),
  target_cost_per_appointment: z.number().optional(),
  ad_platform: z.string().default('meta_ads'),
});

type CampaignFormData = z.infer<typeof campaignSchema>;

const CreateCampaignPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [businessProfiles, setBusinessProfiles] = useState<any[]>([]);
  const [aiGeneratedContent, setAiGeneratedContent] = useState<any>(null);
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const [isCreatingCampaign, setIsCreatingCampaign] = useState(false);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(['meta_ads']);
  const [availablePlatforms, setAvailablePlatforms] = useState<any[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    trigger,
    setValue,
    getValues,
  } = useForm<CampaignFormData>({
    resolver: zodResolver(campaignSchema),
    mode: 'onChange',
    defaultValues: {
      ad_platform: 'meta_ads',
    },
  });

  const watchedGoal = watch('primary_goal');
  const watchedBudget = watch('budget');
  const watchedDuration = watch('duration_days');

  useEffect(() => {
    // Fetch business profiles and available platforms
    fetchBusinessProfiles();
    fetchAvailablePlatforms();
  }, []);

  const fetchBusinessProfiles = async () => {
    try {
      // Mock data - in real implementation, fetch from API
      const mockProfiles = [
        {
          id: 1,
          business_name: 'Zurich Wellness Center',
          business_type: 'massage_therapy',
          city: 'Zurich',
          services_offered: ['Deep tissue massage', 'Swedish massage', 'Sports therapy'],
        },
      ];
      setBusinessProfiles(mockProfiles);

      // Auto-select if only one profile
      if (mockProfiles.length === 1) {
        setValue('business_profile_id', mockProfiles[0].id);
      }
    } catch (error) {
      console.error('Error fetching business profiles:', error);
    }
  };

  const fetchAvailablePlatforms = async () => {
    try {
      // Mock data - in real implementation, fetch from /api/integrations/platforms
      const mockPlatforms = [
        {
          id: 'meta_ads',
          name: 'Meta Ads',
          description: 'Facebook and Instagram advertising',
          icon: '📘',
          supported_business_types: ['all'],
          recommended: true,
        },
        {
          id: 'google_ads',
          name: 'Google Ads',
          description: 'Google Search and Display advertising',
          icon: '🔍',
          supported_business_types: ['all'],
          recommended: true,
        },
        {
          id: 'linkedin_ads',
          name: 'LinkedIn Ads',
          description: 'Professional network advertising',
          icon: '💼',
          supported_business_types: [
            'legal',
            'accounting',
            'consulting',
            'real_estate',
            'insurance',
            'financial_planning',
            'marketing',
            'it_services',
            'architecture',
            'engineering',
          ],
          recommended: false,
        },
      ];
      setAvailablePlatforms(mockPlatforms);
    } catch (error) {
      console.error('Error fetching available platforms:', error);
    }
  };

  const steps = [
    { number: 1, title: 'Campaign Basics', description: 'Name, budget, and goals' },
    { number: 2, title: 'Platform Selection', description: 'Choose advertising platforms' },
    { number: 3, title: 'AI Ad Generation', description: 'Let AI create your ads' },
    { number: 4, title: 'Review & Launch', description: 'Final review and launch' },
  ];

  const campaignGoals = [
    {
      value: 'appointments',
      label: 'Get More Appointments',
      description: 'Drive direct bookings from your ads',
      icon: Calendar,
      color: 'text-blue-600',
    },
    {
      value: 'leads',
      label: 'Generate Leads',
      description: 'Collect contact information for follow-up',
      icon: Target,
      color: 'text-green-600',
    },
    {
      value: 'awareness',
      label: 'Build Awareness',
      description: 'Increase brand recognition in Zurich',
      icon: Eye,
      color: 'text-purple-600',
    },
  ];

  const handleNextStep = async () => {
    const fieldsToValidate = getFieldsForStep(currentStep);
    const isValid = await trigger(fieldsToValidate);

    if (isValid && currentStep === 1) {
      // Generate AI content when moving to step 2
      await generateAIContent();
    }

    if (isValid) {
      setCurrentStep((prev) => Math.min(prev + 1, steps.length));
    }
  };

  const handlePrevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  const getFieldsForStep = (step: number): (keyof CampaignFormData)[] => {
    switch (step) {
      case 1:
        return ['name', 'business_profile_id', 'budget', 'duration_days', 'primary_goal'];
      case 2:
        return []; // Platform selection - no form validation needed
      case 3:
        return []; // AI generation
      case 4:
        return []; // Review
      default:
        return [];
    }
  };

  const generateAIContent = async () => {
    setIsGeneratingAI(true);

    try {
      const formData = getValues();
      const selectedProfile = businessProfiles.find((p) => p.id === formData.business_profile_id);

      if (!selectedProfile) return;

      // Mock AI generation - in real implementation, call the AI service
      await new Promise((resolve) => setTimeout(resolve, 2000)); // Simulate API call

      const mockAIContent = {
        ad_copy_variations: [
          'Book your massage therapy appointment in Zurich today! Professional care you can trust.',
          'Experience premium wellness services in Zurich. Schedule your consultation now.',
          'Transform your wellbeing with expert massage therapy. Book online in seconds!',
        ],
        headlines: ['Massage Therapy Zurich', 'Book Wellness Center', 'Expert Massage Care'],
        descriptions: [
          'Professional massage therapy services in Zurich. Book your appointment today.',
          'Trusted wellness care. Convenient online booking available.',
          'Expert massage treatments. Schedule your visit now.',
        ],
        targeting_suggestions: {
          location: { countries: ['CH'], regions: ['Zurich'], radius: 25 },
          age_range: { min: 25, max: 65 },
          interests: ['Massage therapy', 'Wellness', 'Health and fitness'],
        },
        creative_ideas: [
          'Professional clinic interior photos',
          'Relaxing massage therapy session',
          'Happy customer testimonials video',
        ],
        estimated_performance: {
          estimated_clicks: Math.floor(formData.budget / 2.5),
          estimated_impressions: Math.floor((formData.budget / 2.5) * 100),
          estimated_conversions: Math.floor(formData.budget / 45),
          estimated_cost_per_conversion: 45,
        },
      };

      setAiGeneratedContent(mockAIContent);
    } catch (error) {
      console.error('Error generating AI content:', error);
    } finally {
      setIsGeneratingAI(false);
    }
  };

  const onSubmit = async (data: CampaignFormData) => {
    setIsCreatingCampaign(true);

    try {
      // Create campaign with AI-generated content
      const campaignData = {
        ...data,
        ai_generated_ad_copy: aiGeneratedContent?.ad_copy_variations,
        ai_generated_headlines: aiGeneratedContent?.headlines,
        ai_generated_descriptions: aiGeneratedContent?.descriptions,
        targeting_suggestions: aiGeneratedContent?.targeting_suggestions,
        creative_ideas: aiGeneratedContent?.creative_ideas,
        // Select first variations as defaults
        selected_ad_copy: aiGeneratedContent?.ad_copy_variations[0],
        selected_headline: aiGeneratedContent?.headlines[0],
        selected_description: aiGeneratedContent?.descriptions[0],
        selected_targeting: aiGeneratedContent?.targeting_suggestions,
      };

      console.log('Creating campaign:', campaignData);

      // Mock API call - in real implementation, call campaigns API
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Redirect to campaign dashboard
      router.push('/campaigns');
    } catch (error) {
      console.error('Error creating campaign:', error);
    } finally {
      setIsCreatingCampaign(false);
    }
  };

  const handlePlatformToggle = (platformId: string) => {
    setSelectedPlatforms((prev) => {
      if (prev.includes(platformId)) {
        // Don't allow removing all platforms
        if (prev.length === 1) return prev;
        return prev.filter((id) => id !== platformId);
      } else {
        return [...prev, platformId];
      }
    });
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            {/* Campaign Name */}
            <div>
              <Label htmlFor="name">Campaign Name</Label>
              <Input
                id="name"
                {...register('name')}
                placeholder="Summer Wellness Campaign 2024"
                className="mt-1"
              />
              {errors.name && <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>}
            </div>

            {/* Business Profile Selection */}
            <div>
              <Label>Business Profile</Label>
              <Select
                value={watch('business_profile_id')?.toString()}
                onValueChange={(value) => setValue('business_profile_id', parseInt(value))}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select your business profile" />
                </SelectTrigger>
                <SelectContent>
                  {businessProfiles.map((profile) => (
                    <SelectItem key={profile.id} value={profile.id.toString()}>
                      {profile.business_name} - {profile.city}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.business_profile_id && (
                <p className="text-sm text-red-600 mt-1">{errors.business_profile_id.message}</p>
              )}
            </div>

            {/* Budget and Duration */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="budget">Total Budget (CHF)</Label>
                <div className="relative mt-1">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <Input
                    id="budget"
                    type="number"
                    {...register('budget', { valueAsNumber: true })}
                    placeholder="500"
                    className="pl-10"
                  />
                </div>
                {errors.budget && (
                  <p className="text-sm text-red-600 mt-1">{errors.budget.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="duration_days">Duration (Days)</Label>
                <div className="relative mt-1">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <Input
                    id="duration_days"
                    type="number"
                    {...register('duration_days', { valueAsNumber: true })}
                    placeholder="30"
                    className="pl-10"
                  />
                </div>
                {errors.duration_days && (
                  <p className="text-sm text-red-600 mt-1">{errors.duration_days.message}</p>
                )}
              </div>
            </div>

            {/* Campaign Goal */}
            <div>
              <Label>Primary Campaign Goal</Label>
              <RadioGroup
                value={watchedGoal}
                onValueChange={(value) => setValue('primary_goal', value)}
                className="space-y-3 mt-3"
              >
                {campaignGoals.map((goal) => {
                  const Icon = goal.icon;
                  return (
                    <div key={goal.value} className="flex items-start space-x-3">
                      <RadioGroupItem value={goal.value} id={goal.value} className="mt-1" />
                      <Label
                        htmlFor={goal.value}
                        className="cursor-pointer p-4 rounded-lg border hover:bg-gray-50 flex-1"
                      >
                        <div className="flex items-center space-x-3">
                          <Icon className={`w-5 h-5 ${goal.color}`} />
                          <div>
                            <div className="font-medium">{goal.label}</div>
                            <div className="text-sm text-gray-600 mt-1">{goal.description}</div>
                          </div>
                        </div>
                      </Label>
                    </div>
                  );
                })}
              </RadioGroup>
              {errors.primary_goal && (
                <p className="text-sm text-red-600 mt-1">{errors.primary_goal.message}</p>
              )}
            </div>

            {/* Budget Breakdown */}
            {watchedBudget && watchedDuration && (
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="pt-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-gray-600">Daily Budget</div>
                      <div className="font-semibold">
                        CHF {(watchedBudget / watchedDuration).toFixed(2)}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-600">Est. Appointments</div>
                      <div className="font-semibold">{Math.floor(watchedBudget / 45)}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold mb-2">Choose Your Advertising Platforms</h3>
              <p className="text-gray-600">
                Select one or more platforms where you want to run your ads. You can always add more
                later.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {availablePlatforms.map((platform) => {
                const isSelected = selectedPlatforms.includes(platform.id);
                const isSupported =
                  platform.supported_business_types.includes('all') ||
                  platform.supported_business_types.includes(
                    businessProfiles.find((p) => p.id === watch('business_profile_id'))
                      ?.business_type,
                  );

                return (
                  <div
                    key={platform.id}
                    className={`relative p-6 rounded-lg border-2 cursor-pointer transition-all duration-300 ${
                      isSelected
                        ? 'border-blue-500 bg-blue-50'
                        : isSupported
                          ? 'border-gray-200 hover:border-gray-300 bg-white'
                          : 'border-gray-100 bg-gray-50 opacity-50 cursor-not-allowed'
                    }`}
                    onClick={() => isSupported && handlePlatformToggle(platform.id)}
                  >
                    {platform.recommended && (
                      <div className="absolute -top-2 -right-2">
                        <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                          Recommended
                        </span>
                      </div>
                    )}

                    <div className="text-center">
                      <div className="text-4xl mb-3">{platform.icon}</div>
                      <h4 className="font-semibold text-lg mb-2">{platform.name}</h4>
                      <p className="text-sm text-gray-600 mb-4">{platform.description}</p>

                      {isSelected && (
                        <div className="flex items-center justify-center text-blue-600">
                          <CheckCircle className="w-5 h-5 mr-2" />
                          <span className="font-medium">Selected</span>
                        </div>
                      )}

                      {!isSupported && (
                        <div className="text-xs text-gray-500 mt-2">
                          Not recommended for your business type
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            {selectedPlatforms.length > 0 && (
              <Card className="bg-green-50 border-green-200">
                <CardContent className="pt-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <span className="font-medium text-green-800">
                      {selectedPlatforms.length} platform{selectedPlatforms.length > 1 ? 's' : ''}{' '}
                      selected
                    </span>
                  </div>
                  <p className="text-sm text-green-700">
                    Your campaign will run on:{' '}
                    {selectedPlatforms
                      .map((id) => availablePlatforms.find((p) => p.id === id)?.name)
                      .join(', ')}
                  </p>
                </CardContent>
              </Card>
            )}

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-2">💡 Platform Tips</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>
                  • <strong>Meta Ads:</strong> Great for local businesses, visual services, and
                  broad reach
                </li>
                <li>
                  • <strong>Google Ads:</strong> Perfect for capturing high-intent searches and
                  emergency services
                </li>
                <li>
                  • <strong>LinkedIn Ads:</strong> Ideal for B2B services and professional
                  consultations
                </li>
              </ul>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            {isGeneratingAI ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-spin">
                  <Sparkles className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">AI is Creating Your Ads</h3>
                <p className="text-gray-600">
                  Our AI is analyzing your business and generating compelling ad content...
                </p>
                <div className="mt-4">
                  <div className="w-64 h-2 bg-gray-200 rounded-full mx-auto overflow-hidden">
                    <div className="h-full bg-gradient-to-r from-blue-600 to-purple-600 rounded-full animate-pulse"></div>
                  </div>
                </div>
              </div>
            ) : aiGeneratedContent ? (
              <div className="space-y-6">
                <div className="text-center">
                  <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-3" />
                  <h3 className="text-xl font-semibold mb-2">AI Content Generated!</h3>
                  <p className="text-gray-600">
                    Review the AI-generated content below. You can customize it later.
                  </p>
                </div>

                {/* Ad Copy Variations */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Ad Copy Variations</CardTitle>
                    <CardDescription>Choose your favorite or use all variations</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {aiGeneratedContent.ad_copy_variations.map((copy: string, index: number) => (
                        <div key={index} className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-start justify-between">
                            <p className="text-sm">{copy}</p>
                            <span className="text-xs text-gray-500 ml-2">#{index + 1}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Headlines */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Headlines</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      {aiGeneratedContent.headlines.map((headline: string, index: number) => (
                        <div key={index} className="p-3 bg-gray-50 rounded-lg text-center">
                          <p className="font-medium text-sm">{headline}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Performance Estimate */}
                <Card className="bg-green-50 border-green-200">
                  <CardHeader>
                    <CardTitle className="text-lg text-green-800">Estimated Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {aiGeneratedContent.estimated_performance.estimated_clicks}
                        </div>
                        <div className="text-sm text-gray-600">Expected Clicks</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {aiGeneratedContent.estimated_performance.estimated_impressions.toLocaleString()}
                        </div>
                        <div className="text-sm text-gray-600">Impressions</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {aiGeneratedContent.estimated_performance.estimated_conversions}
                        </div>
                        <div className="text-sm text-gray-600">Appointments</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          CHF{' '}
                          {aiGeneratedContent.estimated_performance.estimated_cost_per_conversion}
                        </div>
                        <div className="text-sm text-gray-600">Cost per Appointment</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : null}
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Zap className="w-12 h-12 text-blue-600 mx-auto mb-3" />
              <h3 className="text-xl font-semibold mb-2">Ready to Launch!</h3>
              <p className="text-gray-600">
                Your multi-platform campaign is ready to go live. Review the summary below.
              </p>
            </div>

            {/* Campaign Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Campaign Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm text-gray-600">Campaign Name</Label>
                    <p className="font-medium">{watch('name')}</p>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-600">Platforms</Label>
                    <p className="font-medium">
                      {selectedPlatforms
                        .map((id) => availablePlatforms.find((p) => p.id === id)?.name)
                        .join(', ')}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-600">Budget</Label>
                    <p className="font-medium">CHF {watch('budget')}</p>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-600">Duration</Label>
                    <p className="font-medium">{watch('duration_days')} days</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Next Steps */}
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="pt-6">
                <h4 className="font-semibold mb-3">What happens next?</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span>Campaign will be created in draft mode</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span>You'll be redirected to connect your Meta Ads account</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span>Review and approve before going live</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Create New Campaign
        </h1>
        <p className="text-gray-600 mt-2">
          Let AI create compelling ads for your wellness practice
        </p>
      </div>

      {/* Progress Steps */}
      <div className="flex items-center justify-center space-x-4 mb-8">
        {steps.map((step, index) => (
          <div key={step.number} className="flex items-center">
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep >= step.number ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
              }`}
            >
              {currentStep > step.number ? <CheckCircle className="w-5 h-5" /> : step.number}
            </div>
            {index < steps.length - 1 && (
              <div
                className={`w-16 h-1 mx-2 ${
                  currentStep > step.number ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <Card className="shadow-lg">
        <CardHeader className="text-center">
          <CardTitle className="text-xl">{steps[currentStep - 1].title}</CardTitle>
          <CardDescription>{steps[currentStep - 1].description}</CardDescription>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderStep()}
            </motion.div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8">
              <Button
                type="button"
                variant="outline"
                onClick={handlePrevStep}
                disabled={currentStep === 1}
                className="px-6"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>

              {currentStep < steps.length ? (
                <Button
                  type="button"
                  onClick={handleNextStep}
                  disabled={isGeneratingAI || (currentStep === 2 && selectedPlatforms.length === 0)}
                  className="px-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  {currentStep === 1 && 'Select Platforms'}
                  {currentStep === 2 && 'Generate AI Content'}
                  {currentStep === 3 && 'Review Campaign'}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              ) : (
                <Button
                  type="submit"
                  disabled={isCreatingCampaign}
                  className="px-6 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
                >
                  {isCreatingCampaign ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Creating Campaign...
                    </>
                  ) : (
                    <>
                      Create Campaign
                      <Zap className="w-4 h-4 ml-2" />
                    </>
                  )}
                </Button>
              )}
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default CreateCampaignPage;
