'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Settings,
  Building,
  Users,
  Bell,
  Shield,
  CreditCard,
  Zap,
  Phone,
  Mail,
  Globe,
  Save,
} from 'lucide-react';
import { useCompany } from '@/contexts/CompanyContext';

const SettingsPage: React.FC = () => {
  const { currentCompany } = useCompany();
  const [loading, setLoading] = useState(false);

  // Company Settings
  const [companySettings, setCompanySettings] = useState({
    name: currentCompany?.name || '',
    email: currentCompany?.email || '',
    phone: currentCompany?.phone || '',
    address: currentCompany?.address || '',
    website: '',
    timezone: 'America/New_York',
    businessHours: {
      monday: { open: '09:00', close: '17:00', enabled: true },
      tuesday: { open: '09:00', close: '17:00', enabled: true },
      wednesday: { open: '09:00', close: '17:00', enabled: true },
      thursday: { open: '09:00', close: '17:00', enabled: true },
      friday: { open: '09:00', close: '17:00', enabled: true },
      saturday: { open: '09:00', close: '15:00', enabled: false },
      sunday: { open: '10:00', close: '14:00', enabled: false },
    },
  });

  // Notification Settings
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    smsNotifications: true,
    pushNotifications: true,
    newBookings: true,
    missedCalls: true,
    customerFeedback: true,
    systemUpdates: false,
    marketingEmails: false,
  });

  // Integration Settings
  const [integrationSettings, setIntegrationSettings] = useState({
    twilioEnabled: true,
    googleCalendarEnabled: true,
    elevenlabsEnabled: true,
    difyEnabled: false,
    webhooksEnabled: false,
    apiAccessEnabled: true,
  });

  const handleSaveCompanySettings = async () => {
    setLoading(true);
    try {
      // TODO: API call to save company settings
      console.log('Saving company settings:', companySettings);
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000));
      alert('Company settings saved successfully!');
    } catch (error) {
      console.error('Error saving company settings:', error);
      alert('Error saving settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveNotificationSettings = async () => {
    setLoading(true);
    try {
      // TODO: API call to save notification settings
      console.log('Saving notification settings:', notificationSettings);
      await new Promise((resolve) => setTimeout(resolve, 1000));
      alert('Notification settings saved successfully!');
    } catch (error) {
      console.error('Error saving notification settings:', error);
      alert('Error saving settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveIntegrationSettings = async () => {
    setLoading(true);
    try {
      // TODO: API call to save integration settings
      console.log('Saving integration settings:', integrationSettings);
      await new Promise((resolve) => setTimeout(resolve, 1000));
      alert('Integration settings saved successfully!');
    } catch (error) {
      console.error('Error saving integration settings:', error);
      alert('Error saving settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex justify-between items-center animate-slide-down">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Settings
          </h1>
          <p className="text-muted-foreground mt-1">Manage your company settings and preferences</p>
        </div>
      </div>

      <Tabs defaultValue="company" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="company" className="flex items-center space-x-2">
            <Building className="w-4 h-4" />
            <span className="hidden sm:inline">Company</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center space-x-2">
            <Bell className="w-4 h-4" />
            <span className="hidden sm:inline">Notifications</span>
          </TabsTrigger>
          <TabsTrigger value="integrations" className="flex items-center space-x-2">
            <Zap className="w-4 h-4" />
            <span className="hidden sm:inline">Integrations</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center space-x-2">
            <Shield className="w-4 h-4" />
            <span className="hidden sm:inline">Security</span>
          </TabsTrigger>
          <TabsTrigger value="billing" className="flex items-center space-x-2">
            <CreditCard className="w-4 h-4" />
            <span className="hidden sm:inline">Billing</span>
          </TabsTrigger>
        </TabsList>

        {/* Company Settings */}
        <TabsContent value="company" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Company Information</CardTitle>
              <CardDescription>
                Update your company details and business information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="companyName">Company Name</Label>
                  <Input
                    id="companyName"
                    value={companySettings.name}
                    onChange={(e) =>
                      setCompanySettings((prev) => ({ ...prev, name: e.target.value }))
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="companyEmail">Email</Label>
                  <Input
                    id="companyEmail"
                    type="email"
                    value={companySettings.email}
                    onChange={(e) =>
                      setCompanySettings((prev) => ({ ...prev, email: e.target.value }))
                    }
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="companyPhone">Phone</Label>
                  <Input
                    id="companyPhone"
                    value={companySettings.phone}
                    onChange={(e) =>
                      setCompanySettings((prev) => ({ ...prev, phone: e.target.value }))
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="companyWebsite">Website</Label>
                  <Input
                    id="companyWebsite"
                    value={companySettings.website}
                    onChange={(e) =>
                      setCompanySettings((prev) => ({ ...prev, website: e.target.value }))
                    }
                    placeholder="https://www.yourcompany.com"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="companyAddress">Address</Label>
                <Textarea
                  id="companyAddress"
                  value={companySettings.address}
                  onChange={(e) =>
                    setCompanySettings((prev) => ({ ...prev, address: e.target.value }))
                  }
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="timezone">Timezone</Label>
                <Select
                  value={companySettings.timezone}
                  onValueChange={(value) =>
                    setCompanySettings((prev) => ({ ...prev, timezone: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="America/New_York">Eastern Time</SelectItem>
                    <SelectItem value="America/Chicago">Central Time</SelectItem>
                    <SelectItem value="America/Denver">Mountain Time</SelectItem>
                    <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-end">
                <Button onClick={handleSaveCompanySettings} disabled={loading}>
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notification Settings */}
        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>
                Choose how you want to be notified about important events
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Email Notifications</Label>
                    <p className="text-sm text-gray-600">Receive notifications via email</p>
                  </div>
                  <Switch
                    checked={notificationSettings.emailNotifications}
                    onCheckedChange={(checked) =>
                      setNotificationSettings((prev) => ({ ...prev, emailNotifications: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>SMS Notifications</Label>
                    <p className="text-sm text-gray-600">Receive notifications via text message</p>
                  </div>
                  <Switch
                    checked={notificationSettings.smsNotifications}
                    onCheckedChange={(checked) =>
                      setNotificationSettings((prev) => ({ ...prev, smsNotifications: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>New Bookings</Label>
                    <p className="text-sm text-gray-600">
                      Get notified when customers book appointments
                    </p>
                  </div>
                  <Switch
                    checked={notificationSettings.newBookings}
                    onCheckedChange={(checked) =>
                      setNotificationSettings((prev) => ({ ...prev, newBookings: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Missed Calls</Label>
                    <p className="text-sm text-gray-600">
                      Get notified about missed customer calls
                    </p>
                  </div>
                  <Switch
                    checked={notificationSettings.missedCalls}
                    onCheckedChange={(checked) =>
                      setNotificationSettings((prev) => ({ ...prev, missedCalls: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Customer Feedback</Label>
                    <p className="text-sm text-gray-600">
                      Get notified when customers leave feedback
                    </p>
                  </div>
                  <Switch
                    checked={notificationSettings.customerFeedback}
                    onCheckedChange={(checked) =>
                      setNotificationSettings((prev) => ({ ...prev, customerFeedback: checked }))
                    }
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={handleSaveNotificationSettings} disabled={loading}>
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Integration Settings */}
        <TabsContent value="integrations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Integration Settings</CardTitle>
              <CardDescription>
                Manage your third-party integrations and API connections
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Twilio Phone Service</Label>
                    <p className="text-sm text-gray-600">Enable phone call handling via Twilio</p>
                  </div>
                  <Switch
                    checked={integrationSettings.twilioEnabled}
                    onCheckedChange={(checked) =>
                      setIntegrationSettings((prev) => ({ ...prev, twilioEnabled: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Google Calendar</Label>
                    <p className="text-sm text-gray-600">Sync appointments with Google Calendar</p>
                  </div>
                  <Switch
                    checked={integrationSettings.googleCalendarEnabled}
                    onCheckedChange={(checked) =>
                      setIntegrationSettings((prev) => ({
                        ...prev,
                        googleCalendarEnabled: checked,
                      }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>ElevenLabs Voice AI</Label>
                    <p className="text-sm text-gray-600">Enable natural voice conversations</p>
                  </div>
                  <Switch
                    checked={integrationSettings.elevenlabsEnabled}
                    onCheckedChange={(checked) =>
                      setIntegrationSettings((prev) => ({ ...prev, elevenlabsEnabled: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Dify AI Platform</Label>
                    <p className="text-sm text-gray-600">Enable text-based AI conversations</p>
                  </div>
                  <Switch
                    checked={integrationSettings.difyEnabled}
                    onCheckedChange={(checked) =>
                      setIntegrationSettings((prev) => ({ ...prev, difyEnabled: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>API Access</Label>
                    <p className="text-sm text-gray-600">
                      Allow external applications to access your data
                    </p>
                  </div>
                  <Switch
                    checked={integrationSettings.apiAccessEnabled}
                    onCheckedChange={(checked) =>
                      setIntegrationSettings((prev) => ({ ...prev, apiAccessEnabled: checked }))
                    }
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={handleSaveIntegrationSettings} disabled={loading}>
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>Manage your account security and access controls</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Security Settings</h3>
                <p className="text-gray-600">
                  Advanced security settings will be available in a future update
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Billing Settings */}
        <TabsContent value="billing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Billing & Subscription</CardTitle>
              <CardDescription>Manage your subscription and billing information</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Billing Management</h3>
                <p className="text-gray-600">
                  Billing and subscription management will be available in a future update
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SettingsPage;
