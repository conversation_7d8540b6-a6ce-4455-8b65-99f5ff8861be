'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';

const IntegrationCallbackPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [message, setMessage] = useState('Processing your connection...');
  const [platformName, setPlatformName] = useState('');

  useEffect(() => {
    handleOAuthCallback();
  }, []);

  const handleOAuthCallback = async () => {
    try {
      const code = searchParams.get('code');
      const state = searchParams.get('state');
      const error = searchParams.get('error');
      const platform = searchParams.get('platform') || 'unknown';

      // Set platform name for display
      const platformNames: { [key: string]: string } = {
        'google_ads': 'Google Ads',
        'linkedin_ads': 'LinkedIn Ads',
        'meta_ads': 'Meta Ads'
      };
      setPlatformName(platformNames[platform] || platform);

      if (error) {
        setStatus('error');
        setMessage(`Connection failed: ${error}`);
        return;
      }

      if (!code) {
        setStatus('error');
        setMessage('No authorization code received');
        return;
      }

      // Exchange code for token
      const response = await fetch(`/api/integrations/${platform}/oauth-callback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code,
          redirect_uri: window.location.origin + '/settings/integrations/callback',
          state
        })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setStatus('success');
        setMessage(`Successfully connected your ${platformNames[platform] || platform} account!`);
        
        // Redirect to integrations page after 3 seconds
        setTimeout(() => {
          router.push('/settings/integrations');
        }, 3000);
      } else {
        setStatus('error');
        setMessage(data.message || 'Failed to connect account');
      }

    } catch (error) {
      console.error('OAuth callback error:', error);
      setStatus('error');
      setMessage('An unexpected error occurred');
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return <RefreshCw className="w-12 h-12 text-blue-600 animate-spin" />;
      case 'success':
        return <CheckCircle className="w-12 h-12 text-green-600" />;
      case 'error':
        return <AlertCircle className="w-12 h-12 text-red-600" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'processing':
        return 'text-blue-600';
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="shadow-lg">
          <CardHeader className="text-center pb-4">
            <div className="flex justify-center mb-4">
              {getStatusIcon()}
            </div>
            <CardTitle className={`text-xl ${getStatusColor()}`}>
              {status === 'processing' && 'Connecting Account'}
              {status === 'success' && 'Connection Successful!'}
              {status === 'error' && 'Connection Failed'}
            </CardTitle>
          </CardHeader>
          
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600">
              {message}
            </p>

            {platformName && (
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-sm text-gray-700">
                  Platform: <span className="font-medium">{platformName}</span>
                </p>
              </div>
            )}

            {status === 'success' && (
              <div className="space-y-2">
                <p className="text-sm text-green-700">
                  You can now create campaigns on this platform!
                </p>
                <p className="text-xs text-gray-500">
                  Redirecting to integrations page in 3 seconds...
                </p>
              </div>
            )}

            {status === 'error' && (
              <div className="space-y-3">
                <p className="text-sm text-red-700">
                  Please try connecting again or contact support if the problem persists.
                </p>
                <Button
                  onClick={() => router.push('/settings/integrations')}
                  variant="outline"
                  className="w-full"
                >
                  Back to Integrations
                </Button>
              </div>
            )}

            {status === 'processing' && (
              <div className="flex justify-center">
                <div className="w-6 h-1 bg-blue-200 rounded-full overflow-hidden">
                  <div className="w-full h-full bg-blue-600 rounded-full animate-pulse"></div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default IntegrationCallbackPage;
