'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Settings, 
  Link as LinkIcon, 
  CheckCircle, 
  AlertCircle,
  ExternalLink,
  Trash2,
  RefreshCw,
  Shield,
  Zap
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: string;
  status: 'connected' | 'disconnected' | 'error';
  connected_at?: string;
  account_info?: {
    name?: string;
    email?: string;
    account_id?: string;
  };
  features: string[];
  business_types: string[];
}

const IntegrationsPage: React.FC = () => {
  const { user } = useAuth();
  const [integrations, setIntegrations] = useState<Integration[]>([]);
  const [loading, setLoading] = useState(true);
  const [connectingPlatform, setConnectingPlatform] = useState<string | null>(null);

  useEffect(() => {
    fetchIntegrations();
  }, []);

  const fetchIntegrations = async () => {
    try {
      // Mock data - in real implementation, fetch from API
      const mockIntegrations: Integration[] = [
        {
          id: 'meta_ads',
          name: 'Meta Ads',
          description: 'Connect your Facebook and Instagram advertising account',
          icon: '📘',
          status: 'connected',
          connected_at: '2024-01-15T10:00:00Z',
          account_info: {
            name: 'Business Account',
            email: '<EMAIL>',
            account_id: 'act_123456789'
          },
          features: ['Campaign Creation', 'Performance Tracking', 'Automated Optimization'],
          business_types: ['all']
        },
        {
          id: 'google_ads',
          name: 'Google Ads',
          description: 'Connect your Google Ads account for search and display advertising',
          icon: '🔍',
          status: 'disconnected',
          features: ['Search Campaigns', 'Display Campaigns', 'Performance Analytics', 'Keyword Management'],
          business_types: ['all']
        },
        {
          id: 'linkedin_ads',
          name: 'LinkedIn Ads',
          description: 'Connect your LinkedIn advertising account for professional targeting',
          icon: '💼',
          status: 'disconnected',
          features: ['Sponsored Content', 'Lead Generation', 'Professional Targeting', 'B2B Analytics'],
          business_types: ['legal', 'accounting', 'consulting', 'real_estate', 'insurance', 'financial_planning']
        }
      ];

      setIntegrations(mockIntegrations);
    } catch (error) {
      console.error('Error fetching integrations:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleConnect = async (platformId: string) => {
    setConnectingPlatform(platformId);
    
    try {
      // Get OAuth URL from backend
      const response = await fetch(`/api/integrations/${platformId}/oauth-url?redirect_uri=${encodeURIComponent(window.location.origin + '/settings/integrations/callback')}`);
      const data = await response.json();
      
      if (data.oauth_url) {
        // Redirect to OAuth provider
        window.location.href = data.oauth_url;
      }
    } catch (error) {
      console.error(`Error connecting to ${platformId}:`, error);
      setConnectingPlatform(null);
    }
  };

  const handleDisconnect = async (platformId: string) => {
    try {
      // Mock disconnect - in real implementation, call API to revoke tokens
      console.log(`Disconnecting ${platformId}`);
      
      // Update local state
      setIntegrations(prev => 
        prev.map(integration => 
          integration.id === platformId 
            ? { ...integration, status: 'disconnected' as const, connected_at: undefined, account_info: undefined }
            : integration
        )
      );
    } catch (error) {
      console.error(`Error disconnecting ${platformId}:`, error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      default:
        return <LinkIcon className="w-4 h-4 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading integrations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Platform Integrations
        </h1>
        <p className="text-gray-600 mt-2">
          Connect your advertising accounts to create and manage campaigns across multiple platforms
        </p>
      </div>

      {/* Integration Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {integrations.map((integration, index) => (
          <motion.div
            key={integration.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <Card className="h-full hover:shadow-lg transition-shadow duration-300">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="text-3xl">{integration.icon}</div>
                    <div>
                      <CardTitle className="text-xl">{integration.name}</CardTitle>
                      <CardDescription className="mt-1">
                        {integration.description}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(integration.status)}
                    <Badge className={getStatusColor(integration.status)}>
                      {integration.status === 'connected' ? 'Connected' : 'Not Connected'}
                    </Badge>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Account Info */}
                {integration.status === 'connected' && integration.account_info && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 className="font-medium text-green-800 mb-2">Connected Account</h4>
                    <div className="text-sm text-green-700 space-y-1">
                      {integration.account_info.name && (
                        <div>Account: {integration.account_info.name}</div>
                      )}
                      {integration.account_info.email && (
                        <div>Email: {integration.account_info.email}</div>
                      )}
                      {integration.account_info.account_id && (
                        <div>ID: {integration.account_info.account_id}</div>
                      )}
                      {integration.connected_at && (
                        <div>Connected: {new Date(integration.connected_at).toLocaleDateString()}</div>
                      )}
                    </div>
                  </div>
                )}

                {/* Features */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Features</h4>
                  <div className="flex flex-wrap gap-2">
                    {integration.features.map((feature, featureIndex) => (
                      <Badge key={featureIndex} variant="secondary" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Business Types */}
                {!integration.business_types.includes('all') && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Recommended For</h4>
                    <div className="flex flex-wrap gap-2">
                      {integration.business_types.slice(0, 3).map((type, typeIndex) => (
                        <Badge key={typeIndex} variant="outline" className="text-xs">
                          {type.replace('_', ' ')}
                        </Badge>
                      ))}
                      {integration.business_types.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{integration.business_types.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t">
                  {integration.status === 'connected' ? (
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDisconnect(integration.id)}
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Disconnect
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => fetchIntegrations()}
                      >
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Refresh
                      </Button>
                    </div>
                  ) : (
                    <Button
                      onClick={() => handleConnect(integration.id)}
                      disabled={connectingPlatform === integration.id}
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                    >
                      {connectingPlatform === integration.id ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          Connecting...
                        </>
                      ) : (
                        <>
                          <LinkIcon className="w-4 h-4 mr-2" />
                          Connect Account
                        </>
                      )}
                    </Button>
                  )}
                  
                  <Button variant="ghost" size="sm">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Learn More
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Security Notice */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <Shield className="w-6 h-6 text-blue-600 mt-1" />
            <div>
              <h3 className="font-semibold text-blue-900 mb-2">Security & Privacy</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <p>
                  Your account credentials are securely stored and encrypted. We only access the minimum 
                  permissions required to create and manage your advertising campaigns.
                </p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>OAuth 2.0 secure authentication</li>
                  <li>Encrypted token storage</li>
                  <li>Limited API permissions</li>
                  <li>Regular security audits</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Benefits */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="w-5 h-5 text-yellow-600" />
            <span>Multi-Platform Benefits</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Increased Reach</h4>
              <p className="text-sm text-gray-600">
                Reach customers across multiple platforms where they spend their time online.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Better ROI</h4>
              <p className="text-sm text-gray-600">
                Optimize budget allocation across platforms based on performance data.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Unified Management</h4>
              <p className="text-sm text-gray-600">
                Manage all your campaigns from one dashboard with consistent reporting.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">AI Optimization</h4>
              <p className="text-sm text-gray-600">
                Our AI automatically optimizes campaigns across platforms for best results.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default IntegrationsPage;
