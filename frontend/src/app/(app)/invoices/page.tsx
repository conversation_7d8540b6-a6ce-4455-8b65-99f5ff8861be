'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Receipt,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  DollarSign,
  Calendar,
  User,
  Eye,
  Edit,
  Send,
  Download,
  CreditCard,
  CheckCircle,
  Clock,
  XCircle,
  AlertTriangle,
  TrendingUp,
  RefreshCw,
  Mail
} from 'lucide-react';
import { useCompany } from '@/contexts/CompanyContext';
import { toast } from 'react-hot-toast';

interface Invoice {
  id: string;
  invoice_number: string;
  customer_id: string;
  customer_name: string;
  customer_email?: string;
  customer_phone?: string;
  billing_address: string;
  service_address?: string;
  line_items: Array<{
    id: string;
    description: string;
    quantity: number;
    unit_price: number;
    total: number;
  }>;
  subtotal: number;
  tax_rate: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  amount_paid: number;
  balance_due: number;
  status: 'draft' | 'sent' | 'viewed' | 'paid' | 'partial' | 'overdue' | 'cancelled';
  payment_status: 'unpaid' | 'partial' | 'paid' | 'refunded';
  payment_method?: 'cash' | 'check' | 'credit_card' | 'bank_transfer' | 'online';
  issue_date: string;
  due_date: string;
  paid_date?: string;
  sent_date?: string;
  payment_terms: string;
  notes?: string;
  recurring?: {
    enabled: boolean;
    frequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly';
    next_invoice_date?: string;
  };
  created_by: string;
  created_at: string;
  updated_at: string;
}

export default function InvoicesPage() {
  const router = useRouter();
  const { currentCompany } = useCompany();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState('all');

  useEffect(() => {
    if (currentCompany) {
      loadInvoices();
    }
  }, [currentCompany]);

  const loadInvoices = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockInvoices: Invoice[] = [
        {
          id: 'inv_001',
          invoice_number: 'INV-2024-001',
          customer_id: 'cust_001',
          customer_name: 'John Smith',
          customer_email: '<EMAIL>',
          customer_phone: '+****************',
          billing_address: '123 Main St, Anytown, CA 12345',
          service_address: '123 Main St, Anytown, CA 12345',
          line_items: [
            {
              id: 'item_001',
              description: 'Kitchen Sink Repair - Fixed leaking faucet and replaced gaskets',
              quantity: 1,
              unit_price: 185.00,
              total: 185.00
            },
            {
              id: 'item_002',
              description: 'Emergency Service Fee - After hours call',
              quantity: 1,
              unit_price: 50.00,
              total: 50.00
            }
          ],
          subtotal: 235.00,
          tax_rate: 8.25,
          tax_amount: 19.39,
          discount_amount: 0,
          total_amount: 254.39,
          amount_paid: 254.39,
          balance_due: 0,
          status: 'paid',
          payment_status: 'paid',
          payment_method: 'credit_card',
          issue_date: '2024-01-15',
          due_date: '2024-01-30',
          paid_date: '2024-01-16',
          sent_date: '2024-01-15',
          payment_terms: 'Net 15',
          notes: 'Thank you for your business!',
          created_by: 'Mike Johnson',
          created_at: '2024-01-15T16:00:00Z',
          updated_at: '2024-01-16T10:30:00Z'
        },
        {
          id: 'inv_002',
          invoice_number: 'INV-2024-002',
          customer_id: 'cust_002',
          customer_name: 'Sarah Wilson',
          customer_email: '<EMAIL>',
          customer_phone: '+****************',
          billing_address: '456 Oak Ave, Business City, CA 54321',
          service_address: '456 Oak Ave, Business City, CA 54321',
          line_items: [
            {
              id: 'item_003',
              description: 'HVAC System Maintenance - Annual inspection and cleaning (3 units)',
              quantity: 3,
              unit_price: 300.00,
              total: 900.00
            }
          ],
          subtotal: 900.00,
          tax_rate: 8.25,
          tax_amount: 74.25,
          discount_amount: 50.00,
          total_amount: 924.25,
          amount_paid: 500.00,
          balance_due: 424.25,
          status: 'partial',
          payment_status: 'partial',
          payment_method: 'check',
          issue_date: '2024-01-20',
          due_date: '2024-02-04',
          sent_date: '2024-01-20',
          payment_terms: 'Net 15',
          notes: 'Commercial account - partial payment received',
          recurring: {
            enabled: true,
            frequency: 'yearly',
            next_invoice_date: '2025-01-20'
          },
          created_by: 'David Brown',
          created_at: '2024-01-20T14:00:00Z',
          updated_at: '2024-01-22T09:15:00Z'
        },
        {
          id: 'inv_003',
          invoice_number: 'INV-2024-003',
          customer_id: 'cust_003',
          customer_name: 'Mike Johnson',
          customer_email: '<EMAIL>',
          customer_phone: '+****************',
          billing_address: '789 Pine St, Hometown, CA 67890',
          service_address: '789 Pine St, Hometown, CA 67890',
          line_items: [
            {
              id: 'item_004',
              description: 'Electrical Panel Upgrade - 200 amp service installation',
              quantity: 1,
              unit_price: 1500.00,
              total: 1500.00
            },
            {
              id: 'item_005',
              description: 'Permit and Inspection Fees',
              quantity: 1,
              unit_price: 150.00,
              total: 150.00
            }
          ],
          subtotal: 1650.00,
          tax_rate: 8.25,
          tax_amount: 136.13,
          discount_amount: 0,
          total_amount: 1786.13,
          amount_paid: 0,
          balance_due: 1786.13,
          status: 'overdue',
          payment_status: 'unpaid',
          issue_date: '2024-01-10',
          due_date: '2024-01-25',
          sent_date: '2024-01-10',
          payment_terms: 'Net 15',
          notes: 'Large project - payment overdue',
          created_by: 'Sarah Wilson',
          created_at: '2024-01-10T11:00:00Z',
          updated_at: '2024-01-25T16:00:00Z'
        },
        {
          id: 'inv_004',
          invoice_number: 'INV-2024-004',
          customer_id: 'cust_004',
          customer_name: 'Lisa Davis',
          customer_email: '<EMAIL>',
          customer_phone: '+****************',
          billing_address: '321 Elm St, Newtown, CA 98765',
          service_address: '321 Elm St, Newtown, CA 98765',
          line_items: [
            {
              id: 'item_006',
              description: 'Bathroom Faucet Replacement',
              quantity: 2,
              unit_price: 125.00,
              total: 250.00
            }
          ],
          subtotal: 250.00,
          tax_rate: 8.25,
          tax_amount: 20.63,
          discount_amount: 25.00,
          total_amount: 245.63,
          amount_paid: 0,
          balance_due: 245.63,
          status: 'sent',
          payment_status: 'unpaid',
          issue_date: '2024-01-25',
          due_date: '2024-02-09',
          sent_date: '2024-01-25',
          payment_terms: 'Net 15',
          notes: 'First-time customer discount applied',
          created_by: 'Mike Johnson',
          created_at: '2024-01-25T13:30:00Z',
          updated_at: '2024-01-25T13:30:00Z'
        }
      ];

      setInvoices(mockInvoices);
    } catch (error) {
      console.error('Failed to load invoices:', error);
      toast.error('Failed to load invoices');
    } finally {
      setLoading(false);
    }
  };

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = 
      invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.line_items.some(item => 
        item.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    
    const matchesTab = selectedTab === 'all' || 
      invoice.status === selectedTab ||
      invoice.payment_status === selectedTab;
    
    return matchesSearch && matchesTab;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'sent':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'viewed':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'paid':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'partial':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Edit className="w-3 h-3" />;
      case 'sent':
        return <Send className="w-3 h-3" />;
      case 'viewed':
        return <Eye className="w-3 h-3" />;
      case 'paid':
        return <CheckCircle className="w-3 h-3" />;
      case 'partial':
        return <Clock className="w-3 h-3" />;
      case 'overdue':
        return <AlertTriangle className="w-3 h-3" />;
      case 'cancelled':
        return <XCircle className="w-3 h-3" />;
      default:
        return <Clock className="w-3 h-3" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const calculateStats = () => {
    const totalInvoiced = invoices.reduce((sum, inv) => sum + inv.total_amount, 0);
    const totalPaid = invoices.reduce((sum, inv) => sum + inv.amount_paid, 0);
    const totalOutstanding = invoices.reduce((sum, inv) => sum + inv.balance_due, 0);
    const overdueAmount = invoices
      .filter(inv => inv.status === 'overdue')
      .reduce((sum, inv) => sum + inv.balance_due, 0);

    return { totalInvoiced, totalPaid, totalOutstanding, overdueAmount };
  };

  const stats = calculateStats();

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Invoices & Payments</h1>
          <p className="text-gray-600">
            Manage invoices, track payments, and monitor cash flow
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <TrendingUp className="w-4 h-4 mr-2" />
            Financial Reports
          </Button>
          <Button onClick={() => router.push('/invoices/new')} variant="gradient">
            <Plus className="w-4 h-4 mr-2" />
            Create Invoice
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Invoiced</CardTitle>
            <Receipt className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalInvoiced)}</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Paid</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalPaid)}</div>
            <p className="text-xs text-muted-foreground">
              Collected revenue
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Outstanding</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalOutstanding)}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting payment
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{formatCurrency(stats.overdueAmount)}</div>
            <p className="text-xs text-muted-foreground">
              Requires attention
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search invoices by number, customer, or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Invoices List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Invoices</CardTitle>
            <Tabs value={selectedTab} onValueChange={setSelectedTab}>
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="unpaid">Unpaid</TabsTrigger>
                <TabsTrigger value="paid">Paid</TabsTrigger>
                <TabsTrigger value="overdue">Overdue</TabsTrigger>
                <TabsTrigger value="draft">Draft</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Invoice #</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Paid</TableHead>
                <TableHead>Balance</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInvoices.map((invoice) => (
                <TableRow 
                  key={invoice.id}
                  className="cursor-pointer hover:bg-accent/5 transition-colors"
                  onClick={() => router.push(`/invoices/${invoice.id}`)}
                >
                  <TableCell>
                    <div>
                      <p className="font-medium">{invoice.invoice_number}</p>
                      <p className="text-sm text-gray-600">
                        {new Date(invoice.issue_date).toLocaleDateString()}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">{invoice.customer_name}</p>
                      <p className="text-sm text-gray-600">{invoice.customer_phone}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">
                      {formatCurrency(invoice.total_amount)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="text-green-600 font-medium">
                      {formatCurrency(invoice.amount_paid)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className={`font-medium ${
                      invoice.balance_due > 0 ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {formatCurrency(invoice.balance_due)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(invoice.status)}>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(invoice.status)}
                        <span className="capitalize">{invoice.status}</span>
                      </div>
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className={`text-sm ${
                      new Date(invoice.due_date) < new Date() && invoice.balance_due > 0
                        ? 'text-red-600 font-medium'
                        : 'text-gray-600'
                    }`}>
                      {new Date(invoice.due_date).toLocaleDateString()}
                    </span>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" onClick={(e) => e.stopPropagation()}>
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-white border shadow-lg">
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/invoices/${invoice.id}`);
                        }}>
                          <Eye className="w-4 h-4 mr-2" />
                          View Invoice
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/invoices/${invoice.id}/edit`);
                        }}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit Invoice
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          // TODO: Implement send invoice
                        }}>
                          <Send className="w-4 h-4 mr-2" />
                          Send Invoice
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          // TODO: Implement record payment
                        }}>
                          <CreditCard className="w-4 h-4 mr-2" />
                          Record Payment
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          // TODO: Implement download
                        }}>
                          <Download className="w-4 h-4 mr-2" />
                          Download PDF
                        </DropdownMenuItem>
                        {invoice.status === 'overdue' && (
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            // TODO: Implement reminder
                          }}>
                            <Mail className="w-4 h-4 mr-2" />
                            Send Reminder
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
