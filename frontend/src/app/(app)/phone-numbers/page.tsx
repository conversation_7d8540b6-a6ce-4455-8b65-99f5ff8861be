'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Phone,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Settings,
  Activity,
  Users,
  MessageSquare,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
} from 'lucide-react';
import { useCompany } from '@/contexts/CompanyContext';

interface PhoneNumber {
  id: number;
  phone_number: string;
  formatted_number: string;
  provider: string;
  status: 'active' | 'inactive' | 'pending';
  agent_id?: number;
  agent_name?: string;
  call_count: number;
  last_call?: string;
  created_at: string;
}

const PhoneNumbersPage: React.FC = () => {
  const { currentCompany } = useCompany();
  const [phoneNumbers, setPhoneNumbers] = useState<PhoneNumber[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState('all');

  useEffect(() => {
    const fetchPhoneNumbers = async () => {
      if (!currentCompany) return;

      try {
        // TODO: Replace with actual API call
        // const response = await fetch(`/api/phone-numbers?company_id=${currentCompany.id}`);
        // const data = await response.json();

        // Mock data for now
        const mockData: PhoneNumber[] = [
          {
            id: 1,
            phone_number: '+15551234567',
            formatted_number: '+****************',
            provider: 'Twilio',
            status: 'active',
            agent_id: 1,
            agent_name: 'AI Assistant Sarah',
            call_count: 156,
            last_call: '2 hours ago',
            created_at: '2024-01-15T10:00:00Z',
          },
          {
            id: 2,
            phone_number: '+15559876543',
            formatted_number: '+****************',
            provider: 'Twilio',
            status: 'active',
            agent_id: 2,
            agent_name: 'AI Assistant Mike',
            call_count: 89,
            last_call: '1 day ago',
            created_at: '2024-01-10T14:30:00Z',
          },
          {
            id: 3,
            phone_number: '+15555555555',
            formatted_number: '+****************',
            provider: 'Twilio',
            status: 'pending',
            call_count: 0,
            created_at: '2024-01-20T09:15:00Z',
          },
        ];

        setPhoneNumbers(mockData);
      } catch (error) {
        console.error('Error fetching phone numbers:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPhoneNumbers();
  }, [currentCompany]);

  const filteredNumbers = phoneNumbers.filter((number) => {
    const matchesSearch =
      number.formatted_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      number.agent_name?.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedTab === 'all') return matchesSearch;
    return matchesSearch && number.status === selectedTab;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'inactive':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex justify-between items-center animate-slide-down">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Phone Numbers
          </h1>
          <p className="text-muted-foreground mt-1">
            Manage your business phone numbers and AI agent assignments
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" className="hover-lift">
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
          <Button className="hover-lift">
            <Plus className="w-4 h-4 mr-2" />
            Add Number
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Numbers</p>
                <p className="text-2xl font-bold text-gray-900">{phoneNumbers.length}</p>
              </div>
              <div className="p-3 rounded-full bg-blue-100">
                <Phone className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Numbers</p>
                <p className="text-2xl font-bold text-gray-900">
                  {phoneNumbers.filter((n) => n.status === 'active').length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-green-100">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Calls</p>
                <p className="text-2xl font-bold text-gray-900">
                  {phoneNumbers.reduce((sum, n) => sum + n.call_count, 0)}
                </p>
              </div>
              <div className="p-3 rounded-full bg-purple-100">
                <Activity className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Assigned Agents</p>
                <p className="text-2xl font-bold text-gray-900">
                  {phoneNumbers.filter((n) => n.agent_id).length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-orange-100">
                <Users className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search phone numbers or agents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline">
          <Filter className="w-4 h-4 mr-2" />
          Filter
        </Button>
      </div>

      {/* Phone Numbers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Phone Numbers</CardTitle>
          <CardDescription>
            Manage your business phone numbers and their AI agent assignments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList>
              <TabsTrigger value="all">All ({phoneNumbers.length})</TabsTrigger>
              <TabsTrigger value="active">
                Active ({phoneNumbers.filter((n) => n.status === 'active').length})
              </TabsTrigger>
              <TabsTrigger value="pending">
                Pending ({phoneNumbers.filter((n) => n.status === 'pending').length})
              </TabsTrigger>
              <TabsTrigger value="inactive">
                Inactive ({phoneNumbers.filter((n) => n.status === 'inactive').length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value={selectedTab} className="mt-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Phone Number</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Assigned Agent</TableHead>
                    <TableHead>Provider</TableHead>
                    <TableHead>Calls</TableHead>
                    <TableHead>Last Call</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredNumbers.map((number) => (
                    <TableRow key={number.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-blue-100 rounded-lg">
                            <Phone className="w-4 h-4 text-blue-600" />
                          </div>
                          <div>
                            <p className="font-medium">{number.formatted_number}</p>
                            <p className="text-sm text-gray-600">
                              Added {new Date(number.created_at).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(number.status)}
                          <Badge className={getStatusColor(number.status)}>{number.status}</Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        {number.agent_name ? (
                          <div className="flex items-center space-x-2">
                            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                              <Users className="w-4 h-4 text-green-600" />
                            </div>
                            <span className="text-sm">{number.agent_name}</span>
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">Not assigned</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{number.provider}</Badge>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">{number.call_count}</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-gray-600">{number.last_call || 'Never'}</span>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredNumbers.length === 0 && (
                <div className="text-center py-8">
                  <Phone className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No phone numbers found</h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm
                      ? 'Try adjusting your search terms'
                      : 'Get started by adding your first phone number'}
                  </p>
                  {!searchTerm && (
                    <Button>
                      <Plus className="w-4 h-4 mr-2" />
                      Add Phone Number
                    </Button>
                  )}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default PhoneNumbersPage;
