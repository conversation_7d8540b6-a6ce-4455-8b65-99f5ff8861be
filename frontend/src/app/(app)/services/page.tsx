'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Wrench,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Clock,
  DollarSign,
  Star,
  Eye,
  Edit,
  Trash2,
  Copy,
  TrendingUp,
  Calendar,
  AlertCircle,
  CheckCircle,
  Zap,
  Home,
  Building2,
  Snowflake,
  Sun
} from 'lucide-react';
import { useCompany } from '@/contexts/CompanyContext';
import { toast } from 'react-hot-toast';

interface Service {
  id: string;
  name: string;
  description: string;
  category: 'plumbing' | 'hvac' | 'electrical' | 'general' | 'emergency';
  service_type: 'residential' | 'commercial' | 'both';
  pricing: {
    type: 'fixed' | 'hourly' | 'estimate';
    base_price: number;
    hourly_rate?: number;
    min_charge?: number;
  };
  duration: {
    estimated_hours: number;
    min_hours: number;
    max_hours: number;
  };
  requirements: {
    skills_required: string[];
    certifications_required: string[];
    tools_required: string[];
    parts_commonly_used: string[];
  };
  availability: {
    seasonal: boolean;
    emergency: boolean;
    business_hours_only: boolean;
    advance_booking_required: boolean;
    min_notice_hours: number;
  };
  popularity: {
    bookings_last_month: number;
    avg_rating: number;
    total_reviews: number;
  };
  status: 'active' | 'inactive' | 'seasonal';
  created_at: string;
  updated_at: string;
}

export default function ServicesPage() {
  const router = useRouter();
  const { currentCompany } = useCompany();
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState('all');

  useEffect(() => {
    if (currentCompany) {
      loadServices();
    }
  }, [currentCompany]);

  const loadServices = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockServices: Service[] = [
        {
          id: 'svc_001',
          name: 'Kitchen Sink Repair',
          description: 'Repair leaking kitchen sinks, replace faucets, fix disposal issues',
          category: 'plumbing',
          service_type: 'residential',
          pricing: {
            type: 'fixed',
            base_price: 150.00,
            min_charge: 75.00
          },
          duration: {
            estimated_hours: 2,
            min_hours: 1,
            max_hours: 4
          },
          requirements: {
            skills_required: ['Plumbing'],
            certifications_required: ['Plumbing License'],
            tools_required: ['Pipe wrench', 'Pliers', 'Screwdrivers'],
            parts_commonly_used: ['Faucet parts', 'Gaskets', 'Pipes']
          },
          availability: {
            seasonal: false,
            emergency: true,
            business_hours_only: false,
            advance_booking_required: false,
            min_notice_hours: 2
          },
          popularity: {
            bookings_last_month: 45,
            avg_rating: 4.8,
            total_reviews: 127
          },
          status: 'active',
          created_at: '2023-01-15T10:00:00Z',
          updated_at: '2024-01-10T14:30:00Z'
        },
        {
          id: 'svc_002',
          name: 'HVAC System Maintenance',
          description: 'Annual HVAC system inspection, cleaning, and tune-up',
          category: 'hvac',
          service_type: 'both',
          pricing: {
            type: 'fixed',
            base_price: 200.00
          },
          duration: {
            estimated_hours: 3,
            min_hours: 2,
            max_hours: 5
          },
          requirements: {
            skills_required: ['HVAC'],
            certifications_required: ['EPA 608', 'HVAC License'],
            tools_required: ['Multimeter', 'Gauges', 'Vacuum pump'],
            parts_commonly_used: ['Filters', 'Belts', 'Refrigerant']
          },
          availability: {
            seasonal: true,
            emergency: false,
            business_hours_only: true,
            advance_booking_required: true,
            min_notice_hours: 24
          },
          popularity: {
            bookings_last_month: 32,
            avg_rating: 4.9,
            total_reviews: 89
          },
          status: 'active',
          created_at: '2023-02-01T09:00:00Z',
          updated_at: '2024-01-05T11:15:00Z'
        },
        {
          id: 'svc_003',
          name: 'Electrical Outlet Installation',
          description: 'Install new electrical outlets, GFCI outlets, USB outlets',
          category: 'electrical',
          service_type: 'residential',
          pricing: {
            type: 'fixed',
            base_price: 125.00,
            min_charge: 85.00
          },
          duration: {
            estimated_hours: 1.5,
            min_hours: 1,
            max_hours: 3
          },
          requirements: {
            skills_required: ['Electrical'],
            certifications_required: ['Electrical License'],
            tools_required: ['Wire strippers', 'Voltage tester', 'Drill'],
            parts_commonly_used: ['Outlets', 'Wire', 'Wire nuts']
          },
          availability: {
            seasonal: false,
            emergency: false,
            business_hours_only: true,
            advance_booking_required: true,
            min_notice_hours: 12
          },
          popularity: {
            bookings_last_month: 28,
            avg_rating: 4.7,
            total_reviews: 64
          },
          status: 'active',
          created_at: '2023-03-10T14:00:00Z',
          updated_at: '2023-12-20T16:45:00Z'
        },
        {
          id: 'svc_004',
          name: 'Emergency Plumbing Service',
          description: '24/7 emergency plumbing repairs for burst pipes, major leaks',
          category: 'emergency',
          service_type: 'both',
          pricing: {
            type: 'hourly',
            base_price: 200.00,
            hourly_rate: 150.00,
            min_charge: 200.00
          },
          duration: {
            estimated_hours: 2,
            min_hours: 1,
            max_hours: 8
          },
          requirements: {
            skills_required: ['Plumbing', 'Emergency Response'],
            certifications_required: ['Master Plumber'],
            tools_required: ['Full plumbing toolkit', 'Emergency supplies'],
            parts_commonly_used: ['Pipes', 'Fittings', 'Shut-off valves']
          },
          availability: {
            seasonal: false,
            emergency: true,
            business_hours_only: false,
            advance_booking_required: false,
            min_notice_hours: 0
          },
          popularity: {
            bookings_last_month: 18,
            avg_rating: 4.6,
            total_reviews: 42
          },
          status: 'active',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2024-01-15T08:30:00Z'
        }
      ];

      setServices(mockServices);
    } catch (error) {
      console.error('Failed to load services:', error);
      toast.error('Failed to load services');
    } finally {
      setLoading(false);
    }
  };

  const filteredServices = services.filter(service => {
    const matchesSearch = 
      service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.category.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesTab = selectedTab === 'all' || 
      service.category === selectedTab ||
      service.status === selectedTab;
    
    return matchesSearch && matchesTab;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'seasonal':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'plumbing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'hvac':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'electrical':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'general':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'emergency':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'plumbing':
        return <Wrench className="w-4 h-4" />;
      case 'hvac':
        return <Snowflake className="w-4 h-4" />;
      case 'electrical':
        return <Zap className="w-4 h-4" />;
      case 'general':
        return <Home className="w-4 h-4" />;
      case 'emergency':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Wrench className="w-4 h-4" />;
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-4 h-4 ${
              i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">({rating.toFixed(1)})</span>
      </div>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPricing = (pricing: Service['pricing']) => {
    switch (pricing.type) {
      case 'fixed':
        return `${formatCurrency(pricing.base_price)} fixed`;
      case 'hourly':
        return `${formatCurrency(pricing.hourly_rate || 0)}/hr`;
      case 'estimate':
        return 'Custom estimate';
      default:
        return 'Contact for pricing';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Service Catalog</h1>
          <p className="text-gray-600">
            Manage your service offerings, pricing, and availability
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <TrendingUp className="w-4 h-4 mr-2" />
            Service Analytics
          </Button>
          <Button onClick={() => router.push('/services/new')} variant="gradient">
            <Plus className="w-4 h-4 mr-2" />
            Add Service
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Services</CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{services.length}</div>
            <p className="text-xs text-muted-foreground">
              Across all categories
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Services</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {services.filter(s => s.status === 'active').length}
            </div>
            <p className="text-xs text-muted-foreground">
              Available for booking
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(services.reduce((sum, s) => sum + s.popularity.avg_rating, 0) / services.length).toFixed(1)}
            </div>
            <p className="text-xs text-muted-foreground">
              Customer satisfaction
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Bookings</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {services.reduce((sum, s) => sum + s.popularity.bookings_last_month, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Last 30 days
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search services by name, description, or category..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Services List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Services</CardTitle>
            <Tabs value={selectedTab} onValueChange={setSelectedTab}>
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="plumbing">Plumbing</TabsTrigger>
                <TabsTrigger value="hvac">HVAC</TabsTrigger>
                <TabsTrigger value="electrical">Electrical</TabsTrigger>
                <TabsTrigger value="emergency">Emergency</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Service</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Pricing</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Bookings</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredServices.map((service) => (
                <TableRow 
                  key={service.id}
                  className="cursor-pointer hover:bg-accent/5 transition-colors"
                  onClick={() => router.push(`/services/${service.id}`)}
                >
                  <TableCell>
                    <div>
                      <p className="font-medium">{service.name}</p>
                      <p className="text-sm text-gray-600 line-clamp-1">
                        {service.description}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Badge className={getCategoryColor(service.category)}>
                        <div className="flex items-center gap-1">
                          {getCategoryIcon(service.category)}
                          <span className="capitalize">{service.category}</span>
                        </div>
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="capitalize">
                      {service.service_type}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">
                      {formatPricing(service.pricing)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      <span className="text-sm">
                        {service.duration.estimated_hours}h
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {renderStars(service.popularity.avg_rating)}
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">
                      {service.popularity.bookings_last_month}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(service.status)}>
                      {service.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" onClick={(e) => e.stopPropagation()}>
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-white border shadow-lg">
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/services/${service.id}`);
                        }}>
                          <Eye className="w-4 h-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/services/${service.id}/edit`);
                        }}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit Service
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          // TODO: Implement duplication
                        }}>
                          <Copy className="w-4 h-4 mr-2" />
                          Duplicate Service
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/bookings/new?service=${service.id}`);
                        }}>
                          <Calendar className="w-4 h-4 mr-2" />
                          Book Service
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
