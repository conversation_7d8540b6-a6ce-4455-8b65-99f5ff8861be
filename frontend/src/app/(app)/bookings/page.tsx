'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Calendar,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Clock,
  User,
  MapPin,
  DollarSign,
  CheckCircle,
  AlertCircle,
  CalendarDays,
  Phone,
} from 'lucide-react';
import { useCompany } from '@/contexts/CompanyContext';
import JobCompletionModal from '@/components/bookings/JobCompletionModal';
import { toast } from 'react-hot-toast';

interface BookingRequest {
  id: number;
  request_number: string;
  customer_name: string;
  customer_phone?: string;
  service_type: string;
  service_description: string;
  scheduled_date?: string;
  scheduled_time?: string;
  service_address: string;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  assigned_technician?: string;
  estimated_cost?: number;
  quoted_price?: number;
  created_at: string;
}

const BookingsPage: React.FC = () => {
  const { currentCompany } = useCompany();
  const router = useRouter();
  const [serviceRequests, setBookingRequests] = useState<BookingRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState('all');
  const [completionModalOpen, setCompletionModalOpen] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<BookingRequest | null>(null);

  useEffect(() => {
    const fetchBookingRequests = async () => {
      if (!currentCompany) return;

      try {
        // TODO: Replace with actual API call
        // const response = await fetch(`/api/bookings?company_id=${currentCompany.id}`);
        // const data = await response.json();

        // Mock data for now
        const mockData: BookingRequest[] = [
          {
            id: 1,
            request_number: 'REQ-2024-001',
            customer_name: 'John Smith',
            customer_phone: '+****************',
            service_type: 'Plumbing Repair',
            service_description: 'Kitchen sink leak repair',
            scheduled_date: '2024-01-22',
            scheduled_time: '14:00',
            service_address: '123 Main St, Anytown, ST 12345',
            status: 'confirmed',
            priority: 'high',
            assigned_technician: 'Mike Johnson',
            estimated_cost: 150,
            quoted_price: 175,
            created_at: '2024-01-20T10:30:00Z',
          },
          {
            id: 2,
            request_number: 'REQ-2024-002',
            customer_name: 'Sarah Wilson',
            customer_phone: '+****************',
            service_type: 'HVAC Maintenance',
            service_description: 'Annual HVAC system inspection and cleaning',
            scheduled_date: '2024-01-23',
            scheduled_time: '10:00',
            service_address: '456 Oak Ave, Anytown, ST 12345',
            status: 'pending',
            priority: 'normal',
            estimated_cost: 200,
            created_at: '2024-01-20T09:15:00Z',
          },
          {
            id: 3,
            request_number: 'REQ-2024-003',
            customer_name: 'Mike Davis',
            customer_phone: '+****************',
            service_type: 'Electrical Repair',
            service_description: 'Outlet not working in home office',
            service_address: '789 Pine St, Anytown, ST 12345',
            status: 'in_progress',
            priority: 'urgent',
            assigned_technician: 'Sarah Thompson',
            estimated_cost: 100,
            quoted_price: 125,
            created_at: '2024-01-20T08:45:00Z',
          },
          {
            id: 4,
            request_number: 'REQ-2024-004',
            customer_name: 'Lisa Brown',
            customer_phone: '+****************',
            service_type: 'General Handyman',
            service_description: 'Install ceiling fan in bedroom',
            scheduled_date: '2024-01-24',
            scheduled_time: '16:00',
            service_address: '321 Elm St, Anytown, ST 12345',
            status: 'completed',
            priority: 'low',
            assigned_technician: 'Tom Wilson',
            estimated_cost: 80,
            quoted_price: 95,
            created_at: '2024-01-19T15:20:00Z',
          },
        ];

        setBookingRequests(mockData);
      } catch (error) {
        console.error('Error fetching service requests:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBookingRequests();
  }, [currentCompany]);

  const filteredRequests = serviceRequests.filter((request) => {
    const matchesSearch =
      request.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.request_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.service_type.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedTab === 'all') return matchesSearch;
    return matchesSearch && request.status === selectedTab;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'normal':
        return 'bg-blue-100 text-blue-800';
      case 'low':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleCompleteJob = (request: BookingRequest) => {
    setSelectedRequest(request);
    setCompletionModalOpen(true);
  };

  const handleJobCompletion = async (completionData: any) => {
    if (!selectedRequest) return;

    try {
      // TODO: Call API to update job status
      // await updateBookingRequest(selectedRequest.id, completionData);

      // Update local state
      setBookingRequests((prev) =>
        prev.map((req) =>
          req.id === selectedRequest.id ? { ...req, status: 'completed' as const } : req,
        ),
      );

      toast.success('Job completed successfully!');
    } catch (error) {
      console.error('Failed to complete job:', error);
      toast.error('Failed to complete job');
      throw error;
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex justify-between items-center animate-slide-down">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Service Bookings
          </h1>
          <p className="text-muted-foreground mt-1">
            Manage customer service requests and appointments
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" className="hover-lift">
            <CalendarDays className="w-4 h-4 mr-2" />
            Calendar View
          </Button>
          <Button className="hover-lift">
            <Plus className="w-4 h-4 mr-2" />
            New Booking
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                <p className="text-2xl font-bold text-gray-900">{serviceRequests.length}</p>
              </div>
              <div className="p-3 rounded-full bg-blue-100">
                <Calendar className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Confirmed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {serviceRequests.filter((r) => r.status === 'confirmed').length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-green-100">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">In Progress</p>
                <p className="text-2xl font-bold text-gray-900">
                  {serviceRequests.filter((r) => r.status === 'in_progress').length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-orange-100">
                <Clock className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  $
                  {serviceRequests
                    .filter((r) => r.quoted_price)
                    .reduce((sum, r) => sum + (r.quoted_price || 0), 0)
                    .toLocaleString()}
                </p>
              </div>
              <div className="p-3 rounded-full bg-purple-100">
                <DollarSign className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search bookings by customer, service type, or request number..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline">
          <Filter className="w-4 h-4 mr-2" />
          Filter
        </Button>
      </div>

      {/* Service Requests Table */}
      <Card>
        <CardHeader>
          <CardTitle>Service Requests</CardTitle>
          <CardDescription>All customer service bookings and their current status</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList>
              <TabsTrigger value="all">All ({serviceRequests.length})</TabsTrigger>
              <TabsTrigger value="pending">
                Pending ({serviceRequests.filter((r) => r.status === 'pending').length})
              </TabsTrigger>
              <TabsTrigger value="confirmed">
                Confirmed ({serviceRequests.filter((r) => r.status === 'confirmed').length})
              </TabsTrigger>
              <TabsTrigger value="in_progress">
                In Progress ({serviceRequests.filter((r) => r.status === 'in_progress').length})
              </TabsTrigger>
              <TabsTrigger value="completed">
                Completed ({serviceRequests.filter((r) => r.status === 'completed').length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value={selectedTab} className="mt-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Request</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Service</TableHead>
                    <TableHead>Scheduled</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Technician</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRequests.map((request) => (
                    <TableRow
                      key={request.id}
                      className="cursor-pointer hover:bg-accent/5 transition-colors"
                      onClick={() => router.push(`/bookings/${request.request_number}`)}
                    >
                      <TableCell>
                        <div>
                          <p className="font-medium">{request.request_number}</p>
                          <p className="text-sm text-gray-600">
                            {new Date(request.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{request.customer_name}</p>
                          <p className="text-sm text-gray-600">{request.customer_phone}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{request.service_type}</p>
                          <p className="text-sm text-gray-600 max-w-xs truncate">
                            {request.service_description}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        {request.scheduled_date ? (
                          <div>
                            <p className="text-sm font-medium">
                              {new Date(request.scheduled_date).toLocaleDateString()}
                            </p>
                            <p className="text-sm text-gray-600">{request.scheduled_time}</p>
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">Not scheduled</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(request.status)}>
                          {request.status.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getPriorityColor(request.priority)}>
                          {request.priority}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">
                          {request.assigned_technician || 'Unassigned'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div>
                          {request.quoted_price && (
                            <p className="font-medium">${request.quoted_price}</p>
                          )}
                          {request.estimated_cost && (
                            <p className="text-sm text-gray-600">Est: ${request.estimated_cost}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {request.status === 'in_progress' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleCompleteJob(request)}
                              className="text-green-600 hover:text-green-700"
                            >
                              <CheckCircle className="w-4 h-4 mr-1" />
                              Complete
                            </Button>
                          )}
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredRequests.length === 0 && (
                <div className="text-center py-8">
                  <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No bookings found</h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm
                      ? 'Try adjusting your search terms'
                      : 'Service bookings will appear here as customers schedule appointments'}
                  </p>
                  {!searchTerm && (
                    <Button>
                      <Plus className="w-4 h-4 mr-2" />
                      Create Booking
                    </Button>
                  )}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Job Completion Modal */}
      <JobCompletionModal
        isOpen={completionModalOpen}
        onClose={() => {
          setCompletionModalOpen(false);
          setSelectedRequest(null);
        }}
        serviceRequest={selectedRequest}
        onComplete={handleJobCompletion}
      />
    </div>
  );
};

export default BookingsPage;
