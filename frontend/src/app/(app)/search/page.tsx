'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Bot, 
  User, 
  Calendar, 
  Phone, 
  MessageSquare,
  ArrowRight,
  Clock,
  CheckCircle
} from 'lucide-react';
import { useCompany } from '@/contexts/CompanyContext';

interface SearchResult {
  id: string;
  type: 'agent' | 'customer' | 'booking' | 'conversation';
  title: string;
  description: string;
  status?: string;
  date?: string;
  metadata?: Record<string, any>;
}

export default function SearchPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { currentCompany } = useCompany();
  const [query, setQuery] = useState(searchParams?.get('q') || '');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const searchQuery = searchParams?.get('q');
    if (searchQuery) {
      setQuery(searchQuery);
      performSearch(searchQuery);
    }
  }, [searchParams]);

  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) return;
    
    setLoading(true);
    try {
      // Mock search results - replace with actual API call
      const mockResults: SearchResult[] = [
        {
          id: 'agent-1',
          type: 'agent',
          title: 'Customer Service Agent',
          description: 'Multi-modal AI agent handling customer inquiries',
          status: 'active',
          metadata: { provider: 'elevenlabs', conversations: 1247 }
        },
        {
          id: 'customer-1',
          type: 'customer',
          title: 'John Smith',
          description: 'Regular customer - Plumbing services',
          date: '2024-01-15',
          metadata: { phone: '+****************', bookings: 3 }
        },
        {
          id: 'booking-1',
          type: 'booking',
          title: 'Plumbing Repair - Kitchen Sink',
          description: 'Scheduled for tomorrow at 2:00 PM',
          status: 'confirmed',
          date: '2024-01-16',
          metadata: { customer: 'John Smith', service: 'Plumbing' }
        },
        {
          id: 'conversation-1',
          type: 'conversation',
          title: 'Phone Call - Emergency Repair',
          description: 'Customer called about urgent plumbing issue',
          date: '2024-01-15',
          metadata: { duration: '5:23', agent: 'Customer Service Agent' }
        }
      ];

      // Filter results based on search query
      const filteredResults = mockResults.filter(result =>
        result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        result.description.toLowerCase().includes(searchQuery.toLowerCase())
      );

      setResults(filteredResults);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      router.push(`/search?q=${encodeURIComponent(query.trim())}`);
    }
  };

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'agent':
        return <Bot className="w-5 h-5 text-blue-600" />;
      case 'customer':
        return <User className="w-5 h-5 text-green-600" />;
      case 'booking':
        return <Calendar className="w-5 h-5 text-purple-600" />;
      case 'conversation':
        return <MessageSquare className="w-5 h-5 text-orange-600" />;
      default:
        return <Search className="w-5 h-5 text-gray-600" />;
    }
  };

  const getResultPath = (result: SearchResult) => {
    switch (result.type) {
      case 'agent':
        return `/agents/${result.id}`;
      case 'customer':
        return `/customers/${result.id}`;
      case 'booking':
        return `/bookings/${result.id}`;
      case 'conversation':
        return `/conversations/${result.id}`;
      default:
        return '#';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Search</h1>
        <p className="text-gray-600 mt-1">
          Find agents, customers, bookings, and conversations
        </p>
      </div>

      {/* Search Form */}
      <Card>
        <CardContent className="p-6">
          <form onSubmit={handleSearch} className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search for anything..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button type="submit" disabled={loading}>
              {loading ? 'Searching...' : 'Search'}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Results */}
      {query && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">
              Search Results for "{query}"
            </h2>
            <Badge variant="outline">
              {results.length} result{results.length !== 1 ? 's' : ''}
            </Badge>
          </div>

          {loading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                        <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : results.length > 0 ? (
            <div className="space-y-4">
              {results.map((result) => (
                <Card 
                  key={result.id} 
                  className="hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => router.push(getResultPath(result))}
                >
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                          {getResultIcon(result.type)}
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">{result.title}</h3>
                          <p className="text-sm text-gray-600">{result.description}</p>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant="outline" className="capitalize">
                              {result.type}
                            </Badge>
                            {result.status && (
                              <Badge 
                                variant={result.status === 'active' || result.status === 'confirmed' ? 'default' : 'secondary'}
                                className="capitalize"
                              >
                                {result.status}
                              </Badge>
                            )}
                            {result.date && (
                              <div className="flex items-center text-xs text-gray-500">
                                <Clock className="w-3 h-3 mr-1" />
                                {result.date}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <ArrowRight className="w-5 h-5 text-gray-400" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                <p className="text-gray-600">
                  Try adjusting your search terms or browse our categories
                </p>
                <div className="flex justify-center gap-2 mt-6">
                  <Button variant="outline" onClick={() => router.push('/agents')}>
                    Browse Agents
                  </Button>
                  <Button variant="outline" onClick={() => router.push('/bookings')}>
                    View Bookings
                  </Button>
                  <Button variant="outline" onClick={() => router.push('/conversations')}>
                    See Conversations
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
