'use client';

import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useCompany } from '@/contexts/CompanyContext';
import {
  ArrowLeft,
  Save,
  Settings,
  Bot,
  Phone,
  MessageSquare,
  Mic,
  Play,
  Pause,
  MoreVertical,
  Edit,
  Trash2,
  TestTube,
  BarChart3,
  Zap,
  Volume2,
  Eye,
  Copy,
  Download,
  Upload,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Info,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'react-hot-toast';
import Link from 'next/link';

interface Agent {
  id: string;
  name: string;
  description: string;
  type: 'voice' | 'chat' | 'multi-modal';
  status: 'active' | 'inactive' | 'training';
  provider: 'elevenlabs' | 'dify' | 'openai';
  system_prompt: string;
  variables: Record<string, string>;
  settings: {
    temperature: number;
    max_tokens: number;
    voice_id?: string;
    language: string;
    response_format: string;
    model?: string;
    voice_settings?: {
      stability: number;
      similarity_boost: number;
      style: number;
      use_speaker_boost: boolean;
    };
  };
  phone_numbers: string[];
  integrations: {
    twilio_enabled: boolean;
    websocket_enabled: boolean;
    chat_widget_enabled: boolean;
  };
  performance: {
    total_conversations: number;
    success_rate: number;
    avg_response_time: number;
    customer_satisfaction: number;
  };
  created_at: string;
  updated_at: string;
}

interface TestResult {
  success: boolean;
  response: string;
  duration: number;
  error?: string;
}

export default function AgentConfigPage() {
  const params = useParams();
  const router = useRouter();
  const { currentCompany } = useCompany();
  const agentId = params.agentId as string;

  const [agent, setAgent] = useState<Agent | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [testing, setTesting] = useState(false);
  const [testMessage, setTestMessage] = useState('Hello, I need help with my plumbing issue.');

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'voice' as Agent['type'],
    provider: 'elevenlabs' as Agent['provider'],
    system_prompt: '',
    variables: {} as Record<string, string>,
    settings: {
      temperature: 0.7,
      max_tokens: 1000,
      voice_id: '',
      language: 'en',
      response_format: 'text',
      model: 'gpt-4',
      voice_settings: {
        stability: 0.5,
        similarity_boost: 0.5,
        style: 0.0,
        use_speaker_boost: true,
      },
    },
    phone_numbers: [] as string[],
    integrations: {
      twilio_enabled: true,
      websocket_enabled: true,
      chat_widget_enabled: true,
    },
  });

  useEffect(() => {
    if (agentId && currentCompany) {
      loadAgent();
    }
  }, [agentId, currentCompany]);

  const loadAgent = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockAgent: Agent = {
        id: agentId,
        name: 'Customer Service Agent',
        description: 'Handles customer inquiries and support requests for home services',
        type: 'multi-modal',
        status: 'active',
        provider: 'elevenlabs',
        system_prompt: `You are a helpful customer service representative for ${currentCompany?.name || 'our home service company'}.

Your role is to:
- Answer customer questions about our services
- Help schedule appointments
- Provide pricing information
- Handle complaints professionally
- Transfer to human agents when needed

Company Information:
- Business Hours: {{business_hours}}
- Phone: {{phone_number}}
- Services: Plumbing, HVAC, Electrical

Always be polite, professional, and helpful. If you cannot help with something, offer to connect them with a human agent.`,
        variables: {
          company_name: currentCompany?.name || 'HomeService Pro',
          business_hours: 'Monday-Friday 8AM-6PM, Saturday 9AM-4PM',
          phone_number: '+****************',
          emergency_number: '+1 (555) 911-HELP',
          website: 'https://homeservicepro.com',
          services: 'Plumbing, HVAC, Electrical, General Repairs',
        },
        settings: {
          temperature: 0.7,
          max_tokens: 1500,
          voice_id: 'rachel',
          language: 'en',
          response_format: 'text',
          model: 'gpt-4',
          voice_settings: {
            stability: 0.6,
            similarity_boost: 0.8,
            style: 0.2,
            use_speaker_boost: true,
          },
        },
        phone_numbers: ['+****************', '+****************'],
        integrations: {
          twilio_enabled: true,
          websocket_enabled: true,
          chat_widget_enabled: true,
        },
        performance: {
          total_conversations: 1247,
          success_rate: 94.2,
          avg_response_time: 1.8,
          customer_satisfaction: 4.6,
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      setAgent(mockAgent);
      setFormData({
        name: mockAgent.name,
        description: mockAgent.description,
        type: mockAgent.type,
        provider: mockAgent.provider,
        system_prompt: mockAgent.system_prompt,
        variables: mockAgent.variables,
        settings: mockAgent.settings,
        phone_numbers: mockAgent.phone_numbers,
        integrations: mockAgent.integrations,
      });
    } catch (error) {
      console.error('Failed to load agent:', error);
      toast.error('Failed to load agent');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      // TODO: Replace with actual API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast.success('Agent updated successfully');
      setIsEditing(false);
      loadAgent(); // Reload to get updated data
    } catch (error) {
      console.error('Failed to save agent:', error);
      toast.error('Failed to save agent');
    } finally {
      setSaving(false);
    }
  };

  const handleToggleStatus = async () => {
    if (!agent) return;

    try {
      // TODO: Replace with actual API call
      const newStatus = agent.status === 'active' ? 'inactive' : 'active';
      setAgent({ ...agent, status: newStatus });
      toast.success(`Agent ${newStatus === 'active' ? 'activated' : 'deactivated'}`);
    } catch (error) {
      console.error('Failed to toggle agent status:', error);
      toast.error('Failed to update agent status');
    }
  };

  const handleTestAgent = async () => {
    if (!testMessage.trim()) {
      toast.error('Please enter a test message');
      return;
    }

    try {
      setTesting(true);
      const startTime = Date.now();

      // TODO: Replace with actual API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      const duration = Date.now() - startTime;
      const mockResponse = `Hello! I understand you're having a plumbing issue. I'd be happy to help you with that. Can you please describe the specific problem you're experiencing? For example, is it a leak, clog, or something else? This will help me provide you with the best assistance and potentially schedule a service appointment if needed.`;

      setTestResult({
        success: true,
        response: mockResponse,
        duration,
      });

      toast.success('Test completed successfully');
    } catch (error) {
      console.error('Failed to test agent:', error);
      setTestResult({
        success: false,
        response: '',
        duration: 0,
        error: 'Test failed: Unable to connect to agent',
      });
      toast.error('Test failed');
    } finally {
      setTesting(false);
    }
  };

  const addVariable = () => {
    const key = prompt('Enter variable name:');
    if (key && !formData.variables[key]) {
      setFormData({
        ...formData,
        variables: { ...formData.variables, [key]: '' },
      });
    }
  };

  const removeVariable = (key: string) => {
    const newVariables = { ...formData.variables };
    delete newVariables[key];
    setFormData({ ...formData, variables: newVariables });
  };

  const addPhoneNumber = () => {
    const phoneNumber = prompt('Enter phone number:');
    if (phoneNumber && !formData.phone_numbers.includes(phoneNumber)) {
      setFormData({
        ...formData,
        phone_numbers: [...formData.phone_numbers, phoneNumber],
      });
    }
  };

  const removePhoneNumber = (phoneNumber: string) => {
    setFormData({
      ...formData,
      phone_numbers: formData.phone_numbers.filter((p) => p !== phoneNumber),
    });
  };

  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-8 bg-gray-200 rounded w-1/4"></div>
        <div className="h-64 bg-gray-200 rounded"></div>
      </div>
    );
  }

  if (!agent) {
    return (
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Agent Not Found</h1>
        <p className="text-gray-600 mb-8">The agent you're looking for doesn't exist.</p>
        <Link href="/agents">
          <Button>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Agents
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/agents">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{agent.name}</h1>
            <p className="text-gray-600">{agent.description}</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Badge
            variant={agent.status === 'active' ? 'default' : 'secondary'}
            className={
              agent.status === 'active'
                ? 'bg-green-500 hover:bg-green-600'
                : 'bg-gray-500 hover:bg-gray-600'
            }
          >
            {agent.status}
          </Badge>
          <Badge variant="outline" className="border-blue-200 text-blue-700 bg-blue-50">
            {agent.provider}
          </Badge>
          <Badge variant="outline" className="border-purple-200 text-purple-700 bg-purple-50">
            {agent.type}
          </Badge>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreVertical className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-white border shadow-lg">
              <DropdownMenuItem onClick={() => setIsEditing(!isEditing)}>
                <Edit className="w-4 h-4 mr-2" />
                {isEditing ? 'Cancel Edit' : 'Edit Agent'}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleToggleStatus}>
                {agent.status === 'active' ? (
                  <>
                    <Pause className="w-4 h-4 mr-2" />
                    Deactivate
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Activate
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Copy className="w-4 h-4 mr-2" />
                Duplicate Agent
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="w-4 h-4 mr-2" />
                Export Configuration
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Upload className="w-4 h-4 mr-2" />
                Import Configuration
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600">
                <Trash2 className="w-4 h-4 mr-2" />
                Delete Agent
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Conversations</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {agent.performance.total_conversations.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">+12% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{agent.performance.success_rate}%</div>
            <Progress value={agent.performance.success_rate} className="mt-2" />
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{agent.performance.avg_response_time}s</div>
            <p className="text-xs text-muted-foreground">-0.3s from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customer Satisfaction</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{agent.performance.customer_satisfaction}/5</div>
            <p className="text-xs text-muted-foreground">+0.2 from last month</p>
          </CardContent>
        </Card>
      </div>

      {/* Configuration Tabs */}
      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="prompt">System Prompt</TabsTrigger>
          <TabsTrigger value="variables">Variables</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
          <TabsTrigger value="testing">Testing</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>Configure the basic settings for your agent</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Agent Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    disabled={!isEditing}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="type">Agent Type</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value: Agent['type']) =>
                      setFormData({ ...formData, type: value })
                    }
                    disabled={!isEditing}
                  >
                    <SelectTrigger className="bg-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-white border shadow-lg">
                      <SelectItem value="voice">Voice Only</SelectItem>
                      <SelectItem value="chat">Chat Only</SelectItem>
                      <SelectItem value="multi-modal">Multi-Modal</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  disabled={!isEditing}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="provider">AI Provider</Label>
                <Select
                  value={formData.provider}
                  onValueChange={(value: Agent['provider']) =>
                    setFormData({ ...formData, provider: value })
                  }
                  disabled={!isEditing}
                >
                  <SelectTrigger className="bg-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-white border shadow-lg">
                    <SelectItem value="elevenlabs">ElevenLabs</SelectItem>
                    <SelectItem value="dify">Dify</SelectItem>
                    <SelectItem value="openai">OpenAI</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Phone Numbers</Label>
                <div className="space-y-2">
                  {formData.phone_numbers.map((phone, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Input value={phone} disabled className="flex-1" />
                      {isEditing && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removePhoneNumber(phone)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                  {isEditing && (
                    <Button variant="outline" onClick={addPhoneNumber}>
                      Add Phone Number
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="prompt" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>System Prompt</CardTitle>
              <CardDescription>Define how your agent should behave and respond</CardDescription>
            </CardHeader>
            <CardContent>
              <Textarea
                value={formData.system_prompt}
                onChange={(e) => setFormData({ ...formData, system_prompt: e.target.value })}
                disabled={!isEditing}
                rows={15}
                placeholder="Enter the system prompt that defines your agent's behavior..."
                className="font-mono text-sm"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="variables" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Agent Variables</CardTitle>
              <CardDescription>
                Set variables that your agent can use in conversations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(formData.variables).map(([key, value]) => (
                <div key={key} className="flex items-center gap-4">
                  <div className="flex-1 space-y-2">
                    <Label>{key.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}</Label>
                    <Input
                      value={value}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          variables: { ...formData.variables, [key]: e.target.value },
                        })
                      }
                      disabled={!isEditing}
                      placeholder={`Enter ${key.replace(/_/g, ' ')}`}
                    />
                  </div>
                  {isEditing && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeVariable(key)}
                      className="mt-6"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              ))}
              {isEditing && (
                <Button variant="outline" onClick={addVariable}>
                  Add Variable
                </Button>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>AI Model Settings</CardTitle>
              <CardDescription>Fine-tune your agent's AI behavior and performance</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="model">Model</Label>
                  <Select
                    value={formData.settings.model}
                    onValueChange={(value) =>
                      setFormData({
                        ...formData,
                        settings: { ...formData.settings, model: value },
                      })
                    }
                    disabled={!isEditing}
                  >
                    <SelectTrigger className="bg-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-white border shadow-lg">
                      <SelectItem value="gpt-4">GPT-4</SelectItem>
                      <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
                      <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="language">Language</Label>
                  <Select
                    value={formData.settings.language}
                    onValueChange={(value) =>
                      setFormData({
                        ...formData,
                        settings: { ...formData.settings, language: value },
                      })
                    }
                    disabled={!isEditing}
                  >
                    <SelectTrigger className="bg-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-white border shadow-lg">
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="es">Spanish</SelectItem>
                      <SelectItem value="fr">French</SelectItem>
                      <SelectItem value="de">German</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="temperature">Temperature ({formData.settings.temperature})</Label>
                  <input
                    type="range"
                    id="temperature"
                    min="0"
                    max="1"
                    step="0.1"
                    value={formData.settings.temperature}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        settings: { ...formData.settings, temperature: parseFloat(e.target.value) },
                      })
                    }
                    disabled={!isEditing}
                    className="w-full"
                  />
                  <p className="text-xs text-gray-500">Controls randomness in responses</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max_tokens">Max Tokens</Label>
                  <Input
                    id="max_tokens"
                    type="number"
                    value={formData.settings.max_tokens}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        settings: { ...formData.settings, max_tokens: parseInt(e.target.value) },
                      })
                    }
                    disabled={!isEditing}
                    min="100"
                    max="4000"
                  />
                  <p className="text-xs text-gray-500">Maximum response length</p>
                </div>
              </div>

              {formData.provider === 'elevenlabs' && (
                <div className="space-y-4">
                  <Separator />
                  <h4 className="text-lg font-medium">Voice Settings</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="voice_id">Voice ID</Label>
                      <Input
                        id="voice_id"
                        value={formData.settings.voice_id}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            settings: { ...formData.settings, voice_id: e.target.value },
                          })
                        }
                        disabled={!isEditing}
                        placeholder="rachel, adam, etc."
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="stability">
                        Stability ({formData.settings.voice_settings?.stability})
                      </Label>
                      <input
                        type="range"
                        id="stability"
                        min="0"
                        max="1"
                        step="0.1"
                        value={formData.settings.voice_settings?.stability || 0.5}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            settings: {
                              ...formData.settings,
                              voice_settings: {
                                ...formData.settings.voice_settings!,
                                stability: parseFloat(e.target.value),
                              },
                            },
                          })
                        }
                        disabled={!isEditing}
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Integration Settings</CardTitle>
              <CardDescription>
                Configure how your agent connects to different channels
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Twilio Phone Integration</Label>
                  <p className="text-sm text-gray-500">Enable voice calls through Twilio</p>
                </div>
                <Switch
                  checked={formData.integrations.twilio_enabled}
                  onCheckedChange={(checked) =>
                    setFormData({
                      ...formData,
                      integrations: { ...formData.integrations, twilio_enabled: checked },
                    })
                  }
                  disabled={!isEditing}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>WebSocket Integration</Label>
                  <p className="text-sm text-gray-500">Enable real-time chat connections</p>
                </div>
                <Switch
                  checked={formData.integrations.websocket_enabled}
                  onCheckedChange={(checked) =>
                    setFormData({
                      ...formData,
                      integrations: { ...formData.integrations, websocket_enabled: checked },
                    })
                  }
                  disabled={!isEditing}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Chat Widget</Label>
                  <p className="text-sm text-gray-500">Enable website chat widget</p>
                </div>
                <Switch
                  checked={formData.integrations.chat_widget_enabled}
                  onCheckedChange={(checked) =>
                    setFormData({
                      ...formData,
                      integrations: { ...formData.integrations, chat_widget_enabled: checked },
                    })
                  }
                  disabled={!isEditing}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="testing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Test Your Agent</CardTitle>
              <CardDescription>Test your agent's responses before deploying</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="test-message">Test Message</Label>
                <Textarea
                  id="test-message"
                  value={testMessage}
                  onChange={(e) => setTestMessage(e.target.value)}
                  placeholder="Enter a message to test your agent..."
                  rows={3}
                />
              </div>

              <Button
                onClick={handleTestAgent}
                disabled={testing || !testMessage.trim()}
                className="w-full"
                variant="success"
              >
                {testing ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Testing...
                  </>
                ) : (
                  <>
                    <TestTube className="w-4 h-4 mr-2" />
                    Test Agent
                  </>
                )}
              </Button>

              {testResult && (
                <div className="mt-4">
                  <Alert
                    className={
                      testResult.success
                        ? 'border-green-200 bg-green-50'
                        : 'border-red-200 bg-red-50'
                    }
                  >
                    {testResult.success ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-600" />
                    )}
                    <AlertDescription>
                      {testResult.success ? (
                        <div className="space-y-2">
                          <p className="font-medium">Test Successful ({testResult.duration}ms)</p>
                          <div className="bg-white p-3 rounded border">
                            <p className="text-sm">{testResult.response}</p>
                          </div>
                        </div>
                      ) : (
                        <p className="text-red-800">{testResult.error}</p>
                      )}
                    </AlertDescription>
                  </Alert>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Save Button */}
      {isEditing && (
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => setIsEditing(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
