'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useCompany } from '@/contexts/CompanyContext';
import { Plus, Bot, Phone, MessageSquare, Settings, Play, Pause, MoreVertical, Search, Filter, Zap, Users, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface Agent {
  id: string;
  name: string;
  description: string;
  type: 'voice' | 'chat' | 'multi-modal';
  status: 'active' | 'inactive' | 'training';
  provider: 'elevenlabs' | 'dify' | 'openai';
  created_at: string;
  updated_at: string;
  performance: {
    total_conversations: number;
    success_rate: number;
    avg_response_time: number;
    customer_satisfaction: number;
  };
}

const AgentsPage: React.FC = () => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const { currentCompany } = useCompany();
  const router = useRouter();

  useEffect(() => {
    if (currentCompany) {
      fetchAgents();
    }
  }, [currentCompany]);

  const fetchAgents = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      // Simulated data for now
      const mockAgents: Agent[] = [
        {
          id: '1',
          name: 'Customer Service Assistant',
          description: 'Handles general customer inquiries and appointment scheduling',
          type: 'voice',
          status: 'active',
          provider: 'elevenlabs',
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-20T14:30:00Z',
          performance: {
            total_conversations: 1247,
            success_rate: 94.2,
            avg_response_time: 2.3,
            customer_satisfaction: 4.6
          }
        },
        {
          id: '2',
          name: 'Emergency Response Agent',
          description: 'Handles urgent service requests and emergency dispatching',
          type: 'multi-modal',
          status: 'active',
          provider: 'dify',
          created_at: '2024-01-10T09:00:00Z',
          updated_at: '2024-01-22T11:15:00Z',
          performance: {
            total_conversations: 892,
            success_rate: 97.8,
            avg_response_time: 1.8,
            customer_satisfaction: 4.8
          }
        },
        {
          id: '3',
          name: 'Quote Generator',
          description: 'Provides service estimates and pricing information',
          type: 'chat',
          status: 'training',
          provider: 'openai',
          created_at: '2024-01-25T16:00:00Z',
          updated_at: '2024-01-25T16:00:00Z',
          performance: {
            total_conversations: 156,
            success_rate: 87.5,
            avg_response_time: 3.1,
            customer_satisfaction: 4.2
          }
        }
      ];
      setAgents(mockAgents);
    } catch (error) {
      console.error('Failed to fetch agents:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredAgents = agents.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         agent.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = filterType === 'all' || agent.type === filterType;
    const matchesStatus = filterStatus === 'all' || agent.status === filterStatus;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'secondary';
      case 'training': return 'warning';
      default: return 'secondary';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'voice': return <Phone className="w-4 h-4" />;
      case 'chat': return <MessageSquare className="w-4 h-4" />;
      case 'multi-modal': return <Zap className="w-4 h-4" />;
      default: return <Bot className="w-4 h-4" />;
    }
  };

  const handleCreateAgent = () => {
    router.push('/agents/new');
  };

  const handleViewTemplates = () => {
    router.push('/agents/templates');
  };

  const handleAgentClick = (agentId: string) => {
    router.push(`/agents/${agentId}`);
  };

  const toggleAgentStatus = async (agentId: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      // TODO: API call to update agent status
      setAgents(prev => prev.map(agent => 
        agent.id === agentId ? { ...agent, status: newStatus as any } : agent
      ));
    } catch (error) {
      console.error('Failed to toggle agent status:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">AI Agents</h1>
          <p className="text-muted-foreground">Manage your AI-powered customer service agents</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleViewTemplates}>
            <Bot className="w-4 h-4 mr-2" />
            Templates
          </Button>
          <Button variant="outline" onClick={handleCreateAgent}>
            <Plus className="w-4 h-4 mr-2" />
            Create Agent
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="animate-slide-up stagger-1">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Agents</p>
                <p className="text-2xl font-bold">{agents.length}</p>
              </div>
              <Bot className="w-8 h-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="animate-slide-up stagger-2">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Agents</p>
                <p className="text-2xl font-bold">
                  {agents.filter((a) => a.status === 'active').length}
                </p>
              </div>
              <Zap className="w-8 h-8 text-success" />
            </div>
          </CardContent>
        </Card>

        <Card className="animate-slide-up stagger-3">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Conversations</p>
                <p className="text-2xl font-bold">
                  {agents
                    .reduce((sum, agent) => sum + agent.performance.total_conversations, 0)
                    .toLocaleString()}
                </p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="animate-slide-up stagger-4">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Success Rate</p>
                <p className="text-2xl font-bold">
                  {agents.length > 0
                    ? (
                        agents.reduce((sum, agent) => sum + agent.performance.success_rate, 0) /
                        agents.length
                      ).toFixed(1)
                    : '0'}
                  %
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className="animate-slide-up stagger-5">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search agents..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="voice">Voice</SelectItem>
                <SelectItem value="chat">Chat</SelectItem>
                <SelectItem value="multi-modal">Multi-Modal</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="training">Training</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Agents Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAgents.map((agent, index) => (
          <Card
            key={agent.id}
            className={`cursor-pointer hover-lift animate-slide-up stagger-${index + 1}`}
            onClick={() => handleAgentClick(agent.id)}
          >
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg">{getTypeIcon(agent.type)}</div>
                  <div>
                    <CardTitle className="text-lg">{agent.name}</CardTitle>
                    <Badge variant={getStatusColor(agent.status)} className="mt-1">
                      {agent.status}
                    </Badge>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                    <Button variant="ghost" size="sm">
                      <MoreVertical className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAgentClick(agent.id);
                      }}
                    >
                      <Settings className="w-4 h-4 mr-2" />
                      Configure
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleAgentStatus(agent.id, agent.status);
                      }}
                    >
                      {agent.status === 'active' ? (
                        <>
                          <Pause className="w-4 h-4 mr-2" />
                          Deactivate
                        </>
                      ) : (
                        <>
                          <Play className="w-4 h-4 mr-2" />
                          Activate
                        </>
                      )}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <CardDescription className="mb-4">{agent.description}</CardDescription>

              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Conversations</span>
                  <span className="font-medium">
                    {agent.performance.total_conversations.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Success Rate</span>
                  <span className="font-medium">{agent.performance.success_rate}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Avg Response</span>
                  <span className="font-medium">{agent.performance.avg_response_time}s</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Satisfaction</span>
                  <span className="font-medium">⭐ {agent.performance.customer_satisfaction}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredAgents.length === 0 && (
        <Card className="animate-fade-in">
          <CardContent className="p-12 text-center">
            <Bot className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No agents found</h3>
            <p className="text-muted-foreground mb-6">
              {searchQuery || filterType !== 'all' || filterStatus !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Create your first AI agent to get started'}
            </p>
            {!searchQuery && filterType === 'all' && filterStatus === 'all' && (
              <Button onClick={handleCreateAgent}>
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Agent
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AgentsPage;
