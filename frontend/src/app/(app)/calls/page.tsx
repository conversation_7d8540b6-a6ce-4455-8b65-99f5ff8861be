'use client';

import React, { useState, useEffect } from 'react';
import { useCompany } from '@/contexts/CompanyContext';
import { Phone, PhoneCall, PhoneIncoming, PhoneOutgoing, PhoneMissed, Clock, User, Calendar, Play, Pause, Volume2, VolumeX, MoreVertical, Search, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

interface Call {
  id: string;
  phone_number: string;
  customer_name?: string;
  direction: 'inbound' | 'outbound';
  status: 'completed' | 'missed' | 'busy' | 'failed' | 'in_progress';
  duration_seconds?: number;
  started_at: string;
  ended_at?: string;
  agent_name?: string;
  recording_url?: string;
  notes?: string;
  outcome?: string;
}

const CallsPage: React.FC = () => {
  const [calls, setCalls] = useState<Call[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterDirection, setFilterDirection] = useState<string>('all');
  const [playingCall, setPlayingCall] = useState<string | null>(null);
  const { currentCompany } = useCompany();

  useEffect(() => {
    if (currentCompany) {
      fetchCalls();
    }
  }, [currentCompany]);

  const fetchCalls = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      // Simulated data for now
      const mockCalls: Call[] = [
        {
          id: '1',
          phone_number: '+****************',
          customer_name: 'John Smith',
          direction: 'inbound',
          status: 'completed',
          duration_seconds: 420,
          started_at: '2024-01-25T10:30:00Z',
          ended_at: '2024-01-25T10:37:00Z',
          agent_name: 'Customer Service Assistant',
          recording_url: '/recordings/call_001.mp3',
          notes: 'Customer called about plumbing leak. Scheduled appointment.',
          outcome: 'appointment_scheduled'
        },
        {
          id: '2',
          phone_number: '+****************',
          customer_name: 'Sarah Johnson',
          direction: 'inbound',
          status: 'completed',
          duration_seconds: 180,
          started_at: '2024-01-25T09:15:00Z',
          ended_at: '2024-01-25T09:18:00Z',
          agent_name: 'Emergency Response Agent',
          recording_url: '/recordings/call_002.mp3',
          notes: 'Quick inquiry about service hours.',
          outcome: 'information_provided'
        },
        {
          id: '3',
          phone_number: '+****************',
          customer_name: 'Mike Wilson',
          direction: 'inbound',
          status: 'missed',
          started_at: '2024-01-25T08:45:00Z',
          notes: 'Missed call - customer did not leave voicemail.'
        },
        {
          id: '4',
          phone_number: '+****************',
          direction: 'outbound',
          status: 'completed',
          duration_seconds: 300,
          started_at: '2024-01-24T16:20:00Z',
          ended_at: '2024-01-24T16:25:00Z',
          agent_name: 'Customer Service Assistant',
          notes: 'Follow-up call for appointment confirmation.',
          outcome: 'appointment_confirmed'
        }
      ];
      setCalls(mockCalls);
    } catch (error) {
      console.error('Failed to fetch calls:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredCalls = calls.filter(call => {
    const matchesSearch = call.phone_number.includes(searchQuery) ||
                         call.customer_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         call.notes?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === 'all' || call.status === filterStatus;
    const matchesDirection = filterDirection === 'all' || call.direction === filterDirection;
    
    return matchesSearch && matchesStatus && matchesDirection;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'missed': return 'destructive';
      case 'busy': return 'warning';
      case 'failed': return 'destructive';
      case 'in_progress': return 'default';
      default: return 'secondary';
    }
  };

  const getDirectionIcon = (direction: string, status: string) => {
    if (status === 'missed') return <PhoneMissed className="w-4 h-4 text-destructive" />;
    if (direction === 'inbound') return <PhoneIncoming className="w-4 h-4 text-blue-500" />;
    return <PhoneOutgoing className="w-4 h-4 text-green-500" />;
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '-';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const handlePlayRecording = (callId: string) => {
    if (playingCall === callId) {
      setPlayingCall(null);
    } else {
      setPlayingCall(callId);
      // TODO: Implement actual audio playback
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 animate-slide-down">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Call Management
          </h1>
          <p className="text-muted-foreground">
            Monitor and manage all customer calls
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="animate-slide-up stagger-1">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Calls</p>
                <p className="text-2xl font-bold">{calls.length}</p>
              </div>
              <Phone className="w-8 h-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="animate-slide-up stagger-2">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold">{calls.filter(c => c.status === 'completed').length}</p>
              </div>
              <PhoneCall className="w-8 h-8 text-success" />
            </div>
          </CardContent>
        </Card>

        <Card className="animate-slide-up stagger-3">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Missed</p>
                <p className="text-2xl font-bold">{calls.filter(c => c.status === 'missed').length}</p>
              </div>
              <PhoneMissed className="w-8 h-8 text-destructive" />
            </div>
          </CardContent>
        </Card>

        <Card className="animate-slide-up stagger-4">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Duration</p>
                <p className="text-2xl font-bold">
                  {formatDuration(
                    Math.round(
                      calls.filter(c => c.duration_seconds).reduce((sum, c) => sum + (c.duration_seconds || 0), 0) /
                      calls.filter(c => c.duration_seconds).length
                    )
                  )}
                </p>
              </div>
              <Clock className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="animate-slide-up stagger-5">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search calls..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="missed">Missed</SelectItem>
                <SelectItem value="busy">Busy</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterDirection} onValueChange={setFilterDirection}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by direction" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Directions</SelectItem>
                <SelectItem value="inbound">Inbound</SelectItem>
                <SelectItem value="outbound">Outbound</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Calls Table */}
      <Card className="animate-slide-up stagger-6" variant="elevated">
        <CardHeader>
          <CardTitle>Recent Calls</CardTitle>
          <CardDescription>
            All customer calls and their details
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Contact</TableHead>
                <TableHead>Direction</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Date & Time</TableHead>
                <TableHead>Agent</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCalls.map((call) => {
                const { date, time } = formatDateTime(call.started_at);
                return (
                  <TableRow key={call.id} className="hover:bg-muted/50">
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {call.customer_name || 'Unknown'}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {call.phone_number}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getDirectionIcon(call.direction, call.status)}
                        <span className="capitalize">{call.direction}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusColor(call.status)}>
                        {call.status.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatDuration(call.duration_seconds)}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{date}</div>
                        <div className="text-sm text-muted-foreground">{time}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {call.agent_name || '-'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {call.recording_url && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handlePlayRecording(call.id)}
                          >
                            {playingCall === call.id ? (
                              <Pause className="w-4 h-4" />
                            ) : (
                              <Play className="w-4 h-4" />
                            )}
                          </Button>
                        )}
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>View Details</DropdownMenuItem>
                            {call.recording_url && (
                              <DropdownMenuItem>Download Recording</DropdownMenuItem>
                            )}
                            <DropdownMenuItem>Add Notes</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>

          {filteredCalls.length === 0 && (
            <div className="text-center py-12">
              <Phone className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No calls found</h3>
              <p className="text-muted-foreground">
                {searchQuery || filterStatus !== 'all' || filterDirection !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'No calls have been made yet'
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CallsPage;
