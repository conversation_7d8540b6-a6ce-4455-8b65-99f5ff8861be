'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useCompany } from '@/contexts/CompanyContext';
import { ArrowLeft, Building2, MapPin, Phone, Globe, Clock, Plus, X } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'react-hot-toast';
import Link from 'next/link';

interface CompanyFormData {
  name: string;
  description: string;
  address: string;
  phone: string;
  website: string;
  business_hours: string;
  service_types: string[];
}

const NewCompanyPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<CompanyFormData>({
    name: '',
    description: '',
    address: '',
    phone: '',
    website: '',
    business_hours: '',
    service_types: [],
  });
  const [newService, setNewService] = useState('');
  
  const { createCompany } = useCompany();
  const router = useRouter();

  const serviceOptions = [
    'Plumbing', 'Electrical', 'HVAC', 'Cleaning', 'Landscaping',
    'Handyman', 'Pest Control', 'Roofing', 'Painting', 'Flooring',
    'Appliance Repair', 'Locksmith', 'Pool Service', 'Tree Service'
  ];

  const handleInputChange = (field: keyof CompanyFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAddService = (service?: string) => {
    const serviceToAdd = service || newService.trim();
    if (serviceToAdd && !formData.service_types.includes(serviceToAdd)) {
      setFormData(prev => ({
        ...prev,
        service_types: [...prev.service_types, serviceToAdd]
      }));
      if (!service) setNewService('');
    }
  };

  const handleRemoveService = (service: string) => {
    setFormData(prev => ({
      ...prev,
      service_types: prev.service_types.filter(s => s !== service)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Company name is required');
      return;
    }

    try {
      setLoading(true);
      const company = await createCompany(formData);
      toast.success('Company created successfully');
      router.push(`/companies/${company.id}/settings`);
    } catch (error) {
      console.error('Failed to create company:', error);
      toast.error('Failed to create company');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex items-center gap-4 animate-slide-down">
        <Link href="/companies">
          <Button variant="outline" size="sm" className="hover-lift">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Companies
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Create New Company
          </h1>
          <p className="text-muted-foreground">
            Set up a new home service company
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card className="animate-slide-up stagger-1" variant="elevated">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="w-5 h-5" />
              Company Information
            </CardTitle>
            <CardDescription>
              Basic information about your company
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Company Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Your Company Name"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="+****************"
                    className="pl-10"
                  />
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe your company and services..."
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Business Address</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="123 Main St, City, State 12345"
                  className="pl-10"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="website">Website</Label>
                <div className="relative">
                  <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <Input
                    id="website"
                    value={formData.website}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                    placeholder="https://yourcompany.com"
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="hours">Business Hours</Label>
                <div className="relative">
                  <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <Input
                    id="hours"
                    value={formData.business_hours}
                    onChange={(e) => handleInputChange('business_hours', e.target.value)}
                    placeholder="Mon-Fri 8AM-6PM, Sat 9AM-4PM"
                    className="pl-10"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Services */}
        <Card className="animate-slide-up stagger-2" variant="elevated">
          <CardHeader>
            <CardTitle>Services Offered</CardTitle>
            <CardDescription>
              Select the services your company provides
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Quick Add Services */}
            <div>
              <Label className="text-sm font-medium mb-3 block">Popular Services</Label>
              <div className="flex flex-wrap gap-2">
                {serviceOptions.map((service) => (
                  <Badge
                    key={service}
                    variant={formData.service_types.includes(service) ? "default" : "outline"}
                    className="cursor-pointer hover:scale-105 transition-transform"
                    onClick={() => 
                      formData.service_types.includes(service) 
                        ? handleRemoveService(service)
                        : handleAddService(service)
                    }
                  >
                    {service}
                    {formData.service_types.includes(service) && (
                      <X className="w-3 h-3 ml-1" />
                    )}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Custom Service */}
            <div>
              <Label className="text-sm font-medium mb-3 block">Add Custom Service</Label>
              <div className="flex gap-2">
                <Input
                  value={newService}
                  onChange={(e) => setNewService(e.target.value)}
                  placeholder="Enter custom service..."
                  onKeyPress={(e) => e.key === 'Enter' && handleAddService()}
                />
                <Button 
                  type="button" 
                  onClick={() => handleAddService()} 
                  disabled={!newService.trim()}
                  variant="outline"
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Selected Services */}
            {formData.service_types.length > 0 && (
              <div>
                <Label className="text-sm font-medium mb-3 block">Selected Services</Label>
                <div className="flex flex-wrap gap-2">
                  {formData.service_types.map((service) => (
                    <Badge key={service} variant="secondary" className="flex items-center gap-1">
                      {service}
                      <button
                        type="button"
                        onClick={() => handleRemoveService(service)}
                        className="ml-1 hover:text-destructive"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Submit */}
        <div className="flex justify-end gap-4 animate-slide-up stagger-3">
          <Link href="/companies">
            <Button variant="outline" type="button">
              Cancel
            </Button>
          </Link>
          <Button 
            type="submit" 
            disabled={loading || !formData.name.trim()}
            loading={loading}
            loadingText="Creating..."
          >
            Create Company
          </Button>
        </div>
      </form>
    </div>
  );
};

export default NewCompanyPage;
