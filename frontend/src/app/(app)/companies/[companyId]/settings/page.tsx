'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useCompany } from '@/contexts/CompanyContext';
import {
  ArrowLeft,
  Save,
  Building2,
  MapPin,
  Phone,
  Globe,
  Clock,
  Users,
  FileText,
  Settings,
  Upload,
  X,
  Palette,
  Bell,
  Shield,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from 'react-hot-toast';
import Link from 'next/link';

interface CompanyFormData {
  name: string;
  description: string;
  address: string;
  phone: string;
  website: string;
  business_hours: string;
  service_types: string[];
  logo_url?: string;
}

const CompanySettingsPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState<CompanyFormData>({
    name: '',
    description: '',
    address: '',
    phone: '',
    website: '',
    business_hours: '',
    service_types: [],
  });
  const [newService, setNewService] = useState('');

  const { companies, updateCompany } = useCompany();
  const router = useRouter();
  const params = useParams();
  const companyId = parseInt(params.companyId as string);

  const company = companies?.find((c) => c.id === companyId);

  useEffect(() => {
    if (company) {
      setFormData({
        name: company.name,
        description: company.description || '',
        address: company.address || '',
        phone: company.phone || '',
        website: company.website || '',
        business_hours: company.business_hours || '',
        service_types: company.service_types || [],
        logo_url: company.logo_url,
      });
    }
  }, [company]);

  const handleInputChange = (field: keyof CompanyFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleAddService = () => {
    if (newService.trim() && !formData.service_types.includes(newService.trim())) {
      setFormData((prev) => ({
        ...prev,
        service_types: [...prev.service_types, newService.trim()],
      }));
      setNewService('');
    }
  };

  const handleRemoveService = (service: string) => {
    setFormData((prev) => ({
      ...prev,
      service_types: prev.service_types.filter((s) => s !== service),
    }));
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      await updateCompany(companyId, formData);
      toast.success('Company settings updated successfully');
    } catch (error) {
      console.error('Failed to update company:', error);
      toast.error('Failed to update company settings');
    } finally {
      setSaving(false);
    }
  };

  if (!company) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Company not found</h2>
          <p className="text-muted-foreground mb-4">
            The company you're looking for doesn't exist.
          </p>
          <Link href="/companies">
            <Button variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Companies
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex items-center justify-between animate-slide-down">
        <div className="flex items-center gap-4">
          <Link href="/companies">
            <Button variant="outline" size="sm" className="hover-lift">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Companies
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Company Settings</h1>
            <p className="text-muted-foreground">
              Manage {company.name} settings and configuration
            </p>
          </div>
        </div>
        <Button onClick={handleSave} disabled={saving} loading={saving} loadingText="Saving...">
          <Save className="w-4 h-4 mr-2" />
          Save Changes
        </Button>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="animate-slide-up stagger-1">
          <TabsTrigger value="general" className="flex items-center gap-2">
            <Building2 className="w-4 h-4" />
            General
          </TabsTrigger>
          <TabsTrigger value="services" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Services
          </TabsTrigger>
          <TabsTrigger value="knowledge" className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            Knowledge Base
          </TabsTrigger>
          <TabsTrigger value="team" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Team
          </TabsTrigger>
        </TabsList>

        {/* General Settings */}
        <TabsContent value="general" className="space-y-6">
          <Card className="animate-slide-up stagger-2" variant="elevated">
            <CardHeader>
              <CardTitle>Company Information</CardTitle>
              <CardDescription>Basic information about your company</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Logo Section */}
              <div className="flex items-center gap-6">
                <Avatar className="w-20 h-20">
                  <AvatarImage src={formData.logo_url} />
                  <AvatarFallback className="text-lg">
                    {formData.name.substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <Label className="text-sm font-medium">Company Logo</Label>
                  <p className="text-sm text-muted-foreground mb-2">
                    Upload a logo for your company (recommended: 200x200px)
                  </p>
                  <Button variant="outline" size="sm">
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Logo
                  </Button>
                </div>
              </div>

              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Company Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Your Company Name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="+****************"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe your company and services..."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Business Address</Label>
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="123 Main St, City, State 12345"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    value={formData.website}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                    placeholder="https://yourcompany.com"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="hours">Business Hours</Label>
                  <Input
                    id="hours"
                    value={formData.business_hours}
                    onChange={(e) => handleInputChange('business_hours', e.target.value)}
                    placeholder="Mon-Fri 8AM-6PM, Sat 9AM-4PM"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Services Settings */}
        <TabsContent value="services" className="space-y-6">
          <Card className="animate-slide-up stagger-2" variant="elevated">
            <CardHeader>
              <CardTitle>Service Types</CardTitle>
              <CardDescription>Manage the services your company offers</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Add New Service */}
              <div className="flex gap-2">
                <Input
                  value={newService}
                  onChange={(e) => setNewService(e.target.value)}
                  placeholder="Enter service type..."
                  onKeyPress={(e) => e.key === 'Enter' && handleAddService()}
                />
                <Button onClick={handleAddService} disabled={!newService.trim()}>
                  Add Service
                </Button>
              </div>

              {/* Current Services */}
              <div>
                <Label className="text-sm font-medium mb-3 block">Current Services</Label>
                <div className="flex flex-wrap gap-2">
                  {formData.service_types.map((service) => (
                    <Badge key={service} variant="secondary" className="flex items-center gap-1">
                      {service}
                      <button
                        onClick={() => handleRemoveService(service)}
                        className="ml-1 hover:text-destructive"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  ))}
                  {formData.service_types.length === 0 && (
                    <p className="text-sm text-muted-foreground">No services added yet</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Knowledge Base Settings */}
        <TabsContent value="knowledge" className="space-y-6">
          <Card className="animate-slide-up stagger-2" variant="elevated">
            <CardHeader>
              <CardTitle>Knowledge Base</CardTitle>
              <CardDescription>
                Manage your company's knowledge base and documentation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Knowledge Base Coming Soon</h3>
                <p className="text-muted-foreground">
                  Upload documents, FAQs, and service information to help your AI agents provide
                  better support.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Team Settings */}
        <TabsContent value="team" className="space-y-6">
          <Card className="animate-slide-up stagger-2" variant="elevated">
            <CardHeader>
              <CardTitle>Team Management</CardTitle>
              <CardDescription>
                Manage team members and their access to your company
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Team Management Coming Soon</h3>
                <p className="text-muted-foreground">
                  Invite team members, manage roles, and control access to your company's features.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CompanySettingsPage;
