'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useCompany } from '@/contexts/CompanyContext';
import { Building2, Settings, Users, Phone, Globe, MapPin, Clock, Edit, Plus, MoreVertical, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

const CompaniesPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const { companies, currentCompany, selectCompany, loading } = useCompany();
  const router = useRouter();

  const filteredCompanies = companies?.filter(company =>
    company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    company.description?.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  const handleCompanyClick = (company: Company) => {
    selectCompany(company);
    router.push('/dashboard');
  };

  const handleEditCompany = (companyId: number) => {
    router.push(`/companies/${companyId}/settings`);
  };

  const handleCreateCompany = () => {
    router.push('/companies/new');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Companies</h1>
          <p className="text-muted-foreground">
            Manage your home service companies and their settings
          </p>
        </div>
        <Button onClick={handleCreateCompany}>
          <Plus className="w-4 h-4 mr-2" />
          Add Company
        </Button>
      </div>

      {/* Search */}
      <Card className="animate-slide-up stagger-1">
        <CardContent className="p-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search companies..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Current Company Highlight */}
      {currentCompany && (
        <Card className="animate-slide-up stagger-2 border-primary/20 bg-primary/5">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Avatar className="w-12 h-12">
                  <AvatarImage src={currentCompany.logo_url} />
                  <AvatarFallback className="bg-primary text-primary-foreground">
                    {currentCompany.name.substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="flex items-center gap-2">
                    {currentCompany.name}
                    <Badge variant="default">Current</Badge>
                  </CardTitle>
                  <CardDescription>{currentCompany.description}</CardDescription>
                </div>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleEditCompany(currentCompany.id)}>
                    <Settings className="w-4 h-4 mr-2" />
                    Settings
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              {currentCompany.address && (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <MapPin className="w-4 h-4" />
                  <span>{currentCompany.address}</span>
                </div>
              )}
              {currentCompany.phone && (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Phone className="w-4 h-4" />
                  <span>{currentCompany.phone}</span>
                </div>
              )}
              {currentCompany.website && (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Globe className="w-4 h-4" />
                  <span>{currentCompany.website}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Companies Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCompanies.map((company, index) => (
          <Card 
            key={company.id} 
            className={`cursor-pointer hover-lift animate-slide-up stagger-${index + 3} ${
              currentCompany?.id === company.id ? 'ring-2 ring-primary' : ''
            }`}
            onClick={() => handleCompanyClick(company)}
          >
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="w-10 h-10">
                    <AvatarImage src={company.logo_url} />
                    <AvatarFallback className="bg-muted">
                      {company.name.substring(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <CardTitle className="text-lg">{company.name}</CardTitle>
                    {currentCompany?.id === company.id && (
                      <Badge variant="default" size="sm" className="mt-1">
                        Active
                      </Badge>
                    )}
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                    <Button variant="ghost" size="sm">
                      <MoreVertical className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={(e) => {
                      e.stopPropagation();
                      handleEditCompany(company.id);
                    }}>
                      <Edit className="w-4 h-4 mr-2" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={(e) => {
                      e.stopPropagation();
                      handleCompanyClick(company);
                    }}>
                      <Building2 className="w-4 h-4 mr-2" />
                      Switch to
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              {company.description && (
                <CardDescription className="mb-4">
                  {company.description}
                </CardDescription>
              )}
              
              <div className="space-y-2 text-sm">
                {company.address && (
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <MapPin className="w-4 h-4" />
                    <span className="truncate">{company.address}</span>
                  </div>
                )}
                {company.phone && (
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Phone className="w-4 h-4" />
                    <span>{company.phone}</span>
                  </div>
                )}
                {company.business_hours && (
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Clock className="w-4 h-4" />
                    <span className="truncate">{company.business_hours}</span>
                  </div>
                )}
                {company.website && (
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Globe className="w-4 h-4" />
                    <span className="truncate">{company.website}</span>
                  </div>
                )}
              </div>

              {company.service_types && company.service_types.length > 0 && (
                <div className="mt-4">
                  <div className="flex flex-wrap gap-1">
                    {company.service_types.slice(0, 3).map((service) => (
                      <Badge key={service} variant="secondary" size="sm">
                        {service}
                      </Badge>
                    ))}
                    {company.service_types.length > 3 && (
                      <Badge variant="secondary" size="sm">
                        +{company.service_types.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredCompanies.length === 0 && (
        <Card className="animate-fade-in">
          <CardContent className="p-12 text-center">
            <Building2 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No companies found</h3>
            <p className="text-muted-foreground mb-6">
              {searchQuery
                ? 'Try adjusting your search query'
                : 'Create your first company to get started'
              }
            </p>
            {!searchQuery && (
              <Button onClick={handleCreateCompany}>
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Company
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CompaniesPage;
