'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  Phone, 
  MessageSquare, 
  User, 
  Clock, 
  Star,
  Download,
  Share,
  MoreVertical,
  Calendar,
  MapPin,
  Mail,
  CheckCircle,
  AlertCircle,
  MessageCircle,
  Globe
} from 'lucide-react';
import { useCompany } from '@/contexts/CompanyContext';
import Link from 'next/link';

interface ConversationDetail {
  id: string;
  customer_name?: string;
  customer_phone?: string;
  customer_email?: string;
  channel: 'phone' | 'web' | 'sms' | 'chat';
  status: 'active' | 'completed' | 'abandoned' | 'transferred';
  agent_name?: string;
  started_at: string;
  ended_at?: string;
  duration_seconds?: number;
  message_count: number;
  customer_satisfaction?: number;
  outcome?: string;
  summary?: string;
  messages: Array<{
    id: string;
    sender: 'customer' | 'agent' | 'system';
    content: string;
    timestamp: string;
    type: 'text' | 'audio' | 'image' | 'file';
  }>;
  metadata: {
    ip_address?: string;
    user_agent?: string;
    referrer?: string;
    location?: string;
  };
}

export default function ConversationDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { currentCompany } = useCompany();
  const conversationId = params.conversationId as string;

  const [conversation, setConversation] = useState<ConversationDetail | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (conversationId && currentCompany) {
      loadConversation();
    }
  }, [conversationId, currentCompany]);

  const loadConversation = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockConversation: ConversationDetail = {
        id: conversationId,
        customer_name: 'John Smith',
        customer_phone: '+****************',
        customer_email: '<EMAIL>',
        channel: 'phone',
        status: 'completed',
        agent_name: 'Customer Service Agent',
        started_at: '2024-01-15T14:30:00Z',
        ended_at: '2024-01-15T14:45:00Z',
        duration_seconds: 900,
        message_count: 12,
        customer_satisfaction: 5,
        outcome: 'appointment_scheduled',
        summary: 'Customer called about plumbing issue. Scheduled appointment for tomorrow at 2 PM.',
        messages: [
          {
            id: 'msg_1',
            sender: 'system',
            content: 'Call started',
            timestamp: '2024-01-15T14:30:00Z',
            type: 'text'
          },
          {
            id: 'msg_2',
            sender: 'agent',
            content: 'Hello! Thank you for calling HomeService Pro. How can I help you today?',
            timestamp: '2024-01-15T14:30:05Z',
            type: 'text'
          },
          {
            id: 'msg_3',
            sender: 'customer',
            content: 'Hi, I have a plumbing issue in my kitchen. The sink is leaking.',
            timestamp: '2024-01-15T14:30:15Z',
            type: 'text'
          },
          {
            id: 'msg_4',
            sender: 'agent',
            content: 'I understand you have a leaking kitchen sink. Can you describe where exactly the leak is coming from?',
            timestamp: '2024-01-15T14:30:25Z',
            type: 'text'
          },
          {
            id: 'msg_5',
            sender: 'customer',
            content: 'It seems to be coming from under the sink, near the pipes.',
            timestamp: '2024-01-15T14:30:45Z',
            type: 'text'
          },
          {
            id: 'msg_6',
            sender: 'agent',
            content: 'That sounds like it could be a pipe joint issue. I can schedule a plumber to come take a look. What time works best for you?',
            timestamp: '2024-01-15T14:31:00Z',
            type: 'text'
          },
          {
            id: 'msg_7',
            sender: 'customer',
            content: 'Tomorrow afternoon would be great if possible.',
            timestamp: '2024-01-15T14:31:15Z',
            type: 'text'
          },
          {
            id: 'msg_8',
            sender: 'agent',
            content: 'Perfect! I have availability tomorrow at 2 PM. Would that work for you?',
            timestamp: '2024-01-15T14:31:30Z',
            type: 'text'
          },
          {
            id: 'msg_9',
            sender: 'customer',
            content: 'Yes, that works perfectly. Thank you!',
            timestamp: '2024-01-15T14:31:45Z',
            type: 'text'
          },
          {
            id: 'msg_10',
            sender: 'agent',
            content: 'Great! I have you scheduled for tomorrow, January 16th at 2 PM. Our plumber will call you 30 minutes before arrival. Is there anything else I can help you with today?',
            timestamp: '2024-01-15T14:32:00Z',
            type: 'text'
          },
          {
            id: 'msg_11',
            sender: 'customer',
            content: 'No, that covers everything. Thank you so much for your help!',
            timestamp: '2024-01-15T14:32:30Z',
            type: 'text'
          },
          {
            id: 'msg_12',
            sender: 'agent',
            content: 'You are very welcome! Have a great day and we will see you tomorrow.',
            timestamp: '2024-01-15T14:32:45Z',
            type: 'text'
          },
          {
            id: 'msg_13',
            sender: 'system',
            content: 'Call ended',
            timestamp: '2024-01-15T14:45:00Z',
            type: 'text'
          }
        ],
        metadata: {
          ip_address: '*************',
          location: 'San Francisco, CA',
        }
      };

      setConversation(mockConversation);
    } catch (error) {
      console.error('Failed to load conversation:', error);
    } finally {
      setLoading(false);
    }
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'phone':
        return <Phone className="w-4 h-4 text-blue-600" />;
      case 'web':
        return <Globe className="w-4 h-4 text-green-600" />;
      case 'sms':
        return <MessageSquare className="w-4 h-4 text-purple-600" />;
      case 'chat':
        return <MessageCircle className="w-4 h-4 text-orange-600" />;
      default:
        return <MessageSquare className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'active':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'abandoned':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'transferred':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const renderStars = (rating?: number) => {
    if (!rating) return <span className="text-gray-400">No rating</span>;
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-4 h-4 ${
              i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">({rating}/5)</span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-8 bg-gray-200 rounded w-1/4"></div>
        <div className="h-64 bg-gray-200 rounded"></div>
      </div>
    );
  }

  if (!conversation) {
    return (
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Conversation Not Found</h1>
        <p className="text-gray-600 mb-8">The conversation you're looking for doesn't exist.</p>
        <Link href="/conversations">
          <Button>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Conversations
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/conversations">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Conversation Details
            </h1>
            <p className="text-gray-600">
              {conversation.customer_name || 'Unknown Customer'} • {conversation.customer_phone}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge className={getStatusColor(conversation.status)}>
            {conversation.status}
          </Badge>
          <div className="flex items-center gap-1">
            {getChannelIcon(conversation.channel)}
            <span className="capitalize text-sm">{conversation.channel}</span>
          </div>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Share className="w-4 h-4 mr-2" />
            Share
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Conversation Messages */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Conversation Timeline</CardTitle>
              <CardDescription>
                {conversation.message_count} messages • {formatDuration(conversation.duration_seconds)} duration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {conversation.messages.map((message) => (
                  <div key={message.id} className={`flex ${
                    message.sender === 'customer' ? 'justify-end' : 'justify-start'
                  }`}>
                    <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      message.sender === 'customer' 
                        ? 'bg-blue-500 text-white' 
                        : message.sender === 'system'
                        ? 'bg-gray-100 text-gray-600 text-sm italic'
                        : 'bg-gray-200 text-gray-900'
                    }`}>
                      <p className="text-sm">{message.content}</p>
                      <p className={`text-xs mt-1 ${
                        message.sender === 'customer' ? 'text-blue-100' : 'text-gray-500'
                      }`}>
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Conversation Details */}
        <div className="space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Name</label>
                <p className="text-sm">{conversation.customer_name || 'Unknown'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Phone</label>
                <p className="text-sm">{conversation.customer_phone}</p>
              </div>
              {conversation.customer_email && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <p className="text-sm">{conversation.customer_email}</p>
                </div>
              )}
              {conversation.metadata.location && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Location</label>
                  <p className="text-sm flex items-center gap-1">
                    <MapPin className="w-3 h-3" />
                    {conversation.metadata.location}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Conversation Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Started</label>
                <p className="text-sm">{new Date(conversation.started_at).toLocaleString()}</p>
              </div>
              {conversation.ended_at && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Ended</label>
                  <p className="text-sm">{new Date(conversation.ended_at).toLocaleString()}</p>
                </div>
              )}
              <div>
                <label className="text-sm font-medium text-gray-500">Agent</label>
                <p className="text-sm">{conversation.agent_name || 'Unassigned'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Outcome</label>
                <p className="text-sm capitalize">{conversation.outcome?.replace('_', ' ') || 'N/A'}</p>
              </div>
              {conversation.summary && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Summary</label>
                  <p className="text-sm">{conversation.summary}</p>
                </div>
              )}
              <div>
                <label className="text-sm font-medium text-gray-500">Customer Satisfaction</label>
                {renderStars(conversation.customer_satisfaction)}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
