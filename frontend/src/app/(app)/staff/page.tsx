'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Users,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Star,
  Eye,
  Edit,
  Trash2,
  Clock,
  Award,
  Briefcase,
  TrendingUp,
  CheckCircle,
  Alert<PERSON>ir<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>
} from 'lucide-react';
import { useCompany } from '@/contexts/CompanyContext';
import { toast } from 'react-hot-toast';

interface Staff {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  role: 'technician' | 'manager' | 'admin' | 'dispatcher';
  status: 'active' | 'inactive' | 'on_leave';
  hire_date: string;
  hourly_rate?: number;
  skills: string[];
  certifications: Array<{
    name: string;
    issued_date: string;
    expiry_date?: string;
    issuer: string;
  }>;
  performance: {
    jobs_completed: number;
    avg_rating: number;
    on_time_percentage: number;
    customer_satisfaction: number;
  };
  schedule: {
    monday: { start: string; end: string; available: boolean };
    tuesday: { start: string; end: string; available: boolean };
    wednesday: { start: string; end: string; available: boolean };
    thursday: { start: string; end: string; available: boolean };
    friday: { start: string; end: string; available: boolean };
    saturday: { start: string; end: string; available: boolean };
    sunday: { start: string; end: string; available: boolean };
  };
  emergency_contact: {
    name: string;
    phone: string;
    relationship: string;
  };
  address?: string;
  avatar?: string;
  notes?: string;
}

export default function StaffPage() {
  const router = useRouter();
  const { currentCompany } = useCompany();
  const [staff, setStaff] = useState<Staff[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState('all');

  useEffect(() => {
    if (currentCompany) {
      loadStaff();
    }
  }, [currentCompany]);

  const loadStaff = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockStaff: Staff[] = [
        {
          id: 'staff_001',
          first_name: 'Mike',
          last_name: 'Johnson',
          email: '<EMAIL>',
          phone: '+****************',
          role: 'technician',
          status: 'active',
          hire_date: '2022-03-15',
          hourly_rate: 35.00,
          skills: ['Plumbing', 'HVAC', 'Electrical'],
          certifications: [
            {
              name: 'EPA 608 Certification',
              issued_date: '2022-01-15',
              expiry_date: '2025-01-15',
              issuer: 'EPA'
            },
            {
              name: 'Master Plumber License',
              issued_date: '2021-06-01',
              expiry_date: '2024-06-01',
              issuer: 'State of California'
            }
          ],
          performance: {
            jobs_completed: 247,
            avg_rating: 4.8,
            on_time_percentage: 94,
            customer_satisfaction: 4.7
          },
          schedule: {
            monday: { start: '08:00', end: '17:00', available: true },
            tuesday: { start: '08:00', end: '17:00', available: true },
            wednesday: { start: '08:00', end: '17:00', available: true },
            thursday: { start: '08:00', end: '17:00', available: true },
            friday: { start: '08:00', end: '17:00', available: true },
            saturday: { start: '09:00', end: '15:00', available: true },
            sunday: { start: '00:00', end: '00:00', available: false }
          },
          emergency_contact: {
            name: 'Sarah Johnson',
            phone: '+****************',
            relationship: 'Spouse'
          },
          address: '456 Oak Street, Anytown, CA 12345',
          notes: 'Excellent with customer service. Prefers commercial jobs.'
        },
        {
          id: 'staff_002',
          first_name: 'Sarah',
          last_name: 'Wilson',
          email: '<EMAIL>',
          phone: '+****************',
          role: 'technician',
          status: 'active',
          hire_date: '2021-08-20',
          hourly_rate: 32.00,
          skills: ['Electrical', 'General Repair'],
          certifications: [
            {
              name: 'Journeyman Electrician',
              issued_date: '2020-04-10',
              expiry_date: '2023-04-10',
              issuer: 'State of California'
            }
          ],
          performance: {
            jobs_completed: 189,
            avg_rating: 4.6,
            on_time_percentage: 91,
            customer_satisfaction: 4.5
          },
          schedule: {
            monday: { start: '07:00', end: '16:00', available: true },
            tuesday: { start: '07:00', end: '16:00', available: true },
            wednesday: { start: '07:00', end: '16:00', available: true },
            thursday: { start: '07:00', end: '16:00', available: true },
            friday: { start: '07:00', end: '16:00', available: true },
            saturday: { start: '00:00', end: '00:00', available: false },
            sunday: { start: '00:00', end: '00:00', available: false }
          },
          emergency_contact: {
            name: 'John Wilson',
            phone: '+****************',
            relationship: 'Father'
          },
          address: '789 Pine Avenue, Hometown, CA 54321'
        },
        {
          id: 'staff_003',
          first_name: 'David',
          last_name: 'Brown',
          email: '<EMAIL>',
          phone: '+****************',
          role: 'manager',
          status: 'active',
          hire_date: '2020-01-10',
          hourly_rate: 45.00,
          skills: ['Management', 'HVAC', 'Customer Service'],
          certifications: [
            {
              name: 'HVAC Excellence Certification',
              issued_date: '2019-11-20',
              expiry_date: '2024-11-20',
              issuer: 'HVAC Excellence'
            }
          ],
          performance: {
            jobs_completed: 156,
            avg_rating: 4.9,
            on_time_percentage: 97,
            customer_satisfaction: 4.8
          },
          schedule: {
            monday: { start: '08:00', end: '18:00', available: true },
            tuesday: { start: '08:00', end: '18:00', available: true },
            wednesday: { start: '08:00', end: '18:00', available: true },
            thursday: { start: '08:00', end: '18:00', available: true },
            friday: { start: '08:00', end: '18:00', available: true },
            saturday: { start: '08:00', end: '14:00', available: true },
            sunday: { start: '00:00', end: '00:00', available: false }
          },
          emergency_contact: {
            name: 'Lisa Brown',
            phone: '+****************',
            relationship: 'Spouse'
          },
          address: '321 Elm Drive, Cityville, CA 98765',
          notes: 'Team lead for HVAC projects. Available for emergency calls.'
        }
      ];

      setStaff(mockStaff);
    } catch (error) {
      console.error('Failed to load staff:', error);
      toast.error('Failed to load staff');
    } finally {
      setLoading(false);
    }
  };

  const filteredStaff = staff.filter(member => {
    const matchesSearch = 
      member.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesTab = selectedTab === 'all' || 
      (selectedTab === 'technicians' && member.role === 'technician') ||
      (selectedTab === 'managers' && member.role === 'manager') ||
      member.status === selectedTab;
    
    return matchesSearch && matchesTab;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'on_leave':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'technician':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'manager':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'admin':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'dispatcher':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-4 h-4 ${
              i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">({rating.toFixed(1)})</span>
      </div>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Staff Management</h1>
          <p className="text-gray-600">
            Manage your team members, schedules, and performance
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <TrendingUp className="w-4 h-4 mr-2" />
            Performance Report
          </Button>
          <Button onClick={() => router.push('/staff/new')} variant="gradient">
            <Plus className="w-4 h-4 mr-2" />
            Add Staff Member
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Staff</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{staff.length}</div>
            <p className="text-xs text-muted-foreground">
              +2 from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Staff</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {staff.filter(s => s.status === 'active').length}
            </div>
            <p className="text-xs text-muted-foreground">
              {Math.round((staff.filter(s => s.status === 'active').length / staff.length) * 100)}% of total
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Performance</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(staff.reduce((sum, s) => sum + s.performance.avg_rating, 0) / staff.length).toFixed(1)}
            </div>
            <p className="text-xs text-muted-foreground">
              Team average rating
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">On-Time Rate</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(staff.reduce((sum, s) => sum + s.performance.on_time_percentage, 0) / staff.length)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Team average
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search staff by name, email, or skills..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Staff List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Team Members</CardTitle>
            <Tabs value={selectedTab} onValueChange={setSelectedTab}>
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="technicians">Technicians</TabsTrigger>
                <TabsTrigger value="managers">Managers</TabsTrigger>
                <TabsTrigger value="active">Active</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Staff Member</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Skills</TableHead>
                <TableHead>Performance</TableHead>
                <TableHead>Hourly Rate</TableHead>
                <TableHead>Jobs Completed</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredStaff.map((member) => (
                <TableRow 
                  key={member.id}
                  className="cursor-pointer hover:bg-accent/5 transition-colors"
                  onClick={() => router.push(`/staff/${member.id}`)}
                >
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={member.avatar} />
                        <AvatarFallback>
                          {member.first_name[0]}{member.last_name[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">
                          {member.first_name} {member.last_name}
                        </p>
                        <p className="text-sm text-gray-600">{member.email}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getRoleColor(member.role)}>
                      {member.role}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(member.status)}>
                      {member.status.replace('_', ' ')}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {member.skills.slice(0, 2).map((skill, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                      {member.skills.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{member.skills.length - 2}
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {renderStars(member.performance.avg_rating)}
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">
                      {member.hourly_rate ? formatCurrency(member.hourly_rate) : 'N/A'}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">{member.performance.jobs_completed}</span>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" onClick={(e) => e.stopPropagation()}>
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-white border shadow-lg">
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/staff/${member.id}`);
                        }}>
                          <Eye className="w-4 h-4 mr-2" />
                          View Profile
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/staff/${member.id}/edit`);
                        }}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit Staff
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/staff/${member.id}/schedule`);
                        }}>
                          <Calendar className="w-4 h-4 mr-2" />
                          Manage Schedule
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          // TODO: Implement assignment
                        }}>
                          <Wrench className="w-4 h-4 mr-2" />
                          Assign Job
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
