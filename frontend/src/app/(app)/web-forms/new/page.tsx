'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useCompany } from '@/contexts/CompanyContext';
import { ArrowLeft, Plus, Trash2, Eye, Save, Settings, Type, Phone, Mail, Calendar, MapPin, DollarSign, MessageSquare, Star, CheckSquare, List } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'react-hot-toast';
import Link from 'next/link';

interface FormField {
  id: string;
  type: 'text' | 'email' | 'phone' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'date' | 'number';
  label: string;
  placeholder?: string;
  required: boolean;
  options?: string[];
}

interface FormData {
  name: string;
  description: string;
  form_type: 'contact' | 'booking' | 'quote' | 'survey' | 'custom';
  fields: FormField[];
  settings: {
    submit_button_text: string;
    success_message: string;
    redirect_url?: string;
    send_email_notification: boolean;
    send_sms_notification: boolean;
    auto_respond: boolean;
  };
}

const NewWebFormPage: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    form_type: 'contact',
    fields: [
      {
        id: '1',
        type: 'text',
        label: 'Full Name',
        placeholder: 'Enter your full name',
        required: true
      },
      {
        id: '2',
        type: 'email',
        label: 'Email Address',
        placeholder: 'Enter your email',
        required: true
      },
      {
        id: '3',
        type: 'phone',
        label: 'Phone Number',
        placeholder: 'Enter your phone number',
        required: false
      }
    ],
    settings: {
      submit_button_text: 'Submit Request',
      success_message: 'Thank you! We\'ll get back to you soon.',
      send_email_notification: true,
      send_sms_notification: false,
      auto_respond: true
    }
  });
  const [saving, setSaving] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  const { currentCompany } = useCompany();
  const router = useRouter();

  const fieldTypes = [
    { value: 'text', label: 'Text Input', icon: <Type className="w-4 h-4" /> },
    { value: 'email', label: 'Email', icon: <Mail className="w-4 h-4" /> },
    { value: 'phone', label: 'Phone', icon: <Phone className="w-4 h-4" /> },
    { value: 'textarea', label: 'Text Area', icon: <MessageSquare className="w-4 h-4" /> },
    { value: 'select', label: 'Dropdown', icon: <List className="w-4 h-4" /> },
    { value: 'checkbox', label: 'Checkbox', icon: <CheckSquare className="w-4 h-4" /> },
    { value: 'date', label: 'Date', icon: <Calendar className="w-4 h-4" /> },
    { value: 'number', label: 'Number', icon: <DollarSign className="w-4 h-4" /> }
  ];

  const handleFormDataChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSettingsChange = (field: keyof FormData['settings'], value: any) => {
    setFormData(prev => ({
      ...prev,
      settings: { ...prev.settings, [field]: value }
    }));
  };

  const addField = () => {
    const newField: FormField = {
      id: Date.now().toString(),
      type: 'text',
      label: 'New Field',
      placeholder: '',
      required: false
    };
    setFormData(prev => ({
      ...prev,
      fields: [...prev.fields, newField]
    }));
  };

  const updateField = (fieldId: string, updates: Partial<FormField>) => {
    setFormData(prev => ({
      ...prev,
      fields: prev.fields.map(field =>
        field.id === fieldId ? { ...field, ...updates } : field
      )
    }));
  };

  const removeField = (fieldId: string) => {
    setFormData(prev => ({
      ...prev,
      fields: prev.fields.filter(field => field.id !== fieldId)
    }));
  };

  const handleSave = async () => {
    if (!formData.name.trim()) {
      toast.error('Form name is required');
      return;
    }

    if (formData.fields.length === 0) {
      toast.error('At least one field is required');
      return;
    }

    try {
      setSaving(true);
      // TODO: Call API to save form
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      toast.success('Form created successfully!');
      router.push('/web-forms');
    } catch (error) {
      console.error('Failed to save form:', error);
      toast.error('Failed to save form');
    } finally {
      setSaving(false);
    }
  };

  const renderFieldPreview = (field: FormField) => {
    const commonProps = {
      placeholder: field.placeholder,
      required: field.required,
      disabled: true
    };

    switch (field.type) {
      case 'text':
      case 'email':
      case 'phone':
      case 'number':
        return <Input {...commonProps} type={field.type} />;
      case 'textarea':
        return <Textarea {...commonProps} rows={3} />;
      case 'select':
        return (
          <Select disabled>
            <SelectTrigger>
              <SelectValue placeholder="Select an option" />
            </SelectTrigger>
          </Select>
        );
      case 'date':
        return <Input {...commonProps} type="date" />;
      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <input type="checkbox" disabled />
            <label className="text-sm">{field.label}</label>
          </div>
        );
      default:
        return <Input {...commonProps} />;
    }
  };

  const renderFormPreview = () => (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>{formData.name}</CardTitle>
        {formData.description && (
          <CardDescription>{formData.description}</CardDescription>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        {formData.fields.map((field) => (
          <div key={field.id} className="space-y-2">
            <Label>
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            {renderFieldPreview(field)}
          </div>
        ))}
        <Button className="w-full" disabled>
          {formData.settings.submit_button_text}
        </Button>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex items-center justify-between animate-slide-down">
        <div className="flex items-center gap-4">
          <Link href="/web-forms">
            <Button variant="outline" size="sm" className="hover-lift">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Forms
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Create Web Form
            </h1>
            <p className="text-muted-foreground">
              Build a custom form for your website
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setPreviewMode(!previewMode)}
          >
            <Eye className="w-4 h-4 mr-2" />
            {previewMode ? 'Edit' : 'Preview'}
          </Button>
          <Button
            onClick={handleSave}
            disabled={saving}
            loading={saving}
            loadingText="Saving..."
          >
            <Save className="w-4 h-4 mr-2" />
            Save Form
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Form Builder */}
        <div className="space-y-6">
          {/* Basic Settings */}
          <Card className="animate-slide-up stagger-1" variant="elevated">
            <CardHeader>
              <CardTitle>Form Settings</CardTitle>
              <CardDescription>Basic information about your form</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Form Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleFormDataChange('name', e.target.value)}
                  placeholder="Enter form name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleFormDataChange('description', e.target.value)}
                  placeholder="Describe what this form is for..."
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label>Form Type</Label>
                <Select
                  value={formData.form_type}
                  onValueChange={(value: any) => handleFormDataChange('form_type', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="contact">Contact Form</SelectItem>
                    <SelectItem value="booking">Service Booking</SelectItem>
                    <SelectItem value="quote">Quote Request</SelectItem>
                    <SelectItem value="survey">Survey</SelectItem>
                    <SelectItem value="custom">Custom Form</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Form Fields */}
          <Card className="animate-slide-up stagger-2" variant="elevated">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Form Fields</CardTitle>
                  <CardDescription>Add and configure form fields</CardDescription>
                </div>
                <Button onClick={addField} size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Field
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.fields.map((field, index) => (
                <Card key={field.id} className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Badge variant="outline">Field {index + 1}</Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeField(field.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div className="space-y-2">
                        <Label>Field Type</Label>
                        <Select
                          value={field.type}
                          onValueChange={(value: any) => updateField(field.id, { type: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {fieldTypes.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                <div className="flex items-center gap-2">
                                  {type.icon}
                                  {type.label}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Required</Label>
                        <div className="flex items-center space-x-2 pt-2">
                          <Switch
                            checked={field.required}
                            onCheckedChange={(checked) => updateField(field.id, { required: checked })}
                          />
                          <span className="text-sm">{field.required ? 'Required' : 'Optional'}</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Field Label</Label>
                      <Input
                        value={field.label}
                        onChange={(e) => updateField(field.id, { label: e.target.value })}
                        placeholder="Enter field label"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Placeholder Text</Label>
                      <Input
                        value={field.placeholder || ''}
                        onChange={(e) => updateField(field.id, { placeholder: e.target.value })}
                        placeholder="Enter placeholder text"
                      />
                    </div>

                    {(field.type === 'select' || field.type === 'radio') && (
                      <div className="space-y-2">
                        <Label>Options (one per line)</Label>
                        <Textarea
                          value={field.options?.join('\n') || ''}
                          onChange={(e) => updateField(field.id, { options: e.target.value.split('\n').filter(Boolean) })}
                          placeholder="Option 1&#10;Option 2&#10;Option 3"
                          rows={3}
                        />
                      </div>
                    )}
                  </div>
                </Card>
              ))}

              {formData.fields.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <MessageSquare className="w-12 h-12 mx-auto mb-2" />
                  <p>No fields added yet</p>
                  <Button onClick={addField} className="mt-2">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Your First Field
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Form Settings */}
          <Card className="animate-slide-up stagger-3" variant="elevated">
            <CardHeader>
              <CardTitle>Submission Settings</CardTitle>
              <CardDescription>Configure what happens after form submission</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Submit Button Text</Label>
                <Input
                  value={formData.settings.submit_button_text}
                  onChange={(e) => handleSettingsChange('submit_button_text', e.target.value)}
                  placeholder="Submit"
                />
              </div>

              <div className="space-y-2">
                <Label>Success Message</Label>
                <Textarea
                  value={formData.settings.success_message}
                  onChange={(e) => handleSettingsChange('success_message', e.target.value)}
                  placeholder="Thank you for your submission!"
                  rows={2}
                />
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Email Notifications</Label>
                    <p className="text-sm text-muted-foreground">Send email when form is submitted</p>
                  </div>
                  <Switch
                    checked={formData.settings.send_email_notification}
                    onCheckedChange={(checked) => handleSettingsChange('send_email_notification', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>SMS Notifications</Label>
                    <p className="text-sm text-muted-foreground">Send SMS when form is submitted</p>
                  </div>
                  <Switch
                    checked={formData.settings.send_sms_notification}
                    onCheckedChange={(checked) => handleSettingsChange('send_sms_notification', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Auto-Respond</Label>
                    <p className="text-sm text-muted-foreground">Send automatic response to submitter</p>
                  </div>
                  <Switch
                    checked={formData.settings.auto_respond}
                    onCheckedChange={(checked) => handleSettingsChange('auto_respond', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Preview */}
        <div className="space-y-6">
          <Card className="animate-slide-up stagger-4" variant="elevated">
            <CardHeader>
              <CardTitle>Form Preview</CardTitle>
              <CardDescription>See how your form will look to users</CardDescription>
            </CardHeader>
            <CardContent>
              {renderFormPreview()}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default NewWebFormPage;
