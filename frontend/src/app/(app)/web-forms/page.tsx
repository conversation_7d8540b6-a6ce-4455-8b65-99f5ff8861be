'use client';

import React, { useState, useEffect } from 'react';
import { useCompany } from '@/contexts/CompanyContext';
import { Plus, Edit, Copy, Eye, MoreVertical, Search, Filter, Zap, Globe, Smartphone, Code, Settings, BarChart3, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';

interface WebForm {
  id: string;
  name: string;
  description?: string;
  form_type: 'contact' | 'booking' | 'quote' | 'survey' | 'custom';
  status: 'active' | 'inactive' | 'draft';
  submissions_count: number;
  conversion_rate: number;
  created_at: string;
  updated_at: string;
  embed_code?: string;
  form_url?: string;
}

const WebFormsPage: React.FC = () => {
  const [webForms, setWebForms] = useState<WebForm[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const { currentCompany } = useCompany();
  const router = useRouter();

  useEffect(() => {
    if (currentCompany) {
      fetchWebForms();
    }
  }, [currentCompany]);

  const fetchWebForms = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockForms: WebForm[] = [
        {
          id: '1',
          name: 'Service Request Form',
          description: 'Main form for customers to request home services',
          form_type: 'booking',
          status: 'active',
          submissions_count: 247,
          conversion_rate: 85,
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-20T14:30:00Z',
          form_url: 'https://forms.homeservice.ai/service-request',
          embed_code: '<iframe src="https://forms.homeservice.ai/embed/service-request" width="100%" height="600"></iframe>'
        },
        {
          id: '2',
          name: 'Quick Quote Form',
          description: 'Fast quote request for common services',
          form_type: 'quote',
          status: 'active',
          submissions_count: 156,
          conversion_rate: 92,
          created_at: '2024-01-10T09:00:00Z',
          updated_at: '2024-01-22T11:15:00Z',
          form_url: 'https://forms.homeservice.ai/quick-quote',
          embed_code: '<iframe src="https://forms.homeservice.ai/embed/quick-quote" width="100%" height="400"></iframe>'
        },
        {
          id: '3',
          name: 'Customer Feedback Survey',
          description: 'Post-service customer satisfaction survey',
          form_type: 'survey',
          status: 'draft',
          submissions_count: 0,
          conversion_rate: 0,
          created_at: '2024-01-25T16:00:00Z',
          updated_at: '2024-01-25T16:00:00Z'
        }
      ];
      setWebForms(mockForms);
    } catch (error) {
      console.error('Failed to fetch web forms:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredForms = webForms.filter(form => {
    const matchesSearch = form.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         form.description?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === 'all' || form.status === filterStatus;
    const matchesType = filterType === 'all' || form.form_type === filterType;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'secondary';
      case 'draft': return 'warning';
      default: return 'secondary';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'contact': return 'blue';
      case 'booking': return 'green';
      case 'quote': return 'purple';
      case 'survey': return 'orange';
      case 'custom': return 'gray';
      default: return 'gray';
    }
  };

  const handleCreateForm = () => {
    router.push('/web-forms/new');
  };

  const handleEditForm = (formId: string) => {
    router.push(`/web-forms/${formId}/edit`);
  };

  const handleViewForm = (formId: string) => {
    const form = webForms.find(f => f.id === formId);
    if (form?.form_url) {
      window.open(form.form_url, '_blank');
    }
  };

  const handleCopyEmbedCode = (formId: string) => {
    const form = webForms.find(f => f.id === formId);
    if (form?.embed_code) {
      navigator.clipboard.writeText(form.embed_code);
      toast.success('Embed code copied to clipboard!');
    }
  };

  const handleViewAnalytics = (formId: string) => {
    router.push(`/web-forms/${formId}/analytics`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 animate-slide-down">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Web Forms
          </h1>
          <p className="text-muted-foreground">
            Create and manage embeddable forms for your website
          </p>
        </div>
        <Button onClick={handleCreateForm}>
          <Plus className="w-4 h-4 mr-2" />
          Create Form
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="animate-slide-up stagger-1">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Forms</p>
                <p className="text-2xl font-bold">{webForms.length}</p>
              </div>
              <Zap className="w-8 h-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="animate-slide-up stagger-2">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Forms</p>
                <p className="text-2xl font-bold">{webForms.filter(f => f.status === 'active').length}</p>
              </div>
              <Globe className="w-8 h-8 text-success" />
            </div>
          </CardContent>
        </Card>

        <Card className="animate-slide-up stagger-3">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Submissions</p>
                <p className="text-2xl font-bold">
                  {webForms.reduce((sum, f) => sum + f.submissions_count, 0).toLocaleString()}
                </p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="animate-slide-up stagger-4">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Conversion</p>
                <p className="text-2xl font-bold">
                  {webForms.length > 0 
                    ? Math.round(webForms.reduce((sum, f) => sum + f.conversion_rate, 0) / webForms.length)
                    : 0
                  }%
                </p>
              </div>
              <BarChart3 className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="animate-slide-up stagger-5">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search forms..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="contact">Contact</SelectItem>
                <SelectItem value="booking">Booking</SelectItem>
                <SelectItem value="quote">Quote</SelectItem>
                <SelectItem value="survey">Survey</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Forms Table */}
      <Card className="animate-slide-up stagger-6" variant="elevated">
        <CardHeader>
          <CardTitle>Web Forms</CardTitle>
          <CardDescription>
            Manage your embeddable web forms and track their performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Form Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submissions</TableHead>
                <TableHead>Conversion Rate</TableHead>
                <TableHead>Last Updated</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredForms.map((form) => (
                <TableRow key={form.id} className="hover:bg-muted/50">
                  <TableCell>
                    <div>
                      <div className="font-medium">{form.name}</div>
                      {form.description && (
                        <div className="text-sm text-muted-foreground">{form.description}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className={`border-${getTypeColor(form.form_type)}-200 text-${getTypeColor(form.form_type)}-700`}>
                      {form.form_type}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusColor(form.status)}>
                      {form.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{form.submissions_count.toLocaleString()}</div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{form.conversion_rate}%</div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {new Date(form.updated_at).toLocaleDateString()}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEditForm(form.id)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleViewForm(form.id)}>
                          <Eye className="w-4 h-4 mr-2" />
                          Preview
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleCopyEmbedCode(form.id)}>
                          <Code className="w-4 h-4 mr-2" />
                          Copy Embed Code
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleViewAnalytics(form.id)}>
                          <BarChart3 className="w-4 h-4 mr-2" />
                          Analytics
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredForms.length === 0 && (
            <div className="text-center py-12">
              <Zap className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No forms found</h3>
              <p className="text-muted-foreground mb-6">
                {searchQuery || filterStatus !== 'all' || filterType !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Create your first web form to get started'
                }
              </p>
              {!searchQuery && filterStatus === 'all' && filterType === 'all' && (
                <Button onClick={handleCreateForm}>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Your First Form
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default WebFormsPage;
