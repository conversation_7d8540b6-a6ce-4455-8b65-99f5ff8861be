'use client';

import React, { useState, useEffect } from 'react';
import { useCompany } from '@/contexts/CompanyContext';
import { Play, Phone, MessageSquare, Globe, Smartphone, Settings, Bug, Activity, Clock, CheckCircle, XCircle, AlertTriangle, BarChart3, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { toast } from 'react-hot-toast';

interface Agent {
  id: string;
  name: string;
  type: 'voice' | 'chat' | 'multi-modal';
  provider: 'elevenlabs' | 'dify' | 'openai';
  status: 'active' | 'inactive';
}

interface TestResult {
  id: string;
  agent_id: string;
  agent_name: string;
  test_type: 'phone' | 'web' | 'sms' | 'chat';
  status: 'running' | 'completed' | 'failed';
  started_at: string;
  completed_at?: string;
  duration_ms?: number;
  success_rate?: number;
  response_time_ms?: number;
  error_message?: string;
  conversation_log?: string[];
}

const TestingPage: React.FC = () => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [testType, setTestType] = useState<string>('phone');
  const [testMessage, setTestMessage] = useState('Hello, I need help with my plumbing issue.');
  const [testPhone, setTestPhone] = useState('+****************');
  const [isRunningTest, setIsRunningTest] = useState(false);
  const [activeTests, setActiveTests] = useState<Set<string>>(new Set());
  const { currentCompany } = useCompany();

  useEffect(() => {
    if (currentCompany) {
      fetchAgents();
      fetchTestResults();
    }
  }, [currentCompany]);

  const fetchAgents = async () => {
    try {
      // TODO: Replace with actual API call
      const mockAgents: Agent[] = [
        {
          id: '1',
          name: 'Customer Service Assistant',
          type: 'voice',
          provider: 'elevenlabs',
          status: 'active'
        },
        {
          id: '2',
          name: 'Emergency Response Agent',
          type: 'multi-modal',
          provider: 'dify',
          status: 'active'
        },
        {
          id: '3',
          name: 'Sales Support Agent',
          type: 'chat',
          provider: 'openai',
          status: 'inactive'
        }
      ];
      setAgents(mockAgents);
      if (mockAgents.length > 0) {
        setSelectedAgent(mockAgents[0].id);
      }
    } catch (error) {
      console.error('Failed to fetch agents:', error);
    }
  };

  const fetchTestResults = async () => {
    try {
      // TODO: Replace with actual API call
      const mockResults: TestResult[] = [
        {
          id: '1',
          agent_id: '1',
          agent_name: 'Customer Service Assistant',
          test_type: 'phone',
          status: 'completed',
          started_at: '2024-01-25T10:30:00Z',
          completed_at: '2024-01-25T10:32:15Z',
          duration_ms: 135000,
          success_rate: 95,
          response_time_ms: 1200,
          conversation_log: [
            'User: Hello, I need help with my plumbing issue.',
            'Agent: Hello! I\'d be happy to help you with your plumbing issue. Can you describe what\'s happening?',
            'User: My kitchen sink is leaking.',
            'Agent: I understand you have a leaking kitchen sink. Let me schedule a technician to come take a look...'
          ]
        },
        {
          id: '2',
          agent_id: '2',
          agent_name: 'Emergency Response Agent',
          test_type: 'chat',
          status: 'failed',
          started_at: '2024-01-25T09:15:00Z',
          completed_at: '2024-01-25T09:16:30Z',
          duration_ms: 90000,
          error_message: 'Agent failed to respond within timeout period'
        }
      ];
      setTestResults(mockResults);
    } catch (error) {
      console.error('Failed to fetch test results:', error);
    }
  };

  const runTest = async () => {
    if (!selectedAgent) {
      toast.error('Please select an agent to test');
      return;
    }

    const agent = agents.find(a => a.id === selectedAgent);
    if (!agent) {
      toast.error('Selected agent not found');
      return;
    }

    try {
      setIsRunningTest(true);
      setActiveTests(prev => new Set(prev).add(selectedAgent));

      // Create test result entry
      const testId = Date.now().toString();
      const newTest: TestResult = {
        id: testId,
        agent_id: selectedAgent,
        agent_name: agent.name,
        test_type: testType as any,
        status: 'running',
        started_at: new Date().toISOString()
      };

      setTestResults(prev => [newTest, ...prev]);

      // TODO: Replace with actual API call
      // Simulate test execution
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Simulate test completion
      const completedTest: TestResult = {
        ...newTest,
        status: 'completed',
        completed_at: new Date().toISOString(),
        duration_ms: 3000,
        success_rate: Math.floor(Math.random() * 20) + 80, // 80-100%
        response_time_ms: Math.floor(Math.random() * 2000) + 500, // 500-2500ms
        conversation_log: [
          `User: ${testMessage}`,
          'Agent: Thank you for contacting us! I\'m here to help you with your request.',
          'Agent: Let me gather some information to better assist you...',
          'Agent: I\'ve scheduled a service appointment for you. You should receive a confirmation shortly.'
        ]
      };

      setTestResults(prev => 
        prev.map(test => test.id === testId ? completedTest : test)
      );

      toast.success(`Test completed successfully! Success rate: ${completedTest.success_rate}%`);

    } catch (error) {
      console.error('Test failed:', error);
      
      // Update test result with failure
      setTestResults(prev => 
        prev.map(test => 
          test.id === testResults[0]?.id 
            ? { ...test, status: 'failed', error_message: 'Test execution failed' }
            : test
        )
      );
      
      toast.error('Test execution failed');
    } finally {
      setIsRunningTest(false);
      setActiveTests(prev => {
        const newSet = new Set(prev);
        newSet.delete(selectedAgent);
        return newSet;
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'warning';
      case 'completed': return 'success';
      case 'failed': return 'destructive';
      default: return 'secondary';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Clock className="w-4 h-4" />;
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'failed': return <XCircle className="w-4 h-4" />;
      default: return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const getTestTypeIcon = (type: string) => {
    switch (type) {
      case 'phone': return <Phone className="w-4 h-4" />;
      case 'chat': return <MessageSquare className="w-4 h-4" />;
      case 'web': return <Globe className="w-4 h-4" />;
      case 'sms': return <Smartphone className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const formatDuration = (ms?: number) => {
    if (!ms) return '-';
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 animate-slide-down">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Agent Testing
          </h1>
          <p className="text-muted-foreground">
            Test and monitor your AI agents across different channels
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <BarChart3 className="w-4 h-4 mr-2" />
            Performance Report
          </Button>
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Test Settings
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Test Configuration */}
        <div className="lg:col-span-1 space-y-6">
          <Card className="animate-slide-up stagger-1" variant="elevated">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="w-5 h-5" />
                Run Test
              </CardTitle>
              <CardDescription>
                Configure and execute agent tests
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Select Agent</Label>
                <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose an agent to test" />
                  </SelectTrigger>
                  <SelectContent>
                    {agents.map((agent) => (
                      <SelectItem key={agent.id} value={agent.id}>
                        <div className="flex items-center gap-2">
                          <Badge variant={agent.status === 'active' ? 'success' : 'secondary'} size="sm">
                            {agent.provider}
                          </Badge>
                          {agent.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Test Channel</Label>
                <Select value={testType} onValueChange={setTestType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="phone">
                      <div className="flex items-center gap-2">
                        <Phone className="w-4 h-4" />
                        Phone Call
                      </div>
                    </SelectItem>
                    <SelectItem value="chat">
                      <div className="flex items-center gap-2">
                        <MessageSquare className="w-4 h-4" />
                        Web Chat
                      </div>
                    </SelectItem>
                    <SelectItem value="sms">
                      <div className="flex items-center gap-2">
                        <Smartphone className="w-4 h-4" />
                        SMS
                      </div>
                    </SelectItem>
                    <SelectItem value="web">
                      <div className="flex items-center gap-2">
                        <Globe className="w-4 h-4" />
                        Web Form
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {testType === 'phone' && (
                <div className="space-y-2">
                  <Label>Test Phone Number</Label>
                  <Input
                    value={testPhone}
                    onChange={(e) => setTestPhone(e.target.value)}
                    placeholder="+****************"
                  />
                </div>
              )}

              <div className="space-y-2">
                <Label>Test Message</Label>
                <Textarea
                  value={testMessage}
                  onChange={(e) => setTestMessage(e.target.value)}
                  placeholder="Enter your test message..."
                  rows={3}
                />
              </div>

              <Button 
                onClick={runTest} 
                disabled={isRunningTest || !selectedAgent}
                className="w-full"
                loading={isRunningTest}
                loadingText="Running Test..."
              >
                <Play className="w-4 h-4 mr-2" />
                Run Test
              </Button>
            </CardContent>
          </Card>

          {/* Active Tests */}
          {activeTests.size > 0 && (
            <Card className="animate-slide-up stagger-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5 animate-pulse" />
                  Active Tests
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Array.from(activeTests).map((agentId) => {
                    const agent = agents.find(a => a.id === agentId);
                    return (
                      <div key={agentId} className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                        <div className="flex-1">
                          <p className="font-medium">{agent?.name}</p>
                          <p className="text-sm text-muted-foreground">Testing {testType}...</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Test Results */}
        <div className="lg:col-span-2">
          <Card className="animate-slide-up stagger-3" variant="elevated">
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
              <CardDescription>
                Recent test executions and performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {testResults.length === 0 ? (
                  <div className="text-center py-12">
                    <Bug className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No test results yet</h3>
                    <p className="text-muted-foreground">
                      Run your first test to see results here
                    </p>
                  </div>
                ) : (
                  testResults.map((result) => (
                    <Card key={result.id} className="border-l-4 border-l-primary/20">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-3">
                            {getTestTypeIcon(result.test_type)}
                            <div>
                              <h4 className="font-semibold">{result.agent_name}</h4>
                              <p className="text-sm text-muted-foreground capitalize">
                                {result.test_type} test
                              </p>
                            </div>
                          </div>
                          <Badge variant={getStatusColor(result.status)} className="flex items-center gap-1">
                            {getStatusIcon(result.status)}
                            {result.status}
                          </Badge>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                          <div>
                            <Label className="text-xs text-muted-foreground">Duration</Label>
                            <p className="font-medium">{formatDuration(result.duration_ms)}</p>
                          </div>
                          {result.success_rate && (
                            <div>
                              <Label className="text-xs text-muted-foreground">Success Rate</Label>
                              <p className="font-medium">{result.success_rate}%</p>
                            </div>
                          )}
                          {result.response_time_ms && (
                            <div>
                              <Label className="text-xs text-muted-foreground">Response Time</Label>
                              <p className="font-medium">{result.response_time_ms}ms</p>
                            </div>
                          )}
                          <div>
                            <Label className="text-xs text-muted-foreground">Started</Label>
                            <p className="font-medium text-xs">
                              {new Date(result.started_at).toLocaleTimeString()}
                            </p>
                          </div>
                        </div>

                        {result.success_rate && (
                          <div className="mb-3">
                            <div className="flex justify-between text-sm mb-1">
                              <span>Performance</span>
                              <span>{result.success_rate}%</span>
                            </div>
                            <Progress value={result.success_rate} className="h-2" />
                          </div>
                        )}

                        {result.error_message && (
                          <div className="bg-destructive/10 text-destructive p-3 rounded-lg text-sm">
                            <strong>Error:</strong> {result.error_message}
                          </div>
                        )}

                        {result.conversation_log && result.conversation_log.length > 0 && (
                          <div className="mt-3">
                            <Label className="text-sm font-medium">Conversation Log</Label>
                            <div className="bg-muted p-3 rounded-lg mt-2 max-h-32 overflow-y-auto">
                              {result.conversation_log.map((message, index) => (
                                <div key={index} className="text-sm mb-1">
                                  {message}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TestingPage;
