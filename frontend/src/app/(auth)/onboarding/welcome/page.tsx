'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  CheckCircle, 
  Sparkles, 
  Target, 
  TrendingUp, 
  Calendar,
  ArrowRight,
  Zap,
  Users,
  BarChart3
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';

const OnboardingWelcomePage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuth();
  const [businessProfile, setBusinessProfile] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    // Get business profile data from localStorage
    const pendingProfile = localStorage.getItem('pendingBusinessProfile');
    if (pendingProfile) {
      setBusinessProfile(JSON.parse(pendingProfile));
    }
  }, []);

  const features = [
    {
      icon: Zap,
      title: 'AI-Powered Ad Creation',
      description: 'Generate compelling ads in seconds with our AI that understands wellness businesses',
      color: 'text-yellow-600'
    },
    {
      icon: Target,
      title: 'Smart Targeting',
      description: 'Reach the right patients in Zurich with precision targeting based on your services',
      color: 'text-blue-600'
    },
    {
      icon: BarChart3,
      title: 'ROI Tracking',
      description: 'See exactly how much each appointment costs and track your return on investment',
      color: 'text-green-600'
    },
    {
      icon: Calendar,
      title: 'Direct Booking',
      description: 'Patients can book appointments directly from your ads - no phone calls needed',
      color: 'text-purple-600'
    }
  ];

  const steps = [
    {
      title: 'Account Created',
      description: 'Your wellness professional account is ready',
      completed: true
    },
    {
      title: 'Business Profile',
      description: 'We\'ll create your business profile next',
      completed: false
    },
    {
      title: 'First Campaign',
      description: 'Generate your first AI-powered ad campaign',
      completed: false
    },
    {
      title: 'Go Live',
      description: 'Launch your ads and start getting appointments',
      completed: false
    }
  ];

  const handleContinue = async () => {
    try {
      // Here we would create the business profile via API
      if (businessProfile) {
        // Mock API call - in real implementation, call the campaigns API
        console.log('Creating business profile:', businessProfile);
        
        // Clear the temporary data
        localStorage.removeItem('pendingBusinessProfile');
        
        // Redirect to dashboard
        router.push('/dashboard');
      } else {
        // If no business profile data, redirect to business setup
        router.push('/onboarding/business-setup');
      }
    } catch (error) {
      console.error('Error creating business profile:', error);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="text-center mb-8"
        >
          <motion.div variants={itemVariants} className="mb-6">
            <div className="w-20 h-20 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
              Welcome to Your Wellness Platform!
            </h1>
            <p className="text-xl text-gray-600">
              {user?.full_name}, you're all set to start growing your practice in Zurich
            </p>
          </motion.div>

          {businessProfile && (
            <motion.div variants={itemVariants}>
              <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 mb-8">
                <CardContent className="pt-6">
                  <div className="flex items-center justify-center space-x-4">
                    <Sparkles className="w-6 h-6 text-blue-600" />
                    <div>
                      <h3 className="font-semibold text-lg">{businessProfile.business_name}</h3>
                      <p className="text-gray-600">
                        {businessProfile.business_type.replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())} • {businessProfile.city}, Switzerland
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </motion.div>

        {/* Progress Steps */}
        <motion.div variants={itemVariants} className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="text-center">Your Journey to Success</CardTitle>
              <CardDescription className="text-center">
                Here's what happens next to get your practice growing
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {steps.map((step, index) => (
                  <div key={index} className="flex items-center space-x-4">
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        step.completed
                          ? 'bg-green-500 text-white'
                          : index === 1
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 text-gray-600'
                      }`}
                    >
                      {step.completed ? (
                        <CheckCircle className="w-4 h-4" />
                      ) : (
                        <span className="text-sm font-medium">{index + 1}</span>
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">{step.title}</h4>
                      <p className="text-sm text-gray-600">{step.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Features Grid */}
        <motion.div variants={containerVariants} className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <motion.div key={index} variants={itemVariants}>
                <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="pt-6">
                    <div className="flex items-start space-x-4">
                      <div className={`p-3 rounded-lg bg-gray-50`}>
                        <Icon className={`w-6 h-6 ${feature.color}`} />
                      </div>
                      <div>
                        <h3 className="font-semibold mb-2">{feature.title}</h3>
                        <p className="text-gray-600 text-sm">{feature.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Success Stats */}
        <motion.div variants={itemVariants} className="mb-8">
          <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
            <CardContent className="pt-6">
              <div className="text-center mb-4">
                <h3 className="text-lg font-semibold text-gray-800">
                  Join 500+ Wellness Professionals in Zurich
                </h3>
                <p className="text-gray-600">Who are already growing their practices with our platform</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">85%</div>
                  <div className="text-sm text-gray-600">Average increase in appointments</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">CHF 45</div>
                  <div className="text-sm text-gray-600">Average cost per appointment</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">3.2x</div>
                  <div className="text-sm text-gray-600">Return on ad spend</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* CTA Button */}
        <motion.div variants={itemVariants} className="text-center">
          <Button
            onClick={handleContinue}
            size="lg"
            className="px-8 py-4 text-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300"
          >
            Complete Setup & Start Growing
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
          <p className="text-sm text-gray-600 mt-4">
            Takes less than 2 minutes • No credit card required to start
          </p>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div variants={itemVariants} className="mt-8 text-center">
          <div className="flex items-center justify-center space-x-8 text-gray-400">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4" />
              <span className="text-sm">GDPR Compliant</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4" />
              <span className="text-sm">Swiss Data Protection</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4" />
              <span className="text-sm">Healthcare Certified</span>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default OnboardingWelcomePage;
