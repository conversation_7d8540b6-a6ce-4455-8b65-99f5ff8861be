'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Heart,
  Stethoscope,
  Activity,
  Brain,
  Dumbbell,
  Sparkles,
  ArrowRight,
  CheckCircle,
  Eye,
  EyeOff,
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/contexts/AuthContext';

// Business type options with icons
const businessTypes = [
  { value: 'physical_therapy', label: 'Physical Therapy', icon: Activity, color: 'text-blue-600' },
  { value: 'dental', label: 'Dental Practice', icon: Stethoscope, color: 'text-green-600' },
  { value: 'massage_therapy', label: 'Massage Therapy', icon: Heart, color: 'text-pink-600' },
  { value: 'chiropractic', label: 'Chiropractic Care', icon: Activity, color: 'text-purple-600' },
  { value: 'mental_health', label: 'Mental Health', icon: Brain, color: 'text-indigo-600' },
  { value: 'nutrition', label: 'Nutrition & Wellness', icon: Sparkles, color: 'text-orange-600' },
  { value: 'fitness', label: 'Fitness & Training', icon: Dumbbell, color: 'text-red-600' },
  { value: 'wellness_spa', label: 'Wellness & Spa', icon: Heart, color: 'text-teal-600' },
];

// Form validation schema
const registrationSchema = z
  .object({
    // Personal Information
    full_name: z.string().min(2, 'Full name must be at least 2 characters'),
    email: z.string().email('Please enter a valid email address'),
    password: z.string().min(8, 'Password must be at least 8 characters'),
    confirm_password: z.string(),

    // Business Information
    business_name: z.string().min(2, 'Business name must be at least 2 characters'),
    business_type: z.string().min(1, 'Please select a business type'),
    description: z.string().optional(),

    // Location
    city: z.string().min(2, 'City is required'),
    postal_code: z.string().min(4, 'Postal code is required'),

    // Contact
    phone: z.string().optional(),
    website: z.string().url().optional().or(z.literal('')),

    // Services
    services: z.string().min(10, 'Please describe your services (at least 10 characters)'),

    // Goals
    primary_goal: z.string().min(1, 'Please select your primary goal'),
  })
  .refine((data) => data.password === data.confirm_password, {
    message: "Passwords don't match",
    path: ['confirm_password'],
  });

type RegistrationFormData = z.infer<typeof registrationSchema>;

const WellnessRegistrationPage: React.FC = () => {
  const router = useRouter();
  const { register: registerUser } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    trigger,
    setValue,
    getValues,
  } = useForm<RegistrationFormData>({
    resolver: zodResolver(registrationSchema),
    mode: 'onChange',
  });

  const watchedBusinessType = watch('business_type');

  const steps = [
    { number: 1, title: 'Personal Info', description: 'Your account details' },
    { number: 2, title: 'Business Type', description: 'What services do you provide?' },
    { number: 3, title: 'Business Details', description: 'Tell us about your practice' },
    { number: 4, title: 'Location & Contact', description: 'Where are you located?' },
    { number: 5, title: 'Goals', description: 'What do you want to achieve?' },
  ];

  const primaryGoals = [
    {
      value: 'appointments',
      label: 'Get More Appointments',
      description: 'Fill your schedule with new patients',
    },
    {
      value: 'awareness',
      label: 'Increase Brand Awareness',
      description: 'Get your practice known in Zurich',
    },
    {
      value: 'leads',
      label: 'Generate More Leads',
      description: 'Build a pipeline of potential patients',
    },
    {
      value: 'retention',
      label: 'Improve Patient Retention',
      description: 'Keep existing patients coming back',
    },
  ];

  const handleNextStep = async () => {
    const fieldsToValidate = getFieldsForStep(currentStep);
    const isValid = await trigger(fieldsToValidate);

    if (isValid) {
      setCurrentStep((prev) => Math.min(prev + 1, steps.length));
    }
  };

  const handlePrevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  const getFieldsForStep = (step: number): (keyof RegistrationFormData)[] => {
    switch (step) {
      case 1:
        return ['full_name', 'email', 'password', 'confirm_password'];
      case 2:
        return ['business_type'];
      case 3:
        return ['business_name', 'description', 'services'];
      case 4:
        return ['city', 'postal_code', 'phone', 'website'];
      case 5:
        return ['primary_goal'];
      default:
        return [];
    }
  };

  const onSubmit = async (data: RegistrationFormData) => {
    setIsLoading(true);

    try {
      // Register user
      await registerUser({
        email: data.email,
        password: data.password,
        full_name: data.full_name,
      });

      // Create business profile (this would be done after successful registration)
      const businessProfileData = {
        business_name: data.business_name,
        business_type: data.business_type,
        description: data.description,
        city: data.city,
        postal_code: data.postal_code,
        country: 'Switzerland',
        phone: data.phone,
        website: data.website,
        services_offered: data.services.split(',').map((s) => s.trim()),
        primary_goal: data.primary_goal,
      };

      // Store business profile data in localStorage temporarily
      localStorage.setItem('pendingBusinessProfile', JSON.stringify(businessProfileData));

      // Redirect to onboarding completion
      router.push('/onboarding/welcome');
    } catch (error) {
      console.error('Registration error:', error);
      // Handle error (show toast, etc.)
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="full_name">Full Name</Label>
              <Input
                id="full_name"
                {...register('full_name')}
                placeholder="Dr. Sarah Johnson"
                className="mt-1"
              />
              {errors.full_name && (
                <p className="text-sm text-red-600 mt-1">{errors.full_name.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                {...register('email')}
                placeholder="<EMAIL>"
                className="mt-1"
              />
              {errors.email && <p className="text-sm text-red-600 mt-1">{errors.email.message}</p>}
            </div>

            <div>
              <Label htmlFor="password">Password</Label>
              <div className="relative mt-1">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  {...register('password')}
                  placeholder="Create a strong password"
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
              {errors.password && (
                <p className="text-sm text-red-600 mt-1">{errors.password.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="confirm_password">Confirm Password</Label>
              <div className="relative mt-1">
                <Input
                  id="confirm_password"
                  type={showConfirmPassword ? 'text' : 'password'}
                  {...register('confirm_password')}
                  placeholder="Confirm your password"
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </button>
              </div>
              {errors.confirm_password && (
                <p className="text-sm text-red-600 mt-1">{errors.confirm_password.message}</p>
              )}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-4">
            <div>
              <Label>What type of wellness business do you run?</Label>
              <RadioGroup
                value={watchedBusinessType}
                onValueChange={(value) => setValue('business_type', value)}
                className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4"
              >
                {businessTypes.map((type) => {
                  const Icon = type.icon;
                  return (
                    <div key={type.value} className="flex items-center space-x-2">
                      <RadioGroupItem value={type.value} id={type.value} />
                      <Label
                        htmlFor={type.value}
                        className="flex items-center space-x-3 cursor-pointer p-3 rounded-lg border hover:bg-gray-50 flex-1"
                      >
                        <Icon className={`w-5 h-5 ${type.color}`} />
                        <span className="font-medium">{type.label}</span>
                      </Label>
                    </div>
                  );
                })}
              </RadioGroup>
              {errors.business_type && (
                <p className="text-sm text-red-600 mt-1">{errors.business_type.message}</p>
              )}
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="business_name">Business Name</Label>
              <Input
                id="business_name"
                {...register('business_name')}
                placeholder="Zurich Wellness Center"
                className="mt-1"
              />
              {errors.business_name && (
                <p className="text-sm text-red-600 mt-1">{errors.business_name.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="description">Business Description (Optional)</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Brief description of your practice and philosophy..."
                className="mt-1"
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="services">Services You Offer</Label>
              <Textarea
                id="services"
                {...register('services')}
                placeholder="List your main services, separated by commas (e.g., Deep tissue massage, Swedish massage, Sports therapy)"
                className="mt-1"
                rows={3}
              />
              {errors.services && (
                <p className="text-sm text-red-600 mt-1">{errors.services.message}</p>
              )}
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="city">City</Label>
                <Input id="city" {...register('city')} placeholder="Zurich" className="mt-1" />
                {errors.city && <p className="text-sm text-red-600 mt-1">{errors.city.message}</p>}
              </div>

              <div>
                <Label htmlFor="postal_code">Postal Code</Label>
                <Input
                  id="postal_code"
                  {...register('postal_code')}
                  placeholder="8001"
                  className="mt-1"
                />
                {errors.postal_code && (
                  <p className="text-sm text-red-600 mt-1">{errors.postal_code.message}</p>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="phone">Phone Number (Optional)</Label>
              <Input
                id="phone"
                {...register('phone')}
                placeholder="+41 44 123 45 67"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="website">Website (Optional)</Label>
              <Input
                id="website"
                {...register('website')}
                placeholder="https://www.yourpractice.ch"
                className="mt-1"
              />
              {errors.website && (
                <p className="text-sm text-red-600 mt-1">{errors.website.message}</p>
              )}
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-4">
            <div>
              <Label>What's your primary goal with ad campaigns?</Label>
              <RadioGroup
                value={watch('primary_goal')}
                onValueChange={(value) => setValue('primary_goal', value)}
                className="space-y-3 mt-4"
              >
                {primaryGoals.map((goal) => (
                  <div key={goal.value} className="flex items-start space-x-3">
                    <RadioGroupItem value={goal.value} id={goal.value} className="mt-1" />
                    <Label
                      htmlFor={goal.value}
                      className="cursor-pointer p-4 rounded-lg border hover:bg-gray-50 flex-1"
                    >
                      <div className="font-medium">{goal.label}</div>
                      <div className="text-sm text-gray-600 mt-1">{goal.description}</div>
                    </Label>
                  </div>
                ))}
              </RadioGroup>
              {errors.primary_goal && (
                <p className="text-sm text-red-600 mt-1">{errors.primary_goal.message}</p>
              )}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.number} className="flex items-center">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep >= step.number
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  {currentStep > step.number ? <CheckCircle className="w-5 h-5" /> : step.number}
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={`w-full h-1 mx-2 ${
                      currentStep > step.number ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
          <div className="mt-4 text-center">
            <h2 className="text-xl font-semibold">{steps[currentStep - 1].title}</h2>
            <p className="text-gray-600">{steps[currentStep - 1].description}</p>
          </div>
        </div>

        {/* Form Card */}
        <Card className="shadow-xl">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Join Zurich's Leading Wellness Platform
            </CardTitle>
            <CardDescription>
              Create your account and start getting more appointments with AI-powered ads
            </CardDescription>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)}>
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {renderStep()}
              </motion.div>

              {/* Navigation Buttons */}
              <div className="flex justify-between mt-8">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePrevStep}
                  disabled={currentStep === 1}
                  className="px-6"
                >
                  Previous
                </Button>

                {currentStep < steps.length ? (
                  <Button
                    type="button"
                    onClick={handleNextStep}
                    className="px-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  >
                    Next
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="px-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  >
                    {isLoading ? 'Creating Account...' : 'Create Account'}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                )}
              </div>
            </form>

            {/* Login Link */}
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Already have an account?{' '}
                <Link href="/login" className="text-blue-600 hover:text-blue-700 font-medium">
                  Sign in here
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default WellnessRegistrationPage;
