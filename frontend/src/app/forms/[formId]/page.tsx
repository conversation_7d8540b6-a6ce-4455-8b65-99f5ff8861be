'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { webFormsApi, type WebForm } from '@/services/webFormsApi';
import { FormRenderer } from '@/components/forms/FormRenderer';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from 'react-hot-toast';

const FormDisplayPage: React.FC = () => {
  const params = useParams();
  const formId = params.formId as string;
  const [form, setForm] = useState<WebForm | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (formId) {
      fetchForm();
    }
  }, [formId]);

  const fetchForm = async () => {
    try {
      const webForm = await webFormsApi.getFormByPublicId(formId);
      setForm(webForm);
    } catch (error) {
      console.error('Failed to fetch form:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFormSubmit = async (data: Record<string, any>) => {
    if (!form) return;

    setSubmitting(true);
    try {
      // Convert form data to API format
      const responses = Object.entries(data).map(([fieldId, value]) => ({
        field_id: fieldId,
        response_value: typeof value === 'string' ? value : JSON.stringify(value),
      }));

      // Submit to API
      const submission = await webFormsApi.submitForm(form.web_form_id, {
        responses,
        user_agent: navigator.userAgent,
        referrer_url: document.referrer,
      });

      console.log('Form submission saved:', submission);
      toast.success(form.thank_you_message || 'Thank you for your submission!');

      // Optionally redirect or reset form
      // router.push('/thank-you');
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('Failed to submit form. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!form) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <h1 className="text-2xl font-bold text-foreground mb-2">Form Not Found</h1>
            <p className="text-muted-foreground">
              The form you're looking for doesn't exist or has been removed.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        <FormRenderer
          template={form.template}
          onSubmit={handleFormSubmit}
          disabled={submitting}
          customProps={{
            zip_code: {
              headerTitle: 'Your Local Service Pro',
            },
          }}
        />
      </div>
    </div>
  );
};

export default FormDisplayPage;
