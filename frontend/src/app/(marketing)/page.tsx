'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import {
  Heart,
  Sparkles,
  Target,
  TrendingUp,
  Calendar,
  ArrowRight,
  CheckCircle,
  Star,
  Users,
  BarChart3,
  Zap,
  Shield,
  Clock,
  Phone,
  Mail,
  MapPin,
  MousePointer,
  Eye,
  Store,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { getPlatformBranding } from '@/config/businessTypes';

const Home: React.FC = () => {
  const { user } = useAuth();
  const branding = getPlatformBranding();
  const t = useTranslations();
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Mock form submission
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // Redirect to registration
    window.location.href = '/register-wellness';
  };

  const features = [
    {
      icon: Zap,
      title: 'One-Click Campaigns',
      description:
        'Launch professional ad campaigns in under 60 seconds with our AI-powered system.',
      color: 'text-yellow-600',
    },
    {
      icon: BarChart3,
      title: 'Measure ROI',
      description: 'Track every franc spent and see exactly how much each appointment costs you.',
      color: 'text-blue-600',
    },
    {
      icon: Calendar,
      title: 'Direct Booking',
      description: 'Patients book appointments directly from your ads - no phone calls needed.',
      color: 'text-green-600',
    },
    {
      icon: Target,
      title: 'AI-Powered',
      description:
        'Our AI understands wellness businesses and creates compelling ads that convert.',
      color: 'text-purple-600',
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <section className="pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  {t(`branding.${branding.category}.name`)}
                </span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                {t(`branding.${branding.category}.tagline`)}
              </p>
            </motion.div>

            <p className="text-xl md:text-2xl text-foreground mb-12 max-w-4xl mx-auto leading-relaxed">
              {t(`branding.${branding.category}.description`)}
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              {user ? (
                <Link href="/dashboard">
                  <Button size="lg" className="text-lg px-10 py-4 group">
                    {t('common.dashboard')}
                    <ArrowRight
                      className="ml-3 group-hover:translate-x-1 transition-transform"
                      size={24}
                    />
                  </Button>
                </Link>
              ) : (
                <>
                  <Link href="/register">
                    <Button size="lg" className="text-lg px-10 py-4 group">
                      {t('common.getStarted')}
                      <ArrowRight
                        className="ml-3 group-hover:translate-x-1 transition-transform"
                        size={24}
                      />
                    </Button>
                  </Link>
                  <Link href="/login">
                    <Button variant="outline" size="lg" className="text-lg px-10 py-4">
                      {t('common.signIn')}
                    </Button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </section>

      <div className="py-20 px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4">Key Features</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Empowering your home services business with intelligent automation
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          <Card className="text-center group">
            <CardHeader>
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary rounded-2xl mb-6 shadow-lg group-hover:scale-110 transition-transform duration-200">
                <Sparkles className="w-8 h-8 text-primary-foreground" />
              </div>
              <CardTitle className="text-xl font-bold mb-3 text-foreground">
                24/7 AI Assistant
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                Automate appointment booking and answer customer FAQs around the clock
              </p>
            </CardContent>
          </Card>

          <Card className="text-center group">
            <CardHeader>
              <div className="inline-flex items-center justify-center w-16 h-16 bg-accent rounded-2xl mb-6 shadow-lg group-hover:scale-110 transition-transform duration-200">
                <Zap className="w-8 h-8 text-accent-foreground" />
              </div>
              <CardTitle className="text-xl font-bold mb-3 text-foreground">
                Intelligent Lead Qualification
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                AI-driven lead scoring and routing to ensure high-quality appointments
              </p>
            </CardContent>
          </Card>

          <Card className="text-center group">
            <CardHeader>
              <div className="inline-flex items-center justify-center w-16 h-16 bg-secondary rounded-2xl mb-6 shadow-lg group-hover:scale-110 transition-transform duration-200">
                <Store className="w-8 h-8 text-secondary-foreground" />
              </div>
              <CardTitle className="text-xl font-bold mb-3 text-foreground">
                Multi-Service Support
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                Seamlessly manage appointments for plumbing, HVAC, electrical, and more
              </p>
            </CardContent>
          </Card>

          <Card className="text-center group">
            <CardHeader>
              <div className="inline-flex items-center justify-center w-16 h-16 bg-destructive rounded-2xl mb-6 shadow-lg group-hover:scale-110 transition-transform duration-200">
                <Shield className="w-8 h-8 text-destructive-foreground" />
              </div>
              <CardTitle className="text-xl font-bold mb-3 text-foreground">
                CRM Integration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                Connect with your existing CRM to sync customer data and appointment details
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="py-20 px-4">
        <div className="grid lg:grid-cols-3 gap-8">
          <Card className="text-center">
            <CardHeader>
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary rounded-2xl mb-6 shadow-lg">
                <Zap className="w-8 h-8 text-primary-foreground" />
              </div>
              <CardTitle className="text-2xl font-bold mb-4 text-foreground">
                Lightning Fast
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-lg leading-relaxed">
                Built for speed with modern technology stack ensuring quick response times
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="inline-flex items-center justify-center w-16 h-16 bg-accent rounded-2xl mb-6 shadow-lg">
                <Shield className="w-8 h-8 text-accent-foreground" />
              </div>
              <CardTitle className="text-2xl font-bold mb-4 text-foreground">
                Secure & Reliable
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-lg leading-relaxed">
                Enterprise-grade security with 99.9% uptime guarantee for your business
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="inline-flex items-center justify-center w-16 h-16 bg-secondary rounded-2xl mb-6 shadow-lg">
                <Sparkles className="w-8 h-8 text-secondary-foreground" />
              </div>
              <CardTitle className="text-2xl font-bold mb-4 text-foreground">Easy to Use</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-lg leading-relaxed">
                Intuitive interface designed for both beginners and home service experts
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
export default Home;
