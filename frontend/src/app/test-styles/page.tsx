'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const TestStylesPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-foreground">Tailwind CSS 4 Test</h1>
          <p className="text-lg text-muted-foreground">
            Testing if all styles are working properly
          </p>

          {/* Direct CSS Variable Test */}
          <div
            className="p-4 rounded-lg"
            style={{ backgroundColor: 'var(--primary)', color: 'var(--primary-foreground)' }}
          >
            Direct CSS Variable Test - This should be blue with white text
          </div>
        </div>

        {/* Color Test */}
        <Card>
          <CardHeader>
            <CardTitle>Color Variables Test</CardTitle>
            <CardDescription>Testing CSS variables and color system</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="p-4 bg-primary text-primary-foreground rounded-lg text-center">
                <div className="font-semibold">Primary</div>
                <div className="text-sm opacity-90">hsl(var(--primary))</div>
              </div>
              <div className="p-4 bg-secondary text-secondary-foreground rounded-lg text-center">
                <div className="font-semibold">Secondary</div>
                <div className="text-sm opacity-90">hsl(var(--secondary))</div>
              </div>
              <div className="p-4 bg-accent text-accent-foreground rounded-lg text-center">
                <div className="font-semibold">Accent</div>
                <div className="text-sm opacity-90">hsl(var(--accent))</div>
              </div>
              <div className="p-4 bg-muted text-muted-foreground rounded-lg text-center">
                <div className="font-semibold">Muted</div>
                <div className="text-sm opacity-90">hsl(var(--muted))</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Button Test */}
        <Card>
          <CardHeader>
            <CardTitle>Button Variants Test</CardTitle>
            <CardDescription>Testing button component variants</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <Button variant="default">Default</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="link">Link</Button>
            </div>
          </CardContent>
        </Card>

        {/* Badge Test */}
        <Card>
          <CardHeader>
            <CardTitle>Badge Variants Test</CardTitle>
            <CardDescription>Testing badge component variants</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <Badge variant="default">Default</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="outline">Outline</Badge>
              <Badge variant="destructive">Destructive</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Layout Test */}
        <Card>
          <CardHeader>
            <CardTitle>Layout & Spacing Test</CardTitle>
            <CardDescription>Testing Tailwind utilities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="p-6 border border-border rounded-lg bg-card">
                <h3 className="font-semibold text-card-foreground mb-2">Card 1</h3>
                <p className="text-muted-foreground">
                  This is a test card with proper spacing and colors.
                </p>
              </div>
              <div className="p-6 border border-border rounded-lg bg-card">
                <h3 className="font-semibold text-card-foreground mb-2">Card 2</h3>
                <p className="text-muted-foreground">Another test card to verify grid layout.</p>
              </div>
              <div className="p-6 border border-border rounded-lg bg-card">
                <h3 className="font-semibold text-card-foreground mb-2">Card 3</h3>
                <p className="text-muted-foreground">Third card for complete grid test.</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Chart Colors Test */}
        <Card>
          <CardHeader>
            <CardTitle>Chart Colors Test</CardTitle>
            <CardDescription>Testing chart color variables</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-5 gap-4">
              <div className="h-16 bg-chart-1 rounded-lg flex items-center justify-center text-white font-semibold">
                Chart 1
              </div>
              <div className="h-16 bg-chart-2 rounded-lg flex items-center justify-center text-white font-semibold">
                Chart 2
              </div>
              <div className="h-16 bg-chart-3 rounded-lg flex items-center justify-center text-white font-semibold">
                Chart 3
              </div>
              <div className="h-16 bg-chart-4 rounded-lg flex items-center justify-center text-white font-semibold">
                Chart 4
              </div>
              <div className="h-16 bg-chart-5 rounded-lg flex items-center justify-center text-white font-semibold">
                Chart 5
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Dark Mode Toggle Test */}
        <Card>
          <CardHeader>
            <CardTitle>Dark Mode Test</CardTitle>
            <CardDescription>Click to toggle dark mode</CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={() => {
                document.documentElement.classList.toggle('dark');
              }}
              variant="outline"
            >
              Toggle Dark Mode
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TestStylesPage;
