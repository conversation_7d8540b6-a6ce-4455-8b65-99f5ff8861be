'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Calendar,
  Clock,
  DollarSign,
  FileText,
  Phone,
  Mail,
  MapPin,
  Star,
  CreditCard,
  Download,
  MessageSquare,
  Plus,
  Eye,
  CheckCircle,
  AlertCircle,
  User,
  Home,
  Settings,
  LogOut,
  Wrench
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface CustomerPortalData {
  customer: {
    id: string;
    name: string;
    email: string;
    phone: string;
    address: string;
    member_since: string;
  };
  recent_services: Array<{
    id: string;
    service_type: string;
    description: string;
    date: string;
    technician: string;
    cost: number;
    status: 'completed' | 'scheduled' | 'cancelled';
    rating?: number;
  }>;
  upcoming_appointments: Array<{
    id: string;
    service_type: string;
    date: string;
    time_slot: string;
    technician: string;
    estimated_cost: number;
    status: 'confirmed' | 'pending';
  }>;
  invoices: Array<{
    id: string;
    invoice_number: string;
    date: string;
    amount: number;
    status: 'paid' | 'pending' | 'overdue';
    due_date: string;
  }>;
  payment_methods: Array<{
    id: string;
    type: 'credit_card' | 'bank_account';
    last_four: string;
    brand?: string;
    is_default: boolean;
  }>;
}

export default function CustomerPortal() {
  const router = useRouter();
  const [portalData, setPortalData] = useState<CustomerPortalData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('dashboard');

  useEffect(() => {
    loadPortalData();
  }, []);

  const loadPortalData = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockData: CustomerPortalData = {
        customer: {
          id: 'cust_001',
          name: 'John Smith',
          email: '<EMAIL>',
          phone: '+****************',
          address: '123 Main St, Anytown, CA 12345',
          member_since: '2023-06-15'
        },
        recent_services: [
          {
            id: 'svc_001',
            service_type: 'Plumbing Repair',
            description: 'Fixed kitchen sink leak',
            date: '2024-01-15',
            technician: 'Mike Johnson',
            cost: 185.00,
            status: 'completed',
            rating: 5
          },
          {
            id: 'svc_002',
            service_type: 'HVAC Maintenance',
            description: 'Annual system inspection',
            date: '2023-12-15',
            technician: 'David Brown',
            cost: 150.00,
            status: 'completed',
            rating: 4
          }
        ],
        upcoming_appointments: [
          {
            id: 'apt_001',
            service_type: 'Plumbing Maintenance',
            date: '2024-02-15',
            time_slot: '2:00 PM - 4:00 PM',
            technician: 'Mike Johnson',
            estimated_cost: 120.00,
            status: 'confirmed'
          }
        ],
        invoices: [
          {
            id: 'inv_001',
            invoice_number: 'INV-2024-001',
            date: '2024-01-15',
            amount: 185.00,
            status: 'paid',
            due_date: '2024-01-30'
          },
          {
            id: 'inv_002',
            invoice_number: 'INV-2023-089',
            date: '2023-12-15',
            amount: 150.00,
            status: 'paid',
            due_date: '2023-12-30'
          }
        ],
        payment_methods: [
          {
            id: 'pm_001',
            type: 'credit_card',
            last_four: '4242',
            brand: 'Visa',
            is_default: true
          }
        ]
      };

      setPortalData(mockData);
    } catch (error) {
      console.error('Failed to load portal data:', error);
      toast.error('Failed to load your information');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'paid':
      case 'confirmed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'scheduled':
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'cancelled':
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-4 h-4 ${
              i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-64"></div>
          <div className="h-64 bg-gray-200 rounded w-96"></div>
        </div>
      </div>
    );
  }

  if (!portalData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-8 text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
            <p className="text-gray-600 mb-6">Please log in to access your customer portal.</p>
            <Button onClick={() => router.push('/auth/login')}>
              Log In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Home className="w-8 h-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-bold text-gray-900">HomeService Pro</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome, {portalData.customer.name}</span>
              <Button variant="ghost" size="sm">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
              <Button variant="ghost" size="sm">
                <LogOut className="w-4 h-4 mr-2" />
                Log Out
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="appointments">Appointments</TabsTrigger>
            <TabsTrigger value="history">Service History</TabsTrigger>
            <TabsTrigger value="invoices">Invoices</TabsTrigger>
            <TabsTrigger value="profile">Profile</TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-6">
            {/* Welcome Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Welcome Back, {portalData.customer.name}!
                </CardTitle>
                <CardDescription>
                  Here's an overview of your account and recent activity
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Next Appointment</CardTitle>
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  {portalData.upcoming_appointments.length > 0 ? (
                    <div>
                      <div className="text-2xl font-bold">
                        {new Date(portalData.upcoming_appointments[0].date).toLocaleDateString()}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {portalData.upcoming_appointments[0].service_type}
                      </p>
                    </div>
                  ) : (
                    <div>
                      <div className="text-2xl font-bold">None</div>
                      <p className="text-xs text-muted-foreground">No upcoming appointments</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Services</CardTitle>
                  <Wrench className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{portalData.recent_services.length}</div>
                  <p className="text-xs text-muted-foreground">
                    Since {new Date(portalData.customer.member_since).getFullYear()}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Account Balance</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">$0.00</div>
                  <p className="text-xs text-muted-foreground">All invoices paid</p>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Services</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {portalData.recent_services.slice(0, 3).map((service) => (
                      <div key={service.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium">{service.service_type}</p>
                          <p className="text-sm text-gray-600">{new Date(service.date).toLocaleDateString()}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{formatCurrency(service.cost)}</p>
                          {service.rating && renderStars(service.rating)}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Upcoming Appointments</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {portalData.upcoming_appointments.length > 0 ? (
                      portalData.upcoming_appointments.map((appointment) => (
                        <div key={appointment.id} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                          <div>
                            <p className="font-medium">{appointment.service_type}</p>
                            <p className="text-sm text-gray-600">
                              {new Date(appointment.date).toLocaleDateString()} • {appointment.time_slot}
                            </p>
                            <p className="text-sm text-gray-600">Technician: {appointment.technician}</p>
                          </div>
                          <div className="text-right">
                            <Badge className={getStatusColor(appointment.status)}>
                              {appointment.status}
                            </Badge>
                            <p className="text-sm text-gray-600 mt-1">
                              Est. {formatCurrency(appointment.estimated_cost)}
                            </p>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600">No upcoming appointments</p>
                        <Button className="mt-4" onClick={() => setActiveTab('appointments')}>
                          <Plus className="w-4 h-4 mr-2" />
                          Schedule Service
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="appointments" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Appointments</h2>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Schedule New Service
              </Button>
            </div>

            <div className="space-y-4">
              {portalData.upcoming_appointments.map((appointment) => (
                <Card key={appointment.id}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold text-lg">{appointment.service_type}</h3>
                        <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            {new Date(appointment.date).toLocaleDateString()}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            {appointment.time_slot}
                          </div>
                          <div className="flex items-center gap-1">
                            <User className="w-4 h-4" />
                            {appointment.technician}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(appointment.status)}>
                          {appointment.status}
                        </Badge>
                        <p className="text-lg font-semibold mt-2">
                          {formatCurrency(appointment.estimated_cost)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <h2 className="text-2xl font-bold">Service History</h2>
            
            <div className="space-y-4">
              {portalData.recent_services.map((service) => (
                <Card key={service.id}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold text-lg">{service.service_type}</h3>
                        <p className="text-gray-600 mt-1">{service.description}</p>
                        <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            {new Date(service.date).toLocaleDateString()}
                          </div>
                          <div className="flex items-center gap-1">
                            <User className="w-4 h-4" />
                            {service.technician}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(service.status)}>
                          {service.status}
                        </Badge>
                        <p className="text-lg font-semibold mt-2">
                          {formatCurrency(service.cost)}
                        </p>
                        {service.rating && (
                          <div className="mt-2">
                            {renderStars(service.rating)}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="invoices" className="space-y-6">
            <h2 className="text-2xl font-bold">Invoices & Payments</h2>
            
            <div className="space-y-4">
              {portalData.invoices.map((invoice) => (
                <Card key={invoice.id}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold text-lg">{invoice.invoice_number}</h3>
                        <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            Issued: {new Date(invoice.date).toLocaleDateString()}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            Due: {new Date(invoice.due_date).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(invoice.status)}>
                          {invoice.status}
                        </Badge>
                        <p className="text-lg font-semibold mt-2">
                          {formatCurrency(invoice.amount)}
                        </p>
                        <div className="flex gap-2 mt-2">
                          <Button variant="outline" size="sm">
                            <Eye className="w-4 h-4 mr-1" />
                            View
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="w-4 h-4 mr-1" />
                            Download
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="profile" className="space-y-6">
            <h2 className="text-2xl font-bold">Profile & Settings</h2>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Name</label>
                    <Input value={portalData.customer.name} disabled />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Email</label>
                    <Input value={portalData.customer.email} disabled />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Phone</label>
                    <Input value={portalData.customer.phone} disabled />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Address</label>
                    <Input value={portalData.customer.address} disabled />
                  </div>
                  <Button>Update Information</Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Payment Methods</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {portalData.payment_methods.map((method) => (
                    <div key={method.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <CreditCard className="w-5 h-5" />
                        <div>
                          <p className="font-medium">
                            {method.brand} •••• {method.last_four}
                          </p>
                          {method.is_default && (
                            <Badge variant="outline" className="text-xs">Default</Badge>
                          )}
                        </div>
                      </div>
                      <Button variant="outline" size="sm">Edit</Button>
                    </div>
                  ))}
                  <Button variant="outline" className="w-full">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Payment Method
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
