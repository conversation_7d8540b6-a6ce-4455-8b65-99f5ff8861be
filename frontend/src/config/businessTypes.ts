import {
  // Home Services Icons
  Wrench,
  Zap,
  <PERSON><PERSON><PERSON>,
  Hammer,
  Sparkles,
  TreePine,
  Home,
  Paintbrush,
  Bug,
  Settings,
  // Professional Services Icons
  Briefcase,
  Calculator,
  Users,
  Building,
  Shield,
  DollarSign,
  TrendingUp,
  Monitor,
  Ruler,
  Cog,
  // Health & Wellness Icons
  Heart,
  Stethoscope,
  Activity,
  Brain,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'lucide-react';

export interface BusinessType {
  value: string;
  icon: any;
  color?: string;
}

export interface BusinessCategory {
  icon: any;
  color: string;
  types: BusinessType[];
}

export const businessCategories: Record<string, BusinessCategory> = {
  home_services: {
    icon: Home,
    color: 'text-blue-600',
    types: [
      { value: 'plumbing', icon: Wrench },
      { value: 'electrical', icon: Zap },
      { value: 'hvac', icon: Snowflake },
      { value: 'handyman', icon: Hammer },
      { value: 'cleaning', icon: Sparkles },
      { value: 'landscaping', icon: TreePine },
      { value: 'roofing', icon: Home },
      { value: 'painting', icon: Paintbrush },
      { value: 'pest_control', icon: Bug },
      { value: 'appliance_repair', icon: Settings },
    ],
  },
  professional_services: {
    icon: Briefcase,
    color: 'text-purple-600',
    types: [
      { value: 'legal', icon: Briefcase },
      { value: 'accounting', icon: Calculator },
      { value: 'consulting', icon: Users },
      { value: 'real_estate', icon: Building },
      { value: 'insurance', icon: Shield },
      { value: 'financial_planning', icon: DollarSign },
      { value: 'marketing', icon: TrendingUp },
      { value: 'it_services', icon: Monitor },
      { value: 'architecture', icon: Ruler },
      { value: 'engineering', icon: Cog },
    ],
  },
  health_wellness: {
    icon: Heart,
    color: 'text-green-600',
    types: [
      { value: 'physical_therapy', icon: Activity },
      { value: 'dental', icon: Stethoscope },
      { value: 'massage_therapy', icon: Heart },
      { value: 'chiropractic', icon: Activity },
      { value: 'mental_health', icon: Brain },
      { value: 'nutrition', icon: Leaf },
      { value: 'fitness', icon: Dumbbell },
      { value: 'wellness_spa', icon: Heart },
    ],
  },
};

// Get all business types as a flat array
export const getAllBusinessTypes = (): BusinessType[] => {
  return Object.values(businessCategories).flatMap((category) => category.types);
};

// Get business type by value
export const getBusinessTypeByValue = (value: string): BusinessType | undefined => {
  return getAllBusinessTypes().find((type) => type.value === value);
};

// Get category by business type value
export const getCategoryByBusinessType = (businessTypeValue: string): string | undefined => {
  for (const [categoryKey, category] of Object.entries(businessCategories)) {
    if (category.types.some((type) => type.value === businessTypeValue)) {
      return categoryKey;
    }
  }
  return undefined;
};

// Environment-based configuration
export const getEnabledCategory = (): string => {
  const enabledCategory = process.env.NEXT_PUBLIC_BUSINESS_CATEGORY;

  if (!enabledCategory || !businessCategories[enabledCategory]) {
    // Default to home_services if not set or invalid
    return 'home_services';
  }

  return enabledCategory;
};

export const getEnabledCategories = (): Record<string, BusinessCategory> => {
  const category = getEnabledCategory();
  return { [category]: businessCategories[category] };
};

// Platform branding based on primary category
export const getPlatformBranding = () => {
  const category = getEnabledCategory();
  return {
    category,
    icon: businessCategories[category].icon,
    color: businessCategories[category].color,
  };
};
