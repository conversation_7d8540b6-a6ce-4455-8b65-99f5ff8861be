export interface WebForm {
  id: string;
  name: string;
  description?: string;
  form_type: 'contact' | 'booking' | 'quote' | 'survey' | 'custom';
  status: 'active' | 'inactive' | 'draft';
  submissions_count: number;
  conversion_rate: number;
  created_at: string;
  updated_at: string;
  embed_code?: string;
  form_url?: string;
  pages: FormPage[];
}

export interface FormPage {
  id: string;
  name: string;
  elements: FormElement[];
}

export interface FormElement {
  id: string;
  type: string;
  label: string;
  placeholder?: string;
  required?: boolean;
  options?: { value: string; label: string }[];
}

export interface FormTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  usage_count: number;
  rating: number;
  tags: string[];
  pages: FormPage[];
}
