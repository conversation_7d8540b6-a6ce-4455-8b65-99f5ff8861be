import React, { useState, useEffect } from "react";
import {
  Store,
  Plus,
  RefreshCw,
  CheckCircle,
  XCircle,
  ToggleLeft,
  ToggleRight,
} from "lucide-react";
import {
  Card,
  Title,
  Text,
  Button,
  Grid,
  Col,
  Flex,
  Badge,
  TextInput,
  ProgressCircle,
  ProgressBar,
} from "@tremor/react";
import { storeService } from "../services/storeService";
import type { Store as StoreType } from "../services/storeService";

const Stores: React.FC = () => {
  const [stores, setStores] = useState<StoreType[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: "",
    admin_access_token: "",
    storefront_access_token: "",
  });
  const [testResult, setTestResult] = useState<any>(null);
  const [testing, setTesting] = useState(false);
  const [syncStatsByStore, setSyncStatsByStore] = useState<Record<number, any>>(
    {}
  );
  const [syncingStoreId, setSyncingStoreId] = useState<number | null>(null);
  const [syncError, setSyncError] = useState<Record<number, string | null>>({});
  const [syncElapsedTime, setSyncElapsedTime] = useState<number>(0);
  const [syncProgress, setSyncProgress] = useState<number>(0);
  const [syncMessage, setSyncMessage] = useState<string>("");
  const [syncDetails, setSyncDetails] = useState<any>(null);
  const [syncStatus, setSyncStatus] = useState<"idle" | "syncing" | "finished" | "error">("idle");

  useEffect(() => {
    let interval: number | null = null;
    if (syncingStoreId !== null) {
      setSyncElapsedTime(0);
      interval = window.setInterval(() => {
        setSyncElapsedTime((prev) => prev + 1);
      }, 1000);
    }
    return () => {
      if (interval !== null) window.clearInterval(interval);
    };
  }, [syncingStoreId]);

  useEffect(() => {
    loadStores();
  }, []);

  const loadStores = async () => {
    try {
      const data = await storeService.getStores();
      setStores(data);
    } catch (error) {
      console.error("Failed to load stores:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async () => {
    setTesting(true);
    setTestResult(null);

    try {
      const result = await storeService.testConnection(formData);
      setTestResult(result);
    } catch (error) {
      setTestResult({ success: false, message: "Connection test failed" });
    } finally {
      setTesting(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      await storeService.createStore(formData);
      setShowAddForm(false);
      setFormData({
        name: "",
        admin_access_token: "",
        storefront_access_token: "",
      });
      setTestResult(null);
      loadStores();
    } catch (error) {
      console.error("Failed to create store:", error);
    }
  };

  const handleSync = async (storeId: number) => {
    setSyncingStoreId(storeId);
    setSyncError((prev) => ({ ...prev, [storeId]: null }));
    setSyncProgress(0);
    setSyncMessage("Connecting...");
    setSyncDetails(null);
    setSyncStatus("syncing");

    const wsUrl = `${import.meta.env.VITE_WS_URL || "ws://localhost:8000"}/api/stores/${storeId}/ws-sync`;
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      console.log("WebSocket connected");
      setSyncMessage("Starting synchronization...");
    };

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      console.log("WebSocket message received:", data);
      setSyncStatus(data.status);
      setSyncMessage(data.message);
      setSyncProgress(data.progress || 0);
      setSyncDetails(data.details || null);

      if (data.status === "finished") {
        setSyncStatsByStore((prev) => ({
          ...prev,
          [storeId]: data.sync_stats || null,
        }));
        loadStores(); // Refresh stores to update last_sync
        ws.close();
        setSyncingStoreId(null);
      } else if (data.status === "error") {
        setSyncError((prev) => ({
          ...prev,
          [storeId]: data.message || "An unknown error occurred during sync.",
        }));
        ws.close();
        setSyncingStoreId(null);
      }
    };

    ws.onclose = () => {
      console.log("WebSocket disconnected");
      if (syncStatus !== "finished" && syncStatus !== "error") {
        setSyncError((prev) => ({
          ...prev,
          [storeId]: "Sync connection closed unexpectedly.",
        }));
        setSyncStatus("error");
      }
      setSyncingStoreId(null);
    };

    ws.onerror = (error) => {
      console.error("WebSocket error:", error);
      setSyncError((prev) => ({
        ...prev,
        [storeId]: "WebSocket error during sync. Check console for details.",
      }));
      setSyncStatus("error");
      ws.close();
      setSyncingStoreId(null);
    };
  };

  const handleToggleActivation = async (storeId: number) => {
    const store = stores.find((s) => s.id === storeId);
    if (!store) {
      console.error("Store not found");
      return;
    }

    const action = store.is_active ? "deactivate" : "activate";
    const storeName = store.shop_name || store.name;

    if (
      window.confirm(
        `Are you sure you want to ${action} the store "${storeName}"?`
      )
    ) {
      try {
        await storeService.toggleStoreActivation(storeId);
        loadStores();
      } catch (error) {
        console.error("Failed to toggle store activation:", error);
      }
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <>
      <Flex justifyContent="between" alignItems="center" className="mb-8">
        <div>
          <Title className="text-3xl font-bold text-gray-900 mb-2">
            Your Stores
          </Title>
          <Text className="text-gray-600">
            Connect and manage your Shopify stores with advanced analytics
          </Text>
        </div>
        <Button
          onClick={() => setShowAddForm(true)}
          icon={Plus}
          className="btn-primary"
        >
          Add Store
        </Button>
      </Flex>

      {/* Add Store Form */}
      {showAddForm && (
        <Card className="mb-8 card-modern">
          <Title className="text-xl font-semibold mb-4">
            Connect Shopify Store
          </Title>
          <Text className="text-gray-600 mb-6">
            Connect your Shopify store using the new GraphQL Admin API. You'll
            need your store name and access tokens.
          </Text>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Store Name
              </label>
              <div className="relative">
                <TextInput
                  value={formData.name}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  placeholder="my-store"
                  required
                  className="input-modern pr-32"
                />
                <span className="absolute inset-y-0 right-3 flex items-center text-gray-500 text-sm pointer-events-none">
                  .myshopify.com
                </span>
              </div>
              <Text className="text-xs text-gray-500 mt-1">
                Enter just the store name (e.g., "my-store" for
                my-store.myshopify.com)
              </Text>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Admin API Access Token
              </label>
              <TextInput
                type="password"
                value={formData.admin_access_token}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setFormData({
                    ...formData,
                    admin_access_token: e.target.value,
                  })
                }
                placeholder="shpat_..."
                required
                className="input-modern"
              />
              <Text className="text-xs text-gray-500 mt-1">
                Required for accessing products, orders, and store data
              </Text>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Storefront API Access Token (Optional)
              </label>
              <TextInput
                type="password"
                value={formData.storefront_access_token}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setFormData({
                    ...formData,
                    storefront_access_token: e.target.value,
                  })
                }
                placeholder="Optional for public storefront data"
                className="input-modern"
              />
              <Text className="text-xs text-gray-500 mt-1">
                Optional: For accessing public storefront data
              </Text>
            </div>

            {/* Test Connection */}
            <Flex alignItems="center" className="space-x-4">
              <Button
                type="button"
                onClick={handleTestConnection}
                disabled={testing}
                className="btn-secondary"
              >
                {testing ? "Testing..." : "Test Connection"}
              </Button>

              {testResult && (
                <Flex
                  alignItems="center"
                  className={`space-x-2 ${
                    testResult.success ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {testResult.success ? (
                    <CheckCircle size={20} />
                  ) : (
                    <XCircle size={20} />
                  )}
                  <Text>{testResult.message}</Text>
                </Flex>
              )}
            </Flex>

            <Flex className="space-x-4">
              <Button
                type="submit"
                disabled={!testResult?.success}
                className="btn-primary"
              >
                Add Store
              </Button>
              <Button
                type="button"
                onClick={() => {
                  setShowAddForm(false);
                  setTestResult(null);
                }}
                className="btn-secondary"
              >
                Cancel
              </Button>
            </Flex>
          </form>
        </Card>
      )}

      {/* Stores List */}
      <Grid numItemsSm={2} numItemsLg={3} className="gap-6">
        {stores.map((store) => (
          <Col key={store.id}>
            <Card className="card-modern h-full flex flex-col">
              <Flex
                justifyContent="between"
                alignItems="start"
                className="mb-4"
              >
                <Flex alignItems="center" className="space-x-3">
                  <Store className="text-blue-500" size={24} />
                  <div>
                    <Title className="font-semibold">
                      {store.shop_name || store.name}
                    </Title>
                    <Text className="text-sm text-gray-600">
                      {store.shop_domain || `${store.name}.myshopify.com`}
                    </Text>
                  </div>
                </Flex>
                <Badge
                  color={store.is_active ? "emerald" : "rose"}
                  className="ml-auto"
                >
                  {store.is_active ? "Active" : "Inactive"}
                </Badge>
              </Flex>

              <div className="mb-4">
                <Badge color="blue" className="inline-block">
                  Shopify
                </Badge>
              </div>

              <Flex
                justifyContent="between"
                alignItems="center"
                className="mb-4"
              >
                <Text className="text-xs text-gray-500">
                  {store.last_sync
                    ? `Last sync: ${new Date(store.last_sync).toLocaleString()}`
                    : "Never synced"}
                </Text>
              </Flex>

              <Flex className="space-x-2 mt-auto pt-4 border-t border-gray-200">
                <Button
                  onClick={() => handleSync(store.id)}
                  disabled={syncingStoreId === store.id || !store.is_active}
                  className="flex-1 btn-secondary"
                  icon={RefreshCw}
                >
                  {syncingStoreId === store.id
                    ? `Syncing...`
                    : "Sync"}
                </Button>
                <Button
                  onClick={() => handleToggleActivation(store.id)}
                  className={`btn-secondary ${
                    store.is_active
                      ? "text-red-500 hover:text-red-600 bg-red-50 hover:bg-red-100"
                      : "text-green-500 hover:text-green-600 bg-green-50 hover:bg-green-100"
                  }`}
                  icon={store.is_active ? ToggleRight : ToggleLeft}
                >
                  {store.is_active ? "Deactivate" : "Activate"}
                </Button>
              </Flex>
              {syncingStoreId === store.id && (
                <div className="mt-4">
                  <Flex justifyContent="between" className="mb-2">
                    <Text className="font-semibold">{syncMessage}</Text>
                    <Text>{syncProgress.toFixed(0)}%</Text>
                  </Flex>
                  <ProgressBar value={syncProgress} className="mt-2" />
                  {syncDetails && syncDetails.step && (
                    <div className="text-xs text-gray-500 mt-1">
                      <Text>Step: {syncDetails.step}</Text>
                      <Text>Processed: {syncDetails.processed} / {syncDetails.total}</Text>
                      {syncDetails.total_fetched !== undefined && (
                        <Text>Total fetched: {syncDetails.total_fetched}</Text>
                      )}
                      {syncDetails.added !== undefined && (
                        <Text>Added: {syncDetails.added}</Text>
                      )}
                      {syncDetails.updated !== undefined && (
                        <Text>Updated: {syncDetails.updated}</Text>
                      )}
                      {syncDetails.unchanged !== undefined && (
                        <Text>Unchanged: {syncDetails.unchanged}</Text>
                      )}
                      {syncDetails.failed !== undefined && (
                        <Text>Failed: {syncDetails.failed}</Text>
                      )}
                    </div>
                  )}
                </div>
              )}
              {syncStatus === "finished" && syncingStoreId === null && (
                <Text color="emerald" className="mt-2 text-sm">
                  Sync completed successfully!
                </Text>
              )}
              {syncError[store.id] && (
                <Text color="rose" className="mt-2 text-sm">
                  {syncError[store.id]}
                </Text>
              )}

              {/* Inline sync stats */}
              {syncStatsByStore[store.id] && syncStatus === "finished" && (
                <div className="mt-4 border-t pt-4">
                  <Text className="font-semibold mb-2 text-sm text-gray-700">
                    Last Sync Stats
                  </Text>
                  <div className="mb-2">
                    <Text className="font-semibold">Products:</Text>
                    <ul className="text-xs text-gray-700 ml-2">
                      <li>
                        Total fetched:{" "}
                        {syncStatsByStore[store.id].products?.total_fetched ??
                          0}
                      </li>
                      <li>
                        Added: {syncStatsByStore[store.id].products?.added ?? 0}
                      </li>
                      <li>
                        Updated:{" "}
                        {syncStatsByStore[store.id].products?.updated ?? 0}
                      </li>
                      <li>
                        Unchanged:{" "}
                        {syncStatsByStore[store.id].products?.unchanged ?? 0}
                      </li>
                      <li>
                        Failed:{" "}
                        {syncStatsByStore[store.id].products?.failed ?? 0}
                      </li>
                    </ul>
                  </div>
                  <div>
                    <Text className="font-semibold">Orders:</Text>
                    <ul className="text-xs text-gray-700 ml-2">
                      <li>
                        Total fetched:{" "}
                        {syncStatsByStore[store.id].orders?.total_fetched ?? 0}
                      </li>
                      <li>
                        Added: {syncStatsByStore[store.id].orders?.added ?? 0}
                      </li>
                      <li>
                        Updated:{" "}
                        {syncStatsByStore[store.id].orders?.updated ?? 0}
                      </li>
                      <li>
                        Unchanged:{" "}
                        {syncStatsByStore[store.id].orders?.unchanged ?? 0}
                      </li>
                      <li>
                        Failed: {syncStatsByStore[store.id].orders?.failed ?? 0}
                      </li>
                    </ul>
                  </div>
                  <div>
                    <Text className="font-semibold">Customers:</Text>
                    <ul className="text-xs text-gray-700 ml-2">
                      <li>
                        Total fetched:{" "}
                        {syncStatsByStore[store.id].customers?.total_fetched ?? 0}
                      </li>
                      <li>
                        Added: {syncStatsByStore[store.id].customers?.added ?? 0}
                      </li>
                      <li>
                        Updated:{" "}
                        {syncStatsByStore[store.id].customers?.updated ?? 0}
                      </li>
                      <li>
                        Unchanged:{" "}
                        {syncStatsByStore[store.id].customers?.unchanged ?? 0}
                      </li>
                      <li>
                        Failed: {syncStatsByStore[store.id].customers?.failed ?? 0}
                      </li>
                    </ul>
                  </div>
                </div>
              )}
            </Card>
          </Col>
        ))}
      </Grid>

      {stores.length === 0 && (
        <Card className="text-center py-12 card-modern">
          <Store className="mx-auto text-gray-400 mb-4" size={64} />
          <Title className="text-xl font-semibold text-gray-600 mb-2">
            No stores connected
          </Title>
          <Text className="text-gray-500 mb-4">
            Connect your first Shopify store to get started with sales
            forecasting
          </Text>
          <Button onClick={() => setShowAddForm(true)} className="btn-primary">
            Add Your First Store
          </Button>
        </Card>
      )}
    </>
  );
};

export default Stores;
