import React, { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  User as UserIcon,
  Mail,
  Shield,
  Briefcase,
  Clock,
} from "lucide-react";
import { api } from "../services/api";
import { useScroll } from "../contexts/ScrollContext";

interface User {
  id: number;
  full_name: string;
  email: string;
  is_active: boolean;
  created_at: string;
  stores: Store[];
}

interface Store {
  id: number;
  name: string;
  platform: string;
  shop_name: string;
}

const UserDetail: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const { scrollToTop } = useScroll();

  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const loadUser = useCallback(async () => {
    try {
      const response = await api.get(`/api/users/${userId}`);
      setUser(response.data);
    } catch (error) {
      console.error("Failed to load user:", error);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    if (userId) {
      scrollToTop();
      loadUser();
    }
  }, [userId, scrollToTop, loadUser]);

  const getStatusColor = (isActive: boolean) => {
    return isActive
      ? "bg-green-100 text-green-800"
      : "bg-red-100 text-red-800";
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <UserIcon className="mx-auto text-gray-400 mb-4" size={64} />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">
            User not found
          </h3>
          <button
            onClick={() => navigate("/users")}
            className="text-blue-500 hover:text-blue-600"
          >
            Back to Users
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate("/users")}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft size={20} className="mr-2" />
                Back to Users
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-xl font-semibold text-gray-900 truncate max-w-md">
                {user.full_name}
              </h1>
            </div>
            <div className="flex items-center space-x-3">
              <span
                className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                  user.is_active
                )}`}
              >
                {user.is_active ? "Active" : "Inactive"}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* User Info */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6 flex flex-col items-center">
                <div className="relative w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <UserIcon className="text-gray-400" size={64} />
                </div>
                <h2 className="text-2xl font-bold text-gray-900">
                  {user.full_name}
                </h2>
                <p className="text-gray-600">{user.email}</p>
              </div>

              {/* User Details */}
              <div className="border-t border-gray-200 p-6 space-y-4">
                <div className="flex items-center space-x-3">
                  <Mail className="text-gray-500" size={20} />
                  <span className="text-gray-700">{user.email}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Shield className="text-gray-500" size={20} />
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                      user.is_active
                    )}`}
                  >
                    {user.is_active ? "Active" : "Inactive"}
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <Clock className="text-gray-500" size={20} />
                  <span className="text-gray-700">
                    Joined on {new Date(user.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Stores List */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center mb-6">
                <Briefcase className="mr-2 text-blue-600" size={24} />
                Associated Stores
              </h2>
              {user.stores && user.stores.length > 0 ? (
                <div className="space-y-4">
                  {user.stores.map((store) => (
                    <div
                      key={store.id}
                      className="bg-gray-50 rounded-lg p-4 flex items-center justify-between"
                    >
                      <div>
                        <h3 className="font-semibold text-gray-800">
                          {store.shop_name || store.name}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {store.platform}
                        </p>
                      </div>
                      <button
                        onClick={() => navigate(`/stores/${store.id}`)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        View Store
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Briefcase
                    className="mx-auto text-gray-400 mb-4"
                    size={48}
                  />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No stores found
                  </h3>
                  <p className="text-gray-500">
                    This user is not associated with any stores.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDetail;
