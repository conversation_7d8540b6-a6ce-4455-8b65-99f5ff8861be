import React, { useState, useEffect, use<PERSON>allback, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Package, Search } from "lucide-react";
import { api } from "../services/api";
import { useScroll } from "../contexts/ScrollContext";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  getPaginationRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
} from "@tanstack/react-table";
import {
  Card,
  Title,
  Text,
  Button,
  TextInput,
  Select,
  SelectItem,
  Flex,
  Table,
  TableHead,
  TableHeaderCell,
  TableBody,
  TableRow,
  TableCell,
  Badge,
} from "@tremor/react";

// --- Interfaces ---
interface Product {
  id: number;
  external_id: string;
  title: string;
  description?: string;
  price: number;
  inventory_quantity: number;
  sku?: string;
  vendor?: string;
  status: string;
  images?: string[];
  variants?: any[]; // Added for product variants
  store_id: number;
  order_count?: number;
}

interface PaginatedProducts {
  total_count: number;
  products: Product[];
}

interface Store {
  id: number;
  name: string;
  shop_name?: string;
}

// --- Custom Hook for Debouncing ---
const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  return debouncedValue;
};

// --- Products Component ---
const Products: React.FC = () => {
  const navigate = useNavigate();
  const { saveScrollPosition, restoreScrollPosition } = useScroll();

  // --- State Management ---
  const [products, setProducts] = useState<Product[]>([]);
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 20,
  });
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([]);

  // Filter States
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStore, setSelectedStore] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("orders_desc");

  // Debounced search term for API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // --- Data Fetching ---
  const loadProducts = useCallback(async () => {
    setLoading(true);
    try {
      const response = await api.get<PaginatedProducts>("/api/products/", {
        params: {
          store_id: selectedStore,
          skip: pagination.pageIndex * pagination.pageSize,
          limit: pagination.pageSize,
          search: debouncedSearchTerm,
          status: statusFilter,
          sort_by: sortBy,
        },
      });
      setProducts(response.data.products);
      table.setPageCount(
        Math.ceil(response.data.total_count / pagination.pageSize)
      );
    } catch (error) {
      console.error("Failed to load products:", error);
    } finally {
      setLoading(false);
    }
  }, [selectedStore, pagination, debouncedSearchTerm, statusFilter, sortBy]);

  const loadStores = async () => {
    try {
      const response = await api.get("/api/stores/");
      setStores(response.data);
    } catch (error) {
      console.error("Failed to load stores:", error);
    }
  };

  // --- Effects ---
  // Effect to reset page to 1 when filters change
  useEffect(() => {
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  }, [debouncedSearchTerm, selectedStore, statusFilter, sortBy]);

  // Primary effect for loading products when page or filters change
  useEffect(() => {
    loadProducts();
  }, [pagination.pageIndex, pagination.pageSize, loadProducts]);

  // Effect to load stores once on mount
  useEffect(() => {
    loadStores();
    restoreScrollPosition("products-list");
  }, [restoreScrollPosition]);

  // --- Table Definition ---
  const columns = useMemo<ColumnDef<Product>[]>(
    () => [
      {
        accessorKey: "images",
        header: "Image",
        cell: (info) => {
          const images = info.getValue() as string[] | undefined;
          return (
            <img
              src={images?.[0] || "/placeholder.svg"}
              alt="Product Image"
              className="w-16 h-16 object-cover rounded-md"
            />
          );
        },
        enableSorting: false,
        enableColumnFilter: false,
      },
      {
        accessorKey: "title",
        header: "Product",
        cell: (info) => (
          <Text className="font-medium text-gray-900">
            {info.getValue() as string}
          </Text>
        ),
      },
      {
        accessorKey: "status",
        header: "Status",
        cell: (info) => (
          <Badge
            color={info.getValue() === "active" ? "emerald" : "gray"}
            className="capitalize"
          >
            {info.getValue() as string}
          </Badge>
        ),
      },
      {
        accessorKey: "inventory_quantity",
        header: "Inventory",
        cell: (info) => <Text>{info.getValue() as number}</Text>,
      },
      {
        accessorKey: "order_count",
        header: "Orders",
        cell: (info) => <Text>{info.getValue() as number}</Text>,
      },
      {
        accessorKey: "price",
        header: "Price",
        cell: (info) => (
          <Text className="font-medium">
            ${(info.getValue() as number).toFixed(2)}
          </Text>
        ),
      },
      {
        id: "actions",
        header: "",
        cell: (info) => (
          <Button
            size="xs"
            variant="secondary"
            onClick={() => handleProductClick(info.row.original)}
          >
            Details
          </Button>
        ),
        enableSorting: false,
        enableColumnFilter: false,
      },
    ],
    []
  );

  const table = useReactTable({
    data: products,
    columns,
    state: {
      columnFilters,
      sorting,
      pagination,
    },
    onColumnFiltersChange: setColumnFilters,
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true, // We'll handle pagination ourselves
    manualFiltering: true,
    manualSorting: true,
  });

  // --- Event Handlers ---
  const handleProductClick = (product: Product) => {
    saveScrollPosition("products-list");
    navigate(`/products/${product.id}`);
  };

  // --- Render Logic ---
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <Flex justifyContent="between" alignItems="center" className="mb-8">
        <div>
          <Title className="text-3xl font-bold text-gray-900 mb-2">
            Products
          </Title>
          <Text className="text-gray-600">
            Manage your product catalog with interactive sales forecasting
          </Text>
        </div>
      </Flex>

      {/* Filters */}
      <Card className="mb-6 card-modern">
        <Flex className="flex-wrap items-center gap-4">
          <TextInput
            icon={Search}
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="input-modern flex-grow"
          />
          <Select
            value={selectedStore || ""}
            onValueChange={(value) => setSelectedStore(value || null)}
            placeholder="All Stores"
            className="input-modern"
          >
            <SelectItem value="">All Stores</SelectItem>
            {stores.map((store) => (
              <SelectItem key={store.id} value={String(store.id)}>
                {store.shop_name || store.name}
              </SelectItem>
            ))}
          </Select>
          <Select
            value={statusFilter}
            onValueChange={setStatusFilter}
            placeholder="All Status"
            className="input-modern"
          >
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="archived">Archived</SelectItem>
          </Select>
          <Select
            value={sortBy}
            onValueChange={setSortBy}
            placeholder="Sort by: Newest"
            className="input-modern"
          >
            <SelectItem value="newest">Sort by: Newest</SelectItem>
            <SelectItem value="orders_desc">
              Sort by: Orders (High to Low)
            </SelectItem>
            <SelectItem value="orders_asc">
              Sort by: Orders (Low to High)
            </SelectItem>
            <SelectItem value="title_asc">Sort by: Title (A-Z)</SelectItem>
            <SelectItem value="title_desc">Sort by: Title (Z-A)</SelectItem>
            <SelectItem value="price_asc">
              Sort by: Price (Low to High)
            </SelectItem>
            <SelectItem value="price_desc">
              Sort by: Price (High to Low)
            </SelectItem>
            <SelectItem value="stock_asc">
              Sort by: Stock (Low to High)
            </SelectItem>
            <SelectItem value="stock_desc">
              Sort by: Stock (High to Low)
            </SelectItem>
          </Select>
        </Flex>
      </Card>

      {/* Products Display */}
      {table.getRowModel().rows.length > 0 ? (
        <Card className="card-modern">
          <Table className="mt-5">
            <TableHead>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHeaderCell key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder ? null : (
                        <div
                          {...{
                            className: header.column.getCanSort()
                              ? "cursor-pointer select-none"
                              : "",
                            onClick: header.column.getToggleSortingHandler(),
                          }}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {{
                            asc: " 🔼",
                            desc: " 🔽",
                          }[header.column.getIsSorted() as string] ?? null}
                        </div>
                      )}
                    </TableHeaderCell>
                  ))}
                </TableRow>
              ))}
            </TableHead>
            <TableBody>
              {table.getRowModel().rows.map((row) => (
                <TableRow key={row.id}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Pagination */}
          <Flex justifyContent="end" className="mt-8 space-x-2">
            <Button
              variant="secondary"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              Previous
            </Button>
            <Button
              variant="secondary"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              Next
            </Button>
            <Text className="ml-4">
              Page {table.getState().pagination.pageIndex + 1} of{" "}
              {table.getPageCount()}
            </Text>
          </Flex>
        </Card>
      ) : (
        <Card className="text-center py-12 card-modern">
          <Package className="mx-auto text-gray-400 mb-4" size={64} />
          <Title className="text-xl font-semibold text-gray-600 mb-2">
            No products found
          </Title>
          <Text className="text-gray-500">
            Try adjusting your search terms or filters, or sync a store to get
            started.
          </Text>
        </Card>
      )}
    </>
  );
};

export default Products;
