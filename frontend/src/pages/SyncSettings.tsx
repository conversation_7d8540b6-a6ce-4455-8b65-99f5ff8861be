import React, { useState, useEffect } from 'react';
import { Card, Title, Text, Button, Select, SelectItem, Flex, Switch } from '@tremor/react';
import { storeService } from '../services/storeService';
import type { Store as StoreType } from '../services/storeService';

const SyncSettings: React.FC = () => {
  const [stores, setStores] = useState<StoreType[]>([]);
  const [selectedStoreId, setSelectedStoreId] = useState<string | undefined>(undefined);
  const [syncing, setSyncing] = useState(false);
  const [syncMessage, setSyncMessage] = useState<string | null>(null);
  const [syncError, setSyncError] = useState<string | null>(null);

  useEffect(() => {
    const loadStores = async () => {
      try {
        const data = await storeService.getStores();
        setStores(data);
        if (data.length > 0) {
          setSelectedStoreId(String(data[0].id));
        } else {
          setSelectedStoreId(undefined);
        }
      } catch (error) {
        console.error("Failed to load stores:", error);
      }
    };
    loadStores();
  }, []);

  const handleSync = async () => {
    if (selectedStoreId === undefined) return; // Check for undefined explicitly

    setSyncing(true);
    setSyncMessage(null);
    setSyncError(null);

    try {
      const result = await storeService.syncStore(Number(selectedStoreId));
      setSyncMessage(result.message || "Sync initiated successfully!");
    } catch (error) {
      console.error("Failed to initiate sync:", error);
      setSyncError("Failed to initiate sync. Please try again.");
    } finally {
      setSyncing(false);
    }
  };

  return (
    <Card className="card-modern">
      <Title className="text-2xl font-bold mb-4">Synchronization Settings</Title>
      <Text className="mb-6">Manage your store synchronization preferences.</Text>

      <div className="space-y-6">
        <div>
          <label htmlFor="store-select" className="block text-sm font-medium text-gray-700 mb-1">
            Select Store
          </label>
          <Select
            id="store-select"
            value={selectedStoreId}
            onValueChange={setSelectedStoreId}
            placeholder="Select a store"
            className="input-modern"
          >
            {stores.map((store) => (
              <SelectItem key={store.id} value={String(store.id)}>
                {store.shop_name || store.name}
              </SelectItem>
            ))}
          </Select>
        </div>

        <Flex justifyContent="between" alignItems="center">
          <Text>Enable Automatic Daily Sync</Text>
          <Switch
            // This would typically be connected to a backend setting
            checked={false} // Placeholder
            onChange={() => alert("Automatic sync toggle (backend integration needed)")}
            className="tremor-switch"
          />
        </Flex>

        <Flex justifyContent="between" alignItems="center">
          <Text>Sync Products</Text>
          <Switch checked={true} onChange={() => {}} className="tremor-switch" />
        </Flex>

        <Flex justifyContent="between" alignItems="center">
          <Text>Sync Orders</Text>
          <Switch checked={true} onChange={() => {}} className="tremor-switch" />
        </Flex>

        <Flex justifyContent="between" alignItems="center">
          <Text>Sync Customers</Text>
          <Switch checked={true} onChange={() => {}} className="tremor-switch" />
        </Flex>

        <Button
          onClick={handleSync}
          disabled={!selectedStoreId || syncing}
          className="btn-primary mt-6"
        >
          {syncing ? "Initiating Sync..." : "Initiate Full Sync Now"}
        </Button>

        {syncMessage && <Text color="emerald" className="mt-4">{syncMessage}</Text>}
        {syncError && <Text color="rose" className="mt-4">{syncError}</Text>}
      </div>
    </Card>
  );
};

export default SyncSettings;
