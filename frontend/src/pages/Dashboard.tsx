import React, { useEffect, useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/useAuth";
import { Store, Package, ShoppingCart, Plus } from "lucide-react";
import { api } from "../services/api";
import {
  Card,
  Title,
  Text,
  Button,
  Grid,
  Col,
  Flex,
  Metric,
  Table,
  TableHead,
  TableHeaderCell,
  TableBody,
  TableRow,
  TableCell,
} from "@tremor/react";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  getPaginationRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
} from "@tanstack/react-table";

interface Customer {
  id: number;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  total_spent?: number;
  orders_count?: number;
  note?: string;
  tags?: string;
}

interface PaginatedCustomers {
  total_count: number;
  consumers: Customer[];
}

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [productCount, setProductCount] = useState<number>(0);
  const [orderCount, setOrderCount] = useState<number>(0);
  const [revenue, setRevenue] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [customerPagination, setCustomerPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });
  const [customerColumnFilters, setCustomerColumnFilters] = useState<ColumnFiltersState>([]);
  const [customerSorting, setCustomerSorting] = useState<SortingState>([]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch products
        const productsRes = await api.get("/api/products/");
        setProductCount(
          Array.isArray(productsRes.data.products)
            ? productsRes.data.products.length
            : 0
        );

        // Fetch orders
        const ordersRes = await api.get("/api/orders/");
        const orders = Array.isArray(ordersRes.data.orders)
          ? ordersRes.data.orders
          : [];
        setOrderCount(orders.length);

        // Fetch customers
        const customersRes = await api.get<PaginatedCustomers>(
          `/api/aggregated/consumers?skip=${
            customerPagination.pageIndex * customerPagination.pageSize
          }&limit=${customerPagination.pageSize}`
        );
        setCustomers(customersRes.data.consumers);
        customerTable.setPageCount(
          Math.ceil(customersRes.data.total_count / customerPagination.pageSize)
        );

        // Calculate revenue for current month
        const now = new Date();
        const currentMonth = now.getMonth();
        const currentYear = now.getFullYear();
        const monthRevenue = orders.reduce(
          (
            sum: number,
            order: { order_date: string; total_price: number | string }
          ) => {
            const orderDate = new Date(order.order_date);
            if (
              orderDate.getMonth() === currentMonth &&
              orderDate.getFullYear() === currentYear
            ) {
              return (
                sum +
                (typeof order.total_price === "string"
                  ? parseFloat(order.total_price)
                  : order.total_price || 0)
              );
            }
            return sum;
          },
          0
        );
        setRevenue(monthRevenue);
      } catch (error) {
        console.error("Failed to fetch dashboard data:", error);
        setProductCount(0);
        setOrderCount(0);
        setRevenue(0);
        setCustomers([]);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [customerPagination.pageIndex, customerPagination.pageSize]);

  const customerColumns = useMemo<ColumnDef<Customer>[]>(
    () => [
      {
        accessorFn: (row) =>
          `${row.first_name || ""} ${row.last_name || ""}`.trim(),
        id: "name",
        header: "Name",
        cell: (info) => <Text>{info.getValue() as string}</Text>,
      },
      {
        accessorKey: "email",
        header: "Email",
        cell: (info) => <Text>{info.getValue() as string}</Text>,
      },
      {
        accessorKey: "phone",
        header: "Phone",
        cell: (info) => <Text>{(info.getValue() as string) || "N/A"}</Text>,
      },
      {
        accessorKey: "total_spent",
        header: "Total Spent",
        cell: (info) => (
          <Text>${((info.getValue() as number) || 0).toFixed(2)}</Text>
        ),
      },
      {
        accessorKey: "orders_count",
        header: "Orders",
        cell: (info) => <Text>{(info.getValue() as number) || 0}</Text>,
      },
      {
        id: "actions",
        header: "",
        cell: (info) => (
          <Button
            size="xs"
            variant="secondary"
            onClick={() => navigate(`/customers/${info.row.original.id}`)}
          >
            View
          </Button>
        ),
        enableSorting: false,
        enableColumnFilter: false,
      },
    ],
    []
  );

  const customerTable = useReactTable({
    data: customers,
    columns: customerColumns,
    state: {
      columnFilters: customerColumnFilters,
      sorting: customerSorting,
      pagination: customerPagination,
    },
    onColumnFiltersChange: setCustomerColumnFilters,
    onSortingChange: setCustomerSorting,
    onPaginationChange: setCustomerPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true,
    manualFiltering: true,
    manualSorting: true,
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <>
      <div className="mb-8">
        <Title className="text-3xl font-bold text-gray-900 mb-2">
          Welcome back, {user?.full_name}!
        </Title>
        <Text className="text-gray-600">
          Manage your e-commerce stores and track your business performance.
        </Text>
      </div>

      {/* Quick Actions */}
      <Grid numItemsSm={2} numItemsLg={4} className="gap-6 mb-8">
        <Col>
          <Card className="card-modern h-full">
            <Flex justifyContent="between" alignItems="start" className="mb-4">
              <Store className="text-blue-500" size={32} />
              <Button
                size="xs"
                variant="secondary"
                icon={Plus}
                onClick={() => navigate("/stores")}
              >
                Add Store
              </Button>
            </Flex>
            <Title className="text-lg font-semibold mb-2">Connect Store</Title>
            <Text className="text-gray-600 text-sm">
              Add your Shopify or WooCommerce store
            </Text>
          </Card>
        </Col>

        <Col>
          <Card className="card-modern h-full">
            <Flex justifyContent="between" alignItems="start" className="mb-4">
              <Package className="text-green-500" size={32} />
              <Metric>{productCount}</Metric>
            </Flex>
            <Title className="text-lg font-semibold mb-2">Products</Title>
            <Text className="text-gray-600 text-sm">
              Manage your product catalog
            </Text>
          </Card>
        </Col>

        <Col>
          <Card className="card-modern h-full">
            <Flex justifyContent="between" alignItems="start" className="mb-4">
              <ShoppingCart className="text-purple-500" size={32} />
              <Metric>{orderCount}</Metric>
            </Flex>
            <Title className="text-lg font-semibold mb-2">Orders</Title>
            <Text className="text-gray-600 text-sm">
              Track your recent orders
            </Text>
          </Card>
        </Col>

        <Col>
          <Card className="card-modern h-full">
            <Flex justifyContent="between" alignItems="start" className="mb-4">
              <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                <Text className="text-white font-bold text-lg">$</Text>
              </div>
              <Metric>
                {`${revenue.toLocaleString(undefined, {
                  maximumFractionDigits: 2,
                })}`}
              </Metric>
            </Flex>
            <Title className="text-lg font-semibold mb-2">Revenue</Title>
            <Text className="text-gray-600 text-sm">
              Total sales this month
            </Text>
          </Card>
        </Col>
      </Grid>

      {/* Getting Started */}
      <Card className="bg-blue-50 rounded-lg p-8 mb-8 card-modern">
        <Title className="text-2xl font-bold text-gray-900 mb-4">
          Getting Started
        </Title>
        <Grid numItemsSm={1} numItemsMd={3} className="gap-6">
          <Flex alignItems="start" className="space-x-3">
            <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold">
              1
            </div>
            <div>
              <Title className="font-semibold mb-1">Connect Your Store</Title>
              <Text className="text-gray-600 text-sm">
                Add your Shopify or WooCommerce store using API credentials
              </Text>
            </div>
          </Flex>

          <Flex alignItems="start" className="space-x-3">
            <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold">
              2
            </div>
            <div>
              <Title className="font-semibold mb-1">Sync Your Data</Title>
              <Text className="text-gray-600 text-sm">
                Import products, inventory, and order history
              </Text>
            </div>
          </Flex>

          <Flex alignItems="start" className="space-x-3">
            <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold">
              3
            </div>
            <div>
              <Title className="font-semibold mb-1">Monitor & Manage</Title>
              <Text className="text-gray-600 text-sm">
                Track performance and manage your business
              </Text>
            </div>
          </Flex>
        </Grid>
      </Card>

      {/* Recent Customers Table */}
      <Card className="card-modern">
        <Title className="text-2xl font-bold mb-4">Recent Customers</Title>
        {customerTable.getRowModel().rows.length > 0 ? (
          <>
            <Table className="mt-5">
              <TableHead>
                {customerTable.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHeaderCell key={header.id} colSpan={header.colSpan}>
                        {header.isPlaceholder ? null : (
                          <div
                            {...{
                              className: header.column.getCanSort()
                                ? "cursor-pointer select-none"
                                : "",
                              onClick: header.column.getToggleSortingHandler(),
                            }}
                          >
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {{
                              asc: " 🔼",
                              desc: " 🔽",
                            }[header.column.getIsSorted() as string] ?? null}
                          </div>
                        )}
                      </TableHeaderCell>
                    ))}
                  </TableRow>
                ))}
              </TableHead>
              <TableBody>
                {customerTable.getRowModel().rows.map((row) => (
                  <TableRow key={row.id}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <Flex justifyContent="end" className="mt-8 space-x-2">
              <Button
                variant="secondary"
                onClick={() => customerTable.previousPage()}
                disabled={!customerTable.getCanPreviousPage()}
              >
                Previous
              </Button>
              <Button
                variant="secondary"
                onClick={() => customerTable.nextPage()}
                disabled={!customerTable.getCanNextPage()}
              >
                Next
              </Button>
              <Text className="ml-4">
                Page {customerTable.getState().pagination.pageIndex + 1} of{" "}
                {customerTable.getPageCount()}
              </Text>
            </Flex>
          </>
        ) : (
          <Card className="text-center py-12 card-modern">
            <ShoppingCart className="mx-auto text-gray-400 mb-4" size={64} />
            <Title className="text-xl font-semibold text-gray-600 mb-2">
              No customers found
            </Title>
            <Text className="text-gray-500">
              No customer data available. Sync a store to get started.
            </Text>
          </Card>
        )}
      </Card>
    </>
  );
};

export default Dashboard;
