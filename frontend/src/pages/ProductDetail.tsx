import React, { useState, useEffect, useCallback } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  Package,
  TrendingUp,
  ExternalLink,
  Tag,
  DollarSign,
  BarChart3,
  ShoppingCart,
  Info,
} from "lucide-react";
import { api } from "../services/api";
import SalesForecastChart from "../components/SalesForecastChart";
import {
  Card,
  Title,
  Text,
  Button,
  Flex,
  Badge,
  Metric,
  Select,
  SelectItem,
  Callout,
  Table,
  TableHead,
  TableHeaderCell,
  TableBody,
  TableRow,
  TableCell,
} from "@tremor/react";

interface Product {
  id: number;
  external_id: string;
  title: string;
  description?: string;
  description_html?: string;
  handle?: string;
  price: number;
  compare_at_price?: number;
  inventory_quantity: number;
  sku?: string;
  barcode?: string;
  weight?: number;
  weight_unit?: string;
  status: string;
  product_type?: string;
  vendor?: string;
  tags?: string[];
  images?: string[];
  variants?: any[];
  store_id: number;
  order_count?: number;
}

interface Order {
  id: number;
  external_id: string;
  order_number: string;
  customer_email?: string;
  total_price: number;
  status: string;
  financial_status: string;
  fulfillment_status: string;
  order_date: string;
  line_items: any[];
}

interface ForecastData {
  date: string;
  predicted_sales: number;
  lower_bound: number;
  upper_bound: number;
}

interface ModelForecast {
  model_name: string;
  forecast_data: ForecastData[];
  model_info: string;
}

interface AnomalyData {
  date: string;
  value: number;
  detector: string;
  type: string;
}

interface ForecastResult {
  success: boolean;
  message?: string;
  forecast_data?: ForecastData[]; // Legacy single model support
  forecasts?: Record<string, ModelForecast>; // Multi-model forecasts
  ensemble_forecast?: ForecastData[]; // Ensemble forecast
  anomalies?: AnomalyData[]; // Anomaly detection results
  historical_data?: any[];
  forecast_period?: string;
  available_models?: string[];
  product_info?: {
    id: number;
    title: string;
    sku?: string;
    current_inventory: number;
  };
}

const emptyForecastData: ForecastData[] = [];
const emptyForecasts: Record<string, ModelForecast> = {};
const emptyEnsembleForecast: ForecastData[] = [];
const emptyAnomalies: AnomalyData[] = [];
const emptyHistoricalData: any[] = [];
const emptyAvailableModels: string[] = [];

const ProductDetail: React.FC = () => {
  const { productId } = useParams<{ productId: string }>();
  const navigate = useNavigate();

  const [product, setProduct] = useState<Product | null>(null);
  const [orders, setOrders] = useState<Order[]>([]);
  const [forecastData, setForecastData] = useState<ForecastResult | null>(null);
  const [loading, setLoading] = useState(true); // Only for product data
  const [ordersLoading, setOrdersLoading] = useState(true); // Separate loading for orders
  const [forecastLoading, setForecastLoading] = useState(false); // Separate loading for forecast
  const [selectedForecastPeriod, setSelectedForecastPeriod] = useState("1M");
  const [imageError, setImageError] = useState(false);

  const productInfoFallback = React.useMemo(() => {
    if (!product) return null;
    return {
      id: product.id,
      title: product.title,
      sku: product.sku,
      current_inventory: product.inventory_quantity,
    };
  }, [product]);

  const loadProduct = useCallback(async () => {
    try {
      const response = await api.get(`/api/products/${productId}`);
      setProduct(response.data);
    } catch (error) {
      console.error("Failed to load product:", error);
    } finally {
      setLoading(false);
    }
  }, [productId]);

  const loadOrders = useCallback(async () => {
    if (!productId) return;

    setOrdersLoading(true);
    try {
      const response = await api.get(`/api/products/${productId}/orders`);
      setOrders(response.data);
    } catch (error) {
      console.error("Failed to load orders:", error);
    } finally {
      setOrdersLoading(false);
    }
  }, [productId]);

  const loadForecast = useCallback(async (forceRefresh = false) => {
    setForecastLoading(true);
    try {
      const response = await api.get(`/api/products/${productId}/forecast`, {
        params: { 
          forecast_period: selectedForecastPeriod,
          force_refresh: forceRefresh,
        },
      });
      setForecastData(response.data);
    } catch (error) {
      console.error("Failed to load forecast:", error);
      setForecastData({
        success: false,
        message: "Failed to load forecast data",
      });
    } finally {
      setForecastLoading(false);
    }
  }, [productId, selectedForecastPeriod]);

  const handleRegenerateForecast = useCallback(() => {
    loadForecast(true);
  }, [loadForecast]);

  const handlePopulateSalesData = useCallback(async () => {
    try {
      await api.post("/api/products/populate-sales-data");
      loadForecast();
    } catch (error) {
      console.error("Failed to populate sales data:", error);
    }
  }, [loadForecast]);

  // Load product and orders on initial mount
  useEffect(() => {
    if (productId) {
      loadProduct();
      loadOrders();
    }
  }, [productId, loadProduct, loadOrders]);

  // Load forecast data separately after the initial render, and when the period changes.
  useEffect(() => {
    if (productId) {
      loadForecast();
    }
  }, [selectedForecastPeriod, productId, loadForecast]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "emerald";
      case "draft":
        return "yellow";
      case "archived":
        return "gray";
      default:
        return "gray";
    }
  };

  if (loading) {
    return (
      <Card className="text-center py-12 card-modern">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <Text>Loading product details...</Text>
      </Card>
    );
  }

  if (!product) {
    return (
      <Card className="text-center py-12 card-modern">
        <Package className="mx-auto text-gray-400 mb-4" size={64} />
        <Title className="text-xl font-semibold text-gray-600 mb-2">
          Product not found
        </Title>
        <Button
          onClick={() => navigate("/products")}
          variant="secondary"
          className="mt-4"
        >
          Back to Products
        </Button>
      </Card>
    );
  }

  const primaryImage =
    product.images && product.images.length > 0 ? product.images[0] : null;
  const hasDiscount =
    product.compare_at_price && product.compare_at_price > product.price;
  const discountPercentage = hasDiscount
    ? Math.round(
        ((product.compare_at_price! - product.price) /
          product.compare_at_price!) *
          100
      )
    : 0;

  return (
    <>
      {/* Header */}
      <Card className="mb-8 card-modern">
        <Flex justifyContent="between" alignItems="center">
          <Flex alignItems="center" className="space-x-4">
            <Button
              onClick={() => navigate("/products")}
              variant="secondary"
              icon={ArrowLeft}
            >
              Back to Products
            </Button>
            <Title className="text-xl font-semibold text-gray-900 truncate max-w-md">
              {product.title}
            </Title>
          </Flex>
          <Badge color={getStatusColor(product.status)} className="capitalize">
            {product.status}
          </Badge>
        </Flex>
      </Card>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Product Info */}
        <div className="lg:col-span-1">
          <Card className="card-modern h-full">
            {/* Product Image */}
            <div className="relative h-64 bg-gray-100 mb-6">
              {primaryImage && !imageError ? (
                <img
                  src={primaryImage}
                  alt={product.title}
                  className="w-full h-full object-cover rounded-md"
                  onError={() => setImageError(true)}
                />
              ) : (
                <Flex
                  justifyContent="center"
                  alignItems="center"
                  className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 rounded-md"
                >
                  <Package className="text-gray-400" size={64} />
                </Flex>
              )}
            </div>

            {/* Product Details */}
            <div className="space-y-4">
              {/* Price */}
              <Flex alignItems="center" className="space-x-2">
                <DollarSign className="text-green-600" size={20} />
                <Flex alignItems="center" className="space-x-2">
                  <Metric className="text-2xl font-bold text-gray-900">
                    ${product.price.toFixed(2)}
                  </Metric>
                  {hasDiscount && (
                    <>
                      <Text className="text-lg text-gray-500 line-through">
                        ${product.compare_at_price!.toFixed(2)}
                      </Text>
                      <Badge color="rose">
                        {discountPercentage}% off
                      </Badge>
                    </>
                  )}
                </Flex>
              </Flex>

              {/* Inventory */}
              <Flex alignItems="center" className="space-x-2">
                <Package className="text-blue-600" size={20} />
                <Text className="text-gray-700">
                  <span className="font-medium">
                    {product.inventory_quantity}
                  </span>{" "}
                  in stock
                </Text>
              </Flex>

              {/* SKU */}
              {product.sku && (
                <Flex alignItems="center" className="space-x-2">
                  <Tag className="text-purple-600" size={20} />
                  <Text className="text-gray-700">
                    SKU: <span className="font-mono">{product.sku}</span>
                  </Text>
                </Flex>
              )}

              {/* Product Type */}
              {product.product_type && (
                <Flex alignItems="center" className="space-x-2">
                  <BarChart3 className="text-orange-600" size={20} />
                  <Text className="text-gray-700">
                    {product.product_type}
                  </Text>
                </Flex>
              )}

              {/* Vendor */}
              {product.vendor && (
                <Flex alignItems="center" className="space-x-2">
                  <ShoppingCart className="text-indigo-600" size={20} />
                  <Text className="text-gray-700">{product.vendor}</Text>
                </Flex>
              )}

              {/* Product Variants */}
              {product.variants && product.variants.length > 1 && (
                <div>
                  <label htmlFor="variant-select" className="block text-sm font-medium text-gray-700 mb-1">
                    Select Variant
                  </label>
                  <Select
                    id="variant-select"
                    placeholder="Select a variant"
                    className="input-modern"
                    // You would typically manage selected variant state here
                    // onValueChange={(value) => handleVariantChange(value)}
                  >
                    {product.variants.map((variant: any) => (
                      <SelectItem key={variant.id} value={variant.id}>
                        {variant.title} (${variant.price.toFixed(2)}) - {variant.inventory_quantity} in stock
                      </SelectItem>
                    ))}
                  </Select>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="mt-6 space-y-3">
              {product.handle && (
                <Button
                  onClick={() =>
                    window.open(
                      `https://shopify.com/admin/products/${product.external_id}`,
                      "_blank"
                    )
                  }
                  icon={ExternalLink}
                  className="btn-primary w-full"
                >
                  View in Shopify
                </Button>
              )}
            </div>
          </Card>
        </div>

        {/* Forecast Chart and Orders List */}
        <div className="lg:col-span-2 space-y-8">
          {/* Forecast Chart */}
          <Card className="card-modern">
            <Flex justifyContent="between" alignItems="center" className="mb-6">
              <Title className="text-xl font-semibold text-gray-900 flex items-center">
                <TrendingUp className="mr-2 text-blue-600" size={24} />
                Sales Forecast
              </Title>

              {/* Forecast Period Selector */}
              <Select
                value={selectedForecastPeriod}
                onValueChange={setSelectedForecastPeriod}
                placeholder="Select Period"
                className="w-32"
              >
                {["5Y", "2Y", "1Y", "6M", "3M", "1M"].map((period) => (
                  <SelectItem key={period} value={period}>
                    {period}
                  </SelectItem>
                ))}
              </Select>
            </Flex>

            {forecastData?.success && forecastData.product_info ? (
              <SalesForecastChart
                forecastData={forecastData.forecast_data || emptyForecastData}
                forecasts={forecastData.forecasts || emptyForecasts}
                ensembleForecast={forecastData.ensemble_forecast || emptyEnsembleForecast}
                anomalies={forecastData.anomalies || emptyAnomalies}
                historicalData={forecastData.historical_data || emptyHistoricalData}
                productInfo={forecastData.product_info}
                isLoading={forecastLoading}
                onRefresh={handleRegenerateForecast}
                onPopulateSalesData={handlePopulateSalesData}
                forecastPeriod={selectedForecastPeriod}
                availableModels={forecastData.available_models || emptyAvailableModels}
              />
            ) : (
              <SalesForecastChart
                forecastData={emptyForecastData}
                forecasts={emptyForecasts}
                ensembleForecast={emptyEnsembleForecast}
                anomalies={emptyAnomalies}
                historicalData={emptyHistoricalData}
                productInfo={productInfoFallback!}
                isLoading={forecastLoading}
                error={forecastData?.message}
                onRefresh={handleRegenerateForecast}
                onPopulateSalesData={handlePopulateSalesData}
                forecastPeriod={selectedForecastPeriod}
                availableModels={emptyAvailableModels}
              />
            )}
          </Card>

          {/* Orders List - Below the forecast chart */}
          <Card className="card-modern">
            <Flex justifyContent="between" alignItems="center" className="mb-6">
              <Title className="text-xl font-semibold text-gray-900 flex items-center">
                <ShoppingCart className="mr-2 text-green-600" size={24} />
                Recent Orders
                {ordersLoading && (
                  <div className="ml-3 animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                )}
              </Title>
            </Flex>

            {ordersLoading ? (
              // Orders Loading Skeleton
              <Table className="min-w-full divide-y divide-gray-200">
                <TableHead>
                  <TableRow>
                    <TableHeaderCell>Order</TableHeaderCell>
                    <TableHeaderCell>Customer</TableHeaderCell>
                    <TableHeaderCell>Total</TableHeaderCell>
                    <TableHeaderCell>Status</TableHeaderCell>
                    <TableHeaderCell>Order Date</TableHeaderCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {[...Array(5)].map((_, index) => (
                    <TableRow key={index} className="animate-pulse">
                      <TableCell>
                        <div className="h-4 bg-gray-200 rounded w-16"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-4 bg-gray-200 rounded w-32"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-4 bg-gray-200 rounded w-20"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-6 bg-gray-200 rounded-full w-16"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-4 bg-gray-200 rounded w-24"></div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : orders.length > 0 ? (
              <Table className="min-w-full divide-y divide-gray-200">
                <TableHead>
                  <TableRow>
                    <TableHeaderCell>Order</TableHeaderCell>
                    <TableHeaderCell>Customer</TableHeaderCell>
                    <TableHeaderCell>Total</TableHeaderCell>
                    <TableHeaderCell>Status</TableHeaderCell>
                    <TableHeaderCell>Order Date</TableHeaderCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {orders.map((order) => (
                    <TableRow
                      key={order.id}
                      className="hover:bg-gray-50 cursor-pointer transition-colors"
                      onClick={() => navigate(`/orders/${order.id}`)}
                    >
                      <TableCell>
                        <Text className="font-medium text-gray-900">
                          #{order.order_number}
                        </Text>
                      </TableCell>
                      <TableCell>
                        <Text className="text-sm text-gray-900">
                          {order.customer_email || "Guest"}
                        </Text>
                      </TableCell>
                      <TableCell>
                        <Text className="text-sm font-medium text-green-600">
                          ${order.total_price.toFixed(2)}
                        </Text>
                      </TableCell>
                      <TableCell>
                        <Badge
                          color={order.status === "fulfilled" ? "emerald" : order.status === "pending" ? "yellow" : "gray"}
                          className="capitalize"
                        >
                          {order.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Text className="text-sm text-gray-500">
                          {new Date(order.order_date).toLocaleDateString()}
                        </Text>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <Callout
                title="No orders found"
                icon={Info}
                color="blue"
                className="mt-4"
              >
                <Text>No orders found for this product.</Text>
              </Callout>
            )}
          </Card>
        </div>
      </div>
    </>
  );
};

export default ProductDetail;
