import React, { useState, useEffect } from 'react';
import { Card, Title, Text, Table, TableHead, TableHeaderCell, TableBody, TableRow, TableCell, Badge, Flex } from '@tremor/react';
import { CheckCircle, XCircle } from 'lucide-react';

interface WebhookLog {
  id: string;
  timestamp: string;
  topic: string;
  status: 'success' | 'failed';
  message: string;
  payload_summary: string;
}

const WebhookMonitor: React.FC = () => {
  const [logs, setLogs] = useState<WebhookLog[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate fetching webhook logs from a backend API
    const fetchLogs = async () => {
      setLoading(true);
      // In a real application, you would make an API call here
      // For now, we'll use mock data
      const mockLogs: WebhookLog[] = [
        {
          id: 'wh_1',
          timestamp: new Date().toLocaleString(),
          topic: 'products/update',
          status: 'success',
          message: 'Product updated successfully',
          payload_summary: 'Product ID: 123, Title: New Product',
        },
        {
          id: 'wh_2',
          timestamp: new Date(Date.now() - 60000).toLocaleString(),
          topic: 'orders/create',
          status: 'failed',
          message: 'Failed to process order: Invalid customer ID',
          payload_summary: 'Order ID: 456, Customer: <EMAIL>',
        },
        {
          id: 'wh_3',
          timestamp: new Date(Date.now() - 120000).toLocaleString(),
          topic: 'customers/create',
          status: 'success',
          message: 'Customer created',
          payload_summary: 'Customer ID: 789, Name: John Doe',
        },
        {
          id: 'wh_4',
          timestamp: new Date(Date.now() - 180000).toLocaleString(),
          topic: 'products/delete',
          status: 'success',
          message: 'Product deleted',
          payload_summary: 'Product ID: 101',
        },
      ];
      setLogs(mockLogs);
      setLoading(false);
    };

    fetchLogs();
    // Simulate polling for new logs every 30 seconds
    const interval = setInterval(fetchLogs, 30000);
    return () => clearInterval(interval);
  }, []);

  return (
    <Card className="card-modern">
      <Title className="text-2xl font-bold mb-4">Webhook Monitor</Title>
      <Text className="mb-6">View incoming webhook logs and their processing status.</Text>

      {loading ? (
        <div className="flex justify-center items-center min-h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      ) : logs.length === 0 ? (
        <Text className="text-center text-gray-500 py-12">No webhook logs available.</Text>
      ) : (
        <Table className="mt-6">
          <TableHead>
            <TableRow>
              <TableHeaderCell>Timestamp</TableHeaderCell>
              <TableHeaderCell>Topic</TableHeaderCell>
              <TableHeaderCell>Status</TableHeaderCell>
              <TableHeaderCell>Message</TableHeaderCell>
              <TableHeaderCell>Payload Summary</TableHeaderCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {logs.map((log) => (
              <TableRow key={log.id}>
                <TableCell>{log.timestamp}</TableCell>
                <TableCell>{log.topic}</TableCell>
                <TableCell>
                  <Flex justifyContent="start" alignItems="center" className="space-x-2">
                    <Badge color={log.status === 'success' ? 'emerald' : 'rose'}>
                      {log.status === 'success' ? <CheckCircle size={16} /> : <XCircle size={16} />}
                      <span className="ml-1 capitalize">{log.status}</span>
                    </Badge>
                  </Flex>
                </TableCell>
                <TableCell>{log.message}</TableCell>
                <TableCell className="text-xs font-mono">{log.payload_summary}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </Card>
  );
};

export default WebhookMonitor;
