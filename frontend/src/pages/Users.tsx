import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { User as UserIcon, Search, Grid, List } from "lucide-react";
import { api } from "../services/api";
import UserCard from "../components/UserCard";
import { useScroll } from "../contexts/ScrollContext";

// --- Interfaces ---
interface User {
  id: number;
  full_name: string;
  email: string;
  is_active: boolean;
}

interface PaginatedUsers {
  total_count: number;
  users: User[];
}

// --- Custom Hook for Debouncing ---
const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  return debouncedValue;
};

// --- Users Component ---
const Users: React.FC = () => {
  const navigate = useNavigate();
  const { saveScrollPosition, restoreScrollPosition } = useScroll();

  // --- State Management ---
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage] = useState(20);

  // Filter States
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("name_asc");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Debounced search term for API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // --- Data Fetching ---
  const loadUsers = useCallback(
    async (page: number) => {
      setLoading(true);
      try {
        const response = await api.get<PaginatedUsers>("/api/users", {
          params: {
            skip: (page - 1) * itemsPerPage,
            limit: itemsPerPage,
            search: debouncedSearchTerm,
            status: statusFilter,
            sort_by: sortBy,
          },
        });
        setUsers(response.data.users);
        setTotalPages(Math.ceil(response.data.total_count / itemsPerPage));
      } catch (error) {
        console.error("Failed to load users:", error);
        // Optionally, set an error state here to show in the UI
      } finally {
        setLoading(false);
      }
    },
    [itemsPerPage, debouncedSearchTerm, statusFilter, sortBy]
  );

  // --- Effects ---
  // Effect to reset page to 1 when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchTerm, statusFilter, sortBy]);

  // Primary effect for loading users when page or filters change
  useEffect(() => {
    loadUsers(currentPage);
  }, [currentPage, loadUsers]);

  // Effect to load stores once on mount
  useEffect(() => {
    restoreScrollPosition("users-list");
  }, [restoreScrollPosition]);

  // --- Event Handlers ---
  const handleUserClick = (user: User) => {
    saveScrollPosition("users-list");
    navigate(`/users/${user.id}`);
  };

  // --- Render Logic ---
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Users</h1>
          <p className="text-gray-600">
            Browse and manage user accounts
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode("grid")}
            className={`p-2 rounded-lg ${
              viewMode === "grid"
                ? "bg-blue-500 text-white"
                : "bg-gray-100 text-gray-600"
            }`}
          >
            <Grid size={20} />
          </button>
          <button
            onClick={() => setViewMode("list")}
            className={`p-2 rounded-lg ${
              viewMode === "list"
                ? "bg-blue-500 text-white"
                : "bg-gray-100 text-gray-600"
            }`}
          >
            <List size={20} />
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="mb-6 flex flex-wrap items-center gap-4">
        <div className="relative flex-grow">
          <Search
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            size={20}
          />
          <input
            type="text"
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="name_asc">Sort by: Name (A-Z)</option>
          <option value="name_desc">Sort by: Name (Z-A)</option>
          <option value="email_asc">Sort by: Email (A-Z)</option>
          <option value="email_desc">Sort by: Email (Z-A)</option>
        </select>
      </div>

      {/* Users Display */}
      {users.length > 0 ? (
        <>
          {viewMode === 'list' && (
            <div className="hidden md:flex items-center p-4 mb-2 text-xs font-bold text-gray-500 uppercase bg-gray-50 rounded-lg">
              <div className="w-24 flex-shrink-0 mr-4"></div> {/* Image placeholder */}
              <div className="flex-grow">User</div>
              <div className="w-32 text-center">Status</div>
              <div className="w-24 text-center"></div> {/* Details button placeholder */}
            </div>
          )}
          <div
            className={
              viewMode === "grid"
                ? "grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                : "space-y-4"
            }
          >
            {users.map((user) => (
              <UserCard
                key={user.id}
                user={user}
                onClick={handleUserClick}
                viewMode={viewMode}
              />
            ))}
          </div>
          {totalPages > 1 && (
            <div className="mt-8 flex justify-center">
              <nav className="inline-flex rounded-md shadow">
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={currentPage === 1}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 disabled:opacity-50"
                >
                  Previous
                </button>
                <span className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border-t border-b border-gray-300">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  disabled={currentPage === totalPages}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 disabled:opacity-50"
                >
                  Next
                </button>
              </nav>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <UserIcon className="mx-auto text-gray-400 mb-4" size={64} />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">
            No users found
          </h3>
          <p className="text-gray-500">
            Try adjusting your search terms or filters.
          </p>
        </div>
      )}
    </div>
  );
};

export default Users;
