import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { ShoppingCart, Search } from "lucide-react";
import { fetchOrders, type Order } from "../services/orderService";
import { api } from "../services/api";
import type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
} from "@tanstack/react-table";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  getPaginationRowModel,
  getFilteredRowModel,
  getSortedRowModel,
} from "@tanstack/react-table";
import {
  Card,
  Title,
  Text,
  Button,
  TextInput,
  Select,
  SelectItem,
  Flex,
  Table,
  TableHead,
  TableHeaderCell,
  TableBody,
  TableRow,
  TableCell,
  Badge,
} from "@tremor/react";

interface Store {
  id: number;
  name: string;
  shop_name?: string;
}

const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  return debouncedValue;
};

const Orders: React.FC = () => {
  const navigate = useNavigate();
  const [orders, setOrders] = useState<Order[]>([]);
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStore, setSelectedStore] = useState<number | null>(null);
  const [sortBy, setSortBy] = useState<string>("newest");
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 20,
  });
  const [totalPages, setTotalPages] = useState(1); // Added totalPages state
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([]);

  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  const loadOrders = useCallback(async () => {
    setLoading(true);
    try {
      const ordersData = await fetchOrders(
        selectedStore !== null ? selectedStore : undefined,
        pagination.pageIndex * pagination.pageSize,
        pagination.pageSize,
        debouncedSearchTerm,
        sortBy
      );
      setOrders(ordersData.orders);
      setTotalPages(Math.ceil(ordersData.total_count / pagination.pageSize)); // Update totalPages
    } catch (error) {
      console.error("Failed to load orders:", error);
    } finally {
      setLoading(false);
    }
  }, [selectedStore, pagination, debouncedSearchTerm, sortBy]);

  const loadStores = async () => {
    try {
      const response = await api.get("/api/stores/");
      setStores(response.data);
    } catch (error) {
      console.error("Failed to load stores:", error);
    }
  };

  useEffect(() => {
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  }, [debouncedSearchTerm, selectedStore, sortBy]);

  useEffect(() => {
    loadOrders();
  }, [pagination.pageIndex, pagination.pageSize, loadOrders]);

  useEffect(() => {
    loadStores();
  }, []);

  const handleOrderClick = (orderId: number) => {
    navigate(`/orders/${orderId}`);
  };

  const columns = useMemo<ColumnDef<Order>[]>(
    () => [
      {
        accessorKey: "order_number",
        header: "Order",
        cell: (info: any) => (
          <Text className="font-semibold text-gray-900">
            #{info.getValue() as string}
          </Text>
        ),
      },
      {
        accessorKey: "order_date",
        header: "Date",
        cell: (info: any) => (
          <Text className="text-sm text-gray-600">
            {new Date(info.getValue() as string).toLocaleDateString()}
          </Text>
        ),
      },
      {
        accessorKey: "customer_email",
        header: "Customer",
        cell: (info: any) => (
          <Text className="text-sm text-gray-900">
            {(info.getValue() as string) || "N/A"}
          </Text>
        ),
      },
      {
        accessorKey: "total_price",
        header: "Total",
        cell: (info: any) => (
          <Text className="text-sm text-gray-900">
            ${(info.getValue() as number).toFixed(2)}
          </Text>
        ),
      },
      {
        accessorKey: "status",
        header: "Status",
        cell: (info: any) => (
          <Badge
            color={(() => {
              switch ((info.getValue() as string).toLowerCase()) {
                case "fulfilled":
                case "completed":
                  return "emerald";
                case "pending":
                case "processing":
                  return "yellow";
                case "cancelled":
                case "refunded":
                  return "rose";
                default:
                  return "gray";
              }
            })()}
            className="capitalize"
          >
            {info.getValue() as string}
          </Badge>
        ),
      },
      {
        accessorKey: "line_items",
        header: "Items",
        cell: (info: any) => (
          <Text className="text-sm text-gray-600">
            {(info.getValue() as any[]).length}
          </Text>
        ),
        enableSorting: false,
        enableColumnFilter: false,
      },
      {
        accessorKey: "store_id",
        header: "Store",
        cell: (info: any) => (
          <Text className="text-sm text-gray-600">
            {stores.find((s) => s.id === (info.getValue() as number))
              ?.shop_name || "N/A"}
          </Text>
        ),
        enableSorting: false,
        enableColumnFilter: false,
      },
      {
        id: "actions",
        header: "",
        cell: (info: any) => (
          <Button
            size="xs"
            variant="secondary"
            onClick={() => handleOrderClick(info.row.original.id)}
          >
            Details
          </Button>
        ),
        enableSorting: false,
        enableColumnFilter: false,
      },
    ],
    [stores]
  );

  const table = useReactTable({
    data: orders,
    columns,
    state: {
      columnFilters,
      sorting,
      pagination,
    },
    onColumnFiltersChange: setColumnFilters,
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true,
    manualFiltering: true,
    manualSorting: true,
    pageCount: totalPages, // Pass totalPages to useReactTable
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <Flex justifyContent="between" alignItems="center" className="mb-8">
        <div>
          <Title className="text-3xl font-bold text-gray-900 mb-2">
            Orders
          </Title>
          <Text className="text-gray-600">
            Track and manage orders from all your stores
          </Text>
        </div>
      </Flex>

      {/* Filters */}
      <Card className="mb-6 card-modern">
        <Flex className="flex-wrap items-center gap-4">
          <TextInput
            icon={Search}
            placeholder="Search orders..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="input-modern flex-grow"
          />
          <Select
            value={selectedStore !== null ? String(selectedStore) : ""}
            onValueChange={(value) =>
              setSelectedStore(value ? Number(value) : null)
            }
            placeholder="All Stores"
            className="input-modern"
          >
            <SelectItem value="">All Stores</SelectItem>
            {stores.map((store) => (
              <SelectItem key={store.id} value={String(store.id)}>
                {store.shop_name || store.name}
              </SelectItem>
            ))}
          </Select>
          <Select
            value={sortBy}
            onValueChange={setSortBy}
            placeholder="Sort by: Newest"
            className="input-modern"
          >
            <SelectItem value="newest">Sort by: Newest</SelectItem>
            <SelectItem value="oldest">Sort by: Oldest</SelectItem>
            <SelectItem value="order_number_asc">
              Sort by: Order # (Asc)
            </SelectItem>
            <SelectItem value="order_number_desc">
              Sort by: Order # (Desc)
            </SelectItem>
            <SelectItem value="total_asc">
              Sort by: Total (Low to High)
            </SelectItem>
            <SelectItem value="total_desc">
              Sort by: Total (High to Low)
            </SelectItem>
          </Select>
        </Flex>
      </Card>

      {table.getRowModel().rows.length > 0 ? (
        <Card className="card-modern">
          <Table className="mt-5">
            <TableHead>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHeaderCell key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder ? null : (
                        <div
                          {...{
                            className: header.column.getCanSort()
                              ? "cursor-pointer select-none"
                              : "",
                            onClick: header.column.getToggleSortingHandler(),
                          }}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {{
                            asc: " 🔼",
                            desc: " 🔽",
                          }[header.column.getIsSorted() as string] ?? null}
                        </div>
                      )}
                    </TableHeaderCell>
                  ))}
                </TableRow>
              ))}
            </TableHead>
            <TableBody>
              {table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  onClick={() => handleOrderClick(row.original.id)}
                  className="cursor-pointer hover:bg-blue-50"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Pagination */}
          <Flex justifyContent="end" className="mt-8 space-x-2">
            <Button
              variant="secondary"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              Previous
            </Button>
            <Button
              variant="secondary"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              Next
            </Button>
            <Text className="ml-4">
              Page {table.getState().pagination.pageIndex + 1} of{" "}
              {table.getPageCount()}
            </Text>
          </Flex>
        </Card>
      ) : (
        <Card className="text-center py-12 card-modern">
          <ShoppingCart className="mx-auto text-gray-400 mb-4" size={64} />
          <Title className="text-xl font-semibold text-gray-600 mb-2">
            No orders found
          </Title>
          <Text className="text-gray-500">
            Try adjusting your search terms or filters.
          </Text>
        </Card>
      )}
    </>
  );
};

export default Orders;
