// Main services export file for the HomeService AI Platform

// Core Services
export { authService } from './auth/authService';
export { apiClient } from './api/apiClient';
export { agentService } from './agents/agentService';
export { callService } from './calls/callService';
export { customersService } from './customers/customersService';
export { dashboardService } from './dashboard/dashboardService';
export { jobsService } from './jobs/jobsService';
export { performanceService } from './performance/performanceService';
export { websocketService } from './websocket/websocketService';
export { configService } from './config/configService';
export { errorHandler } from './error/errorHandler';

// Re-export types and utilities
export type { Agent, AgentCreate, AgentUpdate } from './agents/agentService';
export type { Call, CallCreate, CallUpdate } from './calls/callService';
export type { Customer, CustomerCreate, CustomerUpdate } from './customers/customersService';
export type { Job, JobCreate, JobUpdate } from './jobs/jobsService';

// Service initialization
export const initializeServices = async () => {
  try {
    console.log('HomeService AI Platform services initialized');
    return true;
  } catch (error) {
    console.error('Failed to initialize services:', error);
    return false;
  }
};
