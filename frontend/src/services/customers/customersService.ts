import { BaseApiClient, ApiResponse } from '@/lib/api';
import { Customer } from '@/types';

class CustomersApiClient extends BaseApiClient {
  constructor() {
    super('/api/customers');
  }

  async getAllCustomers(): Promise<ApiResponse<Customer[]>> {
    return this.get<Customer[]>('/');
  }

  async getCustomerById(id: number): Promise<ApiResponse<Customer>> {
    return this.get<Customer>(`/${id}`);
  }

  async createCustomer(
    customerData: Omit<Customer, 'id' | 'created_at' | 'updated_at'>,
  ): Promise<ApiResponse<Customer>> {
    return this.post<Customer>('/', customerData);
  }

  async updateCustomer(
    id: number,
    customerData: Partial<Omit<Customer, 'id' | 'created_at' | 'updated_at'>>,
  ): Promise<ApiResponse<Customer>> {
    return this.put<Customer>(`/${id}`, customerData);
  }

  async deleteCustomer(id: number): Promise<ApiResponse<void>> {
    return this.delete<void>(`/${id}`);
  }
}

const customersApiClient = new CustomersApiClient();

export const customersService = {
  async getAllCustomers(): Promise<Customer[]> {
    const response = await customersApiClient.getAllCustomers();
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch customers');
    }
    return response.data || [];
  },

  async getCustomerById(id: number): Promise<Customer> {
    const response = await customersApiClient.getCustomerById(id);
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch customer');
    }
    return response.data!;
  },

  async createCustomer(
    customerData: Omit<Customer, 'id' | 'created_at' | 'updated_at'>,
  ): Promise<Customer> {
    const response = await customersApiClient.createCustomer(customerData);
    if (!response.success) {
      throw new Error(response.error || 'Failed to create customer');
    }
    return response.data!;
  },

  async updateCustomer(
    id: number,
    customerData: Partial<Omit<Customer, 'id' | 'created_at' | 'updated_at'>>,
  ): Promise<Customer> {
    const response = await customersApiClient.updateCustomer(id, customerData);
    if (!response.success) {
      throw new Error(response.error || 'Failed to update customer');
    }
    return response.data!;
  },

  async deleteCustomer(id: number): Promise<void> {
    const response = await customersApiClient.deleteCustomer(id);
    if (!response.success) {
      throw new Error(response.error || 'Failed to delete customer');
    }
  },
};
