/**
 * Agent Service
 * Provides a clean interface for all agent operations
 */
import { apiClient } from '../api/apiClient';

// Agent interfaces for the HomeService AI Platform
export interface Agent {
  id: string;
  name: string;
  description: string;
  type: 'voice' | 'chat' | 'multi-modal';
  status: 'active' | 'inactive' | 'training';
  provider: 'elevenlabs' | 'dify' | 'openai';
  system_prompt: string;
  variables: Record<string, string>;
  settings: {
    temperature: number;
    max_tokens: number;
    voice_id?: string;
    language: string;
    response_format: string;
  };
  created_at: string;
  updated_at: string;
  performance?: {
    total_conversations: number;
    success_rate: number;
    avg_response_time: number;
    customer_satisfaction: number;
  };
}

export interface AgentCreate {
  name: string;
  description: string;
  type: 'voice' | 'chat' | 'multi-modal';
  provider: 'elevenlabs' | 'dify' | 'openai';
  system_prompt: string;
  variables?: Record<string, string>;
  settings?: {
    temperature?: number;
    max_tokens?: number;
    voice_id?: string;
    language?: string;
    response_format?: string;
  };
}

export interface AgentUpdate {
  name?: string;
  description?: string;
  type?: 'voice' | 'chat' | 'multi-modal';
  provider?: 'elevenlabs' | 'dify' | 'openai';
  system_prompt?: string;
  variables?: Record<string, string>;
  settings?: {
    temperature?: number;
    max_tokens?: number;
    voice_id?: string;
    language?: string;
    response_format?: string;
  };
}

// Agent service for HomeService AI Platform

export const agentService = {
  // Basic CRUD operations for agents
  async create(agentData: AgentCreate): Promise<Agent> {
    try {
      const response = await apiClient.post('/api/agents', agentData);
      return response.data;
    } catch (error) {
      throw new Error('Failed to create agent');
    }
  },

  async getAll(): Promise<Agent[]> {
    try {
      const response = await apiClient.get('/api/agents');
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch agents');
    }
  },

  async getById(id: string): Promise<Agent> {
    try {
      const response = await apiClient.get(`/api/agents/${id}`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch agent');
    }
  },

  async update(id: string, agentData: AgentUpdate): Promise<Agent> {
    try {
      const response = await apiClient.put(`/api/agents/${id}`, agentData);
      return response.data;
    } catch (error) {
      throw new Error('Failed to update agent');
    }
  },

  async delete(id: string): Promise<void> {
    try {
      await apiClient.delete(`/api/agents/${id}`);
    } catch (error) {
      throw new Error('Failed to delete agent');
    }
  },

  async toggleStatus(id: string): Promise<Agent> {
    try {
      const response = await apiClient.post(`/api/agents/${id}/toggle-status`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to toggle agent status');
    }
  },

  // Test agent functionality
  async testAgent(id: string, message: string): Promise<any> {
    try {
      const response = await apiClient.post(`/api/agents/${id}/test`, { message });
      return response.data;
    } catch (error) {
      throw new Error('Failed to test agent');
    }
  },

  // Get agent analytics
  async getAnalytics(id: string, timeRange: string = '7d'): Promise<any> {
    try {
      const response = await apiClient.get(`/api/agents/${id}/analytics?timeRange=${timeRange}`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch agent analytics');
    }
  },
};

export default agentService;
