import { apiClient } from '../api/apiClient';

interface User {
  email: string;
  full_name: string;
  is_active: boolean;
}

export const authService = {
  async login(email: string, password: string): Promise<User> {
    const response = await apiClient.login(email, password);
    return response;
  },

  async logout(): Promise<void> {
    await apiClient.logout();
  },

  async register(email: string, password: string, full_name: string): Promise<User> {
    const response = await apiClient.register(email, password, full_name);
    return response;
  },

  async getCurrentUser(): Promise<User> {
    const response = await apiClient.getCurrentUser();
    return response;
  },

  async forgotPassword(email: string): Promise<void> {
    await apiClient.forgotPassword(email);
  },

  async resetPassword(token: string, password: string): Promise<void> {
    await apiClient.resetPassword(token, password);
  },
};
