// frontend/src/services/dashboard/dashboardService.ts
import { BaseApiClient, ApiResponse } from '@/lib/api';

export interface DashboardStats {
  total_agents: number;
  active_agents: number;
  total_calls: number;
  successful_calls: number;
  total_customers: number;
  active_customers: number;
  total_templates: number;
  featured_templates: number;
}

export interface RecentActivity {
  id: string;
  type: 'agent_created' | 'call_completed' | 'customer_added' | 'template_used';
  title: string;
  description: string;
  timestamp: string;
  metadata?: any;
}

export interface CallMetrics {
  total_calls: number;
  successful_calls: number;
  failed_calls: number;
  average_duration: number;
  success_rate: number;
  calls_by_hour: Array<{ hour: number; count: number }>;
  calls_by_day: Array<{ date: string; count: number }>;
}

export interface AgentMetrics {
  total_agents: number;
  active_agents: number;
  inactive_agents: number;
  agents_by_category: Array<{ category: string; count: number }>;
  most_used_agents: Array<{ id: number; name: string; usage_count: number }>;
}

class DashboardApiClient extends BaseApiClient {
  constructor() {
    super('/api/dashboard');
  }

  async getStats(): Promise<ApiResponse<DashboardStats>> {
    return this.get<DashboardStats>('/stats');
  }

  async getRecentActivity(limit: number = 10): Promise<ApiResponse<RecentActivity[]>> {
    return this.get<RecentActivity[]>(`/activity?limit=${limit}`);
  }

  async getCallMetrics(days: number = 30): Promise<ApiResponse<CallMetrics>> {
    return this.get<CallMetrics>(`/calls/metrics?days=${days}`);
  }

  async getAgentMetrics(): Promise<ApiResponse<AgentMetrics>> {
    return this.get<AgentMetrics>('/agents/metrics');
  }
}

const dashboardApiClient = new DashboardApiClient();

export const dashboardService = {
  async getStats(): Promise<DashboardStats> {
    const response = await dashboardApiClient.getStats();
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch dashboard stats');
    }
    return response.data!;
  },

  async getRecentActivity(limit: number = 10): Promise<RecentActivity[]> {
    const response = await dashboardApiClient.getRecentActivity(limit);
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch recent activity');
    }
    return response.data || [];
  },

  async getCallMetrics(days: number = 30): Promise<CallMetrics> {
    const response = await dashboardApiClient.getCallMetrics(days);
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch call metrics');
    }
    return response.data!;
  },

  async getAgentMetrics(): Promise<AgentMetrics> {
    const response = await dashboardApiClient.getAgentMetrics();
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch agent metrics');
    }
    return response.data!;
  },
};

export default dashboardService;
