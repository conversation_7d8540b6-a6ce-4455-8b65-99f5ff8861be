import { BaseApiClient, ApiResponse } from '@/lib/api';
import { Job } from '@/types';

class JobsApiClient extends BaseApiClient {
  constructor() {
    super('/api/jobs');
  }

  async getAllJobs(params?: {
    skip?: number;
    limit?: number;
    status?: string;
  }): Promise<ApiResponse<Job[]>> {
    const searchParams = new URLSearchParams();
    if (params?.skip) searchParams.set('skip', params.skip.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.status) searchParams.set('status', params.status);

    const query = searchParams.toString();
    return this.get<Job[]>(`/${query ? `?${query}` : ''}`);
  }

  async getJobById(id: number): Promise<ApiResponse<Job>> {
    return this.get<Job>(`/${id}`);
  }

  async createJob(
    jobData: Omit<Job, 'id' | 'created_at' | 'updated_at'>,
  ): Promise<ApiResponse<Job>> {
    return this.post<Job>('/', jobData);
  }

  async updateJob(
    id: number,
    jobData: Partial<Omit<Job, 'id' | 'created_at' | 'updated_at'>>,
  ): Promise<ApiResponse<Job>> {
    return this.put<Job>(`/${id}`, jobData);
  }

  async deleteJob(id: number): Promise<ApiResponse<void>> {
    return this.delete<void>(`/${id}`);
  }
}

const jobsApiClient = new JobsApiClient();

export const jobsService = {
  async getAllJobs(params?: { skip?: number; limit?: number; status?: string }): Promise<Job[]> {
    const response = await jobsApiClient.getAllJobs(params);
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch jobs');
    }
    return response.data || [];
  },

  async getJobById(id: number): Promise<Job> {
    const response = await jobsApiClient.getJobById(id);
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch job');
    }
    return response.data!;
  },

  async createJob(jobData: Omit<Job, 'id' | 'created_at' | 'updated_at'>): Promise<Job> {
    const response = await jobsApiClient.createJob(jobData);
    if (!response.success) {
      throw new Error(response.error || 'Failed to create job');
    }
    return response.data!;
  },

  async updateJob(
    id: number,
    jobData: Partial<Omit<Job, 'id' | 'created_at' | 'updated_at'>>,
  ): Promise<Job> {
    const response = await jobsApiClient.updateJob(id, jobData);
    if (!response.success) {
      throw new Error(response.error || 'Failed to update job');
    }
    return response.data!;
  },

  async deleteJob(id: number): Promise<void> {
    const response = await jobsApiClient.deleteJob(id);
    if (!response.success) {
      throw new Error(response.error || 'Failed to delete job');
    }
  },
};
