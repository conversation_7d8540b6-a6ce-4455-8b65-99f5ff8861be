import { api } from '@/lib/api';
import { errorHandler, ErrorType } from '../error/errorHandler';
import {
  Agent,
  CallHistoryUpdatePayload,
  Customer,
  InitiateCallPayload,
  InitiateTextChatPayload,
  InitiateWebCallPayload,
  Job,
  TestPrompt,
  PhoneNumber,
  PerformanceMetric,
} from '@/types';

// Enhanced API client with comprehensive error handling and retry logic
class ApiClient {
  private baseURL: string;
  private retryAttempts: number = 3;
  private retryDelay: number = 1000;

  constructor(baseURL: string = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000') {
    this.baseURL = baseURL;
  }

  private async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private async retryRequest<T>(
    requestFn: () => Promise<T>,
    attempts: number = this.retryAttempts,
    context?: any,
  ): Promise<T> {
    try {
      return await requestFn();
    } catch (error: any) {
      if (attempts > 1 && this.shouldRetry(error)) {
        await this.delay(this.retryDelay);
        return this.retryRequest(requestFn, attempts - 1, context);
      }

      // Handle error with context
      const appError = errorHandler.handleApiError(error, context);
      throw appError;
    }
  }

  private shouldRetry(error: any): boolean {
    // Retry on network errors, 5xx errors, or timeout
    return (
      !error.response ||
      error.response.status >= 500 ||
      error.code === 'NETWORK_ERROR' ||
      error.code === 'TIMEOUT'
    );
  }

  // Standard HTTP methods with retry logic
  async get(url: string, config?: any) {
    return this.retryRequest(async () => {
      const response = await api.get(url, config);
      return response;
    });
  }

  async post(url: string, data?: any, config?: any) {
    return this.retryRequest(async () => {
      const response = await api.post(url, data, config);
      return response;
    });
  }

  async put(url: string, data?: any, config?: any) {
    return this.retryRequest(async () => {
      const response = await api.put(url, data, config);
      return response;
    });
  }

  async delete(url: string, config?: any) {
    return this.retryRequest(async () => {
      const response = await api.delete(url, config);
      return response;
    });
  }

  // Health Check
  async healthCheck() {
    return this.retryRequest(async () => {
      const response = await api.get('/health');
      return response.data;
    });
  }

  // Auth APIs
  async login(email: string, password: string) {
    return this.retryRequest(async () => {
      const form_data = new URLSearchParams();
      form_data.append('username', email);
      form_data.append('password', password);
      const response = await api.post('/api/auth/token', form_data, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
      return response.data;
    });
  }

  async logout() {
    return this.retryRequest(async () => {
      await api.post('/api/auth/logout');
    });
  }

  async register(email: string, password: string, full_name: string) {
    return this.retryRequest(async () => {
      const response = await api.post('/api/auth/register', {
        email,
        password,
        full_name,
      });
      return response.data;
    });
  }

  async getCurrentUser() {
    return this.retryRequest(async () => {
      const response = await api.get('/api/auth/me');
      return response.data;
    });
  }

  async forgotPassword(email: string) {
    return this.retryRequest(async () => {
      await api.post('/api/auth/forgot-password', { email });
    });
  }

  async resetPassword(token: string, password: string) {
    return this.retryRequest(async () => {
      await api.post('/api/auth/reset-password', { token, password });
    });
  }

  // Call APIs
  async initiateTextChat(request: any) {
    return this.retryRequest(async () => {
      const response = await api.post('/api/call', { ...request, type: 'text' });
      return response.data;
    });
  }

  async initiateVoiceCall(request: any) {
    return this.retryRequest(async () => {
      const response = await api.post('/api/call', { ...request, type: 'voice' });
      return response.data;
    });
  }

  async initiateWebCall(request: any) {
    return this.retryRequest(async () => {
      const response = await api.post('/api/call', { ...request, type: 'web' });
      return response.data;
    });
  }

  async getCallHistory(callHistoryId: number) {
    return this.retryRequest(async () => {
      const response = await api.get(`/api/call/history/${callHistoryId}`);
      return response.data;
    });
  }

  async getAllCallHistories(skip: number = 0, limit: number = 50) {
    return this.retryRequest(async () => {
      const response = await api.get(`/api/call/history?skip=${skip}&limit=${limit}`);
      return response.data;
    });
  }

  createWebCallWebSocket(): WebSocket {
    const wsProtocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
    const wsUrl = this.baseURL.replace(/^https?:\/\//, '');
    return new WebSocket(`${wsProtocol}://${wsUrl}/api/call/ws/web-call`);
  }

  createPhoneRelayWebSocket(callHistoryId: number): WebSocket {
    const wsProtocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
    const wsUrl = this.baseURL.replace(/^https?:\/\//, '');
    return new WebSocket(`${wsProtocol}://${wsUrl}/api/call/ws/phone-relay/${callHistoryId}`);
  }

  // Batch Testing API
  async batchTestNodes(nodeConfigs: any[], testInput: any) {
    return this.retryRequest(async () => {
      const response = await api.post('/api/node/batch-test', {
        node_configs: nodeConfigs,
        test_input: testInput,
      });
      return response.data;
    });
  }

  // Batch Testing API
  async batchTestNodes(nodeConfigs: any[], testInput: any) {
    return this.retryRequest(async () => {
      const response = await api.post('/api/node/batch-test', {
        node_configs: nodeConfigs,
        test_input: testInput,
      });
      return response.data;
    });
  }

  async getTemplateStats() {
    return this.retryRequest(async () => {
      const response = await api.get('/api/templates/stats');
      return response.data;
    });
  }

  async useTemplate(templateId: string, customizations?: Partial<any>) {
    return this.retryRequest(async () => {
      const response = await api.post(`/api/templates/${templateId}/use`, {
        customizations,
      });
      return response.data;
    });
  }

  async trackTemplateUsage(templateId: string) {
    return this.retryRequest(async () => {
      await api.post(`/api/templates/${templateId}/track-usage`);
    });
  }

  async rateTemplate(templateId: string, rating: number, review?: string) {
    return this.retryRequest(async () => {
      await api.post(`/api/templates/${templateId}/rate`, {
        rating,
        review,
      });
    });
  }

  async getTemplateReviews(templateId: string) {
    return this.retryRequest(async () => {
      const response = await api.get(`/api/templates/${templateId}/reviews`);
      return response.data;
    });
  }

  async createTemplate(template: any) {
    return this.retryRequest(async () => {
      const response = await api.post('/api/templates', template);
      return response.data;
    });
  }

  async updateTemplate(id: string, template: Partial<any>) {
    return this.retryRequest(async () => {
      const response = await api.put(`/api/templates/${id}`, template);
      return response.data;
    });
  }

  async deleteTemplate(id: string) {
    return this.retryRequest(async () => {
      await api.delete(`/api/templates/${id}`);
    });
  }

  async shareTemplate(
    templateId: string,
    shareOptions: { public?: boolean; users?: string[]; organizations?: string[] },
  ) {
    return this.retryRequest(async () => {
      const response = await api.post(`/api/templates/${templateId}/share`, shareOptions);
      return response.data;
    });
  }

  async exportTemplate(templateId: string): Promise<Blob> {
    return this.retryRequest(async () => {
      const response = await api.get(`/api/templates/${templateId}/export`, {
        responseType: 'blob',
      });
      return response.data;
    });
  }

  async importTemplate(file: File) {
    return this.retryRequest(async () => {
      const formData = new FormData();
      formData.append('file', file);
      const response = await api.post('/api/templates/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    });
  }

  // Health Check
  async healthCheck() {
    return this.retryRequest(async () => {
      const response = await api.get('/health');
      return response.data;
    });
  }

  // Customer API
  async getAllCustomers(params = {}) {
    return this.retryRequest(async () => {
      const response = await api.get<Customer[]>('/api/customers', { params });
      return response.data;
    });
  }

  async getCustomerById(id: number) {
    return this.retryRequest(async () => {
      const response = await api.get<Customer>(`/api/customers/${id}`);
      return response.data;
    });
  }

  async createCustomer(data: Partial<Customer>) {
    return this.retryRequest(async () => {
      const response = await api.post<Customer>('/api/customers', data);
      return response.data;
    });
  }

  async updateCustomer(id: number, data: Partial<Customer>) {
    return this.retryRequest(async () => {
      const response = await api.put<Customer>(`/api/customers/${id}`, data);
      return response.data;
    });
  }

  async deleteCustomer(id: number) {
    return this.retryRequest(async () => {
      const response = await api.delete<void>(`/api/customers/${id}`);
      return response.data;
    });
  }

  // Phone Numbers API
  async getPhoneNumbers() {
    return this.retryRequest(async () => {
      const response = await api.get<PhoneNumber[]>('/api/call/numbers');
      return response.data;
    });
  }

  async createPhoneNumber(data: Partial<PhoneNumber>) {
    return this.retryRequest(async () => {
      const response = await api.post<PhoneNumber>('/api/call/numbers', data);
      return response.data;
    });
  }

  async updatePhoneNumber(id: number, data: Partial<PhoneNumber>) {
    return this.retryRequest(async () => {
      const response = await api.put<PhoneNumber>(`/api/call/numbers/${id}`, data);
      return response.data;
    });
  }

  async deletePhoneNumber(id: number) {
    return this.retryRequest(async () => {
      const response = await api.delete<void>(`/api/call/numbers/${id}`);
      return response.data;
    });
  }

  // Dashboard API
  async getDashboardSummary() {
    return this.retryRequest(async () => {
      const response = await api.get('/api/dashboard/summary');
      return response.data;
    });
  }

  async getRecentCustomers() {
    return this.retryRequest(async () => {
      const response = await api.get<Customer[]>('/api/dashboard/recent-customers');
      return response.data;
    });
  }

  async getRecentCalls() {
    return this.retryRequest(async () => {
      const response = await api.get('/api/dashboard/recent-calls');
      return response.data;
    });
  }

  // Jobs API
  async getAllJobs(params = {}) {
    return this.retryRequest(async () => {
      const response = await api.get<Job[]>('/api/jobs', { params });
      return response.data;
    });
  }

  async getJobById(id: number) {
    return this.retryRequest(async () => {
      const response = await api.get<Job>(`/api/jobs/${id}`);
      return response.data;
    });
  }

  async createJob(data: Partial<Job>) {
    return this.retryRequest(async () => {
      const response = await api.post<Job>('/api/jobs', data);
      return response.data;
    });
  }

  async updateJob(id: number, data: Partial<Job>) {
    return this.retryRequest(async () => {
      const response = await api.put<Job>(`/api/jobs/${id}`, data);
      return response.data;
    });
  }

  async deleteJob(id: number) {
    return this.retryRequest(async () => {
      const response = await api.delete<void>(`/api/jobs/${id}`);
      return response.data;
    });
  }

  // Voice Call API
  async initiateCall(data: InitiateCallPayload) {
    return this.retryRequest(async () => {
      const response = await api.post('/api/call', { ...data, type: 'voice' });
      return response.data;
    });
  }

  async initiateWebCall(data: InitiateWebCallPayload) {
    return this.retryRequest(async () => {
      const response = await api.post('/api/call', { ...data, type: 'web' });
      return response.data;
    });
  }

  async initiateTextChat(data: InitiateTextChatPayload) {
    return this.retryRequest(async () => {
      const response = await api.post('/api/call', { ...data, type: 'text' });
      return response.data;
    });
  }

  async endCall(callSid: string) {
    return this.retryRequest(async () => {
      const response = await api.post<void>(`/api/call/end/${callSid}`);
      return response.data;
    });
  }

  async getCallDetails(callId: string) {
    return this.retryRequest(async () => {
      const response = await api.get(`/api/call/history/${callId}`);
      return response.data;
    });
  }

  async updateCallHistory(callId: string, data: CallHistoryUpdatePayload) {
    return this.retryRequest(async () => {
      const response = await api.post<void>(`/api/call/history/${callId}`, data);
      return response.data;
    });
  }

  createCallUpdatesWebSocket(callHistoryId: string): WebSocket {
    const wsProtocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
    const wsUrl = this.baseURL.replace(/^https?:\/\//, '');
    return new WebSocket(`${wsProtocol}://${wsUrl}/api/call/ws/call-updates/${callHistoryId}`);
  }

  createTextChatWebSocket(): WebSocket {
    const wsProtocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
    const wsUrl = this.baseURL.replace(/^https?:\/\//, '');
    return new WebSocket(`${wsProtocol}://${wsUrl}/api/call/ws/text-chat`);
  }

  // Performance API
  async getLivePerformance() {
    return this.retryRequest(async () => {
      const response = await api.get<PerformanceMetric[]>('/api/performance/live');
      return response.data;
    });
  }

  // Agent API
  async getAllAgents(params = {}) {
    return this.retryRequest(async () => {
      const response = await api.get<Agent[]>('/api/agents/', { params });
      return response.data;
    });
  }

  async getAgentById(id: number) {
    return this.retryRequest(async () => {
      const response = await api.get<Agent>(`/api/agents/${id}`);
      return response.data;
    });
  }

  async createAgentApi(data: Partial<Agent>) {
    return this.retryRequest(async () => {
      const response = await api.post<Agent>('/api/agents', data);
      return response.data;
    });
  }

  async updateAgentApi(id: number, data: Partial<Agent>) {
    return this.retryRequest(async () => {
      const response = await api.put<Agent>(`/api/agents/${id}`, data);
      return response.data;
    });
  }

  async deleteAgentApi(id: number) {
    return this.retryRequest(async () => {
      const response = await api.delete<void>(`/api/agents/${id}`);
      return response.data;
    });
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Export types for better TypeScript support
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
}

export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

// Utility functions
export const apiUtils = {
  // Format error messages for user display
  formatError: (error: any): string => {
    if (error.response?.data?.detail) {
      return error.response.data.detail;
    }
    if (error.message) {
      return error.message;
    }
    return 'An unexpected error occurred';
  },

  // Check if error is retryable
  isRetryableError: (error: any): boolean => {
    return (
      !error.response ||
      error.response.status >= 500 ||
      error.code === 'NETWORK_ERROR' ||
      error.code === 'TIMEOUT'
    );
  },

  // Generate unique request ID for tracking
  generateRequestId: (): string => {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },
};

export default apiClient;
