import { api } from '@/lib/api';

export interface WebForm {
  id: number;
  web_form_id: string;
  title: string;
  description?: string;
  status: 'draft' | 'published' | 'paused' | 'archived';
  redirect_url?: string;
  total_views: number;
  total_submissions: number;
  completion_rate: number;
  created_at: string;
  updated_at: string;
  company_id: number;
  config?: Record<string, any>; // New field for all other configurations
  fields: WebFormField[];
}

export interface WebFormField {
  id: number;
  title: string;
  description?: string;
  field_type: string;
  is_required: boolean;
  order_index: number;
  options?: any[];
  validation_rules?: any;
  placeholder_text?: string;
  help_text?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  config?: Record<string, any>; // New field for all other configurations
}

export interface WebFormSubmission {
  id: number;
  submission_id: string;
  web_form_id: number;
  company_id: number;
  user_ip?: string;
  user_agent?: string;
  referrer_url?: string;
  contact_name?: string;
  contact_email?: string;
  contact_phone?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'spam';
  completion_percentage: number;
  started_at: string;
  completed_at?: string;
  time_to_complete?: number;
  is_spam: boolean;
  spam_score?: number;
  validation_errors?: any[];
  webhook_sent: boolean;
  webhook_response?: any;
  sms_sent: boolean;
  email_sent: boolean;
  conversation_id?: number;
  booking_request_id?: number;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  extra_metadata?: any;
  created_at: string;
  updated_at: string;
  responses: WebFormResponse[];
}

export interface WebFormResponse {
  id: number;
  submission_id: number;
  field_id: number;
  response_value?: string;
  response_data?: any;
  file_url?: string;
  file_name?: string;
  file_size?: number;
  file_type?: string;
  response_time?: number;
  is_prefilled: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateWebFormRequest {
  title: string;
  description?: string;
  redirect_url?: string;
  config?: Record<string, any>; // New field for all other configurations
  fields: CreateWebFormFieldRequest[];
}

export interface CreateWebFormFieldRequest {
  title: string;
  description?: string;
  field_type: string;
  is_required?: boolean;
  order_index: number;
  options?: any[];
  validation_rules?: any;
  placeholder_text?: string;
  help_text?: string;
  config?: Record<string, any>; // New field for all other configurations
}

export interface SubmitWebFormRequest {
  responses: {
    field_id: string;
    response_value?: string;
    response_data?: any;
  }[];
  user_ip?: string;
  user_agent?: string;
  referrer_url?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  extra_metadata?: any;
}

export interface WebFormListResponse {
  web_forms: WebForm[];
  total: number;
  page: number;
  per_page: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface WebFormSubmissionListResponse {
  submissions: WebFormSubmission[];
  total: number;
  page: number;
  per_page: number;
  has_next: boolean;
  has_prev: boolean;
}

// API Functions
export const webFormsApi = {
  // Forms
  async getForms(
    companyId: number,
    params?: {
      status?: string;
      search?: string;
      skip?: number;
      limit?: number;
    },
  ): Promise<WebFormListResponse> {
    const searchParams = new URLSearchParams({
      company_id: companyId.toString(),
      ...(params?.status && { status: params.status }),
      ...(params?.search && { search: params.search }),
      ...(params?.skip !== undefined && { skip: params.skip.toString() }),
      ...(params?.limit !== undefined && { limit: params.limit.toString() }),
    });

    const response = await api.get(`/web-forms/forms?${searchParams}`);
    return response.data;
  },

  async getForm(formId: number): Promise<WebForm> {
    const response = await api.get(`/web-forms/forms/${formId}`);
    return response.data;
  },

  async getFormByPublicId(webFormId: string): Promise<WebForm> {
    const response = await api.get(`/web-forms/public/${webFormId}`);
    return response.data;
  },

  async createForm(companyId: number, data: CreateWebFormRequest): Promise<WebForm> {
    const response = await api.post('/web-forms/forms', {
      ...data,
      company_id: companyId,
    });
    return response.data;
  },

  async updateForm(formId: number, data: Partial<CreateWebFormRequest>): Promise<WebForm> {
    const response = await api.put(`/web-forms/forms/${formId}`, data);
    return response.data;
  },

  async deleteForm(formId: number): Promise<void> {
    await api.delete(`/web-forms/forms/${formId}`);
  },

  async publishForm(formId: number): Promise<WebForm> {
    const response = await api.post(`/web-forms/forms/${formId}/publish`);
    return response.data;
  },

  async unpublishForm(formId: number): Promise<WebForm> {
    const response = await api.post(`/web-forms/forms/${formId}/unpublish`);
    return response.data;
  },

  // Submissions
  async getSubmissions(
    companyId: number,
    params?: {
      form_id?: number;
      status?: string;
      search?: string;
      skip?: number;
      limit?: number;
    },
  ): Promise<WebFormSubmissionListResponse> {
    const searchParams = new URLSearchParams({
      company_id: companyId.toString(),
      ...(params?.form_id && { form_id: params.form_id.toString() }),
      ...(params?.status && { status: params.status }),
      ...(params?.search && { search: params.search }),
      ...(params?.skip !== undefined && { skip: params.skip.toString() }),
      ...(params?.limit !== undefined && { limit: params.limit.toString() }),
    });

    const response = await api.get(`/web-forms/submissions?${searchParams}`);
    return response.data;
  },

  async getSubmission(submissionId: number): Promise<WebFormSubmission> {
    const response = await api.get(`/web-forms/submissions/${submissionId}`);
    return response.data;
  },

  async submitForm(webFormId: string, data: SubmitWebFormRequest): Promise<WebFormSubmission> {
    const response = await api.post(`/web-forms/public/${webFormId}/submit`, data);
    return response.data;
  },

  async updateSubmissionStatus(submissionId: number, status: string): Promise<WebFormSubmission> {
    const response = await api.patch(`/web-forms/submissions/${submissionId}/status`, {
      status,
    });
    return response.data;
  },

  // Analytics
  async getFormAnalytics(
    formId: number,
    params?: {
      start_date?: string;
      end_date?: string;
      period?: string;
    },
  ): Promise<any> {
    const searchParams = new URLSearchParams({
      ...(params?.start_date && { start_date: params.start_date }),
      ...(params?.end_date && { end_date: params.end_date }),
      ...(params?.period && { period: params.period }),
    });

    const response = await api.get(`/web-forms/forms/${formId}/analytics?${searchParams}`);
    return response.data;
  },

  // Templates
  async getTemplates(params?: {
    category?: string;
    search?: string;
    skip?: number;
    limit?: number;
  }): Promise<any> {
    const searchParams = new URLSearchParams({
      ...(params?.category && { category: params.category }),
      ...(params?.search && { search: params.search }),
      ...(params?.skip !== undefined && { skip: params.skip.toString() }),
      ...(params?.limit !== undefined && { limit: params.limit.toString() }),
    });

    const response = await api.get(`/web-forms/templates?${searchParams}`);
    return response.data;
  },

  async getTemplate(templateId: number): Promise<any> {
    const response = await api.get(`/web-forms/templates/${templateId}`);
    return response.data;
  },
};
