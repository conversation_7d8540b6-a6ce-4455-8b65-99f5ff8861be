import { WebForm } from '@/types/forms'; // Assuming you'll define this type

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'; // Assuming backend runs on 8000

export const fetchWebForms = async (): Promise<WebForm[]> => {
  const response = await fetch(`${API_BASE_URL}/forms`);
  if (!response.ok) {
    throw new Error('Failed to fetch web forms');
  }
  return response.json();
};

export const fetchWebFormById = async (formId: string): Promise<WebForm> => {
  const response = await fetch(`${API_BASE_URL}/forms/${formId}`);
  if (!response.ok) {
    throw new Error(`Failed to fetch web form with ID ${formId}`);
  }
  return response.json();
};

export const saveWebForm = async (form: WebForm): Promise<WebForm> => {
  const response = await fetch(`${API_BASE_URL}/forms/${form.id}`, {
    method: 'PUT', // Or POST if creating a new one
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(form),
  });
  if (!response.ok) {
    throw new Error('Failed to save web form');
  }
  return response.json();
};
