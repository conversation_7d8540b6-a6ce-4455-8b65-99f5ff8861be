import { errorHandler, ErrorType } from '../error/errorHandler';
import { apiClient } from '../api/apiClient';

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
  execution_id?: string;
  node_id?: string;
}

export interface ExecutionUpdate {
  type: 'node_started' | 'node_completed' | 'node_failed' | 'execution_completed' | 'execution_failed' | 'error';
  execution_id: string;
  node_id?: string;
  node_name?: string;
  message?: string;
  result?: any;
  error?: string;
  duration_ms?: number;
  timestamp: string;
}

export interface WebSocketConfig {
  reconnectAttempts: number;
  reconnectDelay: number;
  heartbeatInterval: number;
  maxMessageSize: number;
}

export type WebSocketEventHandler = (message: WebSocketMessage) => void;
export type ExecutionEventHandler = (update: ExecutionUpdate) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private eventHandlers: Map<string, WebSocketEventHandler[]> = new Map();
  private executionHandlers: Map<string, ExecutionEventHandler[]> = new Map();
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private reconnectAttempts: number = 0;
  private isConnecting: boolean = false;
  private isConnected: boolean = false;
  private lastPingTime: number = 0;
  private connectionId: string = '';

  constructor(config?: Partial<WebSocketConfig>) {
    this.config = {
      reconnectAttempts: 5,
      reconnectDelay: 3000,
      heartbeatInterval: 30000,
      maxMessageSize: 1024 * 1024, // 1MB
      ...config
    };
  }

  // Connection Management
  connect(executionId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnecting || this.isConnected) {
        resolve();
        return;
      }

      this.isConnecting = true;
      this.connectionId = executionId;

      try {
        this.ws = apiClient.createExecutionWebSocket(executionId); // Using apiClient

        this.ws.onopen = () => {
          this.isConnecting = false;
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          
          console.log(`WebSocket connected for execution: ${executionId}`);
          this.emit('connected', { execution_id: executionId });
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event);
        };

        this.ws.onclose = (event) => {
          this.handleClose(event);
        };

        this.ws.onerror = (event) => {
          this.handleError(event);
          if (this.isConnecting) {
            reject(new Error('WebSocket connection failed'));
          }
        };

        // Connection timeout
        setTimeout(() => {
          if (this.isConnecting) {
            this.isConnecting = false;
            this.ws?.close();
            reject(new Error('WebSocket connection timeout'));
          }
        }, 10000);

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  disconnect(): void {
    this.stopHeartbeat();
    this.stopReconnect();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.isConnected = false;
    this.isConnecting = false;
    this.connectionId = '';
  }

  // Message Handling
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      // Validate message size
      if (event.data.length > this.config.maxMessageSize) {
        console.warn('Received message exceeds maximum size limit');
        return;
      }

      // Handle heartbeat
      if (message.type === 'pong') {
        this.handlePong();
        return;
      }

      // Emit to general handlers
      this.emit(message.type, message);

      // Handle execution-specific messages
      if (message.execution_id) {
        this.emitExecution(message.execution_id, this.convertToExecutionUpdate(message));
      }

    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
      errorHandler.handleError(
        errorHandler.createError(
          ErrorType.UNKNOWN,
          'Failed to parse WebSocket message',
          'WS_PARSE_ERROR',
          { message: event.data, error }
        ),
        { component: 'websocket', action: 'parse_message' }
      );
    }
  }

  private convertToExecutionUpdate(message: WebSocketMessage): ExecutionUpdate {
    return {
      type: message.type as ExecutionUpdate['type'],
      execution_id: message.execution_id!,
      node_id: message.node_id,
      node_name: message.data?.node_name,
      message: message.data?.message,
      result: message.data?.result,
      error: message.data?.error,
      duration_ms: message.data?.duration_ms,
      timestamp: message.timestamp
    };
  }

  private handleClose(event: CloseEvent): void {
    this.isConnected = false;
    this.stopHeartbeat();

    console.log(`WebSocket closed: ${event.code} - ${event.reason}`);
    this.emit('disconnected', { code: event.code, reason: event.reason });

    // Attempt reconnection if not a clean close
    if (event.code !== 1000 && this.reconnectAttempts < this.config.reconnectAttempts) {
      this.attemptReconnect();
    }
  }

  private handleError(event: Event): void {
    console.error('WebSocket error:', event);
    
    errorHandler.handleError(
      errorHandler.createError(
        ErrorType.NETWORK,
        'WebSocket connection error',
        'WS_CONNECTION_ERROR',
        event
      ),
      { component: 'websocket', action: 'connection_error' }
    );

    this.emit('error', { error: event });
  }

  // Reconnection Logic
  private attemptReconnect(): void {
    if (this.reconnectTimer || !this.connectionId) {
      return;
    }

    this.reconnectAttempts++;
    const delay = this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.config.reconnectAttempts}) in ${delay}ms`);

    this.reconnectTimer = setTimeout(() => {
      this.reconnectTimer = null;
      this.connect(this.connectionId).catch((error) => {
        console.error('Reconnection failed:', error);
        if (this.reconnectAttempts < this.config.reconnectAttempts) {
          this.attemptReconnect();
        } else {
          this.emit('reconnect_failed', { attempts: this.reconnectAttempts });
        }
      });
    }, delay);
  }

  private stopReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    this.reconnectAttempts = 0;
  }

  // Heartbeat
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected && this.ws) {
        this.lastPingTime = Date.now();
        this.send({ type: 'ping', timestamp: new Date().toISOString() });
      }
    }, this.config.heartbeatInterval);
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  private handlePong(): void {
    const latency = Date.now() - this.lastPingTime;
    this.emit('latency', { latency });
  }

  // Message Sending
  send(message: any): boolean {
    if (!this.isConnected || !this.ws) {
      console.warn('Cannot send message: WebSocket not connected');
      return false;
    }

    try {
      const messageStr = JSON.stringify({
        ...message,
        timestamp: message.timestamp || new Date().toISOString()
      });

      if (messageStr.length > this.config.maxMessageSize) {
        console.error('Message exceeds maximum size limit');
        return false;
      }

      this.ws.send(messageStr);
      return true;
    } catch (error) {
      console.error('Failed to send WebSocket message:', error);
      return false;
    }
  }

  // Event Handling
  on(event: string, handler: WebSocketEventHandler): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
  }

  off(event: string, handler: WebSocketEventHandler): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  onExecution(executionId: string, handler: ExecutionEventHandler): void {
    if (!this.executionHandlers.has(executionId)) {
      this.executionHandlers.set(executionId, []);
    }
    this.executionHandlers.get(executionId)!.push(handler);
  }

  offExecution(executionId: string, handler: ExecutionEventHandler): void {
    const handlers = this.executionHandlers.get(executionId);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private emit(event: string, data: any): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler({ type: event, data, timestamp: new Date().toISOString() });
        } catch (error) {
          console.error('Error in WebSocket event handler:', error);
        }
      });
    }
  }

  private emitExecution(executionId: string, update: ExecutionUpdate): void {
    const handlers = this.executionHandlers.get(executionId);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(update);
        } catch (error) {
          console.error('Error in execution event handler:', error);
        }
      });
    }
  }

  // Status
  getConnectionStatus(): {
    connected: boolean;
    connecting: boolean;
    reconnectAttempts: number;
    executionId: string;
  } {
    return {
      connected: this.isConnected,
      connecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      executionId: this.connectionId
    };
  }
}

// Create singleton instance
export const websocketService = new WebSocketService();

// React hook for WebSocket
export const useWebSocket = () => {
  return {
    connect: websocketService.connect.bind(websocketService),
    disconnect: websocketService.disconnect.bind(websocketService),
    send: websocketService.send.bind(websocketService),
    on: websocketService.on.bind(websocketService),
    off: websocketService.off.bind(websocketService),
    onExecution: websocketService.onExecution.bind(websocketService),
    offExecution: websocketService.offExecution.bind(websocketService),
    getConnectionStatus: websocketService.getConnectionStatus.bind(websocketService)
  };
};

export default websocketService;
