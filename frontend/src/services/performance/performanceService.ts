// frontend/src/services/performance/performanceService.ts
import { BaseApiClient, ApiResponse } from '@/lib/api';

export interface PerformanceMetric {
  id: number;
  metric_name: string;
  metric_value: number;
  metric_type: 'counter' | 'gauge' | 'histogram';
  timestamp: string;
  labels?: Record<string, string>;
}

export interface SystemMetrics {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  network_io: {
    bytes_sent: number;
    bytes_received: number;
  };
  active_connections: number;
  response_time_avg: number;
  error_rate: number;
  timestamp: string;
}

export interface AgentPerformance {
  agent_id: number;
  agent_name: string;
  total_calls: number;
  successful_calls: number;
  failed_calls: number;
  average_response_time: number;
  success_rate: number;
  last_24h_calls: number;
  peak_concurrent_calls: number;
}

export interface CallMetrics {
  total_calls: number;
  successful_calls: number;
  failed_calls: number;
  average_duration: number;
  median_duration: number;
  p95_duration: number;
  calls_per_hour: Array<{ hour: string; count: number }>;
  success_rate_trend: Array<{ timestamp: string; rate: number }>;
}

class PerformanceApiClient extends BaseApiClient {
  constructor() {
    super('/api/performance');
  }

  async getLiveMetrics(): Promise<ApiResponse<PerformanceMetric[]>> {
    return this.get<PerformanceMetric[]>('/live');
  }

  async getSystemMetrics(timeRange?: string): Promise<ApiResponse<SystemMetrics[]>> {
    const params = timeRange ? { time_range: timeRange } : undefined;
    return this.get<SystemMetrics[]>('/system', params);
  }

  async getAgentPerformance(agentId?: number): Promise<ApiResponse<AgentPerformance[]>> {
    const endpoint = agentId ? `/agents/${agentId}` : '/agents';
    return this.get<AgentPerformance[]>(endpoint);
  }

  async getCallMetrics(timeRange?: string): Promise<ApiResponse<CallMetrics>> {
    const params = timeRange ? { time_range: timeRange } : undefined;
    return this.get<CallMetrics>('/calls', params);
  }

  async getMetricHistory(metricName: string, timeRange?: string): Promise<ApiResponse<PerformanceMetric[]>> {
    const params = {
      metric_name: metricName,
      ...(timeRange && { time_range: timeRange })
    };
    return this.get<PerformanceMetric[]>('/history', params);
  }
}

const performanceApiClient = new PerformanceApiClient();

export const performanceService = {
  async getLiveMetrics(): Promise<PerformanceMetric[]> {
    const response = await performanceApiClient.getLiveMetrics();
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch live metrics');
    }
    return response.data || [];
  },

  async getSystemMetrics(timeRange: string = '1h'): Promise<SystemMetrics[]> {
    const response = await performanceApiClient.getSystemMetrics(timeRange);
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch system metrics');
    }
    return response.data || [];
  },

  async getAgentPerformance(agentId?: number): Promise<AgentPerformance[]> {
    const response = await performanceApiClient.getAgentPerformance(agentId);
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch agent performance');
    }
    return response.data || [];
  },

  async getCallMetrics(timeRange: string = '24h'): Promise<CallMetrics> {
    const response = await performanceApiClient.getCallMetrics(timeRange);
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch call metrics');
    }
    return response.data!;
  },

  async getMetricHistory(metricName: string, timeRange: string = '24h'): Promise<PerformanceMetric[]> {
    const response = await performanceApiClient.getMetricHistory(metricName, timeRange);
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch metric history');
    }
    return response.data || [];
  },

  // Convenience methods for common metrics
  async getCPUUsage(timeRange: string = '1h'): Promise<PerformanceMetric[]> {
    return this.getMetricHistory('cpu_usage', timeRange);
  },

  async getMemoryUsage(timeRange: string = '1h'): Promise<PerformanceMetric[]> {
    return this.getMetricHistory('memory_usage', timeRange);
  },

  async getResponseTimes(timeRange: string = '1h'): Promise<PerformanceMetric[]> {
    return this.getMetricHistory('response_time', timeRange);
  },

  async getErrorRates(timeRange: string = '1h'): Promise<PerformanceMetric[]> {
    return this.getMetricHistory('error_rate', timeRange);
  },
};

export default performanceService;
