/**
 * Integration tests for the campaigns feature.
 * 
 * Tests the complete user workflow from campaign creation to analytics viewing.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import CreateCampaignPage from '@/app/(app)/campaigns/create/page';
import CampaignsPage from '@/app/(app)/campaigns/page';
import AnalyticsPage from '@/app/(app)/analytics/page';
import { AuthProvider } from '@/contexts/AuthContext';

// Mock API responses
const mockApiResponses = {
  businessProfiles: [
    {
      id: 1,
      business_name: 'Test Wellness Center',
      business_type: 'massage_therapy',
      city: 'Zurich',
      services_offered: ['Deep tissue massage', 'Swedish massage']
    }
  ],
  aiGeneratedContent: {
    ad_copy_variations: [
      'Book your massage therapy appointment in Zurich today!',
      'Experience premium wellness services in Zurich.',
      'Transform your wellbeing with expert massage therapy.'
    ],
    headlines: ['Massage Therapy Zurich', 'Book Wellness Center', 'Expert Massage Care'],
    descriptions: [
      'Professional massage therapy services in Zurich.',
      'Trusted wellness care. Convenient online booking.',
      'Expert massage treatments. Schedule your visit now.'
    ],
    targeting_suggestions: {
      location: { countries: ['CH'], regions: ['Zurich'] },
      age_range: { min: 25, max: 65 },
      interests: ['Massage therapy', 'Wellness', 'Health']
    },
    estimated_performance: {
      estimated_clicks: 200,
      estimated_impressions: 10000,
      estimated_conversions: 10,
      estimated_cost_per_conversion: 50
    }
  },
  campaigns: [
    {
      id: 1,
      name: 'Test Campaign',
      status: 'active',
      budget: 1000,
      spent: 450,
      duration_days: 30,
      primary_goal: 'appointments',
      business_profile: {
        business_name: 'Test Wellness Center',
        business_type: 'massage_therapy'
      },
      analytics: {
        impressions: 15420,
        clicks: 234,
        appointments: 12,
        cost_per_appointment: 37.50,
        roi_percentage: 180
      },
      created_at: '2024-01-15T10:00:00Z'
    }
  ],
  analyticsData: {
    overview: {
      total_spend: 2750,
      total_appointments: 68,
      total_revenue: 12240,
      avg_cost_per_appointment: 40.44,
      total_roi: 345,
      total_roas: 4.45,
      active_campaigns: 3,
      total_impressions: 45680,
      total_clicks: 892,
      avg_ctr: 1.95
    },
    trends: {
      daily_data: [
        { date: '2024-01-01', spend: 85, appointments: 2, revenue: 360, impressions: 1200, clicks: 18 },
        { date: '2024-01-02', spend: 92, appointments: 3, revenue: 540, impressions: 1350, clicks: 21 }
      ]
    },
    campaign_performance: [
      { campaign_name: 'Summer Wellness Campaign', spend: 1200, appointments: 32, roi: 280, status: 'active' }
    ],
    business_insights: {
      top_performing_services: [
        { service: 'Deep Tissue Massage', appointments: 28, revenue: 5040 }
      ],
      peak_hours: [
        { hour: '14:00', bookings: 18 }
      ],
      customer_demographics: [
        { age_group: '25-34', percentage: 35, color: '#3b82f6' }
      ]
    }
  }
};

// Mock fetch globally
global.fetch = jest.fn();

const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  const mockUser = {
    id: 1,
    email: '<EMAIL>',
    full_name: 'Dr. Test User'
  };

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <AuthProvider>
          <div data-testid="mock-user" data-user={JSON.stringify(mockUser)}>
            {children}
          </div>
        </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Campaign Creation Integration', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });

  test('complete campaign creation workflow', async () => {
    const user = userEvent.setup();

    // Mock API calls
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.businessProfiles
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.aiGeneratedContent
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ id: 1, name: 'Test Campaign', status: 'draft' })
      } as Response);

    render(
      <TestWrapper>
        <CreateCampaignPage />
      </TestWrapper>
    );

    // Step 1: Fill campaign basics
    await waitFor(() => {
      expect(screen.getByText('Create New Campaign')).toBeInTheDocument();
    });

    // Fill campaign name
    const nameInput = screen.getByLabelText(/campaign name/i);
    await user.type(nameInput, 'Test Wellness Campaign');

    // Fill budget
    const budgetInput = screen.getByLabelText(/total budget/i);
    await user.type(budgetInput, '1000');

    // Fill duration
    const durationInput = screen.getByLabelText(/duration/i);
    await user.type(durationInput, '30');

    // Select goal
    const appointmentsGoal = screen.getByLabelText(/get more appointments/i);
    await user.click(appointmentsGoal);

    // Go to next step
    const nextButton = screen.getByRole('button', { name: /generate ai content/i });
    await user.click(nextButton);

    // Step 2: AI Content Generation
    await waitFor(() => {
      expect(screen.getByText(/ai is creating your ads/i)).toBeInTheDocument();
    });

    // Wait for AI content to be generated
    await waitFor(() => {
      expect(screen.getByText(/ai content generated/i)).toBeInTheDocument();
    }, { timeout: 5000 });

    // Verify AI content is displayed
    expect(screen.getByText('Book your massage therapy appointment in Zurich today!')).toBeInTheDocument();
    expect(screen.getByText('Massage Therapy Zurich')).toBeInTheDocument();

    // Go to review step
    const reviewButton = screen.getByRole('button', { name: /review campaign/i });
    await user.click(reviewButton);

    // Step 3: Review and Create
    await waitFor(() => {
      expect(screen.getByText(/ready to launch/i)).toBeInTheDocument();
    });

    // Verify campaign summary
    expect(screen.getByText('Test Wellness Campaign')).toBeInTheDocument();
    expect(screen.getByText('CHF 1000')).toBeInTheDocument();
    expect(screen.getByText('30 days')).toBeInTheDocument();

    // Create campaign
    const createButton = screen.getByRole('button', { name: /create campaign/i });
    await user.click(createButton);

    // Verify API calls were made
    expect(mockFetch).toHaveBeenCalledTimes(3);
    
    // Verify business profiles fetch
    expect(mockFetch).toHaveBeenNthCalledWith(1, expect.stringContaining('/api/campaigns/business-profiles'));
    
    // Verify AI generation call
    expect(mockFetch).toHaveBeenNthCalledWith(2, expect.stringContaining('/api/campaigns/ai/generate-ad'));
    
    // Verify campaign creation call
    expect(mockFetch).toHaveBeenNthCalledWith(3, expect.stringContaining('/api/campaigns/'));
  });

  test('handles validation errors gracefully', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <CreateCampaignPage />
      </TestWrapper>
    );

    // Try to proceed without filling required fields
    const nextButton = screen.getByRole('button', { name: /generate ai content/i });
    await user.click(nextButton);

    // Should show validation errors
    await waitFor(() => {
      expect(screen.getByText(/campaign name must be at least 3 characters/i)).toBeInTheDocument();
    });
  });
});

describe('Campaigns Dashboard Integration', () => {
  test('displays campaigns with correct data', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockApiResponses.campaigns
    } as Response);

    render(
      <TestWrapper>
        <CampaignsPage />
      </TestWrapper>
    );

    // Wait for campaigns to load
    await waitFor(() => {
      expect(screen.getByText('Test Campaign')).toBeInTheDocument();
    });

    // Verify campaign data is displayed
    expect(screen.getByText('CHF 1,000')).toBeInTheDocument(); // Budget
    expect(screen.getByText('CHF 450')).toBeInTheDocument(); // Spent
    expect(screen.getByText('12')).toBeInTheDocument(); // Appointments
    expect(screen.getByText('180%')).toBeInTheDocument(); // ROI

    // Verify status badge
    expect(screen.getByText('Active')).toBeInTheDocument();
  });

  test('handles campaign actions', async () => {
    const user = userEvent.setup();

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockApiResponses.campaigns
    } as Response);

    render(
      <TestWrapper>
        <CampaignsPage />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Campaign')).toBeInTheDocument();
    });

    // Click on campaign actions menu
    const actionsButton = screen.getByRole('button', { name: /more options/i });
    await user.click(actionsButton);

    // Verify action menu items
    expect(screen.getByText('View Details')).toBeInTheDocument();
    expect(screen.getByText('Edit Campaign')).toBeInTheDocument();
    expect(screen.getByText('Pause Campaign')).toBeInTheDocument();
  });
});

describe('Analytics Dashboard Integration', () => {
  test('displays analytics data correctly', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockApiResponses.analyticsData
    } as Response);

    render(
      <TestWrapper>
        <AnalyticsPage />
      </TestWrapper>
    );

    // Wait for analytics to load
    await waitFor(() => {
      expect(screen.getByText('Analytics & ROI Dashboard')).toBeInTheDocument();
    });

    // Verify key metrics
    expect(screen.getByText('345%')).toBeInTheDocument(); // Total ROI
    expect(screen.getByText('CHF 12,240')).toBeInTheDocument(); // Total Revenue
    expect(screen.getByText('68')).toBeInTheDocument(); // Total Appointments
    expect(screen.getByText('CHF 40')).toBeInTheDocument(); // Cost per Appointment

    // Verify secondary metrics
    expect(screen.getByText('4.45x')).toBeInTheDocument(); // ROAS
    expect(screen.getByText('45,680')).toBeInTheDocument(); // Total Impressions
    expect(screen.getByText('892')).toBeInTheDocument(); // Total Clicks
  });

  test('handles metric selection changes', async () => {
    const user = userEvent.setup();

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockApiResponses.analyticsData
    } as Response);

    render(
      <TestWrapper>
        <AnalyticsPage />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Performance Trends')).toBeInTheDocument();
    });

    // Find and click the metric selector
    const metricSelector = screen.getByRole('combobox');
    await user.click(metricSelector);

    // Select revenue metric
    const revenueOption = screen.getByText('Revenue');
    await user.click(revenueOption);

    // Chart should update (we can't easily test chart rendering, but we can verify the selector changed)
    expect(metricSelector).toHaveTextContent('Revenue');
  });
});

describe('Error Handling Integration', () => {
  test('handles API errors gracefully', async () => {
    mockFetch.mockRejectedValueOnce(new Error('Network error'));

    render(
      <TestWrapper>
        <CampaignsPage />
      </TestWrapper>
    );

    // Should show error state or loading state
    await waitFor(() => {
      expect(screen.getByText(/loading campaigns/i)).toBeInTheDocument();
    });
  });

  test('handles authentication errors', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 401,
      json: async () => ({ detail: 'Unauthorized' })
    } as Response);

    render(
      <TestWrapper>
        <CampaignsPage />
      </TestWrapper>
    );

    // Should handle unauthorized access
    await waitFor(() => {
      // This would typically redirect to login or show an error message
      expect(mockFetch).toHaveBeenCalled();
    });
  });
});

describe('Performance Integration', () => {
  test('handles large datasets efficiently', async () => {
    // Create large mock dataset
    const largeCampaignList = Array.from({ length: 100 }, (_, i) => ({
      ...mockApiResponses.campaigns[0],
      id: i + 1,
      name: `Campaign ${i + 1}`
    }));

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => largeCampaignList
    } as Response);

    const startTime = performance.now();

    render(
      <TestWrapper>
        <CampaignsPage />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Campaign 1')).toBeInTheDocument();
    });

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // Should render within reasonable time (less than 2 seconds)
    expect(renderTime).toBeLessThan(2000);
  });
});

describe('Accessibility Integration', () => {
  test('campaign creation form is accessible', async () => {
    render(
      <TestWrapper>
        <CreateCampaignPage />
      </TestWrapper>
    );

    // Check for proper form labels
    expect(screen.getByLabelText(/campaign name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/total budget/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/duration/i)).toBeInTheDocument();

    // Check for proper heading structure
    expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();

    // Check for proper button roles
    expect(screen.getByRole('button', { name: /generate ai content/i })).toBeInTheDocument();
  });

  test('analytics dashboard is accessible', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockApiResponses.analyticsData
    } as Response);

    render(
      <TestWrapper>
        <AnalyticsPage />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
    });

    // Check for proper ARIA labels on interactive elements
    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    expect(refreshButton).toBeInTheDocument();

    const exportButton = screen.getByRole('button', { name: /export/i });
    expect(exportButton).toBeInTheDocument();
  });
});
