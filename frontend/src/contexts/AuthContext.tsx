'use client';

import React, { useState, useEffect, createContext, useContext } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { ErrorType } from '@/services/error/errorHandler';
import type { ReactNode } from 'react';
import { authService } from '@/services/auth/authService';

interface User {
  email: string;
  full_name: string;
  is_active: boolean;
}

interface AuthContextType {
  user: User | undefined;
  login: (email: string, password: string) => Promise<void>;
  register: (registrationData: any) => Promise<void>;
  logout: () => Promise<void>;
  loading: boolean;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const checkUser = async () => {
      try {
        const userData = await authService.getCurrentUser();
        setUser(userData);
      } catch (error: any) {
        setUser(undefined);
        if (error.type === ErrorType.AUTHENTICATION && pathname !== '/') {
          router.push('/login');
        }
      } finally {
        setLoading(false);
      }
    };

    checkUser();
  }, []);

  const login = async (email: string, password: string) => {
    const userData = await authService.login(email, password);
    setUser(userData);
  };

  const register = async (registrationData: any) => {
    await authService.register(
      registrationData.email,
      registrationData.password,
      registrationData.full_name,
      registrationData.business_profile,
    );
    await login(registrationData.email, registrationData.password);
  };

  const logout = async () => {
    await authService.logout();
    setUser(undefined);
  };

  const value = {
    user,
    login,
    register,
    logout,
    loading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
