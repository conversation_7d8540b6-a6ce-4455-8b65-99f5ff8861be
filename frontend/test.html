<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tailwind CSS Test</title>
    <style>
        /* CSS Variables */
        :root {
            --background: 0 0% 100%;
            --foreground: 221 41.7% 11.1%;
            --primary: 221.21 83.19% 53.33%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96.1%;
            --secondary-foreground: 222.2 47.4% 11.2%;
            --muted: 210 40% 96.1%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 271.48 81.33% 55.88%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 218 10.6% 64.9%;
            --input: 214.3 31.8% 91.4%;
            --ring: 221.2 83.2% 53.3%;
        }

        /* Test styles */
        .bg-primary {
            background-color: hsl(var(--primary));
        }
        .text-primary-foreground {
            color: hsl(var(--primary-foreground));
        }
        .bg-secondary {
            background-color: hsl(var(--secondary));
        }
        .text-secondary-foreground {
            color: hsl(var(--secondary-foreground));
        }
        .bg-accent {
            background-color: hsl(var(--accent));
        }
        .text-accent-foreground {
            color: hsl(var(--accent-foreground));
        }
        .bg-destructive {
            background-color: hsl(var(--destructive));
        }
        .text-destructive-foreground {
            color: hsl(var(--destructive-foreground));
        }

        /* Button styles */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            white-space: nowrap;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.15s;
            height: 36px;
            padding: 8px 16px;
            cursor: pointer;
            border: none;
        }

        .btn-primary {
            background-color: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
        }

        .btn-secondary {
            background-color: hsl(var(--secondary));
            color: hsl(var(--secondary-foreground));
        }

        .btn-accent {
            background-color: hsl(var(--accent));
            color: hsl(var(--accent-foreground));
        }

        .btn-destructive {
            background-color: hsl(var(--destructive));
            color: hsl(var(--destructive-foreground));
        }

        /* Test direct colors */
        .test-direct {
            background-color: #2563eb;
            color: #f8fafc;
        }
    </style>
</head>
<body style="padding: 20px; font-family: system-ui, sans-serif;">
    <h1>CSS Variables Test</h1>
    
    <h2>CSS Variable Buttons</h2>
    <div style="display: flex; gap: 10px; margin: 20px 0;">
        <button class="btn btn-primary">Primary Button</button>
        <button class="btn btn-secondary">Secondary Button</button>
        <button class="btn btn-accent">Accent Button</button>
        <button class="btn btn-destructive">Destructive Button</button>
    </div>

    <h2>Direct Color Button</h2>
    <div style="margin: 20px 0;">
        <button class="btn test-direct">Direct Color Button</button>
    </div>

    <h2>CSS Variable Values</h2>
    <div style="margin: 20px 0;">
        <p>Primary: <span style="background-color: hsl(var(--primary)); color: hsl(var(--primary-foreground)); padding: 4px 8px; border-radius: 4px;">hsl(var(--primary))</span></p>
        <p>Secondary: <span style="background-color: hsl(var(--secondary)); color: hsl(var(--secondary-foreground)); padding: 4px 8px; border-radius: 4px;">hsl(var(--secondary))</span></p>
        <p>Accent: <span style="background-color: hsl(var(--accent)); color: hsl(var(--accent-foreground)); padding: 4px 8px; border-radius: 4px;">hsl(var(--accent))</span></p>
    </div>

    <h2>Computed Values</h2>
    <div id="computed-values" style="margin: 20px 0;"></div>

    <script>
        // Check computed CSS variable values
        const root = document.documentElement;
        const computedStyle = getComputedStyle(root);
        
        const variables = [
            '--background',
            '--foreground', 
            '--primary',
            '--primary-foreground',
            '--secondary',
            '--secondary-foreground',
            '--accent',
            '--accent-foreground'
        ];

        const computedDiv = document.getElementById('computed-values');
        variables.forEach(variable => {
            const value = computedStyle.getPropertyValue(variable).trim();
            const p = document.createElement('p');
            p.innerHTML = `<strong>${variable}:</strong> ${value || 'undefined'}`;
            computedDiv.appendChild(p);
        });
    </script>
</body>
</html>
