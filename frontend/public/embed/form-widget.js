/**
 * HomeService AI Form Widget
 * Embeddable form widget for websites
 */

(function() {
  'use strict';

  // Get form configuration from script tag
  const scriptTag = document.currentScript;
  const formId = scriptTag.getAttribute('data-form-id');
  const companyId = scriptTag.getAttribute('data-company-id');
  const containerId = scriptTag.getAttribute('data-container') || 'homeservice-form-container';

  if (!formId || !companyId) {
    console.error('HomeService Form Widget: Missing required attributes data-form-id or data-company-id');
    return;
  }

  // Widget state
  let formConfig = null;
  let isSubmitting = false;
  let isSubmitted = false;

  // Load form configuration
  async function loadFormConfig() {
    try {
      const response = await fetch(`/api/forms/${formId}/config`);
      if (!response.ok) {
        throw new Error('Failed to load form configuration');
      }
      formConfig = await response.json();
      renderForm();
    } catch (error) {
      console.error('Form Widget error:', error);
      renderError('Failed to load form. Please try again later.');
    }
  }

  // Render form
  function renderForm() {
    const container = document.getElementById(containerId);
    if (!container) {
      console.error('HomeService Form Widget: Container not found:', containerId);
      return;
    }

    const formHTML = `
      <div class="homeservice-form-widget" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
        <style>
          .homeservice-form-widget * {
            box-sizing: border-box;
          }
          .homeservice-form-widget .form-field {
            margin-bottom: 1rem;
          }
          .homeservice-form-widget .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
          }
          .homeservice-form-widget .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            font-size: 1rem;
            transition: border-color 0.2s;
          }
          .homeservice-form-widget .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
          .homeservice-form-widget .form-textarea {
            resize: vertical;
            min-height: 100px;
          }
          .homeservice-form-widget .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
          }
          .homeservice-form-widget .form-checkbox {
            width: auto;
            margin-right: 0.5rem;
          }
          .homeservice-form-widget .form-button {
            background-color: #3b82f6;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.375rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            width: 100%;
          }
          .homeservice-form-widget .form-button:hover {
            background-color: #2563eb;
          }
          .homeservice-form-widget .form-button:disabled {
            background-color: #9ca3af;
            cursor: not-allowed;
          }
          .homeservice-form-widget .form-error {
            color: #dc2626;
            font-size: 0.875rem;
            margin-top: 0.25rem;
          }
          .homeservice-form-widget .form-success {
            background-color: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
            padding: 1rem;
            border-radius: 0.375rem;
            text-align: center;
          }
          .homeservice-form-widget .required {
            color: #dc2626;
          }
          .homeservice-form-widget .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
          }
          @keyframes spin {
            to { transform: rotate(360deg); }
          }
        </style>
        
        <form id="homeservice-form" class="homeservice-form">
          ${formConfig.description ? `<p style="margin-bottom: 1.5rem; color: #6b7280;">${formConfig.description}</p>` : ''}
          
          ${formConfig.fields.map(field => renderField(field)).join('')}
          
          <div class="form-field">
            <button type="submit" class="form-button" id="submit-button">
              <span id="button-text">${formConfig.settings.submit_button_text}</span>
              <span id="button-loading" class="loading" style="display: none; margin-left: 0.5rem;"></span>
            </button>
          </div>
        </form>
        
        <div id="success-message" class="form-success" style="display: none;">
          ${formConfig.settings.success_message}
        </div>
      </div>
    `;

    container.innerHTML = formHTML;
    initializeForm();
  }

  // Render individual field
  function renderField(field) {
    const required = field.required ? '<span class="required">*</span>' : '';
    const fieldId = `field_${field.id}`;

    switch (field.type) {
      case 'text':
      case 'email':
      case 'phone':
      case 'number':
        return `
          <div class="form-field">
            <label for="${fieldId}" class="form-label">${field.label}${required}</label>
            <input 
              type="${field.type}" 
              id="${fieldId}" 
              name="${field.id}"
              class="form-input" 
              placeholder="${field.placeholder || ''}"
              ${field.required ? 'required' : ''}
            />
            <div class="form-error" id="${fieldId}_error"></div>
          </div>
        `;

      case 'textarea':
        return `
          <div class="form-field">
            <label for="${fieldId}" class="form-label">${field.label}${required}</label>
            <textarea 
              id="${fieldId}" 
              name="${field.id}"
              class="form-input form-textarea" 
              placeholder="${field.placeholder || ''}"
              ${field.required ? 'required' : ''}
            ></textarea>
            <div class="form-error" id="${fieldId}_error"></div>
          </div>
        `;

      case 'select':
        return `
          <div class="form-field">
            <label for="${fieldId}" class="form-label">${field.label}${required}</label>
            <select 
              id="${fieldId}" 
              name="${field.id}"
              class="form-input form-select" 
              ${field.required ? 'required' : ''}
            >
              <option value="">Select an option</option>
              ${(field.options || []).map(option => `<option value="${option}">${option}</option>`).join('')}
            </select>
            <div class="form-error" id="${fieldId}_error"></div>
          </div>
        `;

      case 'checkbox':
        return `
          <div class="form-field">
            <label style="display: flex; align-items: center;">
              <input 
                type="checkbox" 
                id="${fieldId}" 
                name="${field.id}"
                class="form-checkbox" 
                ${field.required ? 'required' : ''}
              />
              ${field.label}${required}
            </label>
            <div class="form-error" id="${fieldId}_error"></div>
          </div>
        `;

      case 'radio':
        return `
          <div class="form-field">
            <label class="form-label">${field.label}${required}</label>
            ${(field.options || []).map((option, index) => `
              <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                <input 
                  type="radio" 
                  name="${field.id}"
                  value="${option}"
                  class="form-checkbox" 
                  ${field.required ? 'required' : ''}
                />
                ${option}
              </label>
            `).join('')}
            <div class="form-error" id="${fieldId}_error"></div>
          </div>
        `;

      case 'date':
        return `
          <div class="form-field">
            <label for="${fieldId}" class="form-label">${field.label}${required}</label>
            <input 
              type="date" 
              id="${fieldId}" 
              name="${field.id}"
              class="form-input" 
              ${field.required ? 'required' : ''}
            />
            <div class="form-error" id="${fieldId}_error"></div>
          </div>
        `;

      default:
        return '';
    }
  }

  // Initialize form event listeners
  function initializeForm() {
    const form = document.getElementById('homeservice-form');
    form.addEventListener('submit', handleSubmit);
  }

  // Handle form submission
  async function handleSubmit(event) {
    event.preventDefault();
    
    if (isSubmitting || isSubmitted) {
      return;
    }

    // Clear previous errors
    clearErrors();

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Show loading state
    setLoadingState(true);
    isSubmitting = true;

    try {
      // Collect form data
      const formData = collectFormData();

      // Submit to API
      const response = await fetch(`/api/forms/${formId}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          company_id: companyId,
          form_data: formData,
          source: 'embed',
          url: window.location.href
        })
      });

      if (!response.ok) {
        throw new Error('Failed to submit form');
      }

      // Show success message
      showSuccess();
      isSubmitted = true;

    } catch (error) {
      console.error('Form submission error:', error);
      showError('Failed to submit form. Please try again.');
    } finally {
      setLoadingState(false);
      isSubmitting = false;
    }
  }

  // Validate form
  function validateForm() {
    let isValid = true;

    formConfig.fields.forEach(field => {
      if (field.required) {
        const element = document.querySelector(`[name="${field.id}"]`);
        const value = getFieldValue(element, field.type);

        if (!value || (Array.isArray(value) && value.length === 0)) {
          showFieldError(field.id, `${field.label} is required`);
          isValid = false;
        }
      }
    });

    return isValid;
  }

  // Get field value based on type
  function getFieldValue(element, type) {
    if (!element) return '';

    switch (type) {
      case 'checkbox':
        return element.checked;
      case 'radio':
        const radioGroup = document.querySelectorAll(`[name="${element.name}"]`);
        for (let radio of radioGroup) {
          if (radio.checked) return radio.value;
        }
        return '';
      default:
        return element.value.trim();
    }
  }

  // Collect form data
  function collectFormData() {
    const data = {};

    formConfig.fields.forEach(field => {
      const element = document.querySelector(`[name="${field.id}"]`);
      data[field.id] = getFieldValue(element, field.type);
    });

    return data;
  }

  // Show field error
  function showFieldError(fieldId, message) {
    const errorElement = document.getElementById(`field_${fieldId}_error`);
    if (errorElement) {
      errorElement.textContent = message;
    }
  }

  // Clear all errors
  function clearErrors() {
    const errorElements = document.querySelectorAll('.form-error');
    errorElements.forEach(element => {
      element.textContent = '';
    });
  }

  // Show general error
  function showError(message) {
    // You could add a general error display here
    alert(message);
  }

  // Show success message
  function showSuccess() {
    const form = document.getElementById('homeservice-form');
    const successMessage = document.getElementById('success-message');
    
    form.style.display = 'none';
    successMessage.style.display = 'block';
  }

  // Set loading state
  function setLoadingState(loading) {
    const submitButton = document.getElementById('submit-button');
    const buttonText = document.getElementById('button-text');
    const buttonLoading = document.getElementById('button-loading');

    if (loading) {
      submitButton.disabled = true;
      buttonText.textContent = 'Submitting...';
      buttonLoading.style.display = 'inline-block';
    } else {
      submitButton.disabled = false;
      buttonText.textContent = formConfig.settings.submit_button_text;
      buttonLoading.style.display = 'none';
    }
  }

  // Render error message
  function renderError(message) {
    const container = document.getElementById(containerId);
    if (container) {
      container.innerHTML = `
        <div style="color: #dc2626; padding: 1rem; border: 1px solid #fecaca; background-color: #fef2f2; border-radius: 0.375rem;">
          ${message}
        </div>
      `;
    }
  }

  // Initialize widget
  function init() {
    loadFormConfig();
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

})();
