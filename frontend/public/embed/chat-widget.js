/**
 * HomeService AI Chat Widget
 * Embeddable chat widget for websites
 */

(function() {
  'use strict';

  // Configuration
  const config = window.HomeServiceChat || {};
  const companyId = config.companyId || 'demo';
  const widgetConfig = config.config || {};

  // Default configuration
  const defaultConfig = {
    enabled: true,
    widget_title: 'Chat with us',
    welcome_message: 'Hi! How can we help you today?',
    placeholder_text: 'Type your message...',
    primary_color: '#3B82F6',
    position: 'bottom-right',
    size: 'medium',
    show_agent_avatar: true,
    show_typing_indicator: true,
    enable_file_upload: false,
    enable_emoji: false,
    business_hours_only: false,
    offline_message: 'We\'re currently offline. Please leave a message and we\'ll get back to you soon!'
  };

  // Merge configurations
  const finalConfig = { ...defaultConfig, ...widgetConfig };

  // Widget state
  let isOpen = false;
  let isInitialized = false;
  let messages = [];
  let isTyping = false;

  // Create widget HTML
  function createWidget() {
    const positionClasses = {
      'bottom-right': 'bottom-4 right-4',
      'bottom-left': 'bottom-4 left-4',
      'top-right': 'top-4 right-4',
      'top-left': 'top-4 left-4'
    };

    const sizeClasses = {
      'small': 'w-80 h-96',
      'medium': 'w-96 h-[500px]',
      'large': 'w-[420px] h-[600px]'
    };

    const widgetHTML = `
      <div id="homeservice-chat-widget" class="fixed ${positionClasses[finalConfig.position]} z-[9999] ${sizeClasses[finalConfig.size]} font-sans">
        <!-- Chat Button -->
        <div id="chat-button" class="absolute bottom-0 right-0 w-14 h-14 rounded-full shadow-lg cursor-pointer flex items-center justify-center text-white transition-transform hover:scale-110" style="background-color: ${finalConfig.primary_color}">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 12H16M8 8H16M8 16H13M7 4V2C7 1.44772 7.44772 1 8 1H16C16.5523 1 17 1.44772 17 2V4H19C20.1046 4 21 4.89543 21 6V18C21 19.1046 20.1046 20 19 20H17V22C17 22.5523 16.5523 23 16 23H8C7.44772 23 7 22.5523 7 22V20H5C3.89543 20 3 19.1046 3 18V6C3 4.89543 3.89543 4 5 4H7Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>

        <!-- Chat Window -->
        <div id="chat-window" class="bg-white rounded-lg shadow-2xl border hidden flex-col h-full">
          <!-- Header -->
          <div class="p-4 rounded-t-lg text-white flex items-center justify-between" style="background-color: ${finalConfig.primary_color}">
            <div class="flex items-center gap-3">
              ${finalConfig.show_agent_avatar ? `
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M12 8C14.21 8 16 9.79 16 12C16 14.21 14.21 16 12 16C9.79 16 8 14.21 8 12C8 9.79 9.79 8 12 8Z"/>
                  </svg>
                </div>
              ` : ''}
              <h3 class="font-semibold">${finalConfig.widget_title}</h3>
            </div>
            <button id="close-chat" class="text-white text-opacity-80 hover:text-opacity-100 text-xl">×</button>
          </div>
          
          <!-- Messages -->
          <div id="messages-container" class="flex-1 p-4 overflow-y-auto space-y-3">
            <div class="bg-gray-100 rounded-lg p-3 max-w-[80%] animate-fade-in">
              <p class="text-sm">${finalConfig.welcome_message}</p>
            </div>
          </div>
          
          <!-- Input -->
          <div class="border-t p-3">
            <div class="flex items-center gap-2">
              <input 
                id="message-input" 
                type="text" 
                placeholder="${finalConfig.placeholder_text}"
                class="flex-1 p-2 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button 
                id="send-button"
                class="p-2 rounded-lg text-white transition-colors"
                style="background-color: ${finalConfig.primary_color}"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Add CSS
    const style = document.createElement('style');
    style.textContent = `
      @keyframes fade-in {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }
      .animate-fade-in {
        animation: fade-in 0.3s ease-out;
      }
      #homeservice-chat-widget * {
        box-sizing: border-box;
      }
    `;
    document.head.appendChild(style);

    // Add widget to page
    document.body.insertAdjacentHTML('beforeend', widgetHTML);
  }

  // Initialize event listeners
  function initializeEventListeners() {
    const chatButton = document.getElementById('chat-button');
    const chatWindow = document.getElementById('chat-window');
    const closeButton = document.getElementById('close-chat');
    const messageInput = document.getElementById('message-input');
    const sendButton = document.getElementById('send-button');

    chatButton.addEventListener('click', toggleChat);
    closeButton.addEventListener('click', closeChat);
    sendButton.addEventListener('click', sendMessage);
    messageInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        sendMessage();
      }
    });
  }

  // Toggle chat window
  function toggleChat() {
    const chatButton = document.getElementById('chat-button');
    const chatWindow = document.getElementById('chat-window');
    
    if (isOpen) {
      closeChat();
    } else {
      openChat();
    }
  }

  // Open chat
  function openChat() {
    const chatButton = document.getElementById('chat-button');
    const chatWindow = document.getElementById('chat-window');
    
    chatButton.style.display = 'none';
    chatWindow.classList.remove('hidden');
    chatWindow.classList.add('flex');
    isOpen = true;

    // Focus on input
    setTimeout(() => {
      document.getElementById('message-input').focus();
    }, 100);
  }

  // Close chat
  function closeChat() {
    const chatButton = document.getElementById('chat-button');
    const chatWindow = document.getElementById('chat-window');
    
    chatWindow.classList.add('hidden');
    chatWindow.classList.remove('flex');
    chatButton.style.display = 'flex';
    isOpen = false;
  }

  // Send message
  function sendMessage() {
    const messageInput = document.getElementById('message-input');
    const message = messageInput.value.trim();
    
    if (!message) return;

    // Add user message
    addMessage(message, 'user');
    messageInput.value = '';

    // Show typing indicator
    if (finalConfig.show_typing_indicator) {
      showTypingIndicator();
    }

    // Send to API
    sendToAPI(message);
  }

  // Add message to chat
  function addMessage(message, sender) {
    const messagesContainer = document.getElementById('messages-container');
    const isUser = sender === 'user';
    
    const messageHTML = `
      <div class="flex ${isUser ? 'justify-end' : 'justify-start'} animate-fade-in">
        <div class="max-w-[80%] p-3 rounded-lg ${isUser ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-800'}">
          <p class="text-sm">${message}</p>
        </div>
      </div>
    `;
    
    messagesContainer.insertAdjacentHTML('beforeend', messageHTML);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
    
    messages.push({ message, sender, timestamp: new Date() });
  }

  // Show typing indicator
  function showTypingIndicator() {
    const messagesContainer = document.getElementById('messages-container');
    
    const typingHTML = `
      <div id="typing-indicator" class="flex justify-start animate-fade-in">
        <div class="bg-gray-100 rounded-lg p-3 max-w-[80%]">
          <div class="flex space-x-1">
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
          </div>
        </div>
      </div>
    `;
    
    messagesContainer.insertAdjacentHTML('beforeend', typingHTML);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
    isTyping = true;
  }

  // Hide typing indicator
  function hideTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
      typingIndicator.remove();
    }
    isTyping = false;
  }

  // Send message to API
  async function sendToAPI(message) {
    try {
      // TODO: Replace with actual API endpoint
      const response = await fetch(`/api/chat/${companyId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: message,
          conversation_id: getConversationId(),
          channel: 'web_chat'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const data = await response.json();
      
      // Hide typing indicator
      if (isTyping) {
        hideTypingIndicator();
      }
      
      // Add bot response
      if (data.response) {
        addMessage(data.response, 'bot');
      }
      
    } catch (error) {
      console.error('Chat API error:', error);
      
      // Hide typing indicator
      if (isTyping) {
        hideTypingIndicator();
      }
      
      // Show error message
      addMessage('Sorry, I\'m having trouble connecting right now. Please try again later.', 'bot');
    }
  }

  // Get or create conversation ID
  function getConversationId() {
    let conversationId = localStorage.getItem('homeservice_conversation_id');
    if (!conversationId) {
      conversationId = 'conv_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      localStorage.setItem('homeservice_conversation_id', conversationId);
    }
    return conversationId;
  }

  // Check business hours
  function isBusinessHours() {
    if (!finalConfig.business_hours_only) {
      return true;
    }
    
    // TODO: Implement business hours check
    const now = new Date();
    const hour = now.getHours();
    return hour >= 9 && hour < 17; // 9 AM to 5 PM
  }

  // Initialize widget
  function init() {
    if (isInitialized || !finalConfig.enabled) {
      return;
    }

    // Check if we're in business hours
    if (finalConfig.business_hours_only && !isBusinessHours()) {
      console.log('HomeService Chat Widget: Outside business hours');
      return;
    }

    createWidget();
    initializeEventListeners();
    isInitialized = true;
    
    console.log('HomeService Chat Widget initialized for company:', companyId);
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

  // Expose API for external control
  window.HomeServiceChatWidget = {
    open: openChat,
    close: closeChat,
    toggle: toggleChat,
    sendMessage: function(message) {
      if (isInitialized) {
        addMessage(message, 'user');
        sendToAPI(message);
      }
    },
    isOpen: function() {
      return isOpen;
    },
    getMessages: function() {
      return messages;
    }
  };

})();
