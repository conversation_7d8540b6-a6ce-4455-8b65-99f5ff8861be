PORT=8000
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

FRONTEND_URLS="http://localhost:5173,http://localhost:3000"

ELEVENLABS_API_KEY="***************************************************"
ELEVENLABS_VOICE_ID="your_elevenlabs_voice_id"
ELEVENLABS_AGENT_ID="agent_1101k2fahc02ed9txr66hvq5z41k"
ELEVENLABS_PHONE_NUMBER_ID="phnum_4101k2fayqz2e4ma0zzn9agv3fsq"

TWILIO_ACCOUNT_SID="**********************************"
TWILIO_AUTH_TOKEN="********************************"
TWILIO_PHONE_NUMBER="+***********"

ASSEMBLYAI_API_KEY="your_assemblyai_api_key"

BASE_URL="skilled-lemur-yearly.ngrok-free.app"

NOVA_API_KEY="7d626fde2092686b252e29509aeaa262b0a72e0d"
NOVA_VOICE_ID="aura-2-odysseus-en"

LLM_PROVIDER="gemini"
STT_PROVIDER="your_stt_provider"
TTS_PROVIDER="your_tts_provider"

OPENAI_API_KEY="********************************************************************************************************************************************************************"
GEMINI_API_KEY="AIzaSyAaCLV6BR4512dl4mkOVkowtF-JdfIbBzw"

SECRET_KEY="36a1a62618d19cd8d93da597a56e0a4bdc7eda88a4ba0f69695e8a25f7d390d5"

DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/test_db"

USE_LANGCHAIN_AGENT=False
ENVIRONMENT=dev

DIFY_API_KEY="your_actual_dify_api_key_here"
DIFY_BASE_URL="https://api.dify.ai/v1"
