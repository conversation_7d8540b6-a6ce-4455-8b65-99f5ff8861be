"""
Multi-Platform Integration Router

Handles OAuth flows and campaign management for Google Ads and LinkedIn Ads.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, Optional, List

from core.db.database import get_db
from modules.auth.service import get_current_user
from modules.users.models import User
from modules.campaigns import crud
from modules.campaigns.models import Campaign

from .google_ads.service import google_ads_service
from .linkedin_ads.service import linkedin_ads_service

router = APIRouter(prefix="/integrations", tags=["integrations"])


# Google Ads Integration Endpoints
@router.get("/google-ads/oauth-url")
async def get_google_ads_oauth_url(
    redirect_uri: str = Query(..., description="OAuth redirect URI"),
    current_user: User = Depends(get_current_user)
):
    """Get Google Ads OAuth authorization URL."""
    
    oauth_url = await google_ads_service.get_oauth_url(
        user_id=current_user.id,
        redirect_uri=redirect_uri
    )
    
    return {"oauth_url": oauth_url}


@router.post("/google-ads/oauth-callback")
async def google_ads_oauth_callback(
    code: str,
    redirect_uri: str,
    current_user: User = Depends(get_current_user)
):
    """Handle Google Ads OAuth callback and exchange code for token."""
    
    try:
        token_data = await google_ads_service.exchange_code_for_token(
            code=code,
            redirect_uri=redirect_uri
        )
        
        # In production, store token_data securely for the user
        # For now, return it to the frontend
        return {
            "success": True,
            "message": "Google Ads account connected successfully",
            "token_data": token_data
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to connect Google Ads account: {str(e)}"
        )


@router.post("/google-ads/create-campaign")
async def create_google_ads_campaign(
    campaign_id: int,
    access_token: str,
    customer_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a campaign on Google Ads platform."""
    
    # Get campaign
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    try:
        result = await google_ads_service.create_campaign_on_google(
            campaign=campaign,
            access_token=access_token,
            customer_id=customer_id
        )
        
        # Update campaign with Google Ads campaign ID
        await crud.update_campaign(
            db, 
            campaign_id, 
            {"external_campaign_id": result["campaign"]["id"]}
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create Google Ads campaign: {str(e)}"
        )


@router.get("/google-ads/campaign-performance/{campaign_id}")
async def get_google_ads_performance(
    campaign_id: int,
    access_token: str,
    customer_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get Google Ads campaign performance data."""
    
    # Get campaign
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    if not campaign.external_campaign_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Campaign not linked to Google Ads"
        )
    
    try:
        performance_data = await google_ads_service.get_campaign_performance(
            google_campaign_id=campaign.external_campaign_id,
            access_token=access_token,
            customer_id=customer_id
        )
        
        return performance_data
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch Google Ads performance: {str(e)}"
        )


@router.post("/google-ads/sync-data/{campaign_id}")
async def sync_google_ads_data(
    campaign_id: int,
    access_token: str,
    customer_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Sync Google Ads performance data to database."""
    
    # Get campaign
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    if not campaign.external_campaign_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Campaign not linked to Google Ads"
        )
    
    try:
        synced_data = await google_ads_service.sync_campaign_data(
            campaign_id=campaign_id,
            google_campaign_id=campaign.external_campaign_id,
            access_token=access_token,
            customer_id=customer_id,
            db=db
        )
        
        return {
            "success": True,
            "synced_records": len(synced_data),
            "message": f"Synced {len(synced_data)} data points from Google Ads"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to sync Google Ads data: {str(e)}"
        )


# LinkedIn Ads Integration Endpoints
@router.get("/linkedin-ads/oauth-url")
async def get_linkedin_ads_oauth_url(
    redirect_uri: str = Query(..., description="OAuth redirect URI"),
    current_user: User = Depends(get_current_user)
):
    """Get LinkedIn Ads OAuth authorization URL."""
    
    oauth_url = await linkedin_ads_service.get_oauth_url(
        user_id=current_user.id,
        redirect_uri=redirect_uri
    )
    
    return {"oauth_url": oauth_url}


@router.post("/linkedin-ads/oauth-callback")
async def linkedin_ads_oauth_callback(
    code: str,
    redirect_uri: str,
    current_user: User = Depends(get_current_user)
):
    """Handle LinkedIn Ads OAuth callback and exchange code for token."""
    
    try:
        token_data = await linkedin_ads_service.exchange_code_for_token(
            code=code,
            redirect_uri=redirect_uri
        )
        
        # In production, store token_data securely for the user
        return {
            "success": True,
            "message": "LinkedIn Ads account connected successfully",
            "token_data": token_data
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to connect LinkedIn Ads account: {str(e)}"
        )


@router.post("/linkedin-ads/create-campaign")
async def create_linkedin_ads_campaign(
    campaign_id: int,
    access_token: str,
    ad_account_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a campaign on LinkedIn Ads platform."""
    
    # Get campaign
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    try:
        result = await linkedin_ads_service.create_campaign_on_linkedin(
            campaign=campaign,
            access_token=access_token,
            ad_account_id=ad_account_id
        )
        
        # Update campaign with LinkedIn campaign ID
        await crud.update_campaign(
            db, 
            campaign_id, 
            {"external_campaign_id": result["campaign"]["id"]}
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create LinkedIn campaign: {str(e)}"
        )


@router.get("/linkedin-ads/campaign-performance/{campaign_id}")
async def get_linkedin_ads_performance(
    campaign_id: int,
    access_token: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get LinkedIn Ads campaign performance data."""
    
    # Get campaign
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    if not campaign.external_campaign_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Campaign not linked to LinkedIn Ads"
        )
    
    try:
        performance_data = await linkedin_ads_service.get_campaign_performance(
            linkedin_campaign_id=campaign.external_campaign_id,
            access_token=access_token
        )
        
        return performance_data
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch LinkedIn performance: {str(e)}"
        )


@router.post("/linkedin-ads/sync-data/{campaign_id}")
async def sync_linkedin_ads_data(
    campaign_id: int,
    access_token: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Sync LinkedIn Ads performance data to database."""
    
    # Get campaign
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    if not campaign.external_campaign_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Campaign not linked to LinkedIn Ads"
        )
    
    try:
        synced_data = await linkedin_ads_service.sync_campaign_data(
            campaign_id=campaign_id,
            linkedin_campaign_id=campaign.external_campaign_id,
            access_token=access_token,
            db=db
        )
        
        return {
            "success": True,
            "synced_records": len(synced_data),
            "message": f"Synced {len(synced_data)} data points from LinkedIn Ads"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to sync LinkedIn data: {str(e)}"
        )


# General Integration Endpoints
@router.get("/platforms")
async def get_supported_platforms():
    """Get list of supported advertising platforms."""
    
    return {
        "platforms": [
            {
                "id": "meta_ads",
                "name": "Meta Ads",
                "description": "Facebook and Instagram advertising",
                "supported_business_types": ["all"]
            },
            {
                "id": "google_ads",
                "name": "Google Ads",
                "description": "Google Search and Display advertising",
                "supported_business_types": ["all"]
            },
            {
                "id": "linkedin_ads",
                "name": "LinkedIn Ads",
                "description": "Professional network advertising",
                "supported_business_types": [
                    "legal", "accounting", "consulting", "real_estate", 
                    "insurance", "financial_planning", "marketing", 
                    "it_services", "architecture", "engineering"
                ]
            }
        ]
    }
