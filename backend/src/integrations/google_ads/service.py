"""
Google Ads Integration Service

Handles OAuth flow, campaign creation, and performance data sync for Google Ads.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from decimal import Decimal

import httpx
from fastapi import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import get_settings
from modules.campaigns import crud
from modules.campaigns.models import Campaign, AnalyticsData, AdPlatform
from modules.campaigns.schemas import AnalyticsDataCreate

logger = logging.getLogger(__name__)


class GoogleAdsService:
    """Service for Google Ads platform integration."""
    
    def __init__(self):
        self.settings = get_settings()
        self.base_url = "https://googleads.googleapis.com/v14"
        self.oauth_url = "https://accounts.google.com/o/oauth2/v2/auth"
        self.token_url = "https://oauth2.googleapis.com/token"
        self.client = httpx.AsyncClient()
    
    async def get_oauth_url(self, user_id: int, redirect_uri: str) -> str:
        """Generate OAuth URL for Google Ads authorization."""
        
        # In production, you would use actual Google Ads client ID
        client_id = "your_google_ads_client_id"  # Replace with actual client ID
        
        oauth_url = (
            f"{self.oauth_url}?"
            f"client_id={client_id}&"
            f"redirect_uri={redirect_uri}&"
            f"scope=https://www.googleapis.com/auth/adwords&"
            f"response_type=code&"
            f"access_type=offline&"
            f"state={user_id}"  # Use user_id as state for security
        )
        
        return oauth_url
    
    async def exchange_code_for_token(self, code: str, redirect_uri: str) -> Dict[str, Any]:
        """Exchange authorization code for access token."""
        
        # Mock implementation - in production, make actual API call to Google
        logger.info(f"Mock: Exchanging Google Ads code {code[:10]}... for access token")
        
        # Return mock token data
        return {
            "access_token": f"mock_google_access_token_{code[:10]}",
            "refresh_token": f"mock_google_refresh_token_{code[:10]}",
            "token_type": "Bearer",
            "expires_in": 3600,
            "scope": "https://www.googleapis.com/auth/adwords"
        }
    
    async def create_campaign_on_google(
        self, 
        campaign: Campaign, 
        access_token: str,
        customer_id: str
    ) -> Dict[str, Any]:
        """Create a campaign on Google Ads platform."""
        
        try:
            # Prepare campaign data for Google Ads API
            campaign_data = {
                "name": campaign.name,
                "advertising_channel_type": self._get_google_channel_type(campaign.primary_goal),
                "status": "PAUSED",  # Start paused for review
                "campaign_budget": {
                    "amount_micros": int(campaign.budget * 1000000),  # Convert to micros
                    "delivery_method": "STANDARD"
                },
                "bidding_strategy": {
                    "target_cpa": {
                        "target_cpa_micros": int(campaign.target_cost_per_appointment * 1000000) if campaign.target_cost_per_appointment else None
                    }
                }
            }
            
            # Mock API call - in production, make actual request
            logger.info(f"Mock: Creating Google Ads campaign for {campaign.name}")
            
            # Simulate API response
            mock_response = {
                "resource_name": f"customers/{customer_id}/campaigns/mock_campaign_{campaign.id}_{datetime.now().timestamp()}",
                "id": f"mock_google_campaign_{campaign.id}",
                "name": campaign.name,
                "status": "PAUSED"
            }
            
            # Create ad group
            adgroup_data = {
                "name": f"{campaign.name} - Ad Group",
                "campaign": mock_response["resource_name"],
                "status": "ENABLED",
                "type": "SEARCH_STANDARD"
            }
            
            mock_adgroup_response = {
                "resource_name": f"customers/{customer_id}/adGroups/mock_adgroup_{campaign.id}",
                "id": f"mock_google_adgroup_{campaign.id}",
                "name": adgroup_data["name"],
                "status": "ENABLED"
            }
            
            # Create keywords
            keywords = self._generate_keywords_for_business(campaign.business_profile)
            mock_keywords = []
            for i, keyword in enumerate(keywords[:5]):  # Limit to 5 keywords
                mock_keywords.append({
                    "resource_name": f"customers/{customer_id}/adGroupCriteria/mock_keyword_{campaign.id}_{i}",
                    "keyword": {"text": keyword, "match_type": "BROAD"},
                    "status": "ENABLED"
                })
            
            # Create ads
            ad_data = {
                "ad_group": mock_adgroup_response["resource_name"],
                "ad": {
                    "expanded_text_ad": {
                        "headline_part1": campaign.selected_headline or campaign.name,
                        "headline_part2": "Professional Service",
                        "description": campaign.selected_description or "Book your appointment today!",
                        "path1": "book",
                        "path2": "now"
                    },
                    "final_urls": [campaign.business_profile.booking_url or campaign.business_profile.website]
                },
                "status": "ENABLED"
            }
            
            mock_ad_response = {
                "resource_name": f"customers/{customer_id}/ads/mock_ad_{campaign.id}",
                "id": f"mock_google_ad_{campaign.id}",
                "status": "ENABLED"
            }
            
            return {
                "campaign": mock_response,
                "adgroup": mock_adgroup_response,
                "keywords": mock_keywords,
                "ad": mock_ad_response,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error creating Google Ads campaign: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to create Google Ads campaign: {str(e)}")
    
    def _get_google_channel_type(self, goal: Optional[str]) -> str:
        """Map campaign goal to Google Ads channel type."""
        goal_mapping = {
            "appointments": "SEARCH",
            "leads": "SEARCH", 
            "awareness": "DISPLAY",
            "traffic": "SEARCH"
        }
        return goal_mapping.get(goal, "SEARCH")
    
    def _generate_keywords_for_business(self, business_profile) -> List[str]:
        """Generate relevant keywords based on business type and location."""
        
        business_type = business_profile.business_type
        city = business_profile.city
        services = business_profile.services_offered or []
        
        keywords = []
        
        # Business type keywords
        if business_type == "plumbing":
            keywords.extend([
                f"plumber {city}", f"plumbing services {city}", f"emergency plumber {city}",
                f"drain cleaning {city}", f"pipe repair {city}", f"water heater repair {city}"
            ])
        elif business_type == "electrical":
            keywords.extend([
                f"electrician {city}", f"electrical services {city}", f"electrical repair {city}",
                f"wiring {city}", f"electrical installation {city}", f"circuit breaker {city}"
            ])
        elif business_type == "hvac":
            keywords.extend([
                f"hvac {city}", f"air conditioning repair {city}", f"heating repair {city}",
                f"furnace repair {city}", f"ac installation {city}", f"hvac maintenance {city}"
            ])
        elif business_type == "cleaning":
            keywords.extend([
                f"house cleaning {city}", f"cleaning service {city}", f"maid service {city}",
                f"office cleaning {city}", f"deep cleaning {city}", f"residential cleaning {city}"
            ])
        elif business_type == "landscaping":
            keywords.extend([
                f"landscaping {city}", f"lawn care {city}", f"tree service {city}",
                f"garden maintenance {city}", f"landscape design {city}", f"yard work {city}"
            ])
        elif business_type == "legal":
            keywords.extend([
                f"lawyer {city}", f"attorney {city}", f"legal services {city}",
                f"law firm {city}", f"legal advice {city}", f"legal consultation {city}"
            ])
        elif business_type == "accounting":
            keywords.extend([
                f"accountant {city}", f"tax preparation {city}", f"bookkeeping {city}",
                f"cpa {city}", f"tax services {city}", f"accounting services {city}"
            ])
        elif business_type == "real_estate":
            keywords.extend([
                f"real estate agent {city}", f"realtor {city}", f"homes for sale {city}",
                f"property {city}", f"real estate {city}", f"buy house {city}"
            ])
        # Add wellness keywords for existing types
        elif business_type == "massage_therapy":
            keywords.extend([
                f"massage therapy {city}", f"massage therapist {city}", f"therapeutic massage {city}",
                f"deep tissue massage {city}", f"sports massage {city}", f"relaxation massage {city}"
            ])
        elif business_type == "dental":
            keywords.extend([
                f"dentist {city}", f"dental care {city}", f"teeth cleaning {city}",
                f"dental checkup {city}", f"oral health {city}", f"dental clinic {city}"
            ])
        
        # Add service-specific keywords
        for service in services:
            keywords.append(f"{service} {city}")
            keywords.append(f"{service} near me")
        
        # Add generic local keywords
        keywords.extend([
            f"{business_type.replace('_', ' ')} near me",
            f"best {business_type.replace('_', ' ')} {city}",
            f"local {business_type.replace('_', ' ')} {city}"
        ])
        
        return keywords
    
    async def get_campaign_performance(
        self, 
        google_campaign_id: str, 
        access_token: str,
        customer_id: str,
        date_range: Optional[Dict[str, datetime]] = None
    ) -> Dict[str, Any]:
        """Fetch campaign performance from Google Ads API."""
        
        try:
            # Prepare date range
            if not date_range:
                end_date = datetime.now()
                start_date = end_date - timedelta(days=7)
            else:
                start_date = date_range["start"]
                end_date = date_range["end"]
            
            # Mock API call - in production, make actual request to Google Ads API
            logger.info(f"Mock: Fetching Google Ads performance for campaign {google_campaign_id}")
            
            # Simulate performance data
            mock_performance = {
                "results": [{
                    "campaign": {
                        "resource_name": f"customers/{customer_id}/campaigns/{google_campaign_id}",
                        "id": google_campaign_id
                    },
                    "metrics": {
                        "impressions": "2150",
                        "clicks": "87",
                        "cost_micros": "185500000",  # $185.50 in micros
                        "conversions": "12.0",
                        "cost_per_conversion": "15458333",  # $15.46 in micros
                        "conversion_rate": "0.1379",
                        "ctr": "0.0405"
                    },
                    "segments": {
                        "date": start_date.strftime("%Y-%m-%d")
                    }
                }]
            }
            
            return mock_performance
            
        except Exception as e:
            logger.error(f"Error fetching Google Ads performance: {str(e)}")
            return {"results": []}
    
    async def sync_campaign_data(
        self, 
        campaign_id: int, 
        google_campaign_id: str,
        access_token: str,
        customer_id: str,
        db: AsyncSession
    ) -> List[AnalyticsData]:
        """Sync performance data from Google Ads to our database."""
        
        try:
            # Get performance data
            performance_data = await self.get_campaign_performance(
                google_campaign_id, access_token, customer_id
            )
            
            synced_data = []
            
            for result in performance_data.get("results", []):
                metrics = result.get("metrics", {})
                
                # Convert Google Ads data to our format
                analytics_data = AnalyticsDataCreate(
                    campaign_id=campaign_id,
                    platform_type=AdPlatform.GOOGLE_ADS.value,
                    date=datetime.now(),
                    total_spend=Decimal(str(int(metrics.get("cost_micros", "0")) / 1000000)),
                    daily_spend=Decimal(str(int(metrics.get("cost_micros", "0")) / 1000000)),
                    impressions=int(metrics.get("impressions", "0")),
                    clicks=int(metrics.get("clicks", "0")),
                    leads=int(float(metrics.get("conversions", "0"))),
                    click_through_rate=float(metrics.get("ctr", "0")) * 100,
                    cost_per_click=Decimal(str(int(metrics.get("cost_micros", "0")) / 1000000 / max(int(metrics.get("clicks", "1")), 1))),
                    platform_data=result
                )
                
                # Create analytics entry
                analytics_entry = await crud.create_analytics_data(db, analytics_data)
                synced_data.append(analytics_entry)
            
            return synced_data
            
        except Exception as e:
            logger.error(f"Error syncing Google Ads data: {str(e)}")
            return []
    
    async def update_campaign_status(
        self, 
        google_campaign_id: str, 
        status: str, 
        access_token: str,
        customer_id: str
    ) -> bool:
        """Update campaign status on Google Ads platform."""
        
        try:
            # Mock API call - in production, make PATCH request to Google Ads API
            logger.info(f"Mock: Updating Google Ads campaign {google_campaign_id} status to {status}")
            
            # Simulate successful update
            return True
            
        except Exception as e:
            logger.error(f"Error updating Google Ads campaign status: {str(e)}")
            return False
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


# Global service instance
google_ads_service = GoogleAdsService()
