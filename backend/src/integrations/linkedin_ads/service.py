"""
LinkedIn Ads Integration Service

Handles OAuth flow, campaign creation, and performance data sync for LinkedIn Ads.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from decimal import Decimal

import httpx
from fastapi import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import get_settings
from modules.campaigns import crud
from modules.campaigns.models import Campaign, AnalyticsData, AdPlatform
from modules.campaigns.schemas import AnalyticsDataCreate

logger = logging.getLogger(__name__)


class LinkedInAdsService:
    """Service for LinkedIn Ads platform integration."""
    
    def __init__(self):
        self.settings = get_settings()
        self.base_url = "https://api.linkedin.com/rest"
        self.oauth_url = "https://www.linkedin.com/oauth/v2/authorization"
        self.token_url = "https://www.linkedin.com/oauth/v2/accessToken"
        self.client = httpx.AsyncClient()
    
    async def get_oauth_url(self, user_id: int, redirect_uri: str) -> str:
        """Generate OAuth URL for LinkedIn Ads authorization."""
        
        # In production, you would use actual LinkedIn client ID
        client_id = "your_linkedin_client_id"  # Replace with actual client ID
        
        oauth_url = (
            f"{self.oauth_url}?"
            f"response_type=code&"
            f"client_id={client_id}&"
            f"redirect_uri={redirect_uri}&"
            f"scope=r_ads,r_ads_reporting,rw_ads&"
            f"state={user_id}"  # Use user_id as state for security
        )
        
        return oauth_url
    
    async def exchange_code_for_token(self, code: str, redirect_uri: str) -> Dict[str, Any]:
        """Exchange authorization code for access token."""
        
        # Mock implementation - in production, make actual API call to LinkedIn
        logger.info(f"Mock: Exchanging LinkedIn code {code[:10]}... for access token")
        
        # Return mock token data
        return {
            "access_token": f"mock_linkedin_access_token_{code[:10]}",
            "token_type": "Bearer",
            "expires_in": 5184000,  # 60 days
            "scope": "r_ads,r_ads_reporting,rw_ads"
        }
    
    async def create_campaign_on_linkedin(
        self, 
        campaign: Campaign, 
        access_token: str,
        ad_account_id: str
    ) -> Dict[str, Any]:
        """Create a campaign on LinkedIn Ads platform."""
        
        try:
            # Prepare campaign data for LinkedIn Ads API
            campaign_data = {
                "name": campaign.name,
                "account": f"urn:li:sponsoredAccount:{ad_account_id}",
                "type": self._get_linkedin_campaign_type(campaign.primary_goal),
                "status": "PAUSED",  # Start paused for review
                "costType": "CPC",
                "dailyBudget": {
                    "amount": str(int(campaign.budget / campaign.duration_days * 100)),  # Convert to cents
                    "currencyCode": "USD"
                },
                "unitCost": {
                    "amount": str(int((campaign.target_cost_per_appointment or 50) * 100)),  # Convert to cents
                    "currencyCode": "USD"
                },
                "targetingCriteria": self._build_linkedin_targeting(campaign.business_profile),
                "creativeSelection": "OPTIMIZED"
            }
            
            # Mock API call - in production, make actual request
            logger.info(f"Mock: Creating LinkedIn campaign for {campaign.name}")
            
            # Simulate API response
            mock_response = {
                "id": f"mock_linkedin_campaign_{campaign.id}_{datetime.now().timestamp()}",
                "name": campaign.name,
                "status": "PAUSED",
                "account": f"urn:li:sponsoredAccount:{ad_account_id}",
                "type": campaign_data["type"]
            }
            
            # Create creative
            creative_data = {
                "campaign": f"urn:li:sponsoredCampaign:{mock_response['id']}",
                "status": "ACTIVE",
                "variables": {
                    "clickUri": campaign.business_profile.booking_url or campaign.business_profile.website,
                    "data": {
                        "com.linkedin.ads.SponsoredTextAdCreativeVariables": {
                            "text": campaign.selected_ad_copy or f"Professional {campaign.business_profile.business_type.replace('_', ' ')} services",
                            "title": campaign.selected_headline or campaign.name
                        }
                    }
                }
            }
            
            mock_creative_response = {
                "id": f"mock_linkedin_creative_{campaign.id}",
                "campaign": f"urn:li:sponsoredCampaign:{mock_response['id']}",
                "status": "ACTIVE"
            }
            
            return {
                "campaign": mock_response,
                "creative": mock_creative_response,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error creating LinkedIn campaign: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to create LinkedIn campaign: {str(e)}")
    
    def _get_linkedin_campaign_type(self, goal: Optional[str]) -> str:
        """Map campaign goal to LinkedIn campaign type."""
        goal_mapping = {
            "appointments": "SPONSORED_CONTENT",
            "leads": "SPONSORED_CONTENT",
            "awareness": "SPONSORED_CONTENT",
            "traffic": "SPONSORED_CONTENT"
        }
        return goal_mapping.get(goal, "SPONSORED_CONTENT")
    
    def _build_linkedin_targeting(self, business_profile) -> Dict[str, Any]:
        """Build LinkedIn targeting criteria based on business profile."""
        
        targeting = {
            "include": {
                "and": []
            }
        }
        
        # Geographic targeting
        if business_profile.city and business_profile.country:
            targeting["include"]["and"].append({
                "or": {
                    "urn:li:geo:*********": {}  # Switzerland - replace with actual geo URN
                }
            })
        
        # Industry targeting based on business type
        industries = self._get_linkedin_industries(business_profile.business_type)
        if industries:
            targeting["include"]["and"].append({
                "or": {industry: {} for industry in industries}
            })
        
        # Job function targeting for professional services
        if business_profile.business_type in ["legal", "accounting", "consulting", "real_estate"]:
            targeting["include"]["and"].append({
                "or": {
                    "urn:li:function:1": {},  # Accounting/Auditing
                    "urn:li:function:2": {},  # Administrative
                    "urn:li:function:3": {},  # Arts and Design
                    "urn:li:function:4": {},  # Business Development
                    "urn:li:function:5": {},  # Community & Social Services
                    "urn:li:function:6": {},  # Consulting
                    "urn:li:function:7": {},  # Education
                    "urn:li:function:8": {},  # Engineering
                    "urn:li:function:9": {},  # Entrepreneurship
                    "urn:li:function:10": {},  # Finance
                    "urn:li:function:11": {},  # Healthcare Services
                    "urn:li:function:12": {},  # Human Resources
                    "urn:li:function:13": {},  # Information Technology
                    "urn:li:function:14": {},  # Legal
                    "urn:li:function:15": {},  # Marketing
                    "urn:li:function:16": {},  # Media & Communications
                    "urn:li:function:17": {},  # Military & Protective Services
                    "urn:li:function:18": {},  # Operations
                    "urn:li:function:19": {},  # Product Management
                    "urn:li:function:20": {},  # Program & Project Management
                    "urn:li:function:21": {},  # Purchasing
                    "urn:li:function:22": {},  # Quality Assurance
                    "urn:li:function:23": {},  # Real Estate
                    "urn:li:function:24": {},  # Research
                    "urn:li:function:25": {},  # Sales
                    "urn:li:function:26": {},  # Support
                }
            })
        
        return targeting
    
    def _get_linkedin_industries(self, business_type: str) -> List[str]:
        """Get relevant LinkedIn industry URNs for business type."""
        
        industry_mapping = {
            "legal": ["urn:li:industry:10"],  # Legal Services
            "accounting": ["urn:li:industry:1"],  # Accounting
            "consulting": ["urn:li:industry:25"],  # Management Consulting
            "real_estate": ["urn:li:industry:44"],  # Real Estate
            "insurance": ["urn:li:industry:42"],  # Insurance
            "financial_planning": ["urn:li:industry:43"],  # Financial Services
            "marketing": ["urn:li:industry:80"],  # Marketing & Advertising
            "it_services": ["urn:li:industry:96"],  # Information Technology & Services
            "architecture": ["urn:li:industry:111"],  # Architecture & Planning
            "engineering": ["urn:li:industry:135"],  # Civil Engineering
            "photography": ["urn:li:industry:131"],  # Photography
            "event_planning": ["urn:li:industry:28"],  # Events Services
        }
        
        return industry_mapping.get(business_type, [])
    
    async def get_campaign_performance(
        self, 
        linkedin_campaign_id: str, 
        access_token: str,
        date_range: Optional[Dict[str, datetime]] = None
    ) -> Dict[str, Any]:
        """Fetch campaign performance from LinkedIn Ads API."""
        
        try:
            # Prepare date range
            if not date_range:
                end_date = datetime.now()
                start_date = end_date - timedelta(days=7)
            else:
                start_date = date_range["start"]
                end_date = date_range["end"]
            
            # Mock API call - in production, make actual request to LinkedIn Ads API
            logger.info(f"Mock: Fetching LinkedIn performance for campaign {linkedin_campaign_id}")
            
            # Simulate performance data
            mock_performance = {
                "elements": [{
                    "pivotValues": [
                        f"urn:li:sponsoredCampaign:{linkedin_campaign_id}"
                    ],
                    "impressions": 1850,
                    "clicks": 65,
                    "costInUsd": 142.75,
                    "externalWebsiteConversions": 8,
                    "costPerConversion": 17.84,
                    "conversionRate": 0.123,
                    "clickThroughRate": 0.0351,
                    "dateRange": {
                        "start": {
                            "day": start_date.day,
                            "month": start_date.month,
                            "year": start_date.year
                        },
                        "end": {
                            "day": end_date.day,
                            "month": end_date.month,
                            "year": end_date.year
                        }
                    }
                }]
            }
            
            return mock_performance
            
        except Exception as e:
            logger.error(f"Error fetching LinkedIn performance: {str(e)}")
            return {"elements": []}
    
    async def sync_campaign_data(
        self, 
        campaign_id: int, 
        linkedin_campaign_id: str,
        access_token: str,
        db: AsyncSession
    ) -> List[AnalyticsData]:
        """Sync performance data from LinkedIn Ads to our database."""
        
        try:
            # Get performance data
            performance_data = await self.get_campaign_performance(
                linkedin_campaign_id, access_token
            )
            
            synced_data = []
            
            for element in performance_data.get("elements", []):
                # Convert LinkedIn data to our format
                analytics_data = AnalyticsDataCreate(
                    campaign_id=campaign_id,
                    platform_type=AdPlatform.LINKEDIN_ADS.value,
                    date=datetime.now(),
                    total_spend=Decimal(str(element.get("costInUsd", 0))),
                    daily_spend=Decimal(str(element.get("costInUsd", 0))),
                    impressions=element.get("impressions", 0),
                    clicks=element.get("clicks", 0),
                    leads=element.get("externalWebsiteConversions", 0),
                    click_through_rate=element.get("clickThroughRate", 0) * 100,
                    cost_per_click=Decimal(str(element.get("costInUsd", 0) / max(element.get("clicks", 1), 1))),
                    platform_data=element
                )
                
                # Create analytics entry
                analytics_entry = await crud.create_analytics_data(db, analytics_data)
                synced_data.append(analytics_entry)
            
            return synced_data
            
        except Exception as e:
            logger.error(f"Error syncing LinkedIn data: {str(e)}")
            return []
    
    async def update_campaign_status(
        self, 
        linkedin_campaign_id: str, 
        status: str, 
        access_token: str
    ) -> bool:
        """Update campaign status on LinkedIn Ads platform."""
        
        try:
            # Mock API call - in production, make PATCH request to LinkedIn Ads API
            logger.info(f"Mock: Updating LinkedIn campaign {linkedin_campaign_id} status to {status}")
            
            # Simulate successful update
            return True
            
        except Exception as e:
            logger.error(f"Error updating LinkedIn campaign status: {str(e)}")
            return False
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


# Global service instance
linkedin_ads_service = LinkedInAdsService()
