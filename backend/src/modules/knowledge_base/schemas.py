"""
Knowledge Base Schemas for API requests and responses.
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field


class DocumentType(str, Enum):
    """Document types for knowledge base."""

    SERVICE_MANUAL = "service_manual"
    PRICING_GUIDE = "pricing_guide"
    FAQ = "faq"
    POLICY = "policy"
    PROCEDURE = "procedure"
    CATALOG = "catalog"
    TRAINING = "training"
    MARKETING = "marketing"
    OTHER = "other"


class DocumentStatus(str, Enum):
    """Document processing status."""

    PENDING = "pending"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"
    ARCHIVED = "archived"


class PriceUnit(str, Enum):
    """Pricing units for services."""

    FLAT_RATE = "flat_rate"
    PER_HOUR = "per_hour"
    PER_SQFT = "per_sqft"
    PER_LINEAR_FT = "per_linear_ft"
    PER_ROOM = "per_room"
    PER_FIXTURE = "per_fixture"
    PER_UNIT = "per_unit"
    CUSTOM = "custom"


class SkillLevel(str, Enum):
    """Skill levels required for services."""

    BASIC = "basic"
    INTERMEDIATE = "intermediate"
    EXPERT = "expert"
    SPECIALIST = "specialist"


# Knowledge Document Schemas
class KnowledgeDocumentBase(BaseModel):
    title: str = Field(..., max_length=500)
    description: Optional[str] = None
    content: Optional[str] = None
    document_type: DocumentType = DocumentType.OTHER
    category: Optional[str] = Field(None, max_length=100)
    tags: Optional[List[str]] = None
    language: str = Field(default="en", max_length=10)
    is_public: bool = True
    access_roles: Optional[List[str]] = None
    version: str = Field(default="1.0", max_length=20)


class KnowledgeDocumentCreate(KnowledgeDocumentBase):
    company_id: int


class KnowledgeDocumentUpdate(BaseModel):
    title: Optional[str] = Field(None, max_length=500)
    description: Optional[str] = None
    content: Optional[str] = None
    document_type: Optional[DocumentType] = None
    category: Optional[str] = Field(None, max_length=100)
    tags: Optional[List[str]] = None
    is_public: Optional[bool] = None
    access_roles: Optional[List[str]] = None
    version: Optional[str] = Field(None, max_length=20)


class KnowledgeDocument(KnowledgeDocumentBase):
    id: int
    company_id: int
    file_path: Optional[str] = None
    file_name: Optional[str] = None
    file_size: Optional[int] = None
    mime_type: Optional[str] = None
    status: DocumentStatus
    processing_error: Optional[str] = None
    keywords: Optional[List[str]] = None
    summary: Optional[str] = None
    word_count: Optional[int] = None
    page_count: Optional[int] = None
    parent_document_id: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    processed_at: Optional[datetime] = None
    created_by: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)


# Service Catalog Schemas
class ServiceCatalogItemBase(BaseModel):
    name: str = Field(..., max_length=255)
    description: Optional[str] = None
    short_description: Optional[str] = Field(None, max_length=500)
    category: Optional[str] = Field(None, max_length=100)
    subcategory: Optional[str] = Field(None, max_length=100)
    service_code: Optional[str] = Field(None, max_length=50)
    base_price: Optional[Decimal] = None
    price_unit: Optional[PriceUnit] = None
    min_price: Optional[Decimal] = None
    max_price: Optional[Decimal] = None
    pricing_notes: Optional[str] = None
    duration_minutes: Optional[int] = None
    materials_included: bool = False
    labor_only: bool = True
    requires_permit: bool = False
    requires_inspection: bool = False
    skill_level_required: Optional[SkillLevel] = None
    tools_required: Optional[List[str]] = None
    materials_list: Optional[List[str]] = None
    is_active: bool = True
    seasonal_availability: Optional[List[str]] = None
    geographic_restrictions: Optional[List[str]] = None
    featured: bool = False
    promotion_text: Optional[str] = Field(None, max_length=500)
    image_urls: Optional[List[str]] = None
    keywords: Optional[List[str]] = None
    search_terms: Optional[List[str]] = None


class ServiceCatalogItemCreate(ServiceCatalogItemBase):
    company_id: int


class ServiceCatalogItemUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    short_description: Optional[str] = Field(None, max_length=500)
    category: Optional[str] = Field(None, max_length=100)
    subcategory: Optional[str] = Field(None, max_length=100)
    service_code: Optional[str] = Field(None, max_length=50)
    base_price: Optional[Decimal] = None
    price_unit: Optional[PriceUnit] = None
    min_price: Optional[Decimal] = None
    max_price: Optional[Decimal] = None
    pricing_notes: Optional[str] = None
    duration_minutes: Optional[int] = None
    materials_included: Optional[bool] = None
    labor_only: Optional[bool] = None
    requires_permit: Optional[bool] = None
    requires_inspection: Optional[bool] = None
    skill_level_required: Optional[SkillLevel] = None
    tools_required: Optional[List[str]] = None
    materials_list: Optional[List[str]] = None
    is_active: Optional[bool] = None
    seasonal_availability: Optional[List[str]] = None
    geographic_restrictions: Optional[List[str]] = None
    featured: Optional[bool] = None
    promotion_text: Optional[str] = Field(None, max_length=500)
    image_urls: Optional[List[str]] = None
    keywords: Optional[List[str]] = None
    search_terms: Optional[List[str]] = None


class ServiceCatalogItem(ServiceCatalogItemBase):
    id: int
    company_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)


# FAQ Schemas
class FAQBase(BaseModel):
    question: str
    answer: str
    category: Optional[str] = Field(None, max_length=100)
    tags: Optional[List[str]] = None
    keywords: Optional[List[str]] = None
    alternative_questions: Optional[List[str]] = None
    is_active: bool = True
    is_featured: bool = False
    sort_order: int = 0


class FAQCreate(FAQBase):
    company_id: int


class FAQUpdate(BaseModel):
    question: Optional[str] = None
    answer: Optional[str] = None
    category: Optional[str] = Field(None, max_length=100)
    tags: Optional[List[str]] = None
    keywords: Optional[List[str]] = None
    alternative_questions: Optional[List[str]] = None
    is_active: Optional[bool] = None
    is_featured: Optional[bool] = None
    sort_order: Optional[int] = None


class FAQ(FAQBase):
    id: int
    company_id: int
    view_count: int = 0
    helpful_votes: int = 0
    unhelpful_votes: int = 0
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)


# Search Schemas
class KnowledgeSearchRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=500)
    search_type: str = Field(default="all")  # "all", "documents", "services", "faqs"
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    limit: int = Field(default=10, ge=1, le=100)
    include_content: bool = False
    semantic_search: bool = True


class KnowledgeSearchResult(BaseModel):
    id: int
    source_type: str  # "document", "service", "faq"
    title: str
    content: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    relevance_score: float
    url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class KnowledgeSearchResponse(BaseModel):
    query: str
    total_results: int
    results: List[KnowledgeSearchResult]
    search_time_ms: float
    suggestions: Optional[List[str]] = None


# File Upload Schemas
class DocumentUploadRequest(BaseModel):
    title: str = Field(..., max_length=500)
    description: Optional[str] = None
    document_type: DocumentType = DocumentType.OTHER
    category: Optional[str] = Field(None, max_length=100)
    tags: Optional[List[str]] = None
    is_public: bool = True
    auto_process: bool = True


class DocumentUploadResponse(BaseModel):
    document_id: int
    upload_url: Optional[str] = None
    status: DocumentStatus
    message: str


# Analytics Schemas
class KnowledgeAnalytics(BaseModel):
    company_id: int
    date: datetime
    period_type: str
    total_searches: int = 0
    successful_searches: int = 0
    failed_searches: int = 0
    average_response_time: float = 0.0
    documents_accessed: int = 0
    services_queried: int = 0
    faqs_viewed: int = 0
    top_documents: Optional[List[Dict[str, Any]]] = None
    top_services: Optional[List[Dict[str, Any]]] = None
    top_search_terms: Optional[List[str]] = None
    agent_queries: int = 0
    human_queries: int = 0

    model_config = ConfigDict(from_attributes=True)


# Bulk Operations
class BulkDocumentUpdate(BaseModel):
    document_ids: List[int]
    updates: KnowledgeDocumentUpdate


class BulkServiceUpdate(BaseModel):
    service_ids: List[int]
    updates: ServiceCatalogItemUpdate


# Response Models
class DocumentListResponse(BaseModel):
    documents: List[KnowledgeDocument]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class ServiceListResponse(BaseModel):
    services: List[ServiceCatalogItem]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class FAQListResponse(BaseModel):
    faqs: List[FAQ]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class ApiResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None
    errors: Optional[List[str]] = None


# Document Processing Schemas
class DocumentProcessingStatus(BaseModel):
    document_id: int
    status: DocumentStatus
    progress: int = 0  # 0-100
    message: Optional[str] = None
    error: Optional[str] = None
    estimated_completion: Optional[datetime] = None
