"""
Knowledge Base API Router.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, File, HTTPException, Query, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.service import get_current_active_user
from modules.companies import crud as company_crud
from modules.users.models import User as DBUser

from . import crud, schemas

logger = logging.getLogger(__name__)

router = APIRouter(tags=["knowledge-base"])


# Knowledge Documents Endpoints
@router.get("/documents", response_model=schemas.DocumentListResponse)
async def get_knowledge_documents(
    company_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    document_type: Optional[schemas.DocumentType] = None,
    status: Optional[schemas.DocumentStatus] = None,
    category: Optional[str] = None,
    search: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get knowledge documents for a company."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    documents = await crud.get_knowledge_documents(
        db=db,
        company_id=company_id,
        skip=skip,
        limit=limit,
        document_type=document_type,
        status=status,
        category=category,
        search=search,
    )

    total = await crud.get_knowledge_documents_count(
        db=db,
        company_id=company_id,
        document_type=document_type,
        status=status,
        category=category,
        search=search,
    )

    return schemas.DocumentListResponse(
        documents=documents,
        total=total,
        page=skip // limit + 1,
        per_page=limit,
        has_next=(skip + limit) < total,
        has_prev=skip > 0,
    )


@router.post("/documents", response_model=schemas.KnowledgeDocument)
async def create_knowledge_document(
    document: schemas.KnowledgeDocumentCreate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a new knowledge document."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, document.company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    try:
        db_document = await crud.create_knowledge_document(
            db=db, document=document, created_by=current_user.id
        )
        return db_document
    except Exception as e:
        logger.error(f"Error creating knowledge document: {e}")
        raise HTTPException(status_code=400, detail="Failed to create document")


@router.get("/documents/{document_id}", response_model=schemas.KnowledgeDocument)
async def get_knowledge_document(
    document_id: int,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get a specific knowledge document."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    document = await crud.get_knowledge_document(db, document_id, company_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    return document


@router.put("/documents/{document_id}", response_model=schemas.KnowledgeDocument)
async def update_knowledge_document(
    document_id: int,
    company_id: int,
    document_update: schemas.KnowledgeDocumentUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Update a knowledge document."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    document = await crud.update_knowledge_document(
        db=db,
        document_id=document_id,
        company_id=company_id,
        document_update=document_update,
    )

    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    return document


@router.delete("/documents/{document_id}")
async def delete_knowledge_document(
    document_id: int,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Delete a knowledge document."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    success = await crud.delete_knowledge_document(
        db=db, document_id=document_id, company_id=company_id
    )

    if not success:
        raise HTTPException(status_code=404, detail="Document not found")

    return {"message": "Document deleted successfully"}


# Service Catalog Endpoints
@router.get("/services", response_model=schemas.ServiceListResponse)
async def get_service_catalog_items(
    company_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    category: Optional[str] = None,
    is_active: Optional[bool] = None,
    featured: Optional[bool] = None,
    search: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get service catalog items for a company."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    services = await crud.get_service_catalog_items(
        db=db,
        company_id=company_id,
        skip=skip,
        limit=limit,
        category=category,
        is_active=is_active,
        featured=featured,
        search=search,
    )

    total = await crud.get_service_catalog_items_count(
        db=db,
        company_id=company_id,
        category=category,
        is_active=is_active,
        featured=featured,
        search=search,
    )

    return schemas.ServiceListResponse(
        services=services,
        total=total,
        page=skip // limit + 1,
        per_page=limit,
        has_next=(skip + limit) < total,
        has_prev=skip > 0,
    )


@router.post("/services", response_model=schemas.ServiceCatalogItem)
async def create_service_catalog_item(
    service: schemas.ServiceCatalogItemCreate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a new service catalog item."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, service.company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    try:
        db_service = await crud.create_service_catalog_item(
            db=db, service=service, created_by=current_user.id
        )
        return db_service
    except Exception as e:
        logger.error(f"Error creating service catalog item: {e}")
        raise HTTPException(status_code=400, detail="Failed to create service")


@router.get("/services/{service_id}", response_model=schemas.ServiceCatalogItem)
async def get_service_catalog_item(
    service_id: int,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get a specific service catalog item."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    service = await crud.get_service_catalog_item(db, service_id, company_id)
    if not service:
        raise HTTPException(status_code=404, detail="Service not found")

    return service


@router.put("/services/{service_id}", response_model=schemas.ServiceCatalogItem)
async def update_service_catalog_item(
    service_id: int,
    company_id: int,
    service_update: schemas.ServiceCatalogItemUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Update a service catalog item."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    service = await crud.update_service_catalog_item(
        db=db,
        service_id=service_id,
        company_id=company_id,
        service_update=service_update,
    )

    if not service:
        raise HTTPException(status_code=404, detail="Service not found")

    return service


@router.delete("/services/{service_id}")
async def delete_service_catalog_item(
    service_id: int,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Delete a service catalog item."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    success = await crud.delete_service_catalog_item(
        db=db, service_id=service_id, company_id=company_id
    )

    if not success:
        raise HTTPException(status_code=404, detail="Service not found")

    return {"message": "Service deleted successfully"}


# FAQ Endpoints
@router.get("/faqs", response_model=schemas.FAQListResponse)
async def get_faqs(
    company_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    category: Optional[str] = None,
    is_active: Optional[bool] = None,
    is_featured: Optional[bool] = None,
    search: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get FAQs for a company."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    faqs = await crud.get_faqs(
        db=db,
        company_id=company_id,
        skip=skip,
        limit=limit,
        category=category,
        is_active=is_active,
        is_featured=is_featured,
        search=search,
    )

    total = await crud.get_faqs_count(
        db=db,
        company_id=company_id,
        category=category,
        is_active=is_active,
        is_featured=is_featured,
        search=search,
    )

    return schemas.FAQListResponse(
        faqs=faqs,
        total=total,
        page=skip // limit + 1,
        per_page=limit,
        has_next=(skip + limit) < total,
        has_prev=skip > 0,
    )


@router.post("/faqs", response_model=schemas.FAQ)
async def create_faq(
    faq: schemas.FAQCreate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a new FAQ."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, faq.company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    try:
        db_faq = await crud.create_faq(db=db, faq=faq, created_by=current_user.id)
        return db_faq
    except Exception as e:
        logger.error(f"Error creating FAQ: {e}")
        raise HTTPException(status_code=400, detail="Failed to create FAQ")


# File Upload Endpoint
@router.post("/documents/upload", response_model=schemas.DocumentUploadResponse)
async def upload_document(
    company_id: int,
    file: UploadFile = File(...),
    title: str = Query(...),
    description: Optional[str] = Query(None),
    document_type: schemas.DocumentType = Query(schemas.DocumentType.OTHER),
    category: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Upload a document file."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    try:
        # Create document record
        document_data = schemas.KnowledgeDocumentCreate(
            company_id=company_id,
            title=title,
            description=description,
            document_type=document_type,
            category=category,
        )

        db_document = await crud.create_knowledge_document(
            db=db, document=document_data, created_by=current_user.id
        )

        # TODO: Implement actual file upload and processing
        # This would involve:
        # 1. Save file to storage (S3, local filesystem, etc.)
        # 2. Extract text content from file
        # 3. Process content for search indexing
        # 4. Update document with processed content

        return schemas.DocumentUploadResponse(
            document_id=db_document.id,
            status=schemas.DocumentStatus.PENDING,
            message="Document uploaded successfully and queued for processing",
        )

    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        raise HTTPException(status_code=500, detail="File upload failed")
