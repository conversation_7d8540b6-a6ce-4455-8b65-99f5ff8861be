"""
Knowledge Base Models for Home Service Platform

Manages documents, service catalogs, pricing, and searchable content.
"""

from enum import Enum as PyEnum

from sqlalchemy import (
    DECIMAL,
    JSON,
    Boolean,
    Column,
    DateTime,
    Float,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class DocumentType(PyEnum):
    """Document types for knowledge base."""

    SERVICE_MANUAL = "service_manual"
    PRICING_GUIDE = "pricing_guide"
    FAQ = "faq"
    POLICY = "policy"
    PROCEDURE = "procedure"
    CATALOG = "catalog"
    TRAINING = "training"
    MARKETING = "marketing"
    OTHER = "other"


class DocumentStatus(PyEnum):
    """Document processing status."""

    PENDING = "pending"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"
    ARCHIVED = "archived"


class KnowledgeDocument(Base):
    """
    Knowledge base documents with text processing and search capabilities.
    """

    __tablename__ = "knowledge_documents"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Document Information
    title = Column(String(500), nullable=False, index=True)
    description = Column(Text, nullable=True)
    content = Column(Text, nullable=True)  # Extracted text content
    file_path = Column(String(1000), nullable=True)  # Original file path
    file_name = Column(String(255), nullable=True)
    file_size = Column(Integer, nullable=True)  # in bytes
    mime_type = Column(String(100), nullable=True)

    # Classification
    document_type = Column(String(50), nullable=False, default=DocumentType.OTHER.value)
    category = Column(String(100), nullable=True)  # Custom category
    tags = Column(JSON, nullable=True, default=list)  # List of tags

    # Processing Status
    status = Column(String(50), nullable=False, default=DocumentStatus.PENDING.value)
    processing_error = Column(Text, nullable=True)

    # Search & Retrieval
    keywords = Column(JSON, nullable=True, default=list)  # Extracted keywords
    summary = Column(Text, nullable=True)  # AI-generated summary

    # Metadata
    language = Column(String(10), nullable=True, default="en")
    word_count = Column(Integer, nullable=True)
    page_count = Column(Integer, nullable=True)

    # Access Control
    is_public = Column(Boolean, default=False)  # Public to all company agents
    access_roles = Column(
        JSON, nullable=True, default=list
    )  # Specific roles with access

    # Versioning
    version = Column(String(20), nullable=True, default="1.0")
    parent_document_id = Column(
        Integer, ForeignKey("knowledge_documents.id"), nullable=True
    )

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    processed_at = Column(DateTime(timezone=True), nullable=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationships
    company = relationship("Company", back_populates="knowledge_documents")
    user = relationship("User", back_populates="knowledge_documents")
    parent_document = relationship("KnowledgeDocument", remote_side=[id])
    child_documents = relationship(
        "KnowledgeDocument", back_populates="parent_document"
    )

    def __repr__(self):
        return f"<KnowledgeDocument(id={self.id}, title='{self.title}', type='{self.document_type}')>"


class ServiceCatalogItem(Base):
    """
    Service catalog with pricing and descriptions.
    """

    __tablename__ = "service_catalog"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Service Information
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    short_description = Column(String(500), nullable=True)

    # Categorization
    category = Column(String(100), nullable=True, index=True)
    subcategory = Column(String(100), nullable=True)
    service_code = Column(String(50), nullable=True, unique=True, index=True)

    # Pricing
    base_price = Column(DECIMAL(10, 2), nullable=True)  # Base price in dollars
    price_unit = Column(
        String(50), nullable=True
    )  # "per_hour", "flat_rate", "per_sqft", etc.
    min_price = Column(DECIMAL(10, 2), nullable=True)
    max_price = Column(DECIMAL(10, 2), nullable=True)
    pricing_notes = Column(Text, nullable=True)

    # Service Details
    duration_minutes = Column(Integer, nullable=True)  # Estimated duration
    materials_included = Column(Boolean, default=False)
    labor_only = Column(Boolean, default=True)

    # Requirements
    requires_permit = Column(Boolean, default=False)
    requires_inspection = Column(Boolean, default=False)
    skill_level_required = Column(
        String(50), nullable=True
    )  # "basic", "intermediate", "expert"
    tools_required = Column(JSON, nullable=True, default=list)
    materials_list = Column(JSON, nullable=True, default=list)

    # Availability
    is_active = Column(Boolean, default=True)
    seasonal_availability = Column(JSON, nullable=True)  # Months when available
    geographic_restrictions = Column(JSON, nullable=True)  # Service areas

    # Marketing
    featured = Column(Boolean, default=False)
    promotion_text = Column(String(500), nullable=True)
    image_urls = Column(JSON, nullable=True, default=list)

    # SEO & Search
    keywords = Column(JSON, nullable=True, default=list)
    search_terms = Column(JSON, nullable=True, default=list)

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationships
    company = relationship("Company", back_populates="service_catalog")

    def __repr__(self):
        return f"<ServiceCatalogItem(id={self.id}, name='{self.name}', price=${self.base_price})>"


class FAQ(Base):
    """
    Frequently Asked Questions for knowledge base.
    """

    __tablename__ = "faqs"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # FAQ Content
    question = Column(Text, nullable=False, index=True)
    answer = Column(Text, nullable=False)

    # Categorization
    category = Column(String(100), nullable=True, index=True)
    tags = Column(JSON, nullable=True, default=list)

    # Usage Statistics
    view_count = Column(Integer, default=0)
    helpful_votes = Column(Integer, default=0)
    unhelpful_votes = Column(Integer, default=0)

    # Search & Matching
    keywords = Column(JSON, nullable=True, default=list)
    alternative_questions = Column(
        JSON, nullable=True, default=list
    )  # Similar questions

    # Status
    is_active = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)

    # Ordering
    sort_order = Column(Integer, default=0)

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationships
    company = relationship("Company")

    def __repr__(self):
        return f"<FAQ(id={self.id}, question='{self.question[:50]}...')>"


class KnowledgeSearchIndex(Base):
    """
    Search index for fast knowledge base queries.
    """

    __tablename__ = "knowledge_search_index"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Source Information
    source_type = Column(String(50), nullable=False)  # "document", "service", "faq"
    source_id = Column(Integer, nullable=False)

    # Search Content
    title = Column(String(500), nullable=False, index=True)
    content = Column(Text, nullable=False)
    keywords = Column(JSON, nullable=True, default=list)

    # Search Metadata
    category = Column(String(100), nullable=True, index=True)
    importance_score = Column(Float, default=1.0)
    freshness_score = Column(Float, default=1.0)
    usage_score = Column(Float, default=1.0)

    # Status
    is_active = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    company = relationship("Company")

    def __repr__(self):
        return f"<KnowledgeSearchIndex(id={self.id}, source_type='{self.source_type}', title='{self.title[:30]}...')>"


class KnowledgeAnalytics(Base):
    """
    Analytics for knowledge base usage and performance.
    """

    __tablename__ = "knowledge_analytics"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Time Period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False, default="daily")

    # Search Metrics
    total_searches = Column(Integer, default=0)
    successful_searches = Column(Integer, default=0)
    failed_searches = Column(Integer, default=0)
    average_response_time = Column(Float, default=0.0)  # in seconds

    # Content Metrics
    documents_accessed = Column(Integer, default=0)
    services_queried = Column(Integer, default=0)
    faqs_viewed = Column(Integer, default=0)

    # Popular Content
    top_documents = Column(JSON, nullable=True, default=list)
    top_services = Column(JSON, nullable=True, default=list)
    top_search_terms = Column(JSON, nullable=True, default=list)

    # Agent Usage
    agent_queries = Column(Integer, default=0)
    human_queries = Column(Integer, default=0)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    company = relationship("Company")

    def __repr__(self):
        return f"<KnowledgeAnalytics(company_id={self.company_id}, date='{self.date}')>"
