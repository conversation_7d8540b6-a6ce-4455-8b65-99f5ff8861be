"""
Knowledge Base CRUD operations.
"""

import logging
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import and_, desc, func, or_, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload

from modules.companies.models import Company

from . import models, schemas

logger = logging.getLogger(__name__)


# Knowledge Document CRUD
async def get_knowledge_document(
    db: AsyncSession, document_id: int, company_id: int
) -> Optional[models.KnowledgeDocument]:
    """Get a single knowledge document by ID."""
    result = await db.execute(
        select(models.KnowledgeDocument).filter(
            and_(
                models.KnowledgeDocument.id == document_id,
                models.KnowledgeDocument.company_id == company_id,
            )
        )
    )
    return result.scalars().first()


async def get_knowledge_documents(
    db: AsyncSession,
    company_id: int,
    skip: int = 0,
    limit: int = 100,
    document_type: Optional[schemas.DocumentType] = None,
    status: Optional[schemas.DocumentStatus] = None,
    category: Optional[str] = None,
    search: Optional[str] = None,
) -> List[models.KnowledgeDocument]:
    """Get knowledge documents with filtering and pagination."""
    query = select(models.KnowledgeDocument).filter(
        models.KnowledgeDocument.company_id == company_id
    )

    # Apply filters
    if document_type:
        query = query.filter(
            models.KnowledgeDocument.document_type == document_type.value
        )

    if status:
        query = query.filter(models.KnowledgeDocument.status == status.value)

    if category:
        query = query.filter(models.KnowledgeDocument.category == category)

    if search:
        query = query.filter(
            or_(
                models.KnowledgeDocument.title.ilike(f"%{search}%"),
                models.KnowledgeDocument.description.ilike(f"%{search}%"),
                models.KnowledgeDocument.content.ilike(f"%{search}%"),
            )
        )

    # Apply pagination and ordering
    query = (
        query.order_by(desc(models.KnowledgeDocument.created_at))
        .offset(skip)
        .limit(limit)
    )

    result = await db.execute(query)
    return result.scalars().all()


async def get_knowledge_documents_count(
    db: AsyncSession,
    company_id: int,
    document_type: Optional[schemas.DocumentType] = None,
    status: Optional[schemas.DocumentStatus] = None,
    category: Optional[str] = None,
    search: Optional[str] = None,
) -> int:
    """Get total count of knowledge documents with filters."""
    query = select(func.count(models.KnowledgeDocument.id)).filter(
        models.KnowledgeDocument.company_id == company_id
    )

    if document_type:
        query = query.filter(
            models.KnowledgeDocument.document_type == document_type.value
        )

    if status:
        query = query.filter(models.KnowledgeDocument.status == status.value)

    if category:
        query = query.filter(models.KnowledgeDocument.category == category)

    if search:
        query = query.filter(
            or_(
                models.KnowledgeDocument.title.ilike(f"%{search}%"),
                models.KnowledgeDocument.description.ilike(f"%{search}%"),
                models.KnowledgeDocument.content.ilike(f"%{search}%"),
            )
        )

    result = await db.execute(query)
    return result.scalar()


async def create_knowledge_document(
    db: AsyncSession,
    document: schemas.KnowledgeDocumentCreate,
    created_by: Optional[int] = None,
) -> models.KnowledgeDocument:
    """Create a new knowledge document."""
    db_document = models.KnowledgeDocument(
        **document.model_dump(), created_by=created_by
    )

    db.add(db_document)
    await db.commit()
    await db.refresh(db_document)

    logger.info(
        f"Created knowledge document {db_document.id} for company {document.company_id}"
    )
    return db_document


async def update_knowledge_document(
    db: AsyncSession,
    document_id: int,
    company_id: int,
    document_update: schemas.KnowledgeDocumentUpdate,
) -> Optional[models.KnowledgeDocument]:
    """Update an existing knowledge document."""
    db_document = await get_knowledge_document(db, document_id, company_id)
    if not db_document:
        return None

    # Update fields
    update_data = document_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_document, field, value)

    await db.commit()
    await db.refresh(db_document)

    logger.info(f"Updated knowledge document {document_id}")
    return db_document


async def delete_knowledge_document(
    db: AsyncSession, document_id: int, company_id: int
) -> bool:
    """Delete a knowledge document."""
    db_document = await get_knowledge_document(db, document_id, company_id)
    if not db_document:
        return False

    await db.delete(db_document)
    await db.commit()

    logger.info(f"Deleted knowledge document {document_id}")
    return True


# Service Catalog CRUD
async def get_service_catalog_item(
    db: AsyncSession, service_id: int, company_id: int
) -> Optional[models.ServiceCatalogItem]:
    """Get a single service catalog item by ID."""
    result = await db.execute(
        select(models.ServiceCatalogItem).filter(
            and_(
                models.ServiceCatalogItem.id == service_id,
                models.ServiceCatalogItem.company_id == company_id,
            )
        )
    )
    return result.scalars().first()


async def get_service_catalog_items(
    db: AsyncSession,
    company_id: int,
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = None,
    is_active: Optional[bool] = None,
    featured: Optional[bool] = None,
    search: Optional[str] = None,
) -> List[models.ServiceCatalogItem]:
    """Get service catalog items with filtering and pagination."""
    query = select(models.ServiceCatalogItem).filter(
        models.ServiceCatalogItem.company_id == company_id
    )

    # Apply filters
    if category:
        query = query.filter(models.ServiceCatalogItem.category == category)

    if is_active is not None:
        query = query.filter(models.ServiceCatalogItem.is_active == is_active)

    if featured is not None:
        query = query.filter(models.ServiceCatalogItem.featured == featured)

    if search:
        query = query.filter(
            or_(
                models.ServiceCatalogItem.name.ilike(f"%{search}%"),
                models.ServiceCatalogItem.description.ilike(f"%{search}%"),
                models.ServiceCatalogItem.category.ilike(f"%{search}%"),
            )
        )

    # Apply pagination and ordering
    query = (
        query.order_by(
            desc(models.ServiceCatalogItem.featured),
            models.ServiceCatalogItem.category,
            models.ServiceCatalogItem.name,
        )
        .offset(skip)
        .limit(limit)
    )

    result = await db.execute(query)
    return result.scalars().all()


async def get_service_catalog_items_count(
    db: AsyncSession,
    company_id: int,
    category: Optional[str] = None,
    is_active: Optional[bool] = None,
    featured: Optional[bool] = None,
    search: Optional[str] = None,
) -> int:
    """Get total count of service catalog items with filters."""
    query = select(func.count(models.ServiceCatalogItem.id)).filter(
        models.ServiceCatalogItem.company_id == company_id
    )

    if category:
        query = query.filter(models.ServiceCatalogItem.category == category)

    if is_active is not None:
        query = query.filter(models.ServiceCatalogItem.is_active == is_active)

    if featured is not None:
        query = query.filter(models.ServiceCatalogItem.featured == featured)

    if search:
        query = query.filter(
            or_(
                models.ServiceCatalogItem.name.ilike(f"%{search}%"),
                models.ServiceCatalogItem.description.ilike(f"%{search}%"),
                models.ServiceCatalogItem.category.ilike(f"%{search}%"),
            )
        )

    result = await db.execute(query)
    return result.scalar()


async def create_service_catalog_item(
    db: AsyncSession,
    service: schemas.ServiceCatalogItemCreate,
    created_by: Optional[int] = None,
) -> models.ServiceCatalogItem:
    """Create a new service catalog item."""
    db_service = models.ServiceCatalogItem(
        **service.model_dump(), created_by=created_by
    )

    db.add(db_service)
    await db.commit()
    await db.refresh(db_service)

    logger.info(
        f"Created service catalog item {db_service.id} for company {service.company_id}"
    )
    return db_service


async def update_service_catalog_item(
    db: AsyncSession,
    service_id: int,
    company_id: int,
    service_update: schemas.ServiceCatalogItemUpdate,
) -> Optional[models.ServiceCatalogItem]:
    """Update an existing service catalog item."""
    db_service = await get_service_catalog_item(db, service_id, company_id)
    if not db_service:
        return None

    # Update fields
    update_data = service_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_service, field, value)

    await db.commit()
    await db.refresh(db_service)

    logger.info(f"Updated service catalog item {service_id}")
    return db_service


async def delete_service_catalog_item(
    db: AsyncSession, service_id: int, company_id: int
) -> bool:
    """Delete a service catalog item."""
    db_service = await get_service_catalog_item(db, service_id, company_id)
    if not db_service:
        return False

    await db.delete(db_service)
    await db.commit()

    logger.info(f"Deleted service catalog item {service_id}")
    return True


# FAQ CRUD
async def get_faq(
    db: AsyncSession, faq_id: int, company_id: int
) -> Optional[models.FAQ]:
    """Get a single FAQ by ID."""
    result = await db.execute(
        select(models.FAQ).filter(
            and_(models.FAQ.id == faq_id, models.FAQ.company_id == company_id)
        )
    )
    return result.scalars().first()


async def get_faqs(
    db: AsyncSession,
    company_id: int,
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = None,
    is_active: Optional[bool] = None,
    is_featured: Optional[bool] = None,
    search: Optional[str] = None,
) -> List[models.FAQ]:
    """Get FAQs with filtering and pagination."""
    query = select(models.FAQ).filter(models.FAQ.company_id == company_id)

    # Apply filters
    if category:
        query = query.filter(models.FAQ.category == category)

    if is_active is not None:
        query = query.filter(models.FAQ.is_active == is_active)

    if is_featured is not None:
        query = query.filter(models.FAQ.is_featured == is_featured)

    if search:
        query = query.filter(
            or_(
                models.FAQ.question.ilike(f"%{search}%"),
                models.FAQ.answer.ilike(f"%{search}%"),
            )
        )

    # Apply pagination and ordering
    query = (
        query.order_by(
            desc(models.FAQ.is_featured),
            models.FAQ.sort_order,
            models.FAQ.category,
            models.FAQ.question,
        )
        .offset(skip)
        .limit(limit)
    )

    result = await db.execute(query)
    return result.scalars().all()


async def get_faqs_count(
    db: AsyncSession,
    company_id: int,
    category: Optional[str] = None,
    is_active: Optional[bool] = None,
    is_featured: Optional[bool] = None,
    search: Optional[str] = None,
) -> int:
    """Get total count of FAQs with filters."""
    query = select(func.count(models.FAQ.id)).filter(
        models.FAQ.company_id == company_id
    )

    if category:
        query = query.filter(models.FAQ.category == category)

    if is_active is not None:
        query = query.filter(models.FAQ.is_active == is_active)

    if is_featured is not None:
        query = query.filter(models.FAQ.is_featured == is_featured)

    if search:
        query = query.filter(
            or_(
                models.FAQ.question.ilike(f"%{search}%"),
                models.FAQ.answer.ilike(f"%{search}%"),
            )
        )

    result = await db.execute(query)
    return result.scalar()


async def create_faq(
    db: AsyncSession, faq: schemas.FAQCreate, created_by: Optional[int] = None
) -> models.FAQ:
    """Create a new FAQ."""
    db_faq = models.FAQ(**faq.model_dump(), created_by=created_by)

    db.add(db_faq)
    await db.commit()
    await db.refresh(db_faq)

    logger.info(f"Created FAQ {db_faq.id} for company {faq.company_id}")
    return db_faq


async def update_faq(
    db: AsyncSession, faq_id: int, company_id: int, faq_update: schemas.FAQUpdate
) -> Optional[models.FAQ]:
    """Update an existing FAQ."""
    db_faq = await get_faq(db, faq_id, company_id)
    if not db_faq:
        return None

    # Update fields
    update_data = faq_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_faq, field, value)

    await db.commit()
    await db.refresh(db_faq)

    logger.info(f"Updated FAQ {faq_id}")
    return db_faq


async def delete_faq(db: AsyncSession, faq_id: int, company_id: int) -> bool:
    """Delete a FAQ."""
    db_faq = await get_faq(db, faq_id, company_id)
    if not db_faq:
        return False

    await db.delete(db_faq)
    await db.commit()

    logger.info(f"Deleted FAQ {faq_id}")
    return True
