"""
Review Management Models

Database models for managing customer reviews and responses.
"""

from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, ForeignKey, Float, <PERSON><PERSON>an, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum as PyEnum

from core.db.database import Base


class ReviewPlatform(PyEnum):
    """Review platforms."""
    GOOGLE_MY_BUSINESS = "google_my_business"
    YELP = "yelp"
    FACEBOOK = "facebook"
    TRUSTPILOT = "trustpilot"


class ReviewStatus(PyEnum):
    """Review response status."""
    PENDING = "pending"
    RESPONDED = "responded"
    FLAGGED = "flagged"
    ARCHIVED = "archived"


class Review(Base):
    """Customer review model."""
    
    __tablename__ = "reviews"
    
    id = Column(Integer, primary_key=True, index=True)
    business_profile_id = Column(Integer, ForeignKey("business_profiles.id"), nullable=False)
    
    # Review details
    platform = Column(String(50), nullable=False)  # ReviewPlatform enum
    platform_review_id = Column(String(255), unique=True, nullable=False)
    
    # Customer information
    customer_name = Column(String(255), nullable=False)
    customer_avatar_url = Column(String(500))
    
    # Review content
    rating = Column(Float, nullable=False)  # 1-5 stars
    title = Column(String(500))
    content = Column(Text, nullable=False)
    
    # Metadata
    review_date = Column(DateTime(timezone=True), nullable=False)
    status = Column(String(50), default=ReviewStatus.PENDING.value)
    
    # Response
    response_text = Column(Text)
    response_date = Column(DateTime(timezone=True))
    responded_by = Column(Integer, ForeignKey("users.id"))
    
    # Platform-specific data
    platform_data = Column(JSON)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    business_profile = relationship("BusinessProfile", back_populates="reviews")
    responder = relationship("User")


class ReviewTemplate(Base):
    """Pre-written response templates for reviews."""
    
    __tablename__ = "review_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    business_profile_id = Column(Integer, ForeignKey("business_profiles.id"))
    
    # Template details
    name = Column(String(255), nullable=False)
    category = Column(String(100))  # positive, negative, neutral
    rating_range = Column(String(20))  # "1-2", "3", "4-5"
    
    # Template content
    template_text = Column(Text, nullable=False)
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User")
    business_profile = relationship("BusinessProfile")


class ReviewAlert(Base):
    """Alerts for new reviews or review milestones."""
    
    __tablename__ = "review_alerts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    business_profile_id = Column(Integer, ForeignKey("business_profiles.id"), nullable=False)
    
    # Alert details
    alert_type = Column(String(100), nullable=False)  # new_review, low_rating, milestone
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    
    # Related review (if applicable)
    review_id = Column(Integer, ForeignKey("reviews.id"))
    
    # Status
    is_read = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User")
    business_profile = relationship("BusinessProfile")
    review = relationship("Review")


# Add relationship to BusinessProfile model
# This would be added to the existing BusinessProfile model
"""
# Add to BusinessProfile model:
reviews = relationship("Review", back_populates="business_profile")
"""
