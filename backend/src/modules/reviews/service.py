"""
Review Management Service

Handles Google My Business integration and review management.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from decimal import Decimal

import httpx
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc

from core.config import get_settings
from .models import Review, ReviewTemplate, ReviewAlert, ReviewPlatform, ReviewStatus

logger = logging.getLogger(__name__)


class ReviewManagementService:
    """Service for managing customer reviews and responses."""
    
    def __init__(self):
        self.settings = get_settings()
        self.gmb_base_url = "https://mybusiness.googleapis.com/v4"
        self.client = httpx.AsyncClient()
    
    async def get_gmb_oauth_url(self, user_id: int, redirect_uri: str) -> str:
        """Generate OAuth URL for Google My Business authorization."""
        
        # In production, use actual Google OAuth credentials
        client_id = "your_google_oauth_client_id"
        
        oauth_url = (
            f"https://accounts.google.com/o/oauth2/v2/auth?"
            f"client_id={client_id}&"
            f"redirect_uri={redirect_uri}&"
            f"scope=https://www.googleapis.com/auth/business.manage&"
            f"response_type=code&"
            f"access_type=offline&"
            f"state={user_id}"
        )
        
        return oauth_url
    
    async def exchange_gmb_code_for_token(self, code: str, redirect_uri: str) -> Dict[str, Any]:
        """Exchange authorization code for GMB access token."""
        
        # Mock implementation - in production, make actual API call
        logger.info(f"Mock: Exchanging GMB code {code[:10]}... for access token")
        
        return {
            "access_token": f"mock_gmb_access_token_{code[:10]}",
            "refresh_token": f"mock_gmb_refresh_token_{code[:10]}",
            "token_type": "Bearer",
            "expires_in": 3600,
            "scope": "https://www.googleapis.com/auth/business.manage"
        }
    
    async def fetch_gmb_reviews(
        self, 
        access_token: str, 
        location_id: str,
        db: AsyncSession,
        business_profile_id: int
    ) -> List[Review]:
        """Fetch reviews from Google My Business."""
        
        try:
            # Mock API call - in production, make actual request to GMB API
            logger.info(f"Mock: Fetching GMB reviews for location {location_id}")
            
            # Simulate GMB API response
            mock_reviews_data = [
                {
                    "name": "accounts/123/locations/456/reviews/review1",
                    "reviewId": "review1",
                    "reviewer": {
                        "displayName": "John Smith",
                        "profilePhotoUrl": "https://example.com/avatar1.jpg"
                    },
                    "starRating": "FIVE",
                    "comment": "Excellent service! Very professional and arrived on time. Highly recommend for any plumbing needs.",
                    "createTime": "2024-01-15T10:30:00Z",
                    "updateTime": "2024-01-15T10:30:00Z"
                },
                {
                    "name": "accounts/123/locations/456/reviews/review2",
                    "reviewId": "review2",
                    "reviewer": {
                        "displayName": "Sarah Johnson",
                        "profilePhotoUrl": "https://example.com/avatar2.jpg"
                    },
                    "starRating": "FOUR",
                    "comment": "Good work overall. Technician was knowledgeable and fixed the issue quickly. Price was fair.",
                    "createTime": "2024-01-10T14:20:00Z",
                    "updateTime": "2024-01-10T14:20:00Z"
                },
                {
                    "name": "accounts/123/locations/456/reviews/review3",
                    "reviewId": "review3",
                    "reviewer": {
                        "displayName": "Mike Davis",
                        "profilePhotoUrl": "https://example.com/avatar3.jpg"
                    },
                    "starRating": "TWO",
                    "comment": "Service was delayed and communication could have been better. Work quality was okay but expected more for the price.",
                    "createTime": "2024-01-08T16:45:00Z",
                    "updateTime": "2024-01-08T16:45:00Z"
                }
            ]
            
            # Convert to our Review models
            reviews = []
            for review_data in mock_reviews_data:
                # Check if review already exists
                existing_review = await db.execute(
                    select(Review).where(
                        Review.platform_review_id == review_data["reviewId"]
                    )
                )
                if existing_review.scalar_one_or_none():
                    continue
                
                # Convert star rating
                star_mapping = {
                    "ONE": 1.0, "TWO": 2.0, "THREE": 3.0, "FOUR": 4.0, "FIVE": 5.0
                }
                rating = star_mapping.get(review_data["starRating"], 3.0)
                
                # Create review record
                review = Review(
                    business_profile_id=business_profile_id,
                    platform=ReviewPlatform.GOOGLE_MY_BUSINESS.value,
                    platform_review_id=review_data["reviewId"],
                    customer_name=review_data["reviewer"]["displayName"],
                    customer_avatar_url=review_data["reviewer"].get("profilePhotoUrl"),
                    rating=rating,
                    content=review_data["comment"],
                    review_date=datetime.fromisoformat(review_data["createTime"].replace('Z', '+00:00')),
                    status=ReviewStatus.PENDING.value,
                    platform_data=review_data
                )
                
                db.add(review)
                reviews.append(review)
            
            await db.commit()
            
            # Create alerts for new reviews
            for review in reviews:
                await self._create_review_alert(db, review, business_profile_id)
            
            return reviews
            
        except Exception as e:
            logger.error(f"Error fetching GMB reviews: {str(e)}")
            return []
    
    async def respond_to_review(
        self,
        db: AsyncSession,
        review_id: int,
        response_text: str,
        user_id: int,
        access_token: Optional[str] = None
    ) -> bool:
        """Respond to a customer review."""
        
        try:
            # Get review
            result = await db.execute(select(Review).where(Review.id == review_id))
            review = result.scalar_one_or_none()
            
            if not review:
                return False
            
            # Mock API call to post response
            if review.platform == ReviewPlatform.GOOGLE_MY_BUSINESS.value and access_token:
                logger.info(f"Mock: Posting response to GMB review {review.platform_review_id}")
                # In production, make actual API call to GMB
            
            # Update review with response
            review.response_text = response_text
            review.response_date = datetime.now()
            review.responded_by = user_id
            review.status = ReviewStatus.RESPONDED.value
            
            await db.commit()
            
            return True
            
        except Exception as e:
            logger.error(f"Error responding to review: {str(e)}")
            return False
    
    async def get_reviews_for_business(
        self,
        db: AsyncSession,
        business_profile_id: int,
        platform: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Review]:
        """Get reviews for a business profile."""
        
        try:
            query = select(Review).where(Review.business_profile_id == business_profile_id)
            
            if platform:
                query = query.where(Review.platform == platform)
            
            if status:
                query = query.where(Review.status == status)
            
            query = query.order_by(desc(Review.review_date)).limit(limit).offset(offset)
            
            result = await db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Error getting reviews: {str(e)}")
            return []
    
    async def get_review_analytics(
        self,
        db: AsyncSession,
        business_profile_id: int,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get review analytics for a business."""
        
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # Get reviews in date range
            result = await db.execute(
                select(Review).where(
                    and_(
                        Review.business_profile_id == business_profile_id,
                        Review.review_date >= start_date,
                        Review.review_date <= end_date
                    )
                )
            )
            reviews = result.scalars().all()
            
            if not reviews:
                return {
                    "total_reviews": 0,
                    "average_rating": 0,
                    "rating_distribution": {},
                    "response_rate": 0,
                    "recent_reviews": []
                }
            
            # Calculate metrics
            total_reviews = len(reviews)
            average_rating = sum(r.rating for r in reviews) / total_reviews
            
            # Rating distribution
            rating_distribution = {}
            for i in range(1, 6):
                count = len([r for r in reviews if int(r.rating) == i])
                rating_distribution[str(i)] = count
            
            # Response rate
            responded_reviews = len([r for r in reviews if r.response_text])
            response_rate = (responded_reviews / total_reviews * 100) if total_reviews > 0 else 0
            
            # Recent reviews
            recent_reviews = sorted(reviews, key=lambda x: x.review_date, reverse=True)[:5]
            recent_reviews_data = [
                {
                    "id": r.id,
                    "customer_name": r.customer_name,
                    "rating": r.rating,
                    "content": r.content[:100] + "..." if len(r.content) > 100 else r.content,
                    "review_date": r.review_date.isoformat(),
                    "platform": r.platform,
                    "has_response": bool(r.response_text)
                }
                for r in recent_reviews
            ]
            
            return {
                "total_reviews": total_reviews,
                "average_rating": round(average_rating, 2),
                "rating_distribution": rating_distribution,
                "response_rate": round(response_rate, 1),
                "recent_reviews": recent_reviews_data,
                "period_days": days
            }
            
        except Exception as e:
            logger.error(f"Error getting review analytics: {str(e)}")
            return {}
    
    async def create_review_template(
        self,
        db: AsyncSession,
        user_id: int,
        business_profile_id: Optional[int],
        template_data: Dict[str, Any]
    ) -> ReviewTemplate:
        """Create a review response template."""
        
        try:
            template = ReviewTemplate(
                user_id=user_id,
                business_profile_id=business_profile_id,
                name=template_data["name"],
                category=template_data.get("category"),
                rating_range=template_data.get("rating_range"),
                template_text=template_data["template_text"]
            )
            
            db.add(template)
            await db.commit()
            await db.refresh(template)
            
            return template
            
        except Exception as e:
            logger.error(f"Error creating review template: {str(e)}")
            raise
    
    async def get_review_templates(
        self,
        db: AsyncSession,
        user_id: int,
        business_profile_id: Optional[int] = None,
        category: Optional[str] = None
    ) -> List[ReviewTemplate]:
        """Get review response templates."""
        
        try:
            query = select(ReviewTemplate).where(ReviewTemplate.user_id == user_id)
            
            if business_profile_id:
                query = query.where(
                    (ReviewTemplate.business_profile_id == business_profile_id) |
                    (ReviewTemplate.business_profile_id.is_(None))
                )
            
            if category:
                query = query.where(ReviewTemplate.category == category)
            
            query = query.order_by(ReviewTemplate.usage_count.desc())
            
            result = await db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Error getting review templates: {str(e)}")
            return []
    
    async def suggest_review_response(
        self,
        review: Review,
        business_name: str,
        templates: List[ReviewTemplate]
    ) -> str:
        """Generate AI-suggested response to a review."""
        
        try:
            # Determine response category based on rating
            if review.rating >= 4:
                category = "positive"
            elif review.rating >= 3:
                category = "neutral"
            else:
                category = "negative"
            
            # Find matching template
            matching_templates = [
                t for t in templates 
                if t.category == category or not t.category
            ]
            
            if matching_templates:
                # Use most used template as base
                base_template = matching_templates[0]
                response = base_template.template_text
                
                # Simple template variable replacement
                response = response.replace("{customer_name}", review.customer_name)
                response = response.replace("{business_name}", business_name)
                
                return response
            
            # Fallback responses
            fallback_responses = {
                "positive": f"Thank you {review.customer_name} for your wonderful review! We're thrilled that you had a great experience with {business_name}. We look forward to serving you again!",
                "neutral": f"Thank you {review.customer_name} for taking the time to review {business_name}. We appreciate your feedback and are always working to improve our service.",
                "negative": f"Thank you {review.customer_name} for your feedback. We take all reviews seriously and would like to make this right. Please contact us directly so we can address your concerns."
            }
            
            return fallback_responses.get(category, "Thank you for your review!")
            
        except Exception as e:
            logger.error(f"Error suggesting review response: {str(e)}")
            return "Thank you for your review!"
    
    async def _create_review_alert(
        self,
        db: AsyncSession,
        review: Review,
        business_profile_id: int
    ):
        """Create alert for new review."""
        
        try:
            # Determine alert type
            if review.rating <= 2:
                alert_type = "low_rating"
                title = "Low Rating Alert"
                message = f"New {review.rating}-star review from {review.customer_name} needs attention."
            else:
                alert_type = "new_review"
                title = "New Review"
                message = f"New {review.rating}-star review from {review.customer_name}."
            
            alert = ReviewAlert(
                user_id=1,  # This should be the business owner's user_id
                business_profile_id=business_profile_id,
                alert_type=alert_type,
                title=title,
                message=message,
                review_id=review.id
            )
            
            db.add(alert)
            await db.commit()
            
        except Exception as e:
            logger.error(f"Error creating review alert: {str(e)}")


# Global service instance
review_service = ReviewManagementService()
