"""
Requests API Router for complete lifecycle management.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.service import get_current_active_user
from modules.companies import crud as company_crud
from modules.users.models import User as DBUser

from . import crud, schemas

logger = logging.getLogger(__name__)

router = APIRouter(tags=["requests"])


@router.get("/requests", response_model=schemas.RequestListResponse)
async def get_requests(
    company_id: int = Query(..., description="Company ID"),
    status: Optional[List[schemas.RequestStatus]] = Query(None),
    request_type: Optional[List[schemas.RequestType]] = Query(None),
    priority: Optional[List[schemas.RequestPriority]] = Query(None),
    assigned_agent_id: Optional[int] = Query(None),
    customer_id: Optional[int] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get requests for a company."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Create filters
    filters = schemas.RequestFilters(
        status=status,
        request_type=request_type,
        priority=priority,
        assigned_agent_id=assigned_agent_id,
        customer_id=customer_id,
    )

    # Get requests and total count
    requests = await crud.get_requests(db, company_id, skip, limit, filters)
    total = await crud.get_request_count(db, company_id, filters)

    return schemas.RequestListResponse(
        requests=requests,
        total=total,
        page=skip // limit + 1,
        per_page=limit,
        has_next=skip + limit < total,
        has_prev=skip > 0,
    )


@router.post("/requests", response_model=schemas.Request)
async def create_request(
    request_data: schemas.RequestCreate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a new request."""
    # Verify company belongs to user
    company = await company_crud.get_company(
        db, request_data.company_id, current_user.id
    )
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Create request
    request = await crud.create_request(db, request_data)
    return request


@router.get("/{request_id}")
async def get_request(
    request_id: int,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get a specific request."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement get request
    raise HTTPException(status_code=404, detail="Request not found")


@router.put("/{request_id}")
async def update_request(
    request_id: int,
    company_id: int,
    request_update: dict,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Update a request."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement request update
    raise HTTPException(status_code=404, detail="Request not found")


@router.get("/{request_id}/activities")
async def get_request_activities(
    request_id: int,
    company_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get activities for a request."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement get request activities
    return []


@router.post("/{request_id}/activities")
async def create_request_activity(
    request_id: int,
    company_id: int,
    activity_data: dict,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create an activity for a request."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement create request activity
    return {"activity_id": "activity_123", "message": "Activity created successfully"}


@router.get("/templates")
async def get_request_templates(
    company_id: int,
    request_type: Optional[str] = None,
    category: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get request templates for a company."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement get request templates
    return []


@router.post("/templates")
async def create_request_template(
    company_id: int,
    template_data: dict,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a request template."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement create request template
    return {
        "template_id": "template_123",
        "message": "Request template created successfully",
    }
