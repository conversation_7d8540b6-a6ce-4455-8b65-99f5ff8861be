"""
Request Management Models for complete lifecycle tracking.

Manages requests from initial contact through service completion.
"""

from enum import Enum as PyEnum

from sqlalchemy import (
    DECIMAL,
    JSON,
    Boolean,
    Column,
    DateTime,
    Float,
    Foreign<PERSON>ey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class RequestStatus(PyEnum):
    """Request lifecycle status."""

    NEW = "new"
    QUALIFIED = "qualified"
    QUOTED = "quoted"
    SCHEDULED = "scheduled"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ON_HOLD = "on_hold"
    FOLLOW_UP = "follow_up"


class RequestPriority(PyEnum):
    """Request priority levels."""

    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"
    EMERGENCY = "emergency"


class RequestSource(PyEnum):
    """Request source channels."""

    PHONE = "phone"
    WEB_FORM = "web_form"
    CHAT = "chat"
    EMAIL = "email"
    SMS = "sms"
    REFERRAL = "referral"
    WALK_IN = "walk_in"
    REPEAT_CUSTOMER = "repeat_customer"


class RequestType(PyEnum):
    """Types of service requests."""

    QUOTE = "quote"
    SERVICE_CALL = "service_call"
    EMERGENCY = "emergency"
    MAINTENANCE = "maintenance"
    INSTALLATION = "installation"
    CONSULTATION = "consultation"
    FOLLOW_UP = "follow_up"


class Request(Base):
    """
    Main request model for complete lifecycle management.
    """

    __tablename__ = "requests"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Request Identification
    request_number = Column(String(50), nullable=False, unique=True, index=True)
    external_reference = Column(String(100), nullable=True, index=True)

    # Request Classification
    request_type = Column(
        String(50), nullable=False, default=RequestType.SERVICE_CALL.value
    )
    status = Column(
        String(50), nullable=False, default=RequestStatus.NEW.value, index=True
    )
    priority = Column(String(50), nullable=False, default=RequestPriority.NORMAL.value)

    # Source Information
    source = Column(
        String(50), nullable=False, default=RequestSource.PHONE.value, index=True
    )
    source_details = Column(JSON, nullable=True, default=dict)

    # Customer Information
    customer_name = Column(String(255), nullable=False)
    customer_phone = Column(String(20), nullable=True, index=True)
    customer_email = Column(String(255), nullable=True, index=True)
    customer_company = Column(String(255), nullable=True)

    # Service Location
    service_address = Column(Text, nullable=True)
    service_city = Column(String(100), nullable=True)
    service_state = Column(String(50), nullable=True)
    service_zip = Column(String(20), nullable=True)
    service_country = Column(String(100), default="US")
    location_notes = Column(Text, nullable=True)

    # Service Details
    service_description = Column(Text, nullable=False)
    service_category = Column(String(100), nullable=True, index=True)
    service_urgency = Column(String(50), nullable=True)

    # Related Records
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=True)
    booking_request_id = Column(
        Integer, ForeignKey("booking_requests.id"), nullable=True
    )
    web_form_submission_id = Column(
        Integer, ForeignKey("web_form_submissions.id"), nullable=True
    )
    chat_session_id = Column(Integer, ForeignKey("chat_sessions.id"), nullable=True)

    # Assignment & Scheduling
    assigned_agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    assigned_team = Column(String(100), nullable=True)

    # Scheduling Information
    preferred_date = Column(DateTime(timezone=True), nullable=True)
    preferred_time = Column(String(50), nullable=True)
    scheduled_date = Column(DateTime(timezone=True), nullable=True, index=True)
    estimated_duration = Column(Integer, nullable=True)  # minutes

    # Financial Information
    estimated_cost = Column(DECIMAL(10, 2), nullable=True)
    quoted_price = Column(DECIMAL(10, 2), nullable=True)
    final_price = Column(DECIMAL(10, 2), nullable=True)
    deposit_amount = Column(DECIMAL(10, 2), nullable=True)
    payment_terms = Column(String(100), nullable=True)

    # Requirements & Preparation
    materials_needed = Column(JSON, nullable=True, default=list)
    tools_required = Column(JSON, nullable=True, default=list)
    permits_required = Column(JSON, nullable=True, default=list)
    special_requirements = Column(Text, nullable=True)
    access_instructions = Column(Text, nullable=True)

    # Communication Preferences
    preferred_contact_method = Column(String(50), default="phone")
    best_contact_time = Column(String(100), nullable=True)
    communication_notes = Column(Text, nullable=True)

    # Quality & Satisfaction
    customer_satisfaction = Column(Integer, nullable=True)  # 1-5 rating
    quality_score = Column(Float, nullable=True)
    completion_notes = Column(Text, nullable=True)

    # Follow-up Information
    follow_up_required = Column(Boolean, default=False)
    follow_up_date = Column(DateTime(timezone=True), nullable=True)
    follow_up_notes = Column(Text, nullable=True)
    warranty_period = Column(Integer, nullable=True)  # days

    # Business Intelligence
    lead_score = Column(Integer, nullable=True)  # 0-100
    conversion_probability = Column(Float, nullable=True)  # 0-1
    lifetime_value_estimate = Column(DECIMAL(10, 2), nullable=True)

    # Metadata & Tags
    tags = Column(JSON, nullable=True, default=list)
    custom_fields = Column(JSON, nullable=True, default=dict)
    internal_notes = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    company = relationship("Company")
    conversation = relationship("Conversation")
    booking_request = relationship("BookingRequest")
    form_submission = relationship("WebFormSubmission")
    chat_session = relationship("ChatSession")
    assigned_agent = relationship("Agent")
    user = relationship("User", foreign_keys=[user_id])
    activities = relationship(
        "RequestActivity", back_populates="request", cascade="all, delete-orphan"
    )
    status_history = relationship(
        "RequestStatusHistory", back_populates="request", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<Request(id={self.id}, request_number='{self.request_number}', status='{self.status}')>"


class RequestActivity(Base):
    """
    Activity log for request lifecycle tracking.
    """

    __tablename__ = "request_activities"

    id = Column(Integer, primary_key=True, index=True)
    request_id = Column(Integer, ForeignKey("requests.id"), nullable=False)

    # Activity Information
    activity_type = Column(
        String(50), nullable=False
    )  # call, email, visit, update, etc.
    activity_description = Column(Text, nullable=False)

    # Activity Details
    duration_minutes = Column(Integer, nullable=True)
    outcome = Column(String(100), nullable=True)
    next_action = Column(String(255), nullable=True)

    # Actor Information
    performed_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    performed_by_agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True)
    performed_by_system = Column(Boolean, default=False)

    # Activity Context
    contact_method = Column(String(50), nullable=True)  # phone, email, in_person, etc.
    location = Column(String(255), nullable=True)

    # Attachments & Files
    attachments = Column(JSON, nullable=True, default=list)

    # Billing Information
    billable = Column(Boolean, default=False)
    billable_amount = Column(DECIMAL(8, 2), nullable=True)

    # Metadata
    extra_metadata = Column(JSON, nullable=True, default=dict)

    # Timestamps
    activity_date = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    request = relationship("Request", back_populates="activities")
    performed_by_user = relationship("User")
    performed_by_agent = relationship("Agent")

    def __repr__(self):
        return f"<RequestActivity(id={self.id}, type='{self.activity_type}', request_id={self.request_id})>"


class RequestStatusHistory(Base):
    """
    History of status changes for requests.
    """

    __tablename__ = "request_status_history"

    id = Column(Integer, primary_key=True, index=True)
    request_id = Column(Integer, ForeignKey("requests.id"), nullable=False)

    # Status Change Information
    from_status = Column(String(50), nullable=True)
    to_status = Column(String(50), nullable=False)
    change_reason = Column(String(255), nullable=True)
    notes = Column(Text, nullable=True)

    # Change Context
    changed_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    changed_by_agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True)
    changed_by_system = Column(Boolean, default=False)

    # Timing
    duration_in_previous_status = Column(Integer, nullable=True)  # seconds

    # Timestamp
    changed_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    request = relationship("Request", back_populates="status_history")
    changed_by_user = relationship("User")
    changed_by_agent = relationship("Agent")

    def __repr__(self):
        return f"<RequestStatusHistory(id={self.id}, from='{self.from_status}', to='{self.to_status}')>"


class RequestTemplate(Base):
    """
    Templates for common request types and workflows.
    """

    __tablename__ = "request_templates"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Template Information
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    request_type = Column(String(50), nullable=False)
    category = Column(String(100), nullable=True)

    # Default Values
    default_priority = Column(String(50), default=RequestPriority.NORMAL.value)
    default_duration = Column(Integer, nullable=True)  # minutes
    default_cost_estimate = Column(DECIMAL(10, 2), nullable=True)

    # Workflow Configuration
    workflow_steps = Column(JSON, nullable=True, default=list)
    required_fields = Column(JSON, nullable=True, default=list)
    optional_fields = Column(JSON, nullable=True, default=list)

    # Automation Rules
    auto_assignment_rules = Column(JSON, nullable=True, default=dict)
    notification_rules = Column(JSON, nullable=True, default=dict)

    # Template Content
    default_description = Column(Text, nullable=True)
    email_templates = Column(JSON, nullable=True, default=dict)
    sms_templates = Column(JSON, nullable=True, default=dict)

    # Usage Statistics
    times_used = Column(Integer, default=0)
    success_rate = Column(Float, nullable=True)  # percentage
    average_completion_time = Column(Integer, nullable=True)  # hours

    # Status
    is_active = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationships
    company = relationship("Company")
    user = relationship("User", foreign_keys=[user_id])

    def __repr__(self):
        return f"<RequestTemplate(id={self.id}, name='{self.name}', type='{self.request_type}')>"


class RequestAnalytics(Base):
    """
    Analytics for request management performance.
    """

    __tablename__ = "request_analytics"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Time Period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False, default="daily")

    # Volume Metrics
    total_requests = Column(Integer, default=0)
    new_requests = Column(Integer, default=0)
    completed_requests = Column(Integer, default=0)
    cancelled_requests = Column(Integer, default=0)

    # Source Breakdown
    phone_requests = Column(Integer, default=0)
    web_requests = Column(Integer, default=0)
    chat_requests = Column(Integer, default=0)
    referral_requests = Column(Integer, default=0)

    # Performance Metrics
    average_response_time = Column(Integer, default=0)  # minutes
    average_completion_time = Column(Integer, default=0)  # hours
    first_call_resolution_rate = Column(Float, default=0.0)  # percentage

    # Conversion Metrics
    quote_to_job_conversion = Column(Float, default=0.0)  # percentage
    lead_to_customer_conversion = Column(Float, default=0.0)  # percentage

    # Financial Metrics
    total_quoted_value = Column(DECIMAL(12, 2), default=0.0)
    total_completed_value = Column(DECIMAL(12, 2), default=0.0)
    average_job_value = Column(DECIMAL(10, 2), default=0.0)

    # Quality Metrics
    average_customer_satisfaction = Column(Float, nullable=True)
    quality_score_average = Column(Float, nullable=True)

    # Efficiency Metrics
    technician_utilization = Column(Float, default=0.0)  # percentage
    schedule_efficiency = Column(Float, default=0.0)  # percentage

    # Popular Services
    top_service_categories = Column(JSON, nullable=True, default=list)
    top_request_types = Column(JSON, nullable=True, default=list)

    # Geographic Data
    service_areas = Column(JSON, nullable=True, default=list)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    company = relationship("Company")

    def __repr__(self):
        return f"<RequestAnalytics(company_id={self.company_id}, date='{self.date}')>"
