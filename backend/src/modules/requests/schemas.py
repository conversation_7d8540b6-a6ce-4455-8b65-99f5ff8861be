"""
Request Schemas for API requests and responses.
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field


class RequestStatus(str, Enum):
    """Request status options."""

    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ON_HOLD = "on_hold"


class RequestPriority(str, Enum):
    """Request priority levels."""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class RequestType(str, Enum):
    """Request types."""

    SERVICE = "service"
    REPAIR = "repair"
    MAINTENANCE = "maintenance"
    CONSULTATION = "consultation"
    EMERGENCY = "emergency"
    QUOTE = "quote"


# Base Request Schemas
class RequestBase(BaseModel):
    title: str = Field(..., max_length=255)
    description: Optional[str] = None
    request_type: RequestType
    priority: RequestPriority = RequestPriority.MEDIUM
    status: RequestStatus = RequestStatus.PENDING
    estimated_cost: Optional[Decimal] = None
    actual_cost: Optional[Decimal] = None
    estimated_duration: Optional[int] = None  # in minutes
    actual_duration: Optional[int] = None  # in minutes
    scheduled_date: Optional[datetime] = None
    completed_date: Optional[datetime] = None
    notes: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class RequestCreate(RequestBase):
    company_id: int
    customer_id: Optional[int] = None
    assigned_agent_id: Optional[int] = None


class RequestUpdate(BaseModel):
    title: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    request_type: Optional[RequestType] = None
    priority: Optional[RequestPriority] = None
    status: Optional[RequestStatus] = None
    estimated_cost: Optional[Decimal] = None
    actual_cost: Optional[Decimal] = None
    estimated_duration: Optional[int] = None
    actual_duration: Optional[int] = None
    scheduled_date: Optional[datetime] = None
    completed_date: Optional[datetime] = None
    notes: Optional[str] = None
    assigned_agent_id: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None


class Request(RequestBase):
    id: int
    company_id: int
    customer_id: Optional[int] = None
    assigned_agent_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Request Activity Schemas
class RequestActivityBase(BaseModel):
    activity_type: str = Field(..., max_length=100)
    description: str
    performed_by_user_id: Optional[int] = None
    performed_by_agent_id: Optional[int] = None
    performed_by_system: bool = False
    contact_method: Optional[str] = Field(None, max_length=50)
    location: Optional[str] = Field(None, max_length=255)
    attachments: Optional[List[Dict[str, Any]]] = None
    billable: bool = False
    billable_amount: Optional[Decimal] = None
    extra_metadata: Optional[Dict[str, Any]] = None


class RequestActivityCreate(RequestActivityBase):
    request_id: int


class RequestActivityUpdate(BaseModel):
    description: Optional[str] = None
    contact_method: Optional[str] = None
    location: Optional[str] = None
    attachments: Optional[List[Dict[str, Any]]] = None
    billable: Optional[bool] = None
    billable_amount: Optional[Decimal] = None
    extra_metadata: Optional[Dict[str, Any]] = None


class RequestActivity(RequestActivityBase):
    id: int
    request_id: int
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Response Schemas
class RequestListResponse(BaseModel):
    requests: List[Request]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class RequestWithActivities(Request):
    activities: List[RequestActivity] = []


# Status Update Schema
class RequestStatusUpdate(BaseModel):
    status: RequestStatus
    notes: Optional[str] = None
    completed_date: Optional[datetime] = None
    actual_cost: Optional[Decimal] = None
    actual_duration: Optional[int] = None


# Assignment Schema
class RequestAssignment(BaseModel):
    assigned_agent_id: Optional[int] = None
    notes: Optional[str] = None


# Completion Schema
class RequestCompletion(BaseModel):
    completion_notes: str
    actual_cost: Optional[Decimal] = None
    actual_duration: Optional[int] = None
    customer_satisfaction: Optional[int] = Field(None, ge=1, le=5)
    work_photos: Optional[List[str]] = None  # URLs to photos
    follow_up_required: bool = False
    follow_up_date: Optional[datetime] = None
    warranty_period: Optional[int] = None  # in days
    completion_metadata: Optional[Dict[str, Any]] = None


# Search and Filter Schemas
class RequestFilters(BaseModel):
    status: Optional[List[RequestStatus]] = None
    request_type: Optional[List[RequestType]] = None
    priority: Optional[List[RequestPriority]] = None
    assigned_agent_id: Optional[int] = None
    customer_id: Optional[int] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    search: Optional[str] = None


# Analytics Schemas
class RequestAnalytics(BaseModel):
    total_requests: int
    completed_requests: int
    pending_requests: int
    in_progress_requests: int
    cancelled_requests: int
    average_completion_time: Optional[float] = None  # in hours
    average_cost: Optional[Decimal] = None
    customer_satisfaction_avg: Optional[float] = None
    revenue_total: Optional[Decimal] = None
    requests_by_type: Dict[str, int]
    requests_by_priority: Dict[str, int]
    monthly_trends: List[Dict[str, Any]]


# Template Schemas
class RequestTemplateBase(BaseModel):
    name: str = Field(..., max_length=255)
    description: Optional[str] = None
    request_type: RequestType
    default_priority: RequestPriority = RequestPriority.MEDIUM
    estimated_cost: Optional[Decimal] = None
    estimated_duration: Optional[int] = None
    template_fields: Optional[Dict[str, Any]] = None
    is_active: bool = True


class RequestTemplateCreate(RequestTemplateBase):
    company_id: int


class RequestTemplateUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    request_type: Optional[RequestType] = None
    default_priority: Optional[RequestPriority] = None
    estimated_cost: Optional[Decimal] = None
    estimated_duration: Optional[int] = None
    template_fields: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class RequestTemplate(RequestTemplateBase):
    id: int
    company_id: int
    times_used: int = 0
    last_used_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)
