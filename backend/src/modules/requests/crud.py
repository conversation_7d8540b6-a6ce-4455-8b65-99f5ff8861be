"""
Request CRUD operations.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload

from . import models, schemas

logger = logging.getLogger(__name__)


async def create_request(
    db: AsyncSession, request: schemas.RequestCreate
) -> models.Request:
    """Create a new request."""
    db_request = models.Request(
        title=request.title,
        description=request.description,
        request_type=request.request_type,
        priority=request.priority,
        status=request.status,
        company_id=request.company_id,
        customer_id=request.customer_id,
        assigned_agent_id=request.assigned_agent_id,
        estimated_cost=request.estimated_cost,
        actual_cost=request.actual_cost,
        estimated_duration=request.estimated_duration,
        actual_duration=request.actual_duration,
        scheduled_date=request.scheduled_date,
        completed_date=request.completed_date,
        notes=request.notes,
        metadata=request.metadata or {},
    )
    db.add(db_request)
    await db.commit()
    await db.refresh(db_request)
    return db_request


async def get_request(
    db: AsyncSession, request_id: int, company_id: int, include_activities: bool = False
) -> Optional[models.Request]:
    """Get a single request by ID."""
    query = select(models.Request).filter(
        and_(models.Request.id == request_id, models.Request.company_id == company_id)
    )

    if include_activities:
        query = query.options(selectinload(models.Request.activities))

    result = await db.execute(query)
    return result.scalars().first()


async def get_requests(
    db: AsyncSession,
    company_id: int,
    skip: int = 0,
    limit: int = 100,
    filters: Optional[schemas.RequestFilters] = None,
) -> List[models.Request]:
    """Get requests with filtering."""
    query = select(models.Request).filter(models.Request.company_id == company_id)

    if filters:
        if filters.status:
            query = query.filter(models.Request.status.in_(filters.status))

        if filters.request_type:
            query = query.filter(models.Request.request_type.in_(filters.request_type))

        if filters.priority:
            query = query.filter(models.Request.priority.in_(filters.priority))

        if filters.assigned_agent_id:
            query = query.filter(
                models.Request.assigned_agent_id == filters.assigned_agent_id
            )

        if filters.customer_id:
            query = query.filter(models.Request.customer_id == filters.customer_id)

        if filters.date_from:
            query = query.filter(models.Request.created_at >= filters.date_from)

        if filters.date_to:
            query = query.filter(models.Request.created_at <= filters.date_to)

        if filters.search:
            query = query.filter(
                or_(
                    models.Request.title.ilike(f"%{filters.search}%"),
                    models.Request.description.ilike(f"%{filters.search}%"),
                    models.Request.notes.ilike(f"%{filters.search}%"),
                )
            )

    query = query.order_by(desc(models.Request.created_at))
    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    return result.scalars().all()


async def update_request(
    db: AsyncSession,
    request_id: int,
    company_id: int,
    request_update: schemas.RequestUpdate,
) -> Optional[models.Request]:
    """Update a request."""
    db_request = await get_request(db, request_id, company_id)
    if not db_request:
        return None

    update_data = request_update.dict(exclude_unset=True)

    for field, value in update_data.items():
        setattr(db_request, field, value)

    await db.commit()
    await db.refresh(db_request)
    return db_request


async def delete_request(db: AsyncSession, request_id: int, company_id: int) -> bool:
    """Delete a request."""
    db_request = await get_request(db, request_id, company_id)
    if not db_request:
        return False

    await db.delete(db_request)
    await db.commit()
    return True


async def update_request_status(
    db: AsyncSession,
    request_id: int,
    company_id: int,
    status_update: schemas.RequestStatusUpdate,
) -> Optional[models.Request]:
    """Update request status."""
    db_request = await get_request(db, request_id, company_id)
    if not db_request:
        return None

    db_request.status = status_update.status
    if status_update.notes:
        db_request.notes = status_update.notes
    if status_update.completed_date:
        db_request.completed_date = status_update.completed_date
    if status_update.actual_cost:
        db_request.actual_cost = status_update.actual_cost
    if status_update.actual_duration:
        db_request.actual_duration = status_update.actual_duration

    await db.commit()
    await db.refresh(db_request)
    return db_request


async def assign_request(
    db: AsyncSession,
    request_id: int,
    company_id: int,
    assignment: schemas.RequestAssignment,
) -> Optional[models.Request]:
    """Assign request to an agent."""
    db_request = await get_request(db, request_id, company_id)
    if not db_request:
        return None

    db_request.assigned_agent_id = assignment.assigned_agent_id
    if assignment.notes:
        db_request.notes = assignment.notes

    await db.commit()
    await db.refresh(db_request)
    return db_request


async def complete_request(
    db: AsyncSession,
    request_id: int,
    company_id: int,
    completion: schemas.RequestCompletion,
) -> Optional[models.Request]:
    """Complete a request."""
    db_request = await get_request(db, request_id, company_id)
    if not db_request:
        return None

    db_request.status = schemas.RequestStatus.COMPLETED
    db_request.completed_date = datetime.utcnow()
    db_request.notes = completion.completion_notes

    if completion.actual_cost:
        db_request.actual_cost = completion.actual_cost
    if completion.actual_duration:
        db_request.actual_duration = completion.actual_duration

    # Update metadata with completion info
    metadata = db_request.metadata or {}
    metadata.update(
        {
            "customer_satisfaction": completion.customer_satisfaction,
            "work_photos": completion.work_photos or [],
            "follow_up_required": completion.follow_up_required,
            "follow_up_date": completion.follow_up_date.isoformat()
            if completion.follow_up_date
            else None,
            "warranty_period": completion.warranty_period,
            "completion_metadata": completion.completion_metadata or {},
        }
    )
    db_request.metadata = metadata

    await db.commit()
    await db.refresh(db_request)
    return db_request


async def get_request_count(
    db: AsyncSession, company_id: int, filters: Optional[schemas.RequestFilters] = None
) -> int:
    """Get total count of requests."""
    query = select(func.count(models.Request.id)).filter(
        models.Request.company_id == company_id
    )

    if filters:
        if filters.status:
            query = query.filter(models.Request.status.in_(filters.status))
        if filters.request_type:
            query = query.filter(models.Request.request_type.in_(filters.request_type))
        if filters.priority:
            query = query.filter(models.Request.priority.in_(filters.priority))
        if filters.assigned_agent_id:
            query = query.filter(
                models.Request.assigned_agent_id == filters.assigned_agent_id
            )
        if filters.customer_id:
            query = query.filter(models.Request.customer_id == filters.customer_id)
        if filters.date_from:
            query = query.filter(models.Request.created_at >= filters.date_from)
        if filters.date_to:
            query = query.filter(models.Request.created_at <= filters.date_to)
        if filters.search:
            query = query.filter(
                or_(
                    models.Request.title.ilike(f"%{filters.search}%"),
                    models.Request.description.ilike(f"%{filters.search}%"),
                    models.Request.notes.ilike(f"%{filters.search}%"),
                )
            )

    result = await db.execute(query)
    return result.scalar() or 0


# Request Activity CRUD
async def create_request_activity(
    db: AsyncSession, activity: schemas.RequestActivityCreate
) -> models.RequestActivity:
    """Create a new request activity."""
    db_activity = models.RequestActivity(
        request_id=activity.request_id,
        activity_type=activity.activity_type,
        description=activity.description,
        performed_by_user_id=activity.performed_by_user_id,
        performed_by_agent_id=activity.performed_by_agent_id,
        performed_by_system=activity.performed_by_system,
        contact_method=activity.contact_method,
        location=activity.location,
        attachments=activity.attachments or [],
        billable=activity.billable,
        billable_amount=activity.billable_amount,
        extra_metadata=activity.extra_metadata or {},
    )
    db.add(db_activity)
    await db.commit()
    await db.refresh(db_activity)
    return db_activity


async def get_request_activities(
    db: AsyncSession, request_id: int, company_id: int
) -> List[models.RequestActivity]:
    """Get activities for a request."""
    # First verify the request belongs to the company
    request_exists = await db.execute(
        select(models.Request.id).filter(
            and_(
                models.Request.id == request_id, models.Request.company_id == company_id
            )
        )
    )
    if not request_exists.scalar():
        return []

    query = (
        select(models.RequestActivity)
        .filter(models.RequestActivity.request_id == request_id)
        .order_by(desc(models.RequestActivity.created_at))
    )

    result = await db.execute(query)
    return result.scalars().all()
