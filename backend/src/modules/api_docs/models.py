"""
API Documentation & SDK Models for developer portal and integrations.

Manages API documentation, client SDKs, and developer resources.
"""

from enum import Enum as PyEnum

from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    Float,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class DocumentationType(PyEnum):
    """Types of API documentation."""

    ENDPOINT = "endpoint"
    SCHEMA = "schema"
    GUIDE = "guide"
    TUTORIAL = "tutorial"
    EXAMPLE = "example"
    CHANGELOG = "changelog"
    SDK = "sdk"


class SDKLanguage(PyEnum):
    """Supported SDK languages."""

    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    PHP = "php"
    RUBY = "ruby"
    JAVA = "java"
    CSHARP = "csharp"
    GO = "go"
    CURL = "curl"


class APIEndpoint(Base):
    """
    API endpoint documentation and specifications.
    """

    __tablename__ = "api_endpoints"

    id = Column(Integer, primary_key=True, index=True)

    # Endpoint Information
    path = Column(String(500), nullable=False, index=True)
    method = Column(String(10), nullable=False)  # GET, POST, PUT, DELETE, etc.
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)

    # Categorization
    category = Column(String(100), nullable=True, index=True)
    tags = Column(JSON, nullable=True, default=list)

    # Authentication
    requires_auth = Column(Boolean, default=True)
    auth_types = Column(JSON, nullable=True, default=list)  # api_key, bearer, oauth
    scopes = Column(JSON, nullable=True, default=list)

    # Request Specification
    request_schema = Column(JSON, nullable=True, default=dict)
    request_examples = Column(JSON, nullable=True, default=list)
    query_parameters = Column(JSON, nullable=True, default=list)
    path_parameters = Column(JSON, nullable=True, default=list)
    headers = Column(JSON, nullable=True, default=list)

    # Response Specification
    response_schema = Column(JSON, nullable=True, default=dict)
    response_examples = Column(JSON, nullable=True, default=list)
    status_codes = Column(JSON, nullable=True, default=list)

    # Rate Limiting
    rate_limit = Column(JSON, nullable=True, default=dict)

    # Versioning
    version = Column(String(20), default="v1")
    deprecated = Column(Boolean, default=False)
    deprecation_date = Column(DateTime(timezone=True), nullable=True)

    # Usage Statistics
    total_calls = Column(Integer, default=0)
    unique_users = Column(Integer, default=0)
    error_rate = Column(Float, default=0.0)

    # Status
    is_public = Column(Boolean, default=True)
    is_beta = Column(Boolean, default=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    code_examples = relationship(
        "CodeExample", back_populates="endpoint", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return (
            f"<APIEndpoint(id={self.id}, method='{self.method}', path='{self.path}')>"
        )


class APISchema(Base):
    """
    API data schemas and models documentation.
    """

    __tablename__ = "api_schemas"

    id = Column(Integer, primary_key=True, index=True)

    # Schema Information
    name = Column(String(255), nullable=False, unique=True, index=True)
    description = Column(Text, nullable=False)

    # Schema Definition
    schema_type = Column(String(50), default="object")  # object, array, string, etc.
    properties = Column(JSON, nullable=False, default=dict)
    required_fields = Column(JSON, nullable=True, default=list)

    # Validation Rules
    validation_rules = Column(JSON, nullable=True, default=dict)
    examples = Column(JSON, nullable=True, default=list)

    # Relationships
    parent_schema_id = Column(Integer, ForeignKey("api_schemas.id"), nullable=True)

    # Versioning
    version = Column(String(20), default="v1")
    deprecated = Column(Boolean, default=False)

    # Usage
    used_in_endpoints = Column(JSON, nullable=True, default=list)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    parent_schema = relationship("APISchema", remote_side=[id])
    child_schemas = relationship("APISchema", back_populates="parent_schema")

    def __repr__(self):
        return f"<APISchema(id={self.id}, name='{self.name}')>"


class CodeExample(Base):
    """
    Code examples for API endpoints in different languages.
    """

    __tablename__ = "code_examples"

    id = Column(Integer, primary_key=True, index=True)
    endpoint_id = Column(Integer, ForeignKey("api_endpoints.id"), nullable=False)

    # Example Information
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    language = Column(String(50), nullable=False, default=SDKLanguage.CURL.value)

    # Code Content
    code = Column(Text, nullable=False)
    explanation = Column(Text, nullable=True)

    # Example Configuration
    is_featured = Column(Boolean, default=False)
    difficulty_level = Column(
        String(20), default="beginner"
    )  # beginner, intermediate, advanced

    # Usage Statistics
    view_count = Column(Integer, default=0)
    copy_count = Column(Integer, default=0)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    endpoint = relationship("APIEndpoint", back_populates="code_examples")

    def __repr__(self):
        return f"<CodeExample(id={self.id}, language='{self.language}', title='{self.title}')>"


class Documentation(Base):
    """
    General API documentation pages and guides.
    """

    __tablename__ = "documentation"

    id = Column(Integer, primary_key=True, index=True)

    # Document Information
    title = Column(String(255), nullable=False)
    slug = Column(String(255), nullable=False, unique=True, index=True)
    content = Column(Text, nullable=False)

    # Document Type & Category
    doc_type = Column(String(50), nullable=False, default=DocumentationType.GUIDE.value)
    category = Column(String(100), nullable=True, index=True)
    tags = Column(JSON, nullable=True, default=list)

    # Organization
    parent_documentation_id = Column(
        Integer, ForeignKey("documentation.id"), nullable=True
    )
    order_index = Column(Integer, default=0)

    # Content Metadata
    reading_time_minutes = Column(Integer, nullable=True)
    difficulty_level = Column(String(20), default="beginner")

    # SEO & Discovery
    meta_description = Column(Text, nullable=True)
    keywords = Column(JSON, nullable=True, default=list)

    # Status & Visibility
    is_published = Column(Boolean, default=False)
    is_featured = Column(Boolean, default=False)

    # Usage Statistics
    view_count = Column(Integer, default=0)
    helpful_votes = Column(Integer, default=0)
    unhelpful_votes = Column(Integer, default=0)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    published_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    parent = relationship(
        "Documentation", remote_side=[id], foreign_keys=[parent_documentation_id]
    )
    children = relationship(
        "Documentation", back_populates="parent", foreign_keys=[parent_documentation_id]
    )

    def __repr__(self):
        return f"<Documentation(id={self.id}, title='{self.title}', type='{self.doc_type}')>"


class SDK(Base):
    """
    Client SDKs and libraries for different programming languages.
    """

    __tablename__ = "sdks"

    id = Column(Integer, primary_key=True, index=True)

    # SDK Information
    name = Column(String(255), nullable=False)
    language = Column(String(50), nullable=False, default=SDKLanguage.PYTHON.value)
    description = Column(Text, nullable=False)

    # Version Information
    version = Column(String(50), nullable=False)
    changelog = Column(Text, nullable=True)

    # Repository Information
    repository_url = Column(String(500), nullable=True)
    documentation_url = Column(String(500), nullable=True)
    package_url = Column(String(500), nullable=True)  # PyPI, npm, etc.

    # Installation Instructions
    installation_command = Column(String(500), nullable=True)
    installation_notes = Column(Text, nullable=True)

    # Requirements
    minimum_version = Column(String(50), nullable=True)  # Minimum language version
    dependencies = Column(JSON, nullable=True, default=list)

    # Features
    supported_endpoints = Column(JSON, nullable=True, default=list)
    features = Column(JSON, nullable=True, default=list)

    # Examples & Documentation
    quick_start_guide = Column(Text, nullable=True)
    code_examples = Column(JSON, nullable=True, default=list)

    # Status
    is_official = Column(Boolean, default=True)
    is_maintained = Column(Boolean, default=True)
    is_beta = Column(Boolean, default=False)

    # Usage Statistics
    download_count = Column(Integer, default=0)
    github_stars = Column(Integer, default=0)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    released_at = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<SDK(id={self.id}, name='{self.name}', language='{self.language}')>"


class APIKey(Base):
    """
    API keys for developer access and authentication.
    """

    __tablename__ = "api_keys"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Key Information
    key_name = Column(String(255), nullable=False)
    key_value = Column(String(255), nullable=False, unique=True, index=True)
    key_prefix = Column(String(20), nullable=True)  # For display purposes

    # Permissions & Scopes
    scopes = Column(JSON, nullable=True, default=list)
    allowed_endpoints = Column(JSON, nullable=True, default=list)

    # Rate Limiting
    rate_limit_per_minute = Column(Integer, default=1000)
    rate_limit_per_hour = Column(Integer, default=10000)
    rate_limit_per_day = Column(Integer, default=100000)

    # IP Restrictions
    allowed_ips = Column(JSON, nullable=True, default=list)
    blocked_ips = Column(JSON, nullable=True, default=list)

    # Usage Statistics
    total_requests = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    last_used_ip = Column(String(45), nullable=True)

    # Status & Control
    is_active = Column(Boolean, default=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationships
    company = relationship("Company")
    created_by_user = relationship("User", foreign_keys=[user_id])

    def __repr__(self):
        return f"<APIKey(id={self.id}, name='{self.key_name}', prefix='{self.key_prefix}')>"


class APIUsageLog(Base):
    """
    Detailed logging of API usage for analytics and monitoring.
    """

    __tablename__ = "api_usage_logs"

    id = Column(Integer, primary_key=True, index=True)
    api_key_id = Column(Integer, ForeignKey("api_keys.id"), nullable=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=True)

    # Request Information
    endpoint_path = Column(String(500), nullable=False, index=True)
    method = Column(String(10), nullable=False)

    # Request Details
    request_headers = Column(JSON, nullable=True, default=dict)
    request_body_size = Column(Integer, nullable=True)
    query_parameters = Column(JSON, nullable=True, default=dict)

    # Response Information
    status_code = Column(Integer, nullable=False, index=True)
    response_size = Column(Integer, nullable=True)
    response_time_ms = Column(Integer, nullable=True)

    # Client Information
    ip_address = Column(String(45), nullable=True, index=True)
    user_agent = Column(String(500), nullable=True)

    # Error Information
    error_message = Column(Text, nullable=True)
    error_code = Column(String(50), nullable=True)

    # Timestamp
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), index=True)

    # Relationships
    api_key = relationship("APIKey")
    company = relationship("Company")

    def __repr__(self):
        return f"<APIUsageLog(id={self.id}, method='{self.method}', path='{self.endpoint_path}')>"
