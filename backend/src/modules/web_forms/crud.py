"""
Web Forms CRUD operations.
"""

import logging
import secrets
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload

from . import models, schemas

logger = logging.getLogger(__name__)


def generate_form_key() -> str:
    """Generate a unique form key."""
    return secrets.token_urlsafe(16)


async def create_form(db: AsyncSession, form: schemas.WebFormCreate) -> models.WebForm:
    """Create a new form."""
    db_form = models.WebForm(
        name=form.name,
        title=form.title,
        description=form.description,
        company_id=form.company_id,
        status=form.status,
        form_key=generate_form_key(),
        settings=form.settings or {},
        styling=form.styling or {},
        integrations=form.integrations or {},
        is_public=form.is_public,
        requires_auth=form.requires_auth,
        max_submissions=form.max_submissions,
        submission_limit_per_user=form.submission_limit_per_user,
        start_date=form.start_date,
        end_date=form.end_date,
        redirect_url=str(form.redirect_url) if form.redirect_url else None,
        success_message=form.success_message,
        error_message=form.error_message,
    )
    db.add(db_form)
    await db.commit()
    await db.refresh(db_form)
    return db_form


async def get_form(
    db: AsyncSession, form_id: int, company_id: int, include_fields: bool = False
) -> Optional[models.WebForm]:
    """Get a single form by ID."""
    query = select(models.WebForm).filter(
        and_(models.WebForm.id == form_id, models.WebForm.company_id == company_id)
    )

    if include_fields:
        query = query.options(selectinload(models.WebForm.fields))

    result = await db.execute(query)
    return result.scalars().first()


async def get_form_by_key(
    db: AsyncSession, form_key: str, include_fields: bool = False
) -> Optional[models.WebForm]:
    """Get a form by its unique key."""
    query = select(models.WebForm).filter(models.WebForm.form_key == form_key)

    if include_fields:
        query = query.options(selectinload(models.WebForm.fields))

    result = await db.execute(query)
    return result.scalars().first()


async def get_forms(
    db: AsyncSession,
    company_id: int,
    skip: int = 0,
    limit: int = 100,
    status: Optional[schemas.WebFormStatus] = None,
    search: Optional[str] = None,
) -> List[models.WebForm]:
    """Get forms with filtering."""
    query = select(models.WebForm).filter(models.WebForm.company_id == company_id)

    if status:
        query = query.filter(models.WebForm.status == status)

    if search:
        query = query.filter(
            or_(
                models.WebForm.name.ilike(f"%{search}%"),
                models.WebForm.title.ilike(f"%{search}%"),
                models.WebForm.description.ilike(f"%{search}%"),
            )
        )

    query = query.order_by(desc(models.WebForm.created_at))
    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    return result.scalars().all()


async def update_form(
    db: AsyncSession, form_id: int, company_id: int, form_update: schemas.WebFormUpdate
) -> Optional[models.WebForm]:
    """Update a form."""
    db_form = await get_form(db, form_id, company_id)
    if not db_form:
        return None

    update_data = form_update.dict(exclude_unset=True)

    # Handle URL conversion
    if "redirect_url" in update_data and update_data["redirect_url"]:
        update_data["redirect_url"] = str(update_data["redirect_url"])

    for field, value in update_data.items():
        setattr(db_form, field, value)

    await db.commit()
    await db.refresh(db_form)
    return db_form


async def delete_form(db: AsyncSession, form_id: int, company_id: int) -> bool:
    """Delete a form."""
    db_form = await get_form(db, form_id, company_id)
    if not db_form:
        return False

    await db.delete(db_form)
    await db.commit()
    return True


async def increment_form_views(db: AsyncSession, form_id: int) -> bool:
    """Increment form view count."""
    result = await db.execute(
        select(models.WebForm).filter(models.WebForm.id == form_id)
    )
    db_form = result.scalars().first()

    if not db_form:
        return False

    db_form.view_count += 1
    await db.commit()
    return True


async def increment_form_submissions(db: AsyncSession, form_id: int) -> bool:
    """Increment form submission count and update last submission time."""
    result = await db.execute(
        select(models.WebForm).filter(models.WebForm.id == form_id)
    )
    db_form = result.scalars().first()

    if not db_form:
        return False

    db_form.submission_count += 1
    db_form.last_submission_at = datetime.utcnow()

    # Update conversion rate
    if db_form.view_count > 0:
        db_form.conversion_rate = (db_form.submission_count / db_form.view_count) * 100

    await db.commit()
    return True


# Form Field CRUD
async def create_form_field(
    db: AsyncSession, field: schemas.WebFormFieldCreate
) -> models.WebFormField:
    """Create a new form field."""
    db_field = models.WebFormField(
        form_id=field.form_id,
        label=field.label,
        field_type=field.field_type,
        placeholder=field.placeholder,
        help_text=field.help_text,
        required=field.required,
        options=field.options or [],
        validation_rules=field.validation_rules or {},
        default_value=field.default_value,
        order_index=field.order_index,
        is_active=field.is_active,
        field_config=field.field_config or {},
    )
    db.add(db_field)
    await db.commit()
    await db.refresh(db_field)
    return db_field


async def get_form_fields(
    db: AsyncSession, form_id: int, company_id: int
) -> List[models.WebFormField]:
    """Get fields for a form."""
    # First verify the form belongs to the company
    form_exists = await db.execute(
        select(models.WebForm.id).filter(
            and_(models.WebForm.id == form_id, models.WebForm.company_id == company_id)
        )
    )
    if not form_exists.scalar():
        return []

    query = (
        select(models.WebFormField)
        .filter(models.WebFormField.form_id == form_id)
        .order_by(models.WebFormField.order_index)
    )

    result = await db.execute(query)
    return result.scalars().all()


async def update_form_field(
    db: AsyncSession,
    field_id: int,
    company_id: int,
    field_update: schemas.WebFormFieldUpdate,
) -> Optional[models.WebFormField]:
    """Update a form field."""
    # First get the field and verify company ownership
    result = await db.execute(
        select(models.WebFormField)
        .join(models.WebForm)
        .filter(
            and_(
                models.WebFormField.id == field_id,
                models.WebForm.company_id == company_id,
            )
        )
    )
    db_field = result.scalars().first()

    if not db_field:
        return None

    update_data = field_update.dict(exclude_unset=True)

    for field, value in update_data.items():
        setattr(db_field, field, value)

    await db.commit()
    await db.refresh(db_field)
    return db_field


async def delete_form_field(db: AsyncSession, field_id: int, company_id: int) -> bool:
    """Delete a form field."""
    # First get the field and verify company ownership
    result = await db.execute(
        select(models.WebFormField)
        .join(models.WebForm)
        .filter(
            and_(
                models.WebFormField.id == field_id,
                models.WebForm.company_id == company_id,
            )
        )
    )
    db_field = result.scalars().first()

    if not db_field:
        return False

    await db.delete(db_field)
    await db.commit()
    return True


# Form Submission CRUD
async def create_form_submission(
    db: AsyncSession, submission: schemas.WebFormSubmissionCreate
) -> models.WebFormSubmission:
    """Create a new form submission."""
    db_submission = models.WebFormSubmission(
        form_id=submission.form_id,
        customer_id=submission.customer_id,
        form_data=submission.form_data,
        source=submission.source,
        user_agent=submission.user_agent,
        ip_address=submission.ip_address,
        referrer=submission.referrer,
        utm_params=submission.utm_params or {},
        metadata=submission.metadata or {},
    )
    db.add(db_submission)
    await db.commit()
    await db.refresh(db_submission)

    # Increment form submission count
    await increment_form_submissions(db, submission.form_id)

    return db_submission


async def get_form_submissions(
    db: AsyncSession,
    form_id: int,
    company_id: int,
    skip: int = 0,
    limit: int = 100,
    status: Optional[schemas.WebFormSubmissionStatus] = None,
) -> List[models.WebFormSubmission]:
    """Get submissions for a form."""
    # First verify the form belongs to the company
    form_exists = await db.execute(
        select(models.WebForm.id).filter(
            and_(models.WebForm.id == form_id, models.WebForm.company_id == company_id)
        )
    )
    if not form_exists.scalar():
        return []

    query = select(models.WebFormSubmission).filter(
        models.WebFormSubmission.form_id == form_id
    )

    if status:
        query = query.filter(models.WebFormSubmission.status == status)

    query = query.order_by(desc(models.WebFormSubmission.submitted_at))
    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    return result.scalars().all()


async def get_form_count(
    db: AsyncSession, company_id: int, status: Optional[schemas.WebFormStatus] = None
) -> int:
    """Get total count of forms."""
    query = select(func.count(models.WebForm.id)).filter(
        models.WebForm.company_id == company_id
    )

    if status:
        query = query.filter(models.WebForm.status == status)

    result = await db.execute(query)
    return result.scalar() or 0
