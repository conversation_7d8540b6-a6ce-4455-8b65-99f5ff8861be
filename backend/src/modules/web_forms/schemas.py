"""
Web Forms Schemas for API requests and responses.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, ConfigDict, Field, HttpUrl


class WebFormFieldType(str, Enum):
    """Form field types."""

    TEXT = "text"
    EMAIL = "email"
    PHONE = "phone"
    NUMBER = "number"
    TEXTAREA = "textarea"
    SELECT = "select"
    RADIO = "radio"
    CHECKBOX = "checkbox"
    DATE = "date"
    TIME = "time"
    DATETIME = "datetime"
    FILE = "file"
    HIDDEN = "hidden"


class WebFormStatus(str, Enum):
    """Form status options."""

    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"
    PAUSED = "paused"


class WebFormSubmissionStatus(str, Enum):
    """Form submission status."""

    PENDING = "pending"
    PROCESSED = "processed"
    COMPLETED = "completed"
    FAILED = "failed"


# Form Field Schemas
class WebFormFieldBase(BaseModel):
    label: str = Field(..., max_length=255)
    field_type: WebFormFieldType
    placeholder: Optional[str] = Field(None, max_length=255)
    help_text: Optional[str] = None
    required: bool = False
    options: Optional[List[str]] = None  # For select, radio, checkbox
    validation_rules: Optional[Dict[str, Any]] = None
    default_value: Optional[str] = None
    order_index: int = 0
    is_active: bool = True
    field_config: Optional[Dict[str, Any]] = None


class WebFormFieldCreate(WebFormFieldBase):
    form_id: int


class WebFormFieldUpdate(BaseModel):
    label: Optional[str] = Field(None, max_length=255)
    placeholder: Optional[str] = Field(None, max_length=255)
    help_text: Optional[str] = None
    required: Optional[bool] = None
    options: Optional[List[str]] = None
    validation_rules: Optional[Dict[str, Any]] = None
    default_value: Optional[str] = None
    order_index: Optional[int] = None
    is_active: Optional[bool] = None
    field_config: Optional[Dict[str, Any]] = None


class WebFormField(WebFormFieldBase):
    id: int
    form_id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Form Schemas
class WebFormBase(BaseModel):
    name: str = Field(..., max_length=255)
    title: str = Field(..., max_length=255)
    description: Optional[str] = None
    status: WebFormStatus = WebFormStatus.DRAFT
    settings: Optional[Dict[str, Any]] = None
    styling: Optional[Dict[str, Any]] = None
    integrations: Optional[Dict[str, Any]] = None
    is_public: bool = True
    requires_auth: bool = False
    max_submissions: Optional[int] = None
    submission_limit_per_user: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    redirect_url: Optional[HttpUrl] = None
    success_message: str = "Thank you for your submission!"
    error_message: str = (
        "There was an error processing your submission. Please try again."
    )


class WebFormCreate(WebFormBase):
    company_id: int


class WebFormUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=255)
    title: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    status: Optional[WebFormStatus] = None
    settings: Optional[Dict[str, Any]] = None
    styling: Optional[Dict[str, Any]] = None
    integrations: Optional[Dict[str, Any]] = None
    is_public: Optional[bool] = None
    requires_auth: Optional[bool] = None
    max_submissions: Optional[int] = None
    submission_limit_per_user: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    redirect_url: Optional[HttpUrl] = None
    success_message: Optional[str] = None
    error_message: Optional[str] = None


class WebForm(WebFormBase):
    id: int
    company_id: int
    form_key: str  # Unique identifier
    submission_count: int = 0
    view_count: int = 0
    conversion_rate: float = 0.0
    last_submission_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class WebFormWithFields(WebForm):
    fields: List[WebFormField] = []


# Form Submission Schemas
class WebFormSubmissionBase(BaseModel):
    form_data: Dict[str, Any]
    source: Optional[str] = None  # web, api, embed, etc.
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    referrer: Optional[str] = None
    utm_params: Optional[Dict[str, str]] = None
    metadata: Optional[Dict[str, Any]] = None


class WebFormSubmissionCreate(WebFormSubmissionBase):
    form_id: int
    customer_id: Optional[int] = None


class WebFormSubmissionUpdate(BaseModel):
    status: Optional[WebFormSubmissionStatus] = None
    processed_data: Optional[Dict[str, Any]] = None
    processing_notes: Optional[str] = None
    assigned_to: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None


class WebFormSubmission(WebFormSubmissionBase):
    id: int
    form_id: int
    customer_id: Optional[int] = None
    status: WebFormSubmissionStatus = WebFormSubmissionStatus.PENDING
    processed_data: Optional[Dict[str, Any]] = None
    processing_notes: Optional[str] = None
    assigned_to: Optional[int] = None
    submitted_at: datetime
    processed_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Response Schemas
class WebFormListResponse(BaseModel):
    forms: List[WebForm]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class WebFormSubmissionListResponse(BaseModel):
    submissions: List[WebFormSubmission]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


# Form Configuration Schemas
class WebFormConfigResponse(BaseModel):
    form: WebForm
    fields: List[WebFormField]
    settings: Dict[str, Any]
    styling: Dict[str, Any]


class WebFormEmbedConfig(BaseModel):
    form_id: int
    embed_type: str = "iframe"  # iframe, script, api
    width: Optional[str] = "100%"
    height: Optional[str] = "auto"
    theme: Optional[str] = "default"
    custom_css: Optional[str] = None
    prefill_data: Optional[Dict[str, str]] = None
    callback_url: Optional[HttpUrl] = None


class WebFormEmbedCode(BaseModel):
    embed_type: str
    html_code: str
    javascript_code: Optional[str] = None
    css_code: Optional[str] = None
    instructions: str


# Analytics Schemas
class WebFormAnalytics(BaseModel):
    form_id: int
    total_views: int
    total_submissions: int
    conversion_rate: float
    average_completion_time: Optional[float] = None
    abandonment_rate: float
    top_exit_fields: List[Dict[str, Any]] = []
    submission_trends: List[Dict[str, Any]] = []
    device_breakdown: Dict[str, int] = {}
    source_breakdown: Dict[str, int] = {}
    geographic_data: List[Dict[str, Any]] = []


class WebFormFieldAnalytics(BaseModel):
    field_id: int
    field_label: str
    completion_rate: float
    average_time_spent: Optional[float] = None
    error_rate: float
    most_common_values: List[Dict[str, Any]] = []


# Validation Schemas
class WebFormValidationRequest(BaseModel):
    form_data: Dict[str, Any]
    form_id: int


class WebFormValidationResponse(BaseModel):
    is_valid: bool
    errors: Dict[str, List[str]] = {}
    warnings: Dict[str, List[str]] = {}


class WebFieldValidationRule(BaseModel):
    rule_type: str  # required, min_length, max_length, pattern, etc.
    value: Union[str, int, float, bool]
    message: Optional[str] = None


# Integration Schemas
class WebFormIntegrationBase(BaseModel):
    integration_type: str = Field(..., max_length=100)  # webhook, email, crm, etc.
    config: Dict[str, Any]
    is_active: bool = True
    trigger_events: List[str] = ["submission"]  # submission, view, completion


class WebFormIntegrationCreate(WebFormIntegrationBase):
    form_id: int


class WebFormIntegrationUpdate(BaseModel):
    config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    trigger_events: Optional[List[str]] = None


class WebFormIntegration(WebFormIntegrationBase):
    id: int
    form_id: int
    last_triggered_at: Optional[datetime] = None
    success_count: int = 0
    error_count: int = 0
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Template Schemas
class WebFormTemplateBase(BaseModel):
    name: str = Field(..., max_length=255)
    description: Optional[str] = None
    category: str = Field(..., max_length=100)
    template_data: Dict[str, Any]
    preview_image: Optional[str] = None
    is_public: bool = True
    tags: Optional[List[str]] = None


class WebFormTemplateCreate(WebFormTemplateBase):
    company_id: Optional[int] = None  # None for public templates


class WebFormTemplate(WebFormTemplateBase):
    id: int
    company_id: Optional[int] = None
    times_used: int = 0
    rating: float = 0.0
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)
