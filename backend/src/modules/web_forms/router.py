"""
Web Forms API Router for embeddable form management.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.service import get_current_active_user
from modules.companies import crud as company_crud
from modules.users.models import User as DBUser

from . import crud, schemas

logger = logging.getLogger(__name__)

router = APIRouter(tags=["web-forms"])


@router.get("/forms", response_model=schemas.WebFormListResponse)
async def get_web_forms(
    company_id: int = Query(..., description="Company ID"),
    status: Optional[schemas.WebFormSubmissionStatus] = None,
    search: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get web forms for a company."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Get forms and total count
    forms = await crud.get_forms(db, company_id, skip, limit, status, search)
    total = await crud.get_form_count(db, company_id, status)

    return schemas.WebFormListResponse(
        web_forms=forms,
        total=total,
        page=skip // limit + 1,
        per_page=limit,
        has_next=skip + limit < total,
        has_prev=skip > 0,
    )


@router.post("/")
async def create_web_form(
    company_id: int,
    form_data: dict,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a new web form."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement web form creation
    return {
        "form_id": "form_123",
        "embed_code": "<script src='https://example.com/embed/form_123.js'></script>",
        "message": "Web form created successfully",
    }


@router.get("/{form_id}")
async def get_web_form(
    form_id: str,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get a specific web form."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement get web form
    raise HTTPException(status_code=404, detail="Web form not found")


@router.get("/{form_id}/submissions")
async def get_web_form_submissions(
    form_id: str,
    company_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get submissions for a web form."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement get form submissions
    return []


# Public endpoint for form submissions (no auth required)
@router.post("/{form_id}/submit")
async def submit_web_form(
    form_id: str, submission_data: dict, db: AsyncSession = Depends(get_db)
):
    """Submit a web form (public endpoint)."""
    # TODO: Implement form submission
    return {"submission_id": "submission_123", "message": "Form submitted successfully"}


@router.get("/{form_id}/embed")
async def get_web_form_embed_code(
    form_id: str,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get embed code for a web form."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Generate embed code
    return {
        "embed_code": f"<script src='https://example.com/embed/{form_id}.js'></script>",
        "iframe_code": f"<iframe src='https://example.com/forms/{form_id}' width='100%' height='600'></iframe>",
        "direct_link": f"https://example.com/forms/{form_id}",
    }
