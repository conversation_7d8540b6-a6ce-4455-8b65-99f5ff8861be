from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON>umn,
    DateTime,
    Integer,
    String,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True)
    full_name = Column(String, nullable=True)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    reset_password_token = Column(String, nullable=True, index=True, unique=True)
    reset_password_token_expires_at = Column(DateTime(timezone=True), nullable=True)

    customers = relationship("Customer", back_populates="user")
    companies = relationship("Company", back_populates="user")
    jobs = relationship("Job", back_populates="user")
    knowledge_documents = relationship("KnowledgeDocument", back_populates="user")
    phone_numbers = relationship("PhoneNumber", back_populates="user")
    call_history = relationship("CallHistory", back_populates="user")
    conversation_logs = relationship("ConversationLog", back_populates="user")
    agents = relationship("Agent", back_populates="user")
    performance_metrics = relationship("PerformanceMetric", back_populates="user")

    # Campaign Management Relationships
    business_profiles = relationship("BusinessProfile", back_populates="user")
    campaigns = relationship("Campaign", back_populates="user")
    appointments = relationship("Appointment", back_populates="user")

    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}')>"
