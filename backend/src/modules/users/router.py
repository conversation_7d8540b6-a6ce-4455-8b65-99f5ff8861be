"""
Users API Router.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.service import get_current_active_user
from modules.users.models import User as DBUser

from . import crud, schemas

logger = logging.getLogger(__name__)

router = APIRouter(tags=["users"])


@router.get("/me", response_model=schemas.UserInDB)
async def get_current_user_profile(
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get current user profile."""
    return current_user


@router.put("/me", response_model=schemas.UserInDB)
async def update_current_user_profile(
    user_update: schemas.UserUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Update current user profile."""
    updated_user = await crud.update_user(db, current_user.id, user_update)
    if not updated_user:
        raise HTTPException(status_code=404, detail="User not found")
    return updated_user


@router.get("/", response_model=List[schemas.UserInDB])
async def get_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = None,
    is_active: Optional[bool] = None,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get users (admin only)."""
    users = await crud.get_users(
        db=db, skip=skip, limit=limit, search=search, is_active=is_active
    )
    return users


@router.get("/{user_id}", response_model=schemas.UserInDB)
async def get_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get user by ID (admin only)."""
    user = await crud.get_user(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user


@router.put("/{user_id}", response_model=schemas.UserInDB)
async def update_user(
    user_id: int,
    user_update: schemas.UserUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Update user (admin only)."""
    updated_user = await crud.update_user(db, user_id, user_update)
    if not updated_user:
        raise HTTPException(status_code=404, detail="User not found")
    return updated_user


@router.delete("/{user_id}")
async def delete_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Delete user (admin only)."""
    success = await crud.delete_user(db, user_id)
    if not success:
        raise HTTPException(status_code=404, detail="User not found")
    return {"message": "User deleted successfully"}


@router.post("/{user_id}/activate", response_model=schemas.UserInDB)
async def activate_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Activate user (admin only)."""
    user = await crud.activate_user(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user


@router.post("/{user_id}/deactivate", response_model=schemas.UserInDB)
async def deactivate_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Deactivate user (admin only)."""
    user = await crud.deactivate_user(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user
