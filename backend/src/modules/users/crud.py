"""
User CRUD operations.
"""

import logging
from typing import List, Optional

from sqlalchemy import and_, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload

from modules.auth.service import get_password_hash, verify_password

from . import models, schemas

logger = logging.getLogger(__name__)


async def create_user(db: AsyncSession, user: schemas.UserCreate) -> models.User:
    """Create a new user."""
    hashed_password = get_password_hash(user.password)
    db_user = models.User(
        email=user.email,
        full_name=user.full_name,
        hashed_password=hashed_password,
        is_active=True,
    )
    db.add(db_user)
    await db.commit()
    await db.refresh(db_user)
    return db_user


async def get_user(db: AsyncSession, user_id: int) -> Optional[models.User]:
    """Get a user by ID."""
    result = await db.execute(select(models.User).filter(models.User.id == user_id))
    return result.scalars().first()


async def get_user_by_email(db: AsyncSession, email: str) -> Optional[models.User]:
    """Get a user by email."""
    result = await db.execute(select(models.User).filter(models.User.email == email))
    return result.scalars().first()


async def get_user_by_id(db: AsyncSession, user_id: int) -> Optional[models.User]:
    """Get a user by ID (alias for get_user)."""
    return await get_user(db, user_id)


async def get_users(
    db: AsyncSession,
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    is_active: Optional[bool] = None,
) -> List[models.User]:
    """Get users with filtering."""
    query = select(models.User)

    if search:
        query = query.filter(
            or_(
                models.User.email.ilike(f"%{search}%"),
                models.User.full_name.ilike(f"%{search}%"),
            )
        )

    if is_active is not None:
        query = query.filter(models.User.is_active == is_active)

    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    return result.scalars().all()


async def update_user(
    db: AsyncSession, user_id: int, user_update: schemas.UserUpdate
) -> Optional[models.User]:
    """Update a user."""
    db_user = await get_user(db, user_id)
    if not db_user:
        return None

    update_data = user_update.dict(exclude_unset=True)

    # Handle password update
    if "password" in update_data:
        update_data["hashed_password"] = get_password_hash(update_data.pop("password"))

    for field, value in update_data.items():
        setattr(db_user, field, value)

    await db.commit()
    await db.refresh(db_user)
    return db_user


async def delete_user(db: AsyncSession, user_id: int) -> bool:
    """Delete a user (soft delete by setting is_active=False)."""
    db_user = await get_user(db, user_id)
    if not db_user:
        return False

    db_user.is_active = False
    await db.commit()
    return True


async def authenticate_user(
    db: AsyncSession, email: str, password: str
) -> Optional[models.User]:
    """Authenticate a user."""
    user = await get_user_by_email(db, email)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user


async def activate_user(db: AsyncSession, user_id: int) -> Optional[models.User]:
    """Activate a user."""
    db_user = await get_user(db, user_id)
    if not db_user:
        return None

    db_user.is_active = True
    await db.commit()
    await db.refresh(db_user)
    return db_user


async def deactivate_user(db: AsyncSession, user_id: int) -> Optional[models.User]:
    """Deactivate a user."""
    db_user = await get_user(db, user_id)
    if not db_user:
        return None

    db_user.is_active = False
    await db.commit()
    await db.refresh(db_user)
    return db_user
