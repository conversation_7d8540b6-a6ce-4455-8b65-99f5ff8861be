from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict


class PerformanceMetricBase(BaseModel):
    service: str
    action: str
    latency: float
    request_id: Optional[str] = None


class PerformanceMetricCreate(PerformanceMetricBase):
    pass


class PerformanceMetric(PerformanceMetricBase):
    id: int
    timestamp: datetime

    model_config = ConfigDict(from_attributes=True)
