from sqlalchemy import (
    Column,
    DateTime,
    Float,
    Foreign<PERSON>ey,
    Integer,
    String,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base
from modules.users.models import User  # noqa: F401, needed for relationship


class PerformanceMetric(Base):
    __tablename__ = "performance_metrics"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    request_id = Column(String, nullable=True, index=True)
    chat_history_id = Column(String, nullable=True, index=True)
    service = Column(String, nullable=False, index=True)
    action = Column(String, nullable=False, index=True)
    latency = Column(Float, nullable=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    user = relationship("User", back_populates="performance_metrics")
