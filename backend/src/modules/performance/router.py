from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.service import get_current_active_user, get_current_user
from modules.users.models import User as DBUser

from . import crud, schemas

router = APIRouter(prefix="/performance", tags=["performance"])


@router.get("/metrics", response_model=List[schemas.PerformanceMetric])
async def read_metrics(
    start_time: Optional[datetime] = None,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """
    Retrieve all collected performance metrics fromthe database.
    """
    metrics = await crud.get_performance_metrics(
        db, start_time=start_time, user_id=current_user.id
    )
    return metrics


@router.get("/live", response_model=List[schemas.PerformanceMetric])
async def get_live_performance(
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    return await crud.get_latest_performance_metrics(db, user_id=current_user.id)
