"""
CRM API Router

FastAPI routes for managing leads, appointments, and customer relationships.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.service import get_current_user
from modules.users.models import User
from .service import crm_service
from .models import LeadStatus, AppointmentStatus

router = APIRouter(prefix="/crm", tags=["crm"])


@router.get("/dashboard")
async def get_crm_dashboard(
    business_profile_id: int,
    days: int = Query(30, description="Number of days for analytics"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get CRM dashboard summary data."""
    
    try:
        dashboard_data = await crm_service.get_crm_dashboard_data(
            db=db,
            business_profile_id=business_profile_id,
            days=days
        )
        
        return dashboard_data
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get CRM dashboard data: {str(e)}"
        )


@router.post("/leads")
async def create_lead(
    lead_data: Dict[str, Any],
    business_profile_id: int,
    campaign_id: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new lead."""
    
    try:
        lead = await crm_service.create_lead(
            db=db,
            lead_data=lead_data,
            business_profile_id=business_profile_id,
            campaign_id=campaign_id
        )
        
        return {
            "id": lead.id,
            "full_name": lead.full_name,
            "email": lead.email,
            "phone": lead.phone,
            "source": lead.source,
            "status": lead.status,
            "lead_score": lead.lead_score,
            "is_qualified": lead.is_qualified,
            "created_at": lead.created_at.isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create lead: {str(e)}"
        )


@router.get("/leads")
async def get_leads(
    business_profile_id: int,
    status: Optional[str] = Query(None, description="Filter by lead status"),
    source: Optional[str] = Query(None, description="Filter by lead source"),
    assigned_to: Optional[int] = Query(None, description="Filter by assigned user"),
    limit: int = Query(50, description="Number of leads to return"),
    offset: int = Query(0, description="Number of leads to skip"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get leads with filtering options."""
    
    try:
        leads = await crm_service.get_leads(
            db=db,
            business_profile_id=business_profile_id,
            status=status,
            source=source,
            assigned_to=assigned_to,
            limit=limit,
            offset=offset
        )
        
        return {
            "leads": [
                {
                    "id": lead.id,
                    "full_name": lead.full_name,
                    "email": lead.email,
                    "phone": lead.phone,
                    "company": lead.company,
                    "source": lead.source,
                    "status": lead.status,
                    "service_interest": lead.service_interest,
                    "lead_score": lead.lead_score,
                    "is_qualified": lead.is_qualified,
                    "estimated_value": lead.estimated_value,
                    "created_at": lead.created_at.isoformat(),
                    "last_contacted": lead.last_contacted.isoformat() if lead.last_contacted else None
                }
                for lead in leads
            ],
            "total": len(leads)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get leads: {str(e)}"
        )


@router.put("/leads/{lead_id}/status")
async def update_lead_status(
    lead_id: int,
    new_status: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update lead status."""
    
    # Validate status
    valid_statuses = [status.value for status in LeadStatus]
    if new_status not in valid_statuses:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid status. Must be one of: {valid_statuses}"
        )
    
    try:
        success = await crm_service.update_lead_status(
            db=db,
            lead_id=lead_id,
            new_status=new_status,
            user_id=current_user.id
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Lead not found"
            )
        
        return {"success": True, "message": f"Lead status updated to {new_status}"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update lead status: {str(e)}"
        )


@router.get("/leads/{lead_id}/activities")
async def get_lead_activities(
    lead_id: int,
    limit: int = Query(20, description="Number of activities to return"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get activities for a specific lead."""
    
    try:
        activities = await crm_service.get_lead_activities(
            db=db,
            lead_id=lead_id,
            limit=limit
        )
        
        return {
            "activities": [
                {
                    "id": activity.id,
                    "activity_type": activity.activity_type,
                    "subject": activity.subject,
                    "description": activity.description,
                    "outcome": activity.outcome,
                    "duration_minutes": activity.duration_minutes,
                    "completed_date": activity.completed_date.isoformat() if activity.completed_date else None,
                    "created_at": activity.created_at.isoformat()
                }
                for activity in activities
            ]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get lead activities: {str(e)}"
        )


@router.post("/leads/{lead_id}/activities")
async def create_lead_activity(
    lead_id: int,
    activity_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new lead activity."""
    
    try:
        activity = await crm_service.create_lead_activity(
            db=db,
            lead_id=lead_id,
            user_id=current_user.id,
            activity_type=activity_data["activity_type"],
            subject=activity_data["subject"],
            description=activity_data.get("description"),
            outcome=activity_data.get("outcome"),
            duration_minutes=activity_data.get("duration_minutes")
        )
        
        return {
            "id": activity.id,
            "activity_type": activity.activity_type,
            "subject": activity.subject,
            "description": activity.description,
            "created_at": activity.created_at.isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create lead activity: {str(e)}"
        )


@router.post("/appointments")
async def create_appointment(
    appointment_data: Dict[str, Any],
    business_profile_id: int,
    campaign_id: Optional[int] = None,
    lead_id: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new appointment."""
    
    try:
        # Parse appointment date if it's a string
        if isinstance(appointment_data.get("appointment_date"), str):
            appointment_data["appointment_date"] = datetime.fromisoformat(
                appointment_data["appointment_date"].replace('Z', '+00:00')
            )
        
        appointment = await crm_service.create_appointment(
            db=db,
            appointment_data=appointment_data,
            business_profile_id=business_profile_id,
            campaign_id=campaign_id,
            lead_id=lead_id
        )
        
        return {
            "id": appointment.id,
            "customer_name": appointment.customer_name,
            "customer_email": appointment.customer_email,
            "customer_phone": appointment.customer_phone,
            "service_type": appointment.service_type,
            "appointment_date": appointment.appointment_date.isoformat(),
            "status": appointment.status,
            "estimated_value": appointment.estimated_value,
            "created_at": appointment.created_at.isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create appointment: {str(e)}"
        )


@router.get("/appointments")
async def get_appointments(
    business_profile_id: int,
    status: Optional[str] = Query(None, description="Filter by appointment status"),
    date_from: Optional[str] = Query(None, description="Filter from date (ISO format)"),
    date_to: Optional[str] = Query(None, description="Filter to date (ISO format)"),
    assigned_to: Optional[int] = Query(None, description="Filter by assigned user"),
    limit: int = Query(50, description="Number of appointments to return"),
    offset: int = Query(0, description="Number of appointments to skip"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get appointments with filtering options."""
    
    try:
        # Parse date filters
        date_from_dt = None
        date_to_dt = None
        
        if date_from:
            date_from_dt = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
        
        if date_to:
            date_to_dt = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
        
        appointments = await crm_service.get_appointments(
            db=db,
            business_profile_id=business_profile_id,
            status=status,
            date_from=date_from_dt,
            date_to=date_to_dt,
            assigned_to=assigned_to,
            limit=limit,
            offset=offset
        )
        
        return {
            "appointments": [
                {
                    "id": appointment.id,
                    "customer_name": appointment.customer_name,
                    "customer_email": appointment.customer_email,
                    "customer_phone": appointment.customer_phone,
                    "service_type": appointment.service_type,
                    "appointment_date": appointment.appointment_date.isoformat(),
                    "duration_minutes": appointment.duration_minutes,
                    "location_type": appointment.location_type,
                    "address": appointment.address,
                    "status": appointment.status,
                    "notes": appointment.notes,
                    "estimated_value": appointment.estimated_value,
                    "actual_value": appointment.actual_value,
                    "created_at": appointment.created_at.isoformat(),
                    "campaign_id": appointment.campaign_id,
                    "lead_id": appointment.lead_id
                }
                for appointment in appointments
            ],
            "total": len(appointments)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get appointments: {str(e)}"
        )


@router.put("/appointments/{appointment_id}/status")
async def update_appointment_status(
    appointment_id: int,
    new_status: str,
    actual_value: Optional[float] = None,
    completion_notes: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update appointment status."""
    
    # Validate status
    valid_statuses = [status.value for status in AppointmentStatus]
    if new_status not in valid_statuses:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid status. Must be one of: {valid_statuses}"
        )
    
    try:
        # Get appointment
        from sqlalchemy import select
        result = await db.execute(select(crm_service.Appointment).where(crm_service.Appointment.id == appointment_id))
        appointment = result.scalar_one_or_none()
        
        if not appointment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Appointment not found"
            )
        
        # Update appointment
        appointment.status = new_status
        
        if new_status == AppointmentStatus.COMPLETED.value:
            appointment.completed_date = datetime.now()
            if actual_value:
                appointment.actual_value = actual_value
            if completion_notes:
                appointment.completion_notes = completion_notes
        
        await db.commit()
        
        return {"success": True, "message": f"Appointment status updated to {new_status}"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update appointment status: {str(e)}"
        )


@router.post("/leads/{lead_id}/convert")
async def convert_lead_to_contact(
    lead_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Convert a lead to a contact."""
    
    try:
        contact = await crm_service.convert_lead_to_contact(
            db=db,
            lead_id=lead_id,
            user_id=current_user.id
        )
        
        if not contact:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Lead not found"
            )
        
        return {
            "success": True,
            "contact_id": contact.id,
            "message": "Lead successfully converted to contact"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to convert lead: {str(e)}"
        )
