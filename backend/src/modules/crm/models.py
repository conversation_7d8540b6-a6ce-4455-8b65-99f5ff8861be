"""
CRM Models

Database models for managing leads, contacts, and appointments.
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Float, Boolean, JSO<PERSON>
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum as PyEnum

from core.db.database import Base


class LeadStatus(PyEnum):
    """Lead status options."""
    NEW = "new"
    CONTACTED = "contacted"
    QUALIFIED = "qualified"
    CONVERTED = "converted"
    LOST = "lost"


class LeadSource(PyEnum):
    """Lead source options."""
    GOOGLE_ADS = "google_ads"
    META_ADS = "meta_ads"
    LINKEDIN_ADS = "linkedin_ads"
    LANDING_PAGE = "landing_page"
    REFERRAL = "referral"
    DIRECT = "direct"
    OTHER = "other"


class AppointmentStatus(PyEnum):
    """Appointment status options."""
    SCHEDULED = "scheduled"
    CONFIRMED = "confirmed"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    NO_SHOW = "no_show"


class Lead(Base):
    """Lead/prospect model."""
    
    __tablename__ = "leads"
    
    id = Column(Integer, primary_key=True, index=True)
    business_profile_id = Column(Integer, ForeignKey("business_profiles.id"), nullable=False)
    campaign_id = Column(Integer, ForeignKey("campaigns.id"))
    
    # Contact information
    first_name = Column(String(100))
    last_name = Column(String(100))
    full_name = Column(String(255), nullable=False)
    email = Column(String(255))
    phone = Column(String(50))
    company = Column(String(255))
    
    # Address
    address = Column(String(500))
    city = Column(String(100))
    state = Column(String(100))
    postal_code = Column(String(20))
    country = Column(String(100))
    
    # Lead details
    source = Column(String(50), nullable=False)  # LeadSource enum
    status = Column(String(50), default=LeadStatus.NEW.value)
    
    # Service interest
    service_interest = Column(String(255))
    message = Column(Text)
    budget_range = Column(String(100))
    timeline = Column(String(100))
    
    # Scoring and qualification
    lead_score = Column(Integer, default=0)
    is_qualified = Column(Boolean, default=False)
    
    # Tracking
    utm_source = Column(String(255))
    utm_medium = Column(String(255))
    utm_campaign = Column(String(255))
    utm_content = Column(String(255))
    utm_term = Column(String(255))
    
    # Conversion tracking
    estimated_value = Column(Float)
    actual_value = Column(Float)
    conversion_date = Column(DateTime(timezone=True))
    
    # Assignment
    assigned_to = Column(Integer, ForeignKey("users.id"))
    
    # Metadata
    platform_data = Column(JSON)  # Store platform-specific data
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_contacted = Column(DateTime(timezone=True))
    
    # Relationships
    business_profile = relationship("BusinessProfile")
    campaign = relationship("Campaign")
    assigned_user = relationship("User")
    appointments = relationship("Appointment", back_populates="lead")
    activities = relationship("LeadActivity", back_populates="lead")


class Appointment(Base):
    """Appointment/booking model."""
    
    __tablename__ = "appointments"
    
    id = Column(Integer, primary_key=True, index=True)
    business_profile_id = Column(Integer, ForeignKey("business_profiles.id"), nullable=False)
    campaign_id = Column(Integer, ForeignKey("campaigns.id"))
    lead_id = Column(Integer, ForeignKey("leads.id"))
    
    # Customer information
    customer_name = Column(String(255), nullable=False)
    customer_email = Column(String(255))
    customer_phone = Column(String(50))
    
    # Appointment details
    service_type = Column(String(255), nullable=False)
    appointment_date = Column(DateTime(timezone=True), nullable=False)
    duration_minutes = Column(Integer, default=60)
    
    # Location
    location_type = Column(String(50))  # on_site, remote, office
    address = Column(String(500))
    
    # Status and notes
    status = Column(String(50), default=AppointmentStatus.SCHEDULED.value)
    notes = Column(Text)
    internal_notes = Column(Text)
    
    # Pricing
    estimated_value = Column(Float)
    actual_value = Column(Float)
    
    # Assignment
    assigned_to = Column(Integer, ForeignKey("users.id"))
    
    # Confirmation
    confirmation_sent = Column(Boolean, default=False)
    confirmation_date = Column(DateTime(timezone=True))
    
    # Completion
    completed_date = Column(DateTime(timezone=True))
    completion_notes = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    business_profile = relationship("BusinessProfile")
    campaign = relationship("Campaign")
    lead = relationship("Lead", back_populates="appointments")
    assigned_user = relationship("User")


class LeadActivity(Base):
    """Lead activity/interaction tracking."""
    
    __tablename__ = "lead_activities"
    
    id = Column(Integer, primary_key=True, index=True)
    lead_id = Column(Integer, ForeignKey("leads.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"))
    
    # Activity details
    activity_type = Column(String(100), nullable=False)  # call, email, meeting, note
    subject = Column(String(255))
    description = Column(Text)
    
    # Outcome
    outcome = Column(String(100))  # successful, no_answer, callback_requested, etc.
    
    # Scheduling
    scheduled_date = Column(DateTime(timezone=True))
    completed_date = Column(DateTime(timezone=True))
    
    # Metadata
    duration_minutes = Column(Integer)
    platform_data = Column(JSON)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    lead = relationship("Lead", back_populates="activities")
    user = relationship("User")


class Contact(Base):
    """Contact/customer model for existing customers."""
    
    __tablename__ = "contacts"
    
    id = Column(Integer, primary_key=True, index=True)
    business_profile_id = Column(Integer, ForeignKey("business_profiles.id"), nullable=False)
    
    # Personal information
    first_name = Column(String(100))
    last_name = Column(String(100))
    full_name = Column(String(255), nullable=False)
    email = Column(String(255))
    phone = Column(String(50))
    company = Column(String(255))
    job_title = Column(String(255))
    
    # Address
    address = Column(String(500))
    city = Column(String(100))
    state = Column(String(100))
    postal_code = Column(String(20))
    country = Column(String(100))
    
    # Customer details
    customer_since = Column(DateTime(timezone=True))
    total_value = Column(Float, default=0)
    last_service_date = Column(DateTime(timezone=True))
    
    # Preferences
    preferred_contact_method = Column(String(50))  # email, phone, text
    communication_preferences = Column(JSON)
    
    # Tags and categories
    tags = Column(JSON)  # Array of tags
    customer_type = Column(String(100))  # residential, commercial, etc.
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    business_profile = relationship("BusinessProfile")


class Pipeline(Base):
    """Sales pipeline/stage tracking."""
    
    __tablename__ = "pipelines"
    
    id = Column(Integer, primary_key=True, index=True)
    business_profile_id = Column(Integer, ForeignKey("business_profiles.id"), nullable=False)
    
    # Pipeline details
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Configuration
    stages = Column(JSON, nullable=False)  # Array of stage objects
    is_default = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    business_profile = relationship("BusinessProfile")


class Deal(Base):
    """Deal/opportunity tracking."""
    
    __tablename__ = "deals"
    
    id = Column(Integer, primary_key=True, index=True)
    business_profile_id = Column(Integer, ForeignKey("business_profiles.id"), nullable=False)
    pipeline_id = Column(Integer, ForeignKey("pipelines.id"))
    lead_id = Column(Integer, ForeignKey("leads.id"))
    contact_id = Column(Integer, ForeignKey("contacts.id"))
    
    # Deal details
    title = Column(String(255), nullable=False)
    description = Column(Text)
    value = Column(Float)
    
    # Pipeline tracking
    stage = Column(String(100), nullable=False)
    probability = Column(Integer, default=0)  # 0-100%
    
    # Dates
    expected_close_date = Column(DateTime(timezone=True))
    actual_close_date = Column(DateTime(timezone=True))
    
    # Assignment
    assigned_to = Column(Integer, ForeignKey("users.id"))
    
    # Status
    is_won = Column(Boolean)
    is_closed = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    business_profile = relationship("BusinessProfile")
    pipeline = relationship("Pipeline")
    lead = relationship("Lead")
    contact = relationship("Contact")
    assigned_user = relationship("User")
