"""
CRM Service

Handles lead management, appointment tracking, and customer relationship management.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from decimal import Decimal

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from sqlalchemy.orm import selectinload

from .models import Lead, Appointment, Contact, LeadActivity, Deal, Pipeline
from .models import LeadStatus, LeadSource, AppointmentStatus

logger = logging.getLogger(__name__)


class CRMService:
    """Service for managing leads, appointments, and customer relationships."""
    
    def __init__(self):
        self.default_lead_score_rules = {
            "has_email": 10,
            "has_phone": 15,
            "has_company": 5,
            "has_budget": 20,
            "urgent_timeline": 25,
            "from_paid_ads": 15,
            "multiple_services": 10
        }
    
    async def create_lead(
        self,
        db: AsyncSession,
        lead_data: Dict[str, Any],
        business_profile_id: int,
        campaign_id: Optional[int] = None
    ) -> Lead:
        """Create a new lead."""
        
        try:
            # Calculate lead score
            lead_score = self._calculate_lead_score(lead_data)
            
            # Create lead
            lead = Lead(
                business_profile_id=business_profile_id,
                campaign_id=campaign_id,
                full_name=lead_data["full_name"],
                first_name=lead_data.get("first_name"),
                last_name=lead_data.get("last_name"),
                email=lead_data.get("email"),
                phone=lead_data.get("phone"),
                company=lead_data.get("company"),
                address=lead_data.get("address"),
                city=lead_data.get("city"),
                state=lead_data.get("state"),
                postal_code=lead_data.get("postal_code"),
                country=lead_data.get("country"),
                source=lead_data.get("source", LeadSource.DIRECT.value),
                service_interest=lead_data.get("service_interest"),
                message=lead_data.get("message"),
                budget_range=lead_data.get("budget_range"),
                timeline=lead_data.get("timeline"),
                lead_score=lead_score,
                is_qualified=lead_score >= 50,  # Auto-qualify high-scoring leads
                utm_source=lead_data.get("utm_source"),
                utm_medium=lead_data.get("utm_medium"),
                utm_campaign=lead_data.get("utm_campaign"),
                utm_content=lead_data.get("utm_content"),
                utm_term=lead_data.get("utm_term"),
                estimated_value=lead_data.get("estimated_value"),
                platform_data=lead_data.get("platform_data", {})
            )
            
            db.add(lead)
            await db.commit()
            await db.refresh(lead)
            
            # Create initial activity
            await self.create_lead_activity(
                db=db,
                lead_id=lead.id,
                activity_type="lead_created",
                subject="New lead created",
                description=f"Lead created from {lead.source}"
            )
            
            logger.info(f"Created new lead: {lead.id} - {lead.full_name}")
            return lead
            
        except Exception as e:
            logger.error(f"Error creating lead: {str(e)}")
            raise
    
    def _calculate_lead_score(self, lead_data: Dict[str, Any]) -> int:
        """Calculate lead score based on available information."""
        
        score = 0
        
        # Contact information completeness
        if lead_data.get("email"):
            score += self.default_lead_score_rules["has_email"]
        
        if lead_data.get("phone"):
            score += self.default_lead_score_rules["has_phone"]
        
        if lead_data.get("company"):
            score += self.default_lead_score_rules["has_company"]
        
        # Intent indicators
        if lead_data.get("budget_range"):
            score += self.default_lead_score_rules["has_budget"]
        
        timeline = lead_data.get("timeline", "").lower()
        if any(word in timeline for word in ["urgent", "asap", "immediately", "today"]):
            score += self.default_lead_score_rules["urgent_timeline"]
        
        # Source quality
        source = lead_data.get("source", "")
        if source in [LeadSource.GOOGLE_ADS.value, LeadSource.META_ADS.value, LeadSource.LINKEDIN_ADS.value]:
            score += self.default_lead_score_rules["from_paid_ads"]
        
        # Service interest
        service_interest = lead_data.get("service_interest", "")
        if "," in service_interest or "and" in service_interest.lower():
            score += self.default_lead_score_rules["multiple_services"]
        
        return min(score, 100)  # Cap at 100
    
    async def get_leads(
        self,
        db: AsyncSession,
        business_profile_id: int,
        status: Optional[str] = None,
        source: Optional[str] = None,
        assigned_to: Optional[int] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Lead]:
        """Get leads with filtering options."""
        
        try:
            query = select(Lead).where(Lead.business_profile_id == business_profile_id)
            
            if status:
                query = query.where(Lead.status == status)
            
            if source:
                query = query.where(Lead.source == source)
            
            if assigned_to:
                query = query.where(Lead.assigned_to == assigned_to)
            
            query = query.order_by(desc(Lead.created_at)).limit(limit).offset(offset)
            
            result = await db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Error getting leads: {str(e)}")
            return []
    
    async def create_appointment(
        self,
        db: AsyncSession,
        appointment_data: Dict[str, Any],
        business_profile_id: int,
        campaign_id: Optional[int] = None,
        lead_id: Optional[int] = None
    ) -> Appointment:
        """Create a new appointment."""
        
        try:
            appointment = Appointment(
                business_profile_id=business_profile_id,
                campaign_id=campaign_id,
                lead_id=lead_id,
                customer_name=appointment_data["customer_name"],
                customer_email=appointment_data.get("customer_email"),
                customer_phone=appointment_data.get("customer_phone"),
                service_type=appointment_data["service_type"],
                appointment_date=appointment_data["appointment_date"],
                duration_minutes=appointment_data.get("duration_minutes", 60),
                location_type=appointment_data.get("location_type", "on_site"),
                address=appointment_data.get("address"),
                notes=appointment_data.get("notes"),
                estimated_value=appointment_data.get("estimated_value"),
                assigned_to=appointment_data.get("assigned_to")
            )
            
            db.add(appointment)
            await db.commit()
            await db.refresh(appointment)
            
            # Update lead status if associated
            if lead_id:
                await self.update_lead_status(db, lead_id, LeadStatus.CONVERTED.value)
            
            logger.info(f"Created appointment: {appointment.id} for {appointment.customer_name}")
            return appointment
            
        except Exception as e:
            logger.error(f"Error creating appointment: {str(e)}")
            raise
    
    async def get_appointments(
        self,
        db: AsyncSession,
        business_profile_id: int,
        status: Optional[str] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        assigned_to: Optional[int] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Appointment]:
        """Get appointments with filtering options."""
        
        try:
            query = select(Appointment).where(Appointment.business_profile_id == business_profile_id)
            
            if status:
                query = query.where(Appointment.status == status)
            
            if date_from:
                query = query.where(Appointment.appointment_date >= date_from)
            
            if date_to:
                query = query.where(Appointment.appointment_date <= date_to)
            
            if assigned_to:
                query = query.where(Appointment.assigned_to == assigned_to)
            
            query = query.order_by(Appointment.appointment_date).limit(limit).offset(offset)
            
            result = await db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Error getting appointments: {str(e)}")
            return []
    
    async def update_lead_status(
        self,
        db: AsyncSession,
        lead_id: int,
        new_status: str,
        user_id: Optional[int] = None
    ) -> bool:
        """Update lead status and create activity record."""
        
        try:
            # Get lead
            result = await db.execute(select(Lead).where(Lead.id == lead_id))
            lead = result.scalar_one_or_none()
            
            if not lead:
                return False
            
            old_status = lead.status
            lead.status = new_status
            lead.updated_at = datetime.now()
            
            if new_status == LeadStatus.CONVERTED.value:
                lead.conversion_date = datetime.now()
            
            await db.commit()
            
            # Create activity record
            await self.create_lead_activity(
                db=db,
                lead_id=lead_id,
                user_id=user_id,
                activity_type="status_change",
                subject=f"Status changed from {old_status} to {new_status}",
                description=f"Lead status updated to {new_status}"
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating lead status: {str(e)}")
            return False
    
    async def create_lead_activity(
        self,
        db: AsyncSession,
        lead_id: int,
        activity_type: str,
        subject: str,
        description: Optional[str] = None,
        user_id: Optional[int] = None,
        outcome: Optional[str] = None,
        duration_minutes: Optional[int] = None
    ) -> LeadActivity:
        """Create a lead activity record."""
        
        try:
            activity = LeadActivity(
                lead_id=lead_id,
                user_id=user_id,
                activity_type=activity_type,
                subject=subject,
                description=description,
                outcome=outcome,
                duration_minutes=duration_minutes,
                completed_date=datetime.now()
            )
            
            db.add(activity)
            await db.commit()
            await db.refresh(activity)
            
            # Update lead's last_contacted timestamp
            if activity_type in ["call", "email", "meeting"]:
                await db.execute(
                    select(Lead).where(Lead.id == lead_id)
                )
                result = await db.execute(select(Lead).where(Lead.id == lead_id))
                lead = result.scalar_one_or_none()
                if lead:
                    lead.last_contacted = datetime.now()
                    await db.commit()
            
            return activity
            
        except Exception as e:
            logger.error(f"Error creating lead activity: {str(e)}")
            raise
    
    async def get_crm_dashboard_data(
        self,
        db: AsyncSession,
        business_profile_id: int,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get CRM dashboard summary data."""
        
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # Get leads summary
            leads_result = await db.execute(
                select(Lead).where(
                    and_(
                        Lead.business_profile_id == business_profile_id,
                        Lead.created_at >= start_date
                    )
                )
            )
            leads = leads_result.scalars().all()
            
            # Get appointments summary
            appointments_result = await db.execute(
                select(Appointment).where(
                    and_(
                        Appointment.business_profile_id == business_profile_id,
                        Appointment.created_at >= start_date
                    )
                )
            )
            appointments = appointments_result.scalars().all()
            
            # Calculate metrics
            total_leads = len(leads)
            qualified_leads = len([l for l in leads if l.is_qualified])
            converted_leads = len([l for l in leads if l.status == LeadStatus.CONVERTED.value])
            
            total_appointments = len(appointments)
            completed_appointments = len([a for a in appointments if a.status == AppointmentStatus.COMPLETED.value])
            
            # Lead sources breakdown
            lead_sources = {}
            for lead in leads:
                source = lead.source
                lead_sources[source] = lead_sources.get(source, 0) + 1
            
            # Conversion rate
            conversion_rate = (converted_leads / total_leads * 100) if total_leads > 0 else 0
            
            # Revenue metrics
            total_estimated_value = sum(a.estimated_value or 0 for a in appointments)
            total_actual_value = sum(a.actual_value or 0 for a in appointments if a.actual_value)
            
            # Recent activities
            recent_leads = sorted(leads, key=lambda x: x.created_at, reverse=True)[:5]
            recent_appointments = sorted(appointments, key=lambda x: x.created_at, reverse=True)[:5]
            
            return {
                "period_days": days,
                "leads": {
                    "total": total_leads,
                    "qualified": qualified_leads,
                    "converted": converted_leads,
                    "conversion_rate": round(conversion_rate, 1),
                    "by_source": lead_sources
                },
                "appointments": {
                    "total": total_appointments,
                    "completed": completed_appointments,
                    "completion_rate": round((completed_appointments / total_appointments * 100) if total_appointments > 0 else 0, 1)
                },
                "revenue": {
                    "estimated": float(total_estimated_value),
                    "actual": float(total_actual_value)
                },
                "recent_leads": [
                    {
                        "id": l.id,
                        "name": l.full_name,
                        "source": l.source,
                        "status": l.status,
                        "score": l.lead_score,
                        "created_at": l.created_at.isoformat()
                    }
                    for l in recent_leads
                ],
                "recent_appointments": [
                    {
                        "id": a.id,
                        "customer_name": a.customer_name,
                        "service_type": a.service_type,
                        "appointment_date": a.appointment_date.isoformat(),
                        "status": a.status,
                        "estimated_value": a.estimated_value
                    }
                    for a in recent_appointments
                ]
            }
            
        except Exception as e:
            logger.error(f"Error getting CRM dashboard data: {str(e)}")
            return {}
    
    async def get_lead_activities(
        self,
        db: AsyncSession,
        lead_id: int,
        limit: int = 20
    ) -> List[LeadActivity]:
        """Get activities for a specific lead."""
        
        try:
            query = select(LeadActivity).where(
                LeadActivity.lead_id == lead_id
            ).order_by(desc(LeadActivity.created_at)).limit(limit)
            
            result = await db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Error getting lead activities: {str(e)}")
            return []
    
    async def convert_lead_to_contact(
        self,
        db: AsyncSession,
        lead_id: int,
        user_id: Optional[int] = None
    ) -> Optional[Contact]:
        """Convert a lead to a contact after successful conversion."""
        
        try:
            # Get lead
            result = await db.execute(select(Lead).where(Lead.id == lead_id))
            lead = result.scalar_one_or_none()
            
            if not lead:
                return None
            
            # Create contact
            contact = Contact(
                business_profile_id=lead.business_profile_id,
                first_name=lead.first_name,
                last_name=lead.last_name,
                full_name=lead.full_name,
                email=lead.email,
                phone=lead.phone,
                company=lead.company,
                address=lead.address,
                city=lead.city,
                state=lead.state,
                postal_code=lead.postal_code,
                country=lead.country,
                customer_since=datetime.now(),
                total_value=lead.actual_value or 0,
                customer_type="residential" if not lead.company else "commercial"
            )
            
            db.add(contact)
            await db.commit()
            await db.refresh(contact)
            
            # Update lead status
            await self.update_lead_status(db, lead_id, LeadStatus.CONVERTED.value, user_id)
            
            logger.info(f"Converted lead {lead_id} to contact {contact.id}")
            return contact
            
        except Exception as e:
            logger.error(f"Error converting lead to contact: {str(e)}")
            return None


# Global service instance
crm_service = CRMService()
