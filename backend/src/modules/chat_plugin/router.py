"""
Chat Plugin API Router for embeddable chat widgets.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.service import get_current_active_user
from modules.companies import crud as company_crud
from modules.users.models import User as DBUser

from . import crud, schemas

logger = logging.getLogger(__name__)

router = APIRouter(tags=["chat-plugin"])


@router.get("/plugins", response_model=schemas.ChatPluginListResponse)
async def get_chat_plugin_configs(
    company_id: int = Query(..., description="Company ID"),
    status: Optional[schemas.PluginStatus] = Query(None),
    agent_id: Optional[int] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get chat widgets for a company."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Get chat plugins and total count
    plugins = await crud.get_chat_plugin_configs(
        db, company_id, skip, limit, status, agent_id
    )
    total = await crud.get_plugin_count(db, company_id, status)

    return schemas.ChatPluginListResponse(
        plugins=plugins,
        total=total,
        page=skip // limit + 1,
        per_page=limit,
        has_next=skip + limit < total,
        has_prev=skip > 0,
    )


@router.post("/plugins")
async def create_chat_plugin_config(
    company_id: int,
    plugin_config_data: schemas.ChatPluginConfigCreate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a new chat plugin configuration."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    db_plugin_config = await crud.create_chat_plugin_config(db, plugin_config_data)
    return db_plugin_config


@router.get("/widgets/{plugin_id}")
async def get_chat_widget(
    plugin_id: str,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get a specific chat widget."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement get chat widget
    raise HTTPException(status_code=404, detail="Chat widget not found")


@router.get("/plugins/{plugin_id}/sessions")
async def get_chat_sessions(
    plugin_id: str,
    company_id: int,
    status: Optional[str] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get chat sessions for a widget."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement get chat sessions
    return []


# Public endpoints for chat functionality (no auth required)
@router.post("/plugins/{plugin_id}/sessions")
async def start_chat_session(
    plugin_id: str, session_data: dict, db: AsyncSession = Depends(get_db)
):
    """Start a new chat session (public endpoint)."""
    # TODO: Implement start chat session
    return {"session_id": "session_123", "message": "Chat session started successfully"}


@router.post("/sessions/{session_id}/messages")
async def send_chat_message(
    session_id: str, message_data: dict, db: AsyncSession = Depends(get_db)
):
    """Send a message in a chat session (public endpoint)."""
    # TODO: Implement send chat message
    return {
        "message_id": "message_123",
        "response": "Thank you for your message. How can I help you today?",
        "timestamp": "2024-01-01T12:00:00Z",
    }


@router.get("/plugins/{plugin_id}/embed")
async def get_widget_embed_code(
    plugin_id: str,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get embed code for a chat widget."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Generate embed code
    return {
        "embed_code": f"<script src='https://example.com/chat/{plugin_id}.js'></script>",
        "config": {
            "plugin_id": plugin_id,
            "position": "bottom-right",
            "theme": "default",
        },
    }
