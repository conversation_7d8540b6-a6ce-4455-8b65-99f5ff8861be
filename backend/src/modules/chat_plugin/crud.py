"""
Chat Plugin CRUD operations.
"""

import logging
import secrets
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload

from . import models, schemas

logger = logging.getLogger(__name__)


def generate_plugin_key() -> str:
    """Generate a unique plugin key."""
    return secrets.token_urlsafe(16)


async def create_chat_plugin_config(
    db: AsyncSession, config: schemas.ChatPluginConfigCreate
) -> models.ChatPluginConfig:
    """Create a new chat plugin configuration."""
    db_config = models.ChatPluginConfig(
        company_id=config.company_id,
        agent_id=config.agent_id,
        plugin_key=generate_plugin_key(),
        enabled=config.enabled,
        widget_title=config.widget_title,
        welcome_message=config.welcome_message,
        placeholder_text=config.placeholder_text,
        primary_color=config.primary_color,
        position=config.position,
        size=config.size,
        theme=config.theme,
        show_agent_avatar=config.show_agent_avatar,
        show_typing_indicator=config.show_typing_indicator,
        enable_file_upload=config.enable_file_upload,
        enable_emoji=config.enable_emoji,
        max_file_size_mb=config.max_file_size_mb,
        allowed_file_types=config.allowed_file_types,
        business_hours_only=config.business_hours_only,
        offline_message=config.offline_message,
        auto_open_delay=config.auto_open_delay,
        sound_notifications=config.sound_notifications,
        show_powered_by=config.show_powered_by,
        custom_css=config.custom_css,
        custom_js=config.custom_js,
        status=schemas.PluginStatus.ACTIVE,
    )
    db.add(db_config)
    await db.commit()
    await db.refresh(db_config)
    return db_config


async def get_chat_plugin_config(
    db: AsyncSession,
    plugin_id: int,
    company_id: int,
    include_business_hours: bool = False,
) -> Optional[models.ChatPluginConfig]:
    """Get a single chat plugin configuration by ID."""
    query = select(models.ChatPluginConfig).filter(
        and_(
            models.ChatPluginConfig.id == plugin_id,
            models.ChatPluginConfig.company_id == company_id,
        )
    )

    if include_business_hours:
        query = query.options(selectinload(models.ChatPluginConfig.business_hours))

    result = await db.execute(query)
    return result.scalars().first()


async def get_chat_plugin_config_by_key(
    db: AsyncSession, plugin_key: str, include_business_hours: bool = False
) -> Optional[models.ChatPluginConfig]:
    """Get a chat plugin configuration by its unique key."""
    query = select(models.ChatPluginConfig).filter(
        models.ChatPluginConfig.plugin_key == plugin_key
    )

    if include_business_hours:
        query = query.options(selectinload(models.ChatPluginConfig.business_hours))

    result = await db.execute(query)
    return result.scalars().first()


async def get_chat_plugin_configs(
    db: AsyncSession,
    company_id: int,
    skip: int = 0,
    limit: int = 100,
    status: Optional[schemas.PluginStatus] = None,
    agent_id: Optional[int] = None,
) -> List[models.ChatPluginConfig]:
    """Get chat plugin configurations with filtering."""
    query = select(models.ChatPluginConfig).filter(
        models.ChatPluginConfig.company_id == company_id
    )

    if status:
        query = query.filter(models.ChatPluginConfig.status == status)

    if agent_id:
        query = query.filter(models.ChatPluginConfig.agent_id == agent_id)

    query = query.order_by(desc(models.ChatPluginConfig.created_at))
    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    return result.scalars().all()


async def update_chat_plugin_config(
    db: AsyncSession,
    plugin_id: int,
    company_id: int,
    config_update: schemas.ChatPluginConfigUpdate,
) -> Optional[models.ChatPluginConfig]:
    """Update a chat plugin configuration."""
    db_config = await get_chat_plugin_config(db, plugin_id, company_id)
    if not db_config:
        return None

    update_data = config_update.dict(exclude_unset=True)

    for field, value in update_data.items():
        setattr(db_config, field, value)

    await db.commit()
    await db.refresh(db_config)
    return db_config


async def delete_chat_plugin_config(
    db: AsyncSession, plugin_id: int, company_id: int
) -> bool:
    """Delete a chat plugin configuration."""
    db_config = await get_chat_plugin_config(db, plugin_id, company_id)
    if not db_config:
        return False

    await db.delete(db_config)
    await db.commit()
    return True


async def update_plugin_activity(db: AsyncSession, plugin_id: int) -> bool:
    """Update plugin last activity timestamp."""
    result = await db.execute(
        select(models.ChatPluginConfig).filter(models.ChatPluginConfig.id == plugin_id)
    )
    db_config = result.scalars().first()

    if not db_config:
        return False

    db_config.last_activity_at = datetime.utcnow()
    await db.commit()
    return True


# Chat Session CRUD
async def create_chat_session(
    db: AsyncSession, session: schemas.ChatSessionCreate
) -> models.ChatSession:
    """Create a new chat session."""
    db_session = models.ChatSession(
        plugin_id=session.plugin_id,
        visitor_id=session.visitor_id,
        session_data=session.session_data or {},
        user_agent=session.user_agent,
        ip_address=session.ip_address,
        referrer=session.referrer,
        page_url=session.page_url,
        country=session.country,
        city=session.city,
        device_type=session.device_type,
        browser=session.browser,
        is_active=True,
    )
    db.add(db_session)
    await db.commit()
    await db.refresh(db_session)

    # Update plugin activity
    await update_plugin_activity(db, session.plugin_id)

    return db_session


async def get_chat_session(
    db: AsyncSession, session_id: int, company_id: int
) -> Optional[models.ChatSession]:
    """Get a chat session by ID."""
    result = await db.execute(
        select(models.ChatSession)
        .join(models.ChatPluginConfig)
        .filter(
            and_(
                models.ChatSession.id == session_id,
                models.ChatPluginConfig.company_id == company_id,
            )
        )
    )
    return result.scalars().first()


async def get_chat_sessions(
    db: AsyncSession,
    plugin_id: int,
    company_id: int,
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = None,
) -> List[models.ChatSession]:
    """Get chat sessions for a plugin."""
    # First verify the plugin belongs to the company
    plugin_exists = await db.execute(
        select(models.ChatPluginConfig.id).filter(
            and_(
                models.ChatPluginConfig.id == plugin_id,
                models.ChatPluginConfig.company_id == company_id,
            )
        )
    )
    if not plugin_exists.scalar():
        return []

    query = select(models.ChatSession).filter(models.ChatSession.plugin_id == plugin_id)

    if is_active is not None:
        query = query.filter(models.ChatSession.is_active == is_active)

    query = query.order_by(desc(models.ChatSession.started_at))
    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    return result.scalars().all()


async def end_chat_session(
    db: AsyncSession,
    session_id: int,
    company_id: int,
    satisfaction_rating: Optional[int] = None,
    feedback: Optional[str] = None,
) -> Optional[models.ChatSession]:
    """End a chat session."""
    db_session = await get_chat_session(db, session_id, company_id)
    if not db_session:
        return None

    now = datetime.utcnow()
    db_session.is_active = False
    db_session.ended_at = now
    db_session.satisfaction_rating = satisfaction_rating
    db_session.feedback = feedback

    # Calculate duration
    if db_session.started_at:
        duration = (now - db_session.started_at).total_seconds()
        db_session.duration_seconds = int(duration)

    await db.commit()
    await db.refresh(db_session)
    return db_session


# Chat Message CRUD
async def create_chat_message(
    db: AsyncSession, message: schemas.ChatMessageCreate
) -> models.ChatMessage:
    """Create a new chat message."""
    db_message = models.ChatMessage(
        session_id=message.session_id,
        message_type=message.message_type,
        content=message.content,
        metadata=message.metadata or {},
    )
    db.add(db_message)
    await db.commit()
    await db.refresh(db_message)

    # Update session message count
    await db.execute(
        select(models.ChatSession)
        .filter(models.ChatSession.id == message.session_id)
        .update({"message_count": models.ChatSession.message_count + 1})
    )
    await db.commit()

    return db_message


async def get_chat_messages(
    db: AsyncSession, session_id: int, company_id: int, skip: int = 0, limit: int = 100
) -> List[models.ChatMessage]:
    """Get messages for a chat session."""
    # First verify the session belongs to the company
    session_exists = await db.execute(
        select(models.ChatSession.id)
        .join(models.ChatPluginConfig)
        .filter(
            and_(
                models.ChatSession.id == session_id,
                models.ChatPluginConfig.company_id == company_id,
            )
        )
    )
    if not session_exists.scalar():
        return []

    query = (
        select(models.ChatMessage)
        .filter(models.ChatMessage.session_id == session_id)
        .order_by(models.ChatMessage.timestamp)
    )

    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    return result.scalars().all()


async def update_message_response_time(
    db: AsyncSession, message_id: int, response_time_ms: int
) -> Optional[models.ChatMessage]:
    """Update message response time."""
    result = await db.execute(
        select(models.ChatMessage).filter(models.ChatMessage.id == message_id)
    )
    db_message = result.scalars().first()

    if not db_message:
        return None

    db_message.response_time_ms = response_time_ms
    await db.commit()
    await db.refresh(db_message)
    return db_message


# Business Hours CRUD
async def create_business_hours(
    db: AsyncSession, hours: schemas.BusinessHoursCreate
) -> models.BusinessHours:
    """Create business hours for a plugin."""
    db_hours = models.BusinessHours(
        plugin_id=hours.plugin_id,
        day_of_week=hours.day_of_week,
        is_open=hours.is_open,
        open_time=hours.open_time,
        close_time=hours.close_time,
        timezone=hours.timezone,
    )
    db.add(db_hours)
    await db.commit()
    await db.refresh(db_hours)
    return db_hours


async def get_business_hours(
    db: AsyncSession, plugin_id: int, company_id: int
) -> List[models.BusinessHours]:
    """Get business hours for a plugin."""
    # First verify the plugin belongs to the company
    plugin_exists = await db.execute(
        select(models.ChatPluginConfig.id).filter(
            and_(
                models.ChatPluginConfig.id == plugin_id,
                models.ChatPluginConfig.company_id == company_id,
            )
        )
    )
    if not plugin_exists.scalar():
        return []

    query = (
        select(models.BusinessHours)
        .filter(models.BusinessHours.plugin_id == plugin_id)
        .order_by(models.BusinessHours.day_of_week)
    )

    result = await db.execute(query)
    return result.scalars().all()


async def get_plugin_count(
    db: AsyncSession, company_id: int, status: Optional[schemas.PluginStatus] = None
) -> int:
    """Get total count of chat plugins."""
    query = select(func.count(models.ChatPluginConfig.id)).filter(
        models.ChatPluginConfig.company_id == company_id
    )

    if status:
        query = query.filter(models.ChatPluginConfig.status == status)

    result = await db.execute(query)
    return result.scalar() or 0
