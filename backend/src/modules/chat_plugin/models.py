"""
Chat Plugin Models for embeddable website chat widgets.

Supports customizable chat widgets with agent integration and branding.
"""

from enum import Enum as PyEnum

from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    Float,
    Foreign<PERSON>ey,
    Integer,
    String,
    Text,
    Time,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base
from modules.conversations.models import Conversation


class WidgetStatus(PyEnum):
    """Chat widget status options."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    PAUSED = "paused"
    MAINTENANCE = "maintenance"


class ChatStatus(PyEnum):
    """Chat session status."""

    ACTIVE = "active"
    WAITING = "waiting"
    ENDED = "ended"
    TRANSFERRED = "transferred"
    ABANDONED = "abandoned"


class MessageType(PyEnum):
    """Chat message types."""

    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    SYSTEM = "system"
    QUICK_REPLY = "quick_reply"
    CARD = "card"
    CAROUSEL = "carousel"


class ChatPluginConfig(Base):
    """
    Chat plugin configuration.
    """

    __tablename__ = "chat_plugin_configs"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Widget Information
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    widget_id = Column(
        String(100), nullable=False, unique=True, index=True
    )  # Public widget ID

    # Widget Configuration
    status = Column(
        String(50), nullable=False, default=WidgetStatus.ACTIVE.value, index=True
    )

    # Agent Assignment
    primary_agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True)
    backup_agent_ids = Column(JSON, nullable=True, default=list)
    auto_assign_agent = Column(Boolean, default=True)

    # Appearance & Branding
    theme = Column(JSON, nullable=True, default=dict)  # Colors, fonts, styling
    position = Column(
        String(20), default="bottom-right"
    )  # bottom-right, bottom-left, etc.
    size = Column(String(20), default="medium")  # small, medium, large

    # Widget Behavior
    auto_open = Column(Boolean, default=False)
    auto_open_delay = Column(Integer, default=5)  # seconds
    show_agent_avatar = Column(Boolean, default=True)
    show_agent_name = Column(Boolean, default=True)
    show_typing_indicator = Column(Boolean, default=True)

    # Welcome Message
    welcome_message = Column(Text, nullable=True)
    welcome_message_delay = Column(Integer, default=2)  # seconds
    show_welcome_message = Column(Boolean, default=True)

    # Business Hours
    respect_business_hours = Column(Boolean, default=True)
    offline_message = Column(Text, nullable=True)
    offline_form_enabled = Column(Boolean, default=True)

    # Features
    file_upload_enabled = Column(Boolean, default=True)
    emoji_enabled = Column(Boolean, default=True)
    quick_replies_enabled = Column(Boolean, default=True)
    rating_enabled = Column(Boolean, default=True)

    # Integration Settings
    google_analytics_enabled = Column(Boolean, default=False)
    google_analytics_id = Column(String(50), nullable=True)

    # Security & Privacy
    require_email = Column(Boolean, default=False)
    require_name = Column(Boolean, default=False)
    gdpr_compliant = Column(Boolean, default=True)
    data_retention_days = Column(Integer, default=365)

    # Allowed Domains
    allowed_domains = Column(JSON, nullable=True, default=list)
    blocked_domains = Column(JSON, nullable=True, default=list)

    # Rate Limiting
    rate_limit_enabled = Column(Boolean, default=True)
    max_messages_per_minute = Column(Integer, default=10)
    max_sessions_per_ip = Column(Integer, default=3)

    # Custom CSS & JavaScript
    custom_css = Column(Text, nullable=True)
    custom_javascript = Column(Text, nullable=True)

    # Statistics
    total_sessions = Column(Integer, default=0)
    total_messages = Column(Integer, default=0)
    average_session_duration = Column(Float, default=0.0)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationships
    company = relationship("Company")
    primary_agent = relationship("Agent", foreign_keys=[primary_agent_id])
    created_by_user = relationship("User", foreign_keys=[user_id])
    chat_sessions = relationship(
        "ChatSession", back_populates="plugin_config", cascade="all, delete-orphan"
    )
    quick_replies = relationship(
        "QuickReply", back_populates="plugin_config", cascade="all, delete-orphan"
    )
    business_hours = relationship(
        "BusinessHours", back_populates="plugin_config", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<ChatPluginConfig(id={self.id}, widget_id='{self.widget_id}', name='{self.name}')>"


class BusinessHours(Base):
    """
    Business hours configuration for a chat plugin.
    """

    __tablename__ = "business_hours"

    id = Column(Integer, primary_key=True, index=True)
    plugin_id = Column(Integer, ForeignKey("chat_plugin_configs.id"), nullable=False)
    day_of_week = Column(Integer, nullable=False)  # 0-6 for Monday-Sunday
    is_open = Column(Boolean, default=True)
    open_time = Column(Time, nullable=True)
    close_time = Column(Time, nullable=True)
    timezone = Column(String(100), nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    plugin_config = relationship("ChatPluginConfig", back_populates="business_hours")

    def __repr__(self):
        return f"<BusinessHours(id={self.id}, plugin_id={self.plugin_id}, day_of_week={self.day_of_week})>"


class ChatSession(Base):
    """
    Individual chat sessions with website visitors.
    """

    __tablename__ = "chat_sessions"

    id = Column(Integer, primary_key=True, index=True)
    plugin_id = Column(Integer, ForeignKey("chat_plugin_configs.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Session Information
    session_id = Column(String(100), nullable=False, unique=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=True)

    # Visitor Information
    visitor_id = Column(String(100), nullable=True, index=True)  # Anonymous visitor ID
    visitor_name = Column(String(255), nullable=True)
    visitor_email = Column(String(255), nullable=True, index=True)
    visitor_phone = Column(String(20), nullable=True)

    # Session Context
    page_url = Column(String(1000), nullable=True)
    page_title = Column(String(500), nullable=True)
    referrer_url = Column(String(1000), nullable=True)
    user_agent = Column(String(500), nullable=True)
    ip_address = Column(String(45), nullable=True)

    # Agent Assignment
    assigned_agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True)
    agent_joined_at = Column(DateTime(timezone=True), nullable=True)

    # Session Status
    status = Column(
        String(50), nullable=False, default=ChatStatus.ACTIVE.value, index=True
    )

    # Timing
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    ended_at = Column(DateTime(timezone=True), nullable=True)
    duration_seconds = Column(Integer, nullable=True)

    # Interaction Metrics
    total_messages = Column(Integer, default=0)
    visitor_messages = Column(Integer, default=0)
    agent_messages = Column(Integer, default=0)

    # Quality Metrics
    visitor_satisfaction = Column(Integer, nullable=True)  # 1-5 rating
    resolution_achieved = Column(Boolean, nullable=True)

    # Business Outcomes
    lead_captured = Column(Boolean, default=False)
    appointment_scheduled = Column(Boolean, default=False)
    sale_made = Column(Boolean, default=False)

    # UTM & Tracking
    utm_source = Column(String(255), nullable=True)
    utm_medium = Column(String(255), nullable=True)
    utm_campaign = Column(String(255), nullable=True)
    utm_term = Column(String(255), nullable=True)
    utm_content = Column(String(255), nullable=True)

    # Geographic Data
    country = Column(String(100), nullable=True)
    region = Column(String(100), nullable=True)
    city = Column(String(100), nullable=True)

    # Device Information
    device_type = Column(String(50), nullable=True)  # desktop, mobile, tablet
    browser = Column(String(100), nullable=True)
    operating_system = Column(String(100), nullable=True)

    # Metadata
    extra_metadata = Column(JSON, nullable=True, default=dict)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    plugin_config = relationship("ChatPluginConfig", back_populates="chat_sessions")
    company = relationship("Company", back_populates="chat_sessions")
    conversation = relationship("Conversation")
    assigned_agent = relationship("Agent")
    messages = relationship(
        "ChatMessage", back_populates="session", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<ChatSession(id={self.id}, session_id='{self.session_id}', status='{self.status}')>"


class ChatMessage(Base):
    """
    Individual messages within a chat session.
    """

    __tablename__ = "chat_messages"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("chat_sessions.id"), nullable=False)

    # Message Information
    message_id = Column(String(100), nullable=False, unique=True, index=True)
    message_type = Column(String(50), nullable=False, default=MessageType.TEXT.value)

    # Message Content
    content = Column(Text, nullable=False)
    formatted_content = Column(Text, nullable=True)  # HTML formatted content

    # Message Metadata
    sender_type = Column(String(50), nullable=False)  # visitor, agent, system
    sender_id = Column(String(100), nullable=True)
    sender_name = Column(String(255), nullable=True)

    # File Attachments
    file_url = Column(String(500), nullable=True)
    file_name = Column(String(255), nullable=True)
    file_size = Column(Integer, nullable=True)
    file_type = Column(String(100), nullable=True)

    # Rich Content
    quick_replies = Column(JSON, nullable=True, default=list)
    cards = Column(JSON, nullable=True, default=list)

    # Message Status
    is_read = Column(Boolean, default=False)
    read_at = Column(DateTime(timezone=True), nullable=True)

    # AI Analysis
    sentiment_score = Column(Float, nullable=True)
    intent = Column(String(100), nullable=True)
    entities = Column(JSON, nullable=True, default=list)

    # Timestamps
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    session = relationship("ChatSession", back_populates="messages")

    def __repr__(self):
        return f"<ChatMessage(id={self.id}, message_id='{self.message_id}', sender_type='{self.sender_type}')>"


class QuickReply(Base):
    """
    Predefined quick reply buttons for chat plugins.
    """

    __tablename__ = "quick_replies"

    id = Column(Integer, primary_key=True, index=True)
    plugin_id = Column(Integer, ForeignKey("chat_plugin_configs.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Quick Reply Information
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)

    # Configuration
    category = Column(String(100), nullable=True)
    order_index = Column(Integer, default=0)

    # Conditions
    show_conditions = Column(JSON, nullable=True, default=list)

    # Usage Statistics
    times_used = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True), nullable=True)

    # Status
    is_active = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    plugin_config = relationship("ChatPluginConfig", back_populates="quick_replies")
    company = relationship("Company")

    def __repr__(self):
        return f"<QuickReply(id={self.id}, title='{self.title}')>"


class ChatAnalytics(Base):
    """
    Analytics for chat plugin performance and user engagement.
    """

    __tablename__ = "chat_analytics"

    id = Column(Integer, primary_key=True, index=True)
    plugin_id = Column(Integer, ForeignKey("chat_plugin_configs.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Time Period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False, default="daily")

    # Session Metrics
    total_sessions = Column(Integer, default=0)
    active_sessions = Column(Integer, default=0)
    completed_sessions = Column(Integer, default=0)
    abandoned_sessions = Column(Integer, default=0)

    # Message Metrics
    total_messages = Column(Integer, default=0)
    visitor_messages = Column(Integer, default=0)
    agent_messages = Column(Integer, default=0)
    average_messages_per_session = Column(Float, default=0.0)

    # Engagement Metrics
    average_session_duration = Column(Float, default=0.0)  # seconds
    bounce_rate = Column(Float, default=0.0)  # percentage
    return_visitor_rate = Column(Float, default=0.0)  # percentage

    # Conversion Metrics
    leads_captured = Column(Integer, default=0)
    appointments_scheduled = Column(Integer, default=0)
    sales_made = Column(Integer, default=0)
    conversion_rate = Column(Float, default=0.0)  # percentage

    # Quality Metrics
    average_satisfaction = Column(Float, nullable=True)
    resolution_rate = Column(Float, default=0.0)  # percentage

    # Agent Performance
    agent_response_time = Column(Float, default=0.0)  # seconds
    agent_utilization = Column(Float, default=0.0)  # percentage

    # Popular Content
    top_pages = Column(JSON, nullable=True, default=list)
    top_referrers = Column(JSON, nullable=True, default=list)
    popular_quick_replies = Column(JSON, nullable=True, default=list)

    # Device & Browser
    device_breakdown = Column(JSON, nullable=True, default=dict)
    browser_breakdown = Column(JSON, nullable=True, default=dict)

    # Geographic Data
    country_breakdown = Column(JSON, nullable=True, default=dict)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    plugin_config = relationship("ChatPluginConfig")
    company = relationship("Company")

    def __repr__(self):
        return f"<ChatAnalytics(widget_id={self.widget_id}, date='{self.date}')>"


class ChatFeedback(Base):
    """
    Visitor feedback and ratings for chat sessions.
    """

    __tablename__ = "chat_feedback"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("chat_sessions.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Feedback Content
    rating = Column(Integer, nullable=False)  # 1-5 stars
    comment = Column(Text, nullable=True)

    # Feedback Categories
    helpfulness_rating = Column(Integer, nullable=True)  # 1-5
    speed_rating = Column(Integer, nullable=True)  # 1-5
    friendliness_rating = Column(Integer, nullable=True)  # 1-5

    # Feedback Context
    feedback_trigger = Column(
        String(100), nullable=True
    )  # end_of_chat, proactive, etc.

    # Visitor Information
    visitor_email = Column(String(255), nullable=True)
    visitor_name = Column(String(255), nullable=True)

    # Analysis
    sentiment_score = Column(Float, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    session = relationship("ChatSession")
    company = relationship("Company")

    def __repr__(self):
        return f"<ChatFeedback(id={self.id}, rating={self.rating}, session_id={self.session_id})>"
