"""
Chat Plugin Schemas for API requests and responses.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field, HttpUrl


class PluginStatus(str, Enum):
    """Plugin status options."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    TESTING = "testing"
    MAINTENANCE = "maintenance"


class PluginPosition(str, Enum):
    """Plugin position options."""

    BOTTOM_RIGHT = "bottom-right"
    BOTTOM_LEFT = "bottom-left"
    TOP_RIGHT = "top-right"
    TOP_LEFT = "top-left"


class PluginSize(str, Enum):
    """Plugin size options."""

    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"


class PluginTheme(str, Enum):
    """Plugin theme options."""

    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"


# Chat Plugin Configuration Schemas
class ChatPluginConfigBase(BaseModel):
    enabled: bool = True
    widget_title: str = Field(default="Chat with us", max_length=100)
    welcome_message: str = Field(
        default="Hi! How can we help you today?", max_length=500
    )
    placeholder_text: str = Field(default="Type your message...", max_length=100)
    primary_color: str = Field(default="#3B82F6", pattern=r"^#[0-9A-Fa-f]{6}$")
    position: PluginPosition = PluginPosition.BOTTOM_RIGHT
    size: PluginSize = PluginSize.MEDIUM
    theme: PluginTheme = PluginTheme.AUTO
    show_agent_avatar: bool = True
    show_typing_indicator: bool = True
    enable_file_upload: bool = False
    enable_emoji: bool = False
    max_file_size_mb: int = Field(default=10, ge=1, le=50)
    allowed_file_types: List[str] = Field(
        default_factory=lambda: ["jpg", "jpeg", "png", "pdf", "doc", "docx"]
    )
    business_hours_only: bool = False
    offline_message: str = Field(
        default="We're currently offline. Please leave a message and we'll get back to you soon!",
        max_length=500,
    )
    auto_open_delay: Optional[int] = Field(None, ge=0, le=60)  # seconds
    sound_notifications: bool = True
    show_powered_by: bool = True
    custom_css: Optional[str] = None
    custom_js: Optional[str] = None


class ChatPluginConfigCreate(ChatPluginConfigBase):
    company_id: int
    agent_id: Optional[int] = None
    widget_id: str


class ChatPluginConfigUpdate(BaseModel):
    enabled: Optional[bool] = None
    widget_title: Optional[str] = Field(None, max_length=100)
    welcome_message: Optional[str] = Field(None, max_length=500)
    placeholder_text: Optional[str] = Field(None, max_length=100)
    primary_color: Optional[str] = Field(None, pattern=r"^#[0-9A-Fa-f]{6}$")
    position: Optional[PluginPosition] = None
    size: Optional[PluginSize] = None
    theme: Optional[PluginTheme] = None
    show_agent_avatar: Optional[bool] = None
    show_typing_indicator: Optional[bool] = None
    enable_file_upload: Optional[bool] = None
    enable_emoji: Optional[bool] = None
    max_file_size_mb: Optional[int] = Field(None, ge=1, le=50)
    allowed_file_types: Optional[List[str]] = None
    business_hours_only: Optional[bool] = None
    offline_message: Optional[str] = Field(None, max_length=500)
    auto_open_delay: Optional[int] = Field(None, ge=0, le=60)
    sound_notifications: Optional[bool] = None
    show_powered_by: Optional[bool] = None
    custom_css: Optional[str] = None
    custom_js: Optional[str] = None
    agent_id: Optional[int] = None


class ChatPluginConfig(ChatPluginConfigBase):
    id: int
    company_id: int
    agent_id: Optional[int] = None
    widget_id: str
    plugin_key: str  # Unique identifier
    status: PluginStatus = PluginStatus.ACTIVE
    total_conversations: int = 0
    total_messages: int = 0
    last_activity_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Business Hours Schemas
class BusinessHoursBase(BaseModel):
    day_of_week: int = Field(..., ge=0, le=6)  # 0=Monday, 6=Sunday
    is_open: bool = True
    open_time: Optional[str] = Field(
        None, pattern=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$"
    )  # HH:MM format
    close_time: Optional[str] = Field(
        None, pattern=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$"
    )  # HH:MM format
    timezone: str = Field(default="UTC", max_length=50)


class BusinessHoursCreate(BusinessHoursBase):
    plugin_id: int


class BusinessHoursUpdate(BaseModel):
    is_open: Optional[bool] = None
    open_time: Optional[str] = Field(None, pattern=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$")
    close_time: Optional[str] = Field(None, pattern=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$")
    timezone: Optional[str] = Field(None, max_length=50)


class BusinessHours(BusinessHoursBase):
    id: int
    plugin_id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Chat Session Schemas
class ChatSessionBase(BaseModel):
    visitor_id: str = Field(..., max_length=255)
    session_data: Optional[Dict[str, Any]] = None
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    referrer: Optional[str] = None
    page_url: Optional[str] = None
    country: Optional[str] = Field(None, max_length=100)
    city: Optional[str] = Field(None, max_length=100)
    device_type: Optional[str] = Field(None, max_length=50)
    browser: Optional[str] = Field(None, max_length=100)


class ChatSessionCreate(ChatSessionBase):
    plugin_id: int


class ChatSession(ChatSessionBase):
    id: int
    plugin_id: int
    conversation_id: Optional[str] = None
    is_active: bool = True
    started_at: datetime
    ended_at: Optional[datetime] = None
    duration_seconds: Optional[int] = None
    message_count: int = 0
    satisfaction_rating: Optional[int] = Field(None, ge=1, le=5)
    feedback: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


# Chat Message Schemas
class ChatMessageBase(BaseModel):
    message_type: str = Field(..., max_length=50)  # user, agent, system
    content: str
    metadata: Optional[Dict[str, Any]] = None


class ChatMessageCreate(ChatMessageBase):
    session_id: int


class ChatMessage(ChatMessageBase):
    id: int
    session_id: int
    timestamp: datetime
    is_read: bool = False
    response_time_ms: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)


# Plugin Analytics Schemas
class PluginAnalytics(BaseModel):
    plugin_id: int
    total_visitors: int
    total_conversations: int
    total_messages: int
    average_session_duration: Optional[float] = None
    conversion_rate: float
    satisfaction_rating: Optional[float] = None
    top_pages: List[Dict[str, Any]] = []
    visitor_countries: Dict[str, int] = {}
    device_breakdown: Dict[str, int] = {}
    browser_breakdown: Dict[str, int] = {}
    hourly_activity: List[Dict[str, Any]] = []
    daily_trends: List[Dict[str, Any]] = []


class PluginPerformanceMetrics(BaseModel):
    plugin_id: int
    date: datetime
    unique_visitors: int = 0
    new_conversations: int = 0
    messages_sent: int = 0
    messages_received: int = 0
    average_response_time: Optional[float] = None
    satisfaction_scores: List[int] = []
    bounce_rate: float = 0.0


# Embed Code Schemas
class EmbedCodeRequest(BaseModel):
    plugin_id: int
    domain: Optional[str] = None
    custom_config: Optional[Dict[str, Any]] = None


class EmbedCodeResponse(BaseModel):
    plugin_id: int
    plugin_key: str
    script_url: str
    embed_code: str
    installation_instructions: str
    test_url: str


# Plugin Installation Schemas
class PluginInstallationBase(BaseModel):
    domain: str = Field(..., max_length=255)
    installation_method: str = Field(..., max_length=50)  # script, iframe, api
    is_verified: bool = False
    last_seen_at: Optional[datetime] = None
    custom_config: Optional[Dict[str, Any]] = None


class PluginInstallationCreate(PluginInstallationBase):
    plugin_id: int


class PluginInstallationUpdate(BaseModel):
    is_verified: Optional[bool] = None
    last_seen_at: Optional[datetime] = None
    custom_config: Optional[Dict[str, Any]] = None


class PluginInstallation(PluginInstallationBase):
    id: int
    plugin_id: int
    installation_key: str
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Response Schemas
class ChatPluginListResponse(BaseModel):
    plugins: List[ChatPluginConfig]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class ChatSessionListResponse(BaseModel):
    sessions: List[ChatSession]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class ChatMessageListResponse(BaseModel):
    messages: List[ChatMessage]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


# Plugin Configuration with Business Hours
class ChatPluginConfigWithHours(ChatPluginConfig):
    business_hours: List[BusinessHours] = []


# Live Chat Schemas
class LiveChatRequest(BaseModel):
    plugin_key: str
    visitor_id: str
    message: str
    session_data: Optional[Dict[str, Any]] = None
    page_context: Optional[Dict[str, Any]] = None


class LiveChatResponse(BaseModel):
    success: bool
    message: str
    conversation_id: Optional[str] = None
    response: Optional[str] = None
    session_id: Optional[int] = None
    error: Optional[str] = None


# Plugin Status Update Schema
class PluginStatusUpdate(BaseModel):
    status: PluginStatus
    reason: Optional[str] = None


# Plugin Testing Schemas
class PluginTestRequest(BaseModel):
    test_message: str
    visitor_context: Optional[Dict[str, Any]] = None


class PluginTestResponse(BaseModel):
    success: bool
    response_message: str
    response_time_ms: int
    conversation_id: Optional[str] = None
    error: Optional[str] = None
