"""
Company CRUD operations for home service platform.
"""

import logging
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload

from modules.users.models import User

from . import models, schemas

logger = logging.getLogger(__name__)


async def get_company(
    db: AsyncSession, company_id: int, user_id: int, include_details: bool = False
) -> Optional[models.Company]:
    """Get a single company by ID."""
    query = select(models.Company).filter(
        and_(models.Company.id == company_id, models.Company.user_id == user_id)
    )

    if include_details:
        query = query.options(
            selectinload(models.Company.agents),
            selectinload(models.Company.phone_numbers),
            selectinload(models.Company.service_catalog),
        )

    result = await db.execute(query)
    return result.scalars().first()


async def get_companies(
    db: AsyncSession,
    user_id: int,
    skip: int = 0,
    limit: int = 100,
    business_type: Optional[schemas.BusinessType] = None,
    is_active: Optional[bool] = None,
    search: Optional[str] = None,
) -> List[models.Company]:
    """Get companies with filtering and pagination."""
    query = select(models.Company).filter(models.Company.user_id == user_id)

    # Apply filters
    if business_type:
        query = query.filter(models.Company.business_type == business_type)

    if is_active is not None:
        query = query.filter(models.Company.is_active == is_active)

    if search:
        query = query.filter(
            or_(
                models.Company.name.ilike(f"%{search}%"),
                models.Company.description.ilike(f"%{search}%"),
                models.Company.business_type.ilike(f"%{search}%"),
            )
        )

    # Apply pagination and ordering
    query = query.order_by(desc(models.Company.created_at)).offset(skip).limit(limit)

    result = await db.execute(query)
    return result.scalars().all()


async def get_companies_count(
    db: AsyncSession,
    user_id: int,
    business_type: Optional[schemas.BusinessType] = None,
    is_active: Optional[bool] = None,
    search: Optional[str] = None,
) -> int:
    """Get total count of companies with filters."""
    query = select(func.count(models.Company.id)).filter(
        models.Company.user_id == user_id
    )

    if business_type:
        query = query.filter(models.Company.business_type == business_type)

    if is_active is not None:
        query = query.filter(models.Company.is_active == is_active)

    if search:
        query = query.filter(
            or_(
                models.Company.name.ilike(f"%{search}%"),
                models.Company.description.ilike(f"%{search}%"),
                models.Company.business_type.ilike(f"%{search}%"),
            )
        )

    result = await db.execute(query)
    return result.scalar()


async def create_company(
    db: AsyncSession, company: schemas.CompanyCreate, user_id: int
) -> models.Company:
    """Create a new company."""
    db_company = models.Company(**company.model_dump(), user_id=user_id)

    db.add(db_company)
    await db.commit()
    await db.refresh(db_company)

    # Create default settings for the company
    await create_company_settings(
        db, schemas.CompanySettingsCreate(company_id=db_company.id)
    )

    logger.info(f"Created company {db_company.id} for user {user_id}")
    return db_company


async def update_company(
    db: AsyncSession,
    company_id: int,
    user_id: int,
    company_update: schemas.CompanyUpdate,
) -> Optional[models.Company]:
    """Update an existing company."""
    db_company = await get_company(db, company_id, user_id)
    if not db_company:
        return None

    # Update fields
    update_data = company_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_company, field, value)

    await db.commit()
    await db.refresh(db_company)

    logger.info(f"Updated company {company_id}")
    return db_company


async def delete_company(db: AsyncSession, company_id: int, user_id: int) -> bool:
    """Delete a company and all related data."""
    db_company = await get_company(db, company_id, user_id)
    if not db_company:
        return False

    # The cascade="all, delete-orphan" in relationships will handle cleanup
    await db.delete(db_company)
    await db.commit()

    logger.info(f"Deleted company {company_id}")
    return True


# Company Settings CRUD
async def get_company_settings(
    db: AsyncSession, company_id: int, user_id: int
) -> Optional[models.CompanySettings]:
    """Get company settings."""
    # Verify company belongs to user
    company = await get_company(db, company_id, user_id)
    if not company:
        return None

    result = await db.execute(
        select(models.CompanySettings).filter(
            models.CompanySettings.company_id == company_id
        )
    )
    return result.scalars().first()


async def create_company_settings(
    db: AsyncSession, settings: schemas.CompanySettingsCreate
) -> models.CompanySettings:
    """Create company settings."""
    db_settings = models.CompanySettings(**settings.model_dump())

    db.add(db_settings)
    await db.commit()
    await db.refresh(db_settings)

    return db_settings


async def update_company_settings(
    db: AsyncSession,
    company_id: int,
    user_id: int,
    settings_update: schemas.CompanySettingsUpdate,
) -> Optional[models.CompanySettings]:
    """Update company settings."""
    # Verify company belongs to user
    company = await get_company(db, company_id, user_id)
    if not company:
        return None

    db_settings = await get_company_settings(db, company_id, user_id)
    if not db_settings:
        # Create settings if they don't exist
        db_settings = await create_company_settings(
            db,
            schemas.CompanySettingsCreate(
                company_id=company_id, **settings_update.model_dump(exclude_unset=True)
            ),
        )
        return db_settings

    # Update existing settings
    update_data = settings_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_settings, field, value)

    await db.commit()
    await db.refresh(db_settings)

    return db_settings


# Company Members CRUD
async def get_company_members(
    db: AsyncSession, company_id: int, user_id: int
) -> List[models.CompanyMember]:
    """Get all members of a company."""
    # Verify company belongs to user
    company = await get_company(db, company_id, user_id)
    if not company:
        return []

    result = await db.execute(
        select(models.CompanyMember)
        .options(joinedload(models.CompanyMember.user))
        .filter(models.CompanyMember.company_id == company_id)
        .order_by(models.CompanyMember.role, models.CompanyMember.created_at)
    )
    return result.scalars().all()


async def add_company_member(
    db: AsyncSession,
    company_id: int,
    user_id: int,
    member_data: schemas.CompanyMemberCreate,
) -> Optional[models.CompanyMember]:
    """Add a member to a company."""
    # Verify company belongs to user
    company = await get_company(db, company_id, user_id)
    if not company:
        return None

    # Check if user is already a member
    existing_member = await db.execute(
        select(models.CompanyMember).filter(
            and_(
                models.CompanyMember.company_id == company_id,
                models.CompanyMember.user_id == member_data.user_id,
            )
        )
    )
    if existing_member.scalars().first():
        return None  # User is already a member

    db_member = models.CompanyMember(**member_data.model_dump())

    db.add(db_member)
    await db.commit()
    await db.refresh(db_member)

    logger.info(f"Added member {member_data.user_id} to company {company_id}")
    return db_member


async def update_company_member(
    db: AsyncSession,
    member_id: int,
    company_id: int,
    user_id: int,
    member_update: schemas.CompanyMemberUpdate,
) -> Optional[models.CompanyMember]:
    """Update a company member."""
    # Verify company belongs to user
    company = await get_company(db, company_id, user_id)
    if not company:
        return None

    result = await db.execute(
        select(models.CompanyMember).filter(
            and_(
                models.CompanyMember.id == member_id,
                models.CompanyMember.company_id == company_id,
            )
        )
    )
    db_member = result.scalars().first()

    if not db_member:
        return None

    update_data = member_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_member, field, value)

    await db.commit()
    await db.refresh(db_member)

    return db_member


async def remove_company_member(
    db: AsyncSession, member_id: int, company_id: int, user_id: int
) -> bool:
    """Remove a member from a company."""
    # Verify company belongs to user
    company = await get_company(db, company_id, user_id)
    if not company:
        return False

    result = await db.execute(
        select(models.CompanyMember).filter(
            and_(
                models.CompanyMember.id == member_id,
                models.CompanyMember.company_id == company_id,
            )
        )
    )
    db_member = result.scalars().first()

    if not db_member:
        return False

    # Don't allow removing the owner
    if db_member.role == schemas.CompanyRole.OWNER:
        return False

    await db.delete(db_member)
    await db.commit()

    logger.info(f"Removed member {member_id} from company {company_id}")
    return True


async def get_company_dashboard_data(
    db: AsyncSession, company_id: int, user_id: int
) -> Optional[Dict[str, Any]]:
    """Get dashboard data for a company."""
    # Verify company belongs to user
    company = await get_company(db, company_id, user_id)
    if not company:
        return None

    # This would be implemented with actual queries to get:
    # - Today's stats (calls, bookings, etc.)
    # - Week/month stats
    # - Recent activities
    # - Agent status
    # - Upcoming appointments

    # Placeholder implementation
    return {
        "today_stats": {
            "total_calls": 0,
            "answered_calls": 0,
            "total_bookings": 0,
            "confirmed_bookings": 0,
        },
        "week_stats": {"total_calls": 0, "total_bookings": 0, "new_customers": 0},
        "month_stats": {"total_calls": 0, "total_bookings": 0, "revenue": 0},
        "recent_calls": [],
        "recent_bookings": [],
        "agent_status": [],
        "upcoming_appointments": [],
    }
