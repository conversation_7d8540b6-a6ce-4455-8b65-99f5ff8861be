"""
Company Schemas for API requests and responses.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, EmailStr, Field


class BusinessType(str, Enum):
    """Common home service business types."""

    PLUMBING = "plumbing"
    ELECTRICAL = "electrical"
    HVAC = "hvac"
    LANDSCAPING = "landscaping"
    CLEANING = "cleaning"
    HANDYMAN = "handyman"
    ROOFING = "roofing"
    PAINTING = "painting"
    FLOORING = "flooring"
    PEST_CONTROL = "pest_control"
    APPLIANCE_REPAIR = "appliance_repair"
    GENERAL_CONTRACTOR = "general_contractor"
    OTHER = "other"


class CompanyRole(str, Enum):
    """Company member roles."""

    OWNER = "owner"
    ADMIN = "admin"
    MANAGER = "manager"
    MEMBER = "member"


class SubscriptionTier(str, Enum):
    """Subscription tiers."""

    BASIC = "basic"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"


# Company Base Schemas
class CompanyBase(BaseModel):
    name: str = Field(..., max_length=255)
    description: Optional[str] = None
    phone: Optional[str] = Field(None, max_length=20)
    email: Optional[EmailStr] = None
    website: Optional[str] = Field(None, max_length=255)
    address: Optional[str] = Field(None, max_length=500)
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=50)
    zip_code: Optional[str] = Field(None, max_length=20)
    country: str = Field(default="US", max_length=100)
    business_type: Optional[BusinessType] = None
    license_number: Optional[str] = Field(None, max_length=100)
    insurance_info: Optional[Dict[str, Any]] = None
    business_hours: Optional[Dict[str, Any]] = None
    timezone: str = Field(default="UTC", max_length=50)
    service_areas: Optional[List[str]] = None
    logo_url: Optional[str] = Field(None, max_length=500)
    brand_colors: Optional[Dict[str, str]] = None
    custom_css: Optional[str] = None


class CompanyCreate(CompanyBase):
    pass


class CompanyUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    phone: Optional[str] = Field(None, max_length=20)
    email: Optional[EmailStr] = None
    website: Optional[str] = Field(None, max_length=255)
    address: Optional[str] = Field(None, max_length=500)
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=50)
    zip_code: Optional[str] = Field(None, max_length=20)
    country: Optional[str] = Field(None, max_length=100)
    business_type: Optional[BusinessType] = None
    license_number: Optional[str] = Field(None, max_length=100)
    insurance_info: Optional[Dict[str, Any]] = None
    business_hours: Optional[Dict[str, Any]] = None
    timezone: Optional[str] = Field(None, max_length=50)
    service_areas: Optional[List[str]] = None
    logo_url: Optional[str] = Field(None, max_length=500)
    brand_colors: Optional[Dict[str, str]] = None
    custom_css: Optional[str] = None
    is_active: Optional[bool] = None


class Company(CompanyBase):
    id: int
    user_id: int
    is_active: bool = True
    is_verified: bool = False
    subscription_tier: SubscriptionTier = SubscriptionTier.BASIC
    settings: Optional[Dict[str, Any]] = None
    notification_settings: Optional[Dict[str, Any]] = None
    integration_settings: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class CompanyWithDetails(Company):
    """Company with additional details like agents, phone numbers, etc."""

    agents_count: int = 0
    phone_numbers_count: int = 0
    active_bookings_count: int = 0
    total_calls_today: int = 0


# Company Member Schemas
class CompanyMemberBase(BaseModel):
    role: CompanyRole = CompanyRole.MEMBER
    title: Optional[str] = Field(None, max_length=100)
    department: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    permissions: Optional[List[str]] = None


class CompanyMemberCreate(CompanyMemberBase):
    user_id: int
    company_id: int


class CompanyMemberUpdate(BaseModel):
    role: Optional[CompanyRole] = None
    title: Optional[str] = Field(None, max_length=100)
    department: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    permissions: Optional[List[str]] = None
    is_active: Optional[bool] = None


class CompanyMember(CompanyMemberBase):
    id: int
    company_id: int
    user_id: int
    is_active: bool = True
    invited_at: Optional[datetime] = None
    joined_at: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class CompanyMemberWithUser(CompanyMember):
    """Company member with user details."""

    user_email: str
    user_full_name: Optional[str] = None


# Company Settings Schemas
class CompanySettingsBase(BaseModel):
    default_agent_id: Optional[int] = None
    auto_assign_calls: bool = True
    call_recording_enabled: bool = False
    booking_enabled: bool = True
    require_approval: bool = False
    advance_booking_days: int = 30
    booking_buffer_minutes: int = 15
    email_notifications: bool = True
    sms_notifications: bool = False
    webhook_url: Optional[str] = Field(None, max_length=500)
    business_hours_enforcement: bool = True
    holiday_schedule: Optional[Dict[str, Any]] = None
    emergency_contact: Optional[Dict[str, Any]] = None
    google_calendar_enabled: bool = False
    google_calendar_id: Optional[str] = Field(None, max_length=255)
    stripe_enabled: bool = False
    stripe_account_id: Optional[str] = Field(None, max_length=255)
    custom_fields: Optional[Dict[str, Any]] = None


class CompanySettingsCreate(CompanySettingsBase):
    company_id: int


class CompanySettingsUpdate(CompanySettingsBase):
    pass


class CompanySettings(CompanySettingsBase):
    id: int
    company_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Company Analytics Schemas
class CompanyAnalytics(BaseModel):
    company_id: int
    date: datetime
    period_type: str
    total_calls: int = 0
    answered_calls: int = 0
    missed_calls: int = 0
    average_call_duration: int = 0
    total_bookings: int = 0
    confirmed_bookings: int = 0
    cancelled_bookings: int = 0
    booking_conversion_rate: int = 0
    new_customers: int = 0
    returning_customers: int = 0
    customer_satisfaction: Optional[int] = None
    total_revenue: int = 0
    average_job_value: int = 0
    agent_utilization: int = 0
    response_time_avg: int = 0

    model_config = ConfigDict(from_attributes=True)


# Business Hours Schema
class BusinessHours(BaseModel):
    """Business hours configuration."""

    monday: Optional[Dict[str, str]] = None  # {"open": "09:00", "close": "17:00"}
    tuesday: Optional[Dict[str, str]] = None
    wednesday: Optional[Dict[str, str]] = None
    thursday: Optional[Dict[str, str]] = None
    friday: Optional[Dict[str, str]] = None
    saturday: Optional[Dict[str, str]] = None
    sunday: Optional[Dict[str, str]] = None
    timezone: str = "UTC"
    holidays: Optional[List[str]] = None  # List of holiday dates


# Brand Configuration Schema
class BrandConfig(BaseModel):
    """Brand configuration for widgets and forms."""

    primary_color: str = "#007bff"
    secondary_color: str = "#6c757d"
    accent_color: str = "#28a745"
    logo_url: Optional[str] = None
    font_family: str = "Arial, sans-serif"
    custom_css: Optional[str] = None


# Company Dashboard Schema
class CompanyDashboard(BaseModel):
    """Company dashboard summary."""

    company: Company
    today_stats: Dict[str, int]
    week_stats: Dict[str, int]
    month_stats: Dict[str, int]
    recent_calls: List[Dict[str, Any]]
    recent_bookings: List[Dict[str, Any]]
    agent_status: List[Dict[str, Any]]
    upcoming_appointments: List[Dict[str, Any]]


# Response Models
class CompanyListResponse(BaseModel):
    """Response for company list with pagination."""

    companies: List[Company]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class ApiResponse(BaseModel):
    """Standard API response wrapper."""

    success: bool
    message: str
    data: Optional[Any] = None
    errors: Optional[List[str]] = None


# Company Invitation Schema
class CompanyInvitation(BaseModel):
    """Company member invitation."""

    email: EmailStr
    role: CompanyRole = CompanyRole.MEMBER
    title: Optional[str] = None
    department: Optional[str] = None
    permissions: Optional[List[str]] = None
    message: Optional[str] = None
