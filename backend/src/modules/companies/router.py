"""
Company API Router for home service platform.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.service import get_current_active_user
from modules.users.models import User as DBUser

from . import crud, schemas

logger = logging.getLogger(__name__)

router = APIRouter(tags=["companies"])


@router.get("/", response_model=schemas.CompanyListResponse)
async def get_companies(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    business_type: Optional[schemas.BusinessType] = None,
    is_active: Optional[bool] = None,
    search: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get list of companies for the current user."""
    companies = await crud.get_companies(
        db=db,
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        business_type=business_type,
        is_active=is_active,
        search=search,
    )

    total = await crud.get_companies_count(
        db=db,
        user_id=current_user.id,
        business_type=business_type,
        is_active=is_active,
        search=search,
    )

    return schemas.CompanyListResponse(
        companies=companies,
        total=total,
        page=skip // limit + 1,
        per_page=limit,
        has_next=(skip + limit) < total,
        has_prev=skip > 0,
    )


@router.post("/", response_model=schemas.Company)
async def create_company(
    company: schemas.CompanyCreate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a new company."""
    try:
        db_company = await crud.create_company(
            db=db, company=company, user_id=current_user.id
        )
        return db_company
    except Exception as e:
        logger.error(f"Error creating company: {e}")
        raise HTTPException(status_code=400, detail="Failed to create company")


@router.get("/{company_id}", response_model=schemas.CompanyWithDetails)
async def get_company(
    company_id: int,
    include_details: bool = Query(False),
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get a specific company by ID."""
    db_company = await crud.get_company(
        db=db,
        company_id=company_id,
        user_id=current_user.id,
        include_details=include_details,
    )

    if not db_company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Add additional details if requested
    if include_details:
        # Get counts for various related entities
        # This would be implemented with actual queries
        company_data = schemas.CompanyWithDetails(
            **db_company.__dict__,
            agents_count=0,  # len(db_company.agents) if db_company.agents else 0,
            phone_numbers_count=0,  # len(db_company.phone_numbers) if db_company.phone_numbers else 0,
            active_bookings_count=0,
            total_calls_today=0,
        )
        return company_data

    return db_company


@router.put("/{company_id}", response_model=schemas.Company)
async def update_company(
    company_id: int,
    company_update: schemas.CompanyUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Update a company."""
    db_company = await crud.update_company(
        db=db,
        company_id=company_id,
        user_id=current_user.id,
        company_update=company_update,
    )

    if not db_company:
        raise HTTPException(status_code=404, detail="Company not found")

    return db_company


@router.delete("/{company_id}")
async def delete_company(
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Delete a company."""
    success = await crud.delete_company(
        db=db, company_id=company_id, user_id=current_user.id
    )

    if not success:
        raise HTTPException(status_code=404, detail="Company not found")

    return {"message": "Company deleted successfully"}


# Company Settings Endpoints
@router.get("/{company_id}/settings", response_model=schemas.CompanySettings)
async def get_company_settings(
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get company settings."""
    settings = await crud.get_company_settings(
        db=db, company_id=company_id, user_id=current_user.id
    )

    if not settings:
        raise HTTPException(status_code=404, detail="Company or settings not found")

    return settings


@router.put("/{company_id}/settings", response_model=schemas.CompanySettings)
async def update_company_settings(
    company_id: int,
    settings_update: schemas.CompanySettingsUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Update company settings."""
    settings = await crud.update_company_settings(
        db=db,
        company_id=company_id,
        user_id=current_user.id,
        settings_update=settings_update,
    )

    if not settings:
        raise HTTPException(status_code=404, detail="Company not found")

    return settings


# Company Members Endpoints
@router.get("/{company_id}/members", response_model=List[schemas.CompanyMember])
async def get_company_members(
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get company members."""
    members = await crud.get_company_members(
        db=db, company_id=company_id, user_id=current_user.id
    )

    return members


@router.post("/{company_id}/members", response_model=schemas.CompanyMember)
async def add_company_member(
    company_id: int,
    member_data: schemas.CompanyMemberCreate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Add a member to the company."""
    # Ensure the company_id matches
    member_data.company_id = company_id

    member = await crud.add_company_member(
        db=db, company_id=company_id, user_id=current_user.id, member_data=member_data
    )

    if not member:
        raise HTTPException(
            status_code=400,
            detail="Failed to add member (company not found or user already a member)",
        )

    return member


@router.put("/{company_id}/members/{member_id}", response_model=schemas.CompanyMember)
async def update_company_member(
    company_id: int,
    member_id: int,
    member_update: schemas.CompanyMemberUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Update a company member."""
    member = await crud.update_company_member(
        db=db,
        member_id=member_id,
        company_id=company_id,
        user_id=current_user.id,
        member_update=member_update,
    )

    if not member:
        raise HTTPException(status_code=404, detail="Member not found")

    return member


@router.delete("/{company_id}/members/{member_id}")
async def remove_company_member(
    company_id: int,
    member_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Remove a member from the company."""
    success = await crud.remove_company_member(
        db=db, member_id=member_id, company_id=company_id, user_id=current_user.id
    )

    if not success:
        raise HTTPException(
            status_code=404, detail="Member not found or cannot be removed"
        )

    return {"message": "Member removed successfully"}


# Company Dashboard
@router.get("/{company_id}/dashboard", response_model=schemas.CompanyDashboard)
async def get_company_dashboard(
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get company dashboard data."""
    # Get company
    company = await crud.get_company(
        db=db, company_id=company_id, user_id=current_user.id
    )

    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Get dashboard data
    dashboard_data = await crud.get_company_dashboard_data(
        db=db, company_id=company_id, user_id=current_user.id
    )

    return schemas.CompanyDashboard(company=company, **dashboard_data)


# Business Hours Helper Endpoint
@router.get("/business-types", response_model=List[str])
async def get_business_types():
    """Get list of available business types."""
    return [bt.value for bt in schemas.BusinessType]
