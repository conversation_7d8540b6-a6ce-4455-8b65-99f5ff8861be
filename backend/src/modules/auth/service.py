import logging
from datetime import datetime, timedelta, timezone
from typing import Annotated, Optional, cast

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import get_settings
from core.db.database import get_db
from modules.auth.schemas import TokenData, UserPublic
from modules.users.models import User as DBUser

logger = logging.getLogger(__name__)

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


settings = get_settings()


SECRET_KEY = settings.SECRET_KEY
ALGORITHM = "HS256"


class OAuth2PasswordBearerWithCookie(OAuth2PasswordBearer):
    async def __call__(self, request: Request) -> Optional[str]:
        token = request.cookies.get("access_token")
        if not token:
            # If the cookie is not found, try to get it from the Authorization header
            auth_header = request.headers.get("Authorization")
            if not auth_header:
                # Raise HTTPException instead of returning None
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Not authenticated",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            parts = auth_header.split()
            if len(parts) == 2 and parts[0] == "Bearer":
                return parts[1]

            # Raise HTTPException instead of returning None
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Not authenticated",
                headers={"WWW-Authenticate": "Bearer"},
            )

        return token


oauth2_scheme = OAuth2PasswordBearerWithCookie(tokenUrl="/api/auth/token")


def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)


async def create_user(
    db: AsyncSession, email: str, password: str, full_name: str
) -> DBUser:
    hashed_password = get_password_hash(password)
    db_user = DBUser(
        email=email,
        hashed_password=hashed_password,
        is_active=True,
        full_name=full_name,
    )
    db.add(db_user)
    await db.commit()
    await db.refresh(db_user)
    return db_user


async def get_user_by_email(db: AsyncSession, email: str):
    result = await db.execute(select(DBUser).filter(DBUser.email == email))
    return result.scalars().first()


async def get_user_by_reset_token(db: AsyncSession, token: str):
    result = await db.execute(
        select(DBUser).filter(DBUser.reset_password_token == token)
    )
    return result.scalars().first()


async def authenticate_user(email: str, password: str, db: AsyncSession):
    user = await get_user_by_email(db, email)
    if not user:
        logger.warning(f"Failed login attempt for email: {email} (user not found)")
        return None
    if not verify_password(password, user.hashed_password):
        logger.warning(f"Failed login attempt for email: {email} (incorrect password)")
        return None
    logger.info(f"Successful login for email: {email}")
    return user


def create_access_token(data: dict, expires_delta: timedelta | None = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_current_user(
    token: Annotated[str, Depends(oauth2_scheme)],
    db: Annotated[AsyncSession, Depends(get_db)],
) -> DBUser:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    # Token should not be None since OAuth2PasswordBearerWithCookie raises exception
    # But let's be defensive
    if not token:
        raise credentials_exception

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email: str | None = payload.get("sub")
        if email is None:
            raise credentials_exception
        token_data = TokenData(email=email)
    except JWTError:
        raise credentials_exception

    user = await get_user_by_email(db, cast(str, token_data.email))
    if user is None:
        raise credentials_exception
    return user


async def get_current_active_user(
    current_user: Annotated[DBUser, Depends(get_current_user)],
):
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user
