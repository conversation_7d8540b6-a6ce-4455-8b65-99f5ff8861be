import secrets
from datetime import datetime, timed<PERSON>ta
from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Response, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import Settings, get_settings
from core.db.database import get_db
from modules.auth.schemas import UserCreate, UserPublic
from modules.auth.service import (
    authenticate_user,
    create_access_token,
    create_user,
    get_current_user,
    get_password_hash,
    get_user_by_email,
    get_user_by_reset_token,
)

router = APIRouter(prefix="/auth", tags=["auth"])


@router.post("/register", response_model=UserPublic)
async def register_user(
    user_create: UserCreate, db: Annotated[AsyncSession, Depends(get_db)]
):
    existing_user = await get_user_by_email(db, user_create.email)
    if existing_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    user = await create_user(
        db, user_create.email, user_create.password, user_create.full_name
    )
    return UserPublic(
        email=user.email, full_name=user.full_name, is_active=user.is_active
    )


@router.post("/token", response_model=UserPublic)
async def login_for_access_token(
    response: Response,
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
    db: Annotated[AsyncSession, Depends(get_db)],
    settings: Settings = Depends(get_settings),
):
    user = await authenticate_user(form_data.username, form_data.password, db)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )

    response.set_cookie(
        key="access_token",
        value=access_token,
        httponly=True,
        secure=False
        if settings.ENVIRONMENT == "dev"
        else True,  # Ensure secure is False for dev
        samesite="lax"
        if settings.ENVIRONMENT == "dev"
        else "none",  # Use lax for dev, none for prod
        path="/",  # Set cookie path to root to make it available for all /api routes
        expires=int(access_token_expires.total_seconds()),
    )

    # Return user data for frontend
    return UserPublic(
        email=user.email, full_name=user.full_name, is_active=user.is_active
    )


@router.post("/logout")
async def logout(response: Response):
    response.delete_cookie("access_token")
    return {"message": "Logout successful"}


@router.post("/forgot-password")
async def forgot_password(email: str, db: Annotated[AsyncSession, Depends(get_db)]):
    user = await get_user_by_email(db, email)
    if user:
        # In a real application, you would send an email to the user with a link to reset their password.
        # For now, we'll just generate the token and print it.
        reset_token = secrets.token_urlsafe(32)
        reset_token_expires = datetime.utcnow() + timedelta(hours=1)
        user.reset_password_token = reset_token
        user.reset_password_token_expires_at = reset_token_expires
        await db.commit()
        print(f"Password reset token for {email}: {reset_token}")

    return {
        "message": "If an account with that email exists, a password reset link has been sent."
    }


@router.post("/reset-password")
async def reset_password(
    token: str,
    new_password: str,
    db: Annotated[AsyncSession, Depends(get_db)],
):
    user = await get_user_by_reset_token(db, token)
    if not user or user.reset_password_token_expires_at < datetime.utcnow():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired password reset token",
        )

    user.hashed_password = get_password_hash(new_password)
    user.reset_password_token = None
    user.reset_password_token_expires_at = None
    await db.commit()

    return {"message": "Password has been reset successfully."}


@router.get("/me", response_model=UserPublic)
async def read_users_me(current_user: Annotated[UserPublic, Depends(get_current_user)]):
    return current_user
