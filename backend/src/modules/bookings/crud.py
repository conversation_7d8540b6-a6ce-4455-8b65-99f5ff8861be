"""
Booking CRUD operations with Google Calendar integration.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, or_, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload

from modules.agents.models import Agent
from modules.companies.models import Company
from modules.customers.models import Customer

from . import models, schemas

logger = logging.getLogger(__name__)


async def create_booking(
    db: AsyncSession,
    booking: schemas.BookingRequestCreate,
    company_id: int,
    customer_id: Optional[int] = None,
) -> models.BookingRequest:
    """Create a new booking."""
    db_booking = models.BookingRequest(
        **booking.dict(),
        company_id=company_id,
        customer_id=customer_id,
        created_at=datetime.utcnow(),
    )
    db.add(db_booking)
    await db.commit()
    await db.refresh(db_booking)

    # TODO: Integrate with Google Calendar
    # await create_calendar_event(db_booking)

    return db_booking


async def get_booking(
    db: AsyncSession, booking_id: int, company_id: int, include_customer: bool = False
) -> Optional[models.BookingRequest]:
    """Get a single booking by ID."""
    query = select(models.BookingRequest).filter(
        and_(
            models.BookingRequest.id == booking_id,
            models.BookingRequest.company_id == company_id,
        )
    )

    if include_customer:
        query = query.options(
            joinedload(models.BookingRequest.customer),
            joinedload(models.BookingRequest.agent),
        )

    result = await db.execute(query)
    return result.scalars().first()


async def get_bookings(
    db: AsyncSession,
    company_id: int,
    skip: int = 0,
    limit: int = 100,
    status: Optional[schemas.BookingStatus] = None,
    service_type: Optional[str] = None,
    agent_id: Optional[int] = None,
    customer_id: Optional[int] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    search: Optional[str] = None,
) -> List[models.BookingRequest]:
    """Get bookings with filtering."""
    query = select(models.BookingRequest).filter(
        models.BookingRequest.company_id == company_id
    )

    # Apply filters
    if status:
        query = query.filter(models.BookingRequest.status == status)

    if service_type:
        query = query.filter(models.BookingRequest.service_type == service_type)

    if agent_id:
        query = query.filter(models.BookingRequest.agent_id == agent_id)

    if customer_id:
        query = query.filter(models.BookingRequest.customer_id == customer_id)

    if start_date:
        query = query.filter(models.BookingRequest.scheduled_at >= start_date)

    if end_date:
        query = query.filter(models.BookingRequest.scheduled_at <= end_date)

    if search:
        query = query.filter(
            or_(
                models.BookingRequest.customer_name.ilike(f"%{search}%"),
                models.BookingRequest.customer_phone.ilike(f"%{search}%"),
                models.BookingRequest.service_type.ilike(f"%{search}%"),
                models.BookingRequest.description.ilike(f"%{search}%"),
            )
        )

    # Order by scheduled date
    query = query.order_by(desc(models.BookingRequest.scheduled_at))
    query = query.offset(skip).limit(limit)

    # Include related data
    query = query.options(
        joinedload(models.BookingRequest.customer),
        joinedload(models.BookingRequest.agent),
    )

    result = await db.execute(query)
    return result.scalars().all()


async def update_booking(
    db: AsyncSession,
    booking_id: int,
    company_id: int,
    booking_update: schemas.BookingRequestUpdate,
) -> Optional[models.BookingRequest]:
    """Update a booking."""
    db_booking = await get_booking(db, booking_id, company_id)
    if not db_booking:
        return None

    update_data = booking_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_booking, field, value)

    db_booking.updated_at = datetime.utcnow()
    await db.commit()
    await db.refresh(db_booking)

    # TODO: Update Google Calendar event
    # await update_calendar_event(db_booking)

    return db_booking


async def update_booking_status(
    db: AsyncSession,
    booking_id: int,
    company_id: int,
    status: schemas.BookingStatus,
    notes: Optional[str] = None,
) -> Optional[models.BookingRequest]:
    """Update booking status with optional notes."""
    db_booking = await get_booking(db, booking_id, company_id)
    if not db_booking:
        return None

    old_status = db_booking.status
    db_booking.status = status
    db_booking.updated_at = datetime.utcnow()

    if notes:
        db_booking.notes = notes

    # Set completion time for completed bookings
    if (
        status == schemas.BookingStatus.COMPLETED
        and old_status != schemas.BookingStatus.COMPLETED
    ):
        db_booking.completed_at = datetime.utcnow()

    await db.commit()
    await db.refresh(db_booking)

    # TODO: Send customer notification
    # await send_booking_status_notification(db_booking, old_status)

    return db_booking


async def delete_booking(db: AsyncSession, booking_id: int, company_id: int) -> bool:
    """Delete a booking."""
    db_booking = await get_booking(db, booking_id, company_id)
    if not db_booking:
        return False

    # TODO: Delete Google Calendar event
    # await delete_calendar_event(db_booking)

    await db.delete(db_booking)
    await db.commit()
    return True


async def get_available_slots(
    db: AsyncSession,
    company_id: int,
    service_type: str,
    date: datetime,
    duration_minutes: int = 60,
    agent_id: Optional[int] = None,
) -> List[Dict[str, Any]]:
    """Get available time slots for booking."""
    # TODO: Implement availability checking logic
    # This would check:
    # 1. Business hours
    # 2. Existing bookings
    # 3. Agent availability
    # 4. Service duration requirements

    # For now, return mock available slots
    available_slots = []
    start_hour = 9  # 9 AM
    end_hour = 17  # 5 PM

    for hour in range(start_hour, end_hour):
        for minute in [0, 30]:  # 30-minute intervals
            slot_time = date.replace(hour=hour, minute=minute, second=0, microsecond=0)

            # Check if slot is not already booked
            existing_booking = await db.execute(
                select(models.BookingRequest).filter(
                    and_(
                        models.BookingRequest.company_id == company_id,
                        models.BookingRequest.scheduled_at == slot_time,
                        models.BookingRequest.status.in_(
                            [
                                schemas.BookingStatus.CONFIRMED,
                                schemas.BookingStatus.IN_PROGRESS,
                            ]
                        ),
                    )
                )
            )

            if not existing_booking.scalars().first():
                available_slots.append(
                    {
                        "time": slot_time.isoformat(),
                        "available": True,
                        "duration_minutes": duration_minutes,
                    }
                )

    return available_slots


async def get_booking_analytics(
    db: AsyncSession,
    company_id: int,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    agent_id: Optional[int] = None,
) -> Dict[str, Any]:
    """Get booking analytics."""
    if not start_date:
        start_date = datetime.utcnow() - timedelta(days=30)
    if not end_date:
        end_date = datetime.utcnow()

    base_query = select(models.BookingRequest).filter(
        and_(
            models.BookingRequest.company_id == company_id,
            models.BookingRequest.created_at >= start_date,
            models.BookingRequest.created_at <= end_date,
        )
    )

    if agent_id:
        base_query = base_query.filter(models.BookingRequest.agent_id == agent_id)

    # Total bookings
    total_result = await db.execute(
        select(func.count()).select_from(base_query.subquery())
    )
    total_bookings = total_result.scalar()

    # Bookings by status
    status_result = await db.execute(
        select(models.BookingRequest.status, func.count(models.BookingRequest.id))
        .filter(
            and_(
                models.BookingRequest.company_id == company_id,
                models.BookingRequest.created_at >= start_date,
                models.BookingRequest.created_at <= end_date,
            )
        )
        .group_by(models.BookingRequest.status)
    )
    status_breakdown = {row[0]: row[1] for row in status_result.fetchall()}

    # Revenue
    revenue_result = await db.execute(
        select(func.sum(models.BookingRequest.estimated_price)).filter(
            and_(
                models.BookingRequest.company_id == company_id,
                models.BookingRequest.created_at >= start_date,
                models.BookingRequest.created_at <= end_date,
                models.BookingRequest.status == schemas.BookingStatus.COMPLETED,
            )
        )
    )
    total_revenue = revenue_result.scalar() or 0

    # Daily booking counts
    daily_result = await db.execute(
        select(
            func.date(models.BookingRequest.created_at).label("date"),
            func.count(models.BookingRequest.id).label("count"),
        )
        .filter(
            and_(
                models.BookingRequest.company_id == company_id,
                models.BookingRequest.created_at >= start_date,
                models.BookingRequest.created_at <= end_date,
            )
        )
        .group_by(func.date(models.BookingRequest.created_at))
        .order_by(func.date(models.BookingRequest.created_at))
    )
    daily_counts = [
        {"date": row[0].isoformat(), "count": row[1]} for row in daily_result.fetchall()
    ]

    return {
        "total_bookings": total_bookings,
        "status_breakdown": status_breakdown,
        "total_revenue": float(total_revenue),
        "daily_counts": daily_counts,
        "period": {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
        },
    }


# TODO: Google Calendar integration functions
async def create_calendar_event(booking: models.BookingRequest) -> Optional[str]:
    """Create a Google Calendar event for the booking."""
    # Implementation would go here
    pass


async def update_calendar_event(booking: models.BookingRequest) -> bool:
    """Update a Google Calendar event."""
    # Implementation would go here
    pass


async def delete_calendar_event(booking: models.BookingRequest) -> bool:
    """Delete a Google Calendar event."""
    # Implementation would go here
    pass


# TODO: Customer notification functions
async def send_booking_status_notification(
    booking: models.BookingRequest, old_status: str
) -> bool:
    """Send SMS/email notification to customer about booking status change."""
    # Implementation would go here
    pass
