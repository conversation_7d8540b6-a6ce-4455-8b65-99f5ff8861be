"""
Booking Schemas for API requests and responses.
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field


class BookingStatus(str, Enum):
    """Booking status options."""

    PENDING = "pending"
    CONFIRMED = "confirmed"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    NO_SHOW = "no_show"
    RESCHEDULED = "rescheduled"


class BookingPriority(str, Enum):
    """Booking priority levels."""

    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"
    EMERGENCY = "emergency"


class PaymentStatus(str, Enum):
    """Payment status for bookings."""

    PENDING = "pending"
    PAID = "paid"
    PARTIAL = "partial"
    REFUNDED = "refunded"
    FAILED = "failed"


# Booking Request Schemas
class BookingRequestBase(BaseModel):
    customer_name: str = Field(..., max_length=255)
    customer_phone: Optional[str] = Field(None, max_length=20)
    customer_email: Optional[str] = Field(None, max_length=255)
    customer_address: Optional[str] = None
    customer_notes: Optional[str] = None
    service_type: str = Field(..., max_length=100)
    service_description: str
    service_catalog_item_id: Optional[int] = None
    preferred_date: Optional[datetime] = None
    preferred_time_start: Optional[str] = Field(None, max_length=10)
    preferred_time_end: Optional[str] = Field(None, max_length=10)
    scheduled_date: Optional[datetime] = None
    scheduled_duration: Optional[int] = None
    service_address: Optional[str] = None
    service_city: Optional[str] = Field(None, max_length=100)
    service_state: Optional[str] = Field(None, max_length=50)
    service_zip: Optional[str] = Field(None, max_length=20)
    location_notes: Optional[str] = None
    status: BookingStatus = BookingStatus.PENDING
    priority: BookingPriority = BookingPriority.NORMAL
    assigned_technician_id: Optional[int] = None
    assigned_team: Optional[str] = Field(None, max_length=100)
    estimated_cost: Optional[Decimal] = None
    quoted_price: Optional[Decimal] = None
    final_price: Optional[Decimal] = None
    payment_status: PaymentStatus = PaymentStatus.PENDING
    materials_needed: Optional[List[str]] = None
    tools_required: Optional[List[str]] = None
    special_instructions: Optional[str] = None
    access_instructions: Optional[str] = None
    follow_up_required: bool = False
    follow_up_date: Optional[datetime] = None
    warranty_period_days: Optional[int] = None
    preferred_contact_method: str = Field(default="phone", max_length=50)
    send_reminders: bool = True
    reminder_hours_before: int = 24
    source: Optional[str] = Field(None, max_length=50)
    referral_source: Optional[str] = Field(None, max_length=255)
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None


class BookingRequestCreate(BookingRequestBase):
    company_id: int
    conversation_id: Optional[int] = None


class BookingRequestUpdate(BaseModel):
    customer_name: Optional[str] = Field(None, max_length=255)
    customer_phone: Optional[str] = Field(None, max_length=20)
    customer_email: Optional[str] = Field(None, max_length=255)
    customer_address: Optional[str] = None
    customer_notes: Optional[str] = None
    service_description: Optional[str] = None
    preferred_date: Optional[datetime] = None
    preferred_time_start: Optional[str] = Field(None, max_length=10)
    preferred_time_end: Optional[str] = Field(None, max_length=10)
    scheduled_date: Optional[datetime] = None
    scheduled_duration: Optional[int] = None
    service_address: Optional[str] = None
    service_city: Optional[str] = Field(None, max_length=100)
    service_state: Optional[str] = Field(None, max_length=50)
    service_zip: Optional[str] = Field(None, max_length=20)
    location_notes: Optional[str] = None
    status: Optional[BookingStatus] = None
    priority: Optional[BookingPriority] = None
    assigned_technician_id: Optional[int] = None
    assigned_team: Optional[str] = Field(None, max_length=100)
    estimated_cost: Optional[Decimal] = None
    quoted_price: Optional[Decimal] = None
    final_price: Optional[Decimal] = None
    payment_status: Optional[PaymentStatus] = None
    materials_needed: Optional[List[str]] = None
    tools_required: Optional[List[str]] = None
    special_instructions: Optional[str] = None
    access_instructions: Optional[str] = None
    follow_up_required: Optional[bool] = None
    follow_up_date: Optional[datetime] = None
    warranty_period_days: Optional[int] = None
    preferred_contact_method: Optional[str] = Field(None, max_length=50)
    send_reminders: Optional[bool] = None
    reminder_hours_before: Optional[int] = None
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None


class BookingRequest(BookingRequestBase):
    id: int
    company_id: int
    conversation_id: Optional[int] = None
    request_number: str
    external_id: Optional[str] = None
    google_calendar_event_id: Optional[str] = None
    calendar_sync_enabled: bool = True
    created_at: datetime
    updated_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class BookingRequestWithDetails(BookingRequest):
    """Service request with additional details."""

    booking_history: Optional[List["BookingHistory"]] = None
    reminders: Optional[List["BookingReminder"]] = None
    assigned_technician_name: Optional[str] = None
    service_catalog_item_name: Optional[str] = None


# Booking History Schemas
class BookingHistoryBase(BaseModel):
    action: str = Field(..., max_length=50)
    field_changed: Optional[str] = Field(None, max_length=100)
    old_value: Optional[str] = None
    new_value: Optional[str] = None
    change_reason: Optional[str] = Field(None, max_length=255)
    notes: Optional[str] = None


class BookingHistoryCreate(BookingHistoryBase):
    booking_request_id: int
    changed_by_user_id: Optional[int] = None


class BookingHistory(BookingHistoryBase):
    id: int
    booking_request_id: int
    changed_by_user_id: Optional[int] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Booking Reminder Schemas
class BookingReminderBase(BaseModel):
    reminder_type: str = Field(..., max_length=50)
    send_method: str = Field(..., max_length=50)
    scheduled_time: datetime
    hours_before_appointment: Optional[int] = None
    message_template: Optional[str] = Field(None, max_length=100)
    custom_message: Optional[str] = None


class BookingReminderCreate(BookingReminderBase):
    booking_request_id: int
    company_id: int


class BookingReminderUpdate(BaseModel):
    reminder_type: Optional[str] = Field(None, max_length=50)
    send_method: Optional[str] = Field(None, max_length=50)
    scheduled_time: Optional[datetime] = None
    hours_before_appointment: Optional[int] = None
    message_template: Optional[str] = Field(None, max_length=100)
    custom_message: Optional[str] = None


class BookingReminder(BookingReminderBase):
    id: int
    booking_request_id: int
    company_id: int
    status: str = "pending"
    sent_at: Optional[datetime] = None
    delivery_status: Optional[str] = None
    customer_response: Optional[str] = None
    response_received_at: Optional[datetime] = None
    provider_message_id: Optional[str] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Availability Slot Schemas
class AvailabilitySlotBase(BaseModel):
    date: datetime
    start_time: str = Field(..., max_length=10)
    end_time: str = Field(..., max_length=10)
    duration_minutes: int
    is_available: bool = True
    max_bookings: int = 1
    current_bookings: int = 0
    service_types: Optional[List[str]] = None
    buffer_time_before: int = 0
    buffer_time_after: int = 0
    slot_price_modifier: float = 1.0
    is_recurring: bool = False
    recurrence_pattern: Optional[Dict[str, Any]] = None
    is_active: bool = True


class AvailabilitySlotCreate(AvailabilitySlotBase):
    company_id: int
    technician_id: Optional[int] = None


class AvailabilitySlotUpdate(BaseModel):
    start_time: Optional[str] = Field(None, max_length=10)
    end_time: Optional[str] = Field(None, max_length=10)
    duration_minutes: Optional[int] = None
    is_available: Optional[bool] = None
    max_bookings: Optional[int] = None
    service_types: Optional[List[str]] = None
    buffer_time_before: Optional[int] = None
    buffer_time_after: Optional[int] = None
    slot_price_modifier: Optional[float] = None
    is_recurring: Optional[bool] = None
    recurrence_pattern: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class AvailabilitySlot(AvailabilitySlotBase):
    id: int
    company_id: int
    technician_id: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Booking Template Schemas
class BookingTemplateBase(BaseModel):
    name: str = Field(..., max_length=255)
    description: Optional[str] = None
    service_type: str = Field(..., max_length=100)
    default_duration: int
    default_price: Optional[Decimal] = None
    required_fields: Optional[List[str]] = None
    optional_fields: Optional[List[str]] = None
    allow_online_booking: bool = True
    require_approval: bool = False
    advance_booking_days: int = 30
    auto_assign_technician: bool = False
    auto_send_confirmation: bool = True
    auto_send_reminders: bool = True
    is_active: bool = True


class BookingTemplateCreate(BookingTemplateBase):
    company_id: int


class BookingTemplateUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    service_type: Optional[str] = Field(None, max_length=100)
    default_duration: Optional[int] = None
    default_price: Optional[Decimal] = None
    required_fields: Optional[List[str]] = None
    optional_fields: Optional[List[str]] = None
    allow_online_booking: Optional[bool] = None
    require_approval: Optional[bool] = None
    advance_booking_days: Optional[int] = None
    auto_assign_technician: Optional[bool] = None
    auto_send_confirmation: Optional[bool] = None
    auto_send_reminders: Optional[bool] = None
    is_active: Optional[bool] = None


class BookingTemplate(BookingTemplateBase):
    id: int
    company_id: int
    times_used: int = 0
    last_used_at: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Response Models
class BookingRequestListResponse(BaseModel):
    booking_requests: List[BookingRequest]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class ApiResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None
    errors: Optional[List[str]] = None
