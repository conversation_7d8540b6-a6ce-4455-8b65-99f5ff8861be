"""
Booking Request Models with Google Calendar integration.

Manages appointments, service requests, and scheduling.
"""

from enum import Enum as PyEnum

from sqlalchemy import (
    DECIMAL,
    JSON,
    Boolean,
    Column,
    DateTime,
    Float,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class BookingStatus(PyEnum):
    """Booking status options."""

    PENDING = "pending"
    CONFIRMED = "confirmed"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    NO_SHOW = "no_show"
    RESCHEDULED = "rescheduled"


class BookingPriority(PyEnum):
    """Booking priority levels."""

    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"
    EMERGENCY = "emergency"


class PaymentStatus(PyEnum):
    """Payment status for bookings."""

    PENDING = "pending"
    PAID = "paid"
    PARTIAL = "partial"
    REFUNDED = "refunded"
    FAILED = "failed"


class BookingRequest(Base):
    """
    Main service booking/request model.
    """

    __tablename__ = "booking_requests"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=True)

    # Request Identification
    request_number = Column(String(50), nullable=False, unique=True, index=True)
    external_id = Column(String(100), nullable=True, index=True)  # External system ID

    # Customer Information
    customer_name = Column(String(255), nullable=False)
    customer_phone = Column(String(20), nullable=True, index=True)
    customer_email = Column(String(255), nullable=True, index=True)
    customer_address = Column(Text, nullable=True)
    customer_notes = Column(Text, nullable=True)

    # Service Details
    service_type = Column(String(100), nullable=False, index=True)
    service_description = Column(Text, nullable=False)
    service_catalog_item_id = Column(
        Integer, ForeignKey("service_catalog.id"), nullable=True
    )

    # Scheduling
    preferred_date = Column(DateTime(timezone=True), nullable=True)
    preferred_time_start = Column(String(10), nullable=True)  # "09:00"
    preferred_time_end = Column(String(10), nullable=True)  # "17:00"
    scheduled_date = Column(DateTime(timezone=True), nullable=True, index=True)
    scheduled_duration = Column(Integer, nullable=True)  # minutes

    # Location
    service_address = Column(Text, nullable=True)
    service_city = Column(String(100), nullable=True)
    service_state = Column(String(50), nullable=True)
    service_zip = Column(String(20), nullable=True)
    location_notes = Column(Text, nullable=True)

    # Status & Priority
    status = Column(
        String(50), nullable=False, default=BookingStatus.PENDING.value, index=True
    )
    priority = Column(String(50), nullable=False, default=BookingPriority.NORMAL.value)

    # Assignment
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    assigned_team = Column(String(100), nullable=True)

    # Pricing
    estimated_cost = Column(DECIMAL(10, 2), nullable=True)
    quoted_price = Column(DECIMAL(10, 2), nullable=True)
    final_price = Column(DECIMAL(10, 2), nullable=True)
    payment_status = Column(String(50), default=PaymentStatus.PENDING.value)

    # Calendar Integration
    google_calendar_event_id = Column(String(255), nullable=True)
    calendar_sync_enabled = Column(Boolean, default=True)

    # Requirements & Preparation
    materials_needed = Column(JSON, nullable=True, default=list)
    tools_required = Column(JSON, nullable=True, default=list)
    special_instructions = Column(Text, nullable=True)
    access_instructions = Column(Text, nullable=True)

    # Follow-up
    follow_up_required = Column(Boolean, default=False)
    follow_up_date = Column(DateTime(timezone=True), nullable=True)
    warranty_period_days = Column(Integer, nullable=True)

    # Communication Preferences
    preferred_contact_method = Column(String(50), default="phone")  # phone, email, sms
    send_reminders = Column(Boolean, default=True)
    reminder_hours_before = Column(Integer, default=24)

    # Metadata
    source = Column(String(50), nullable=True)  # phone, web, chat, referral
    referral_source = Column(String(255), nullable=True)
    tags = Column(JSON, nullable=True, default=list)
    custom_fields = Column(JSON, nullable=True, default=dict)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    company = relationship("Company", back_populates="bookings")
    conversation = relationship("Conversation")
    service_catalog_item = relationship("ServiceCatalogItem")
    user = relationship("User", foreign_keys=[user_id])
    booking_history = relationship(
        "BookingHistory", back_populates="booking_request", cascade="all, delete-orphan"
    )
    reminders = relationship(
        "BookingReminder",
        back_populates="booking_request",
        cascade="all, delete-orphan",
    )

    def __repr__(self):
        return f"<BookingRequest(id={self.id}, request_number='{self.request_number}', status='{self.status}')>"


class BookingHistory(Base):
    """
    History of changes to service requests.
    """

    __tablename__ = "booking_history"

    id = Column(Integer, primary_key=True, index=True)
    booking_request_id = Column(
        Integer, ForeignKey("booking_requests.id"), nullable=False
    )

    # Change Information
    action = Column(String(50), nullable=False)  # created, updated, cancelled, etc.
    field_changed = Column(String(100), nullable=True)
    old_value = Column(Text, nullable=True)
    new_value = Column(Text, nullable=True)

    # Change Context
    changed_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    change_reason = Column(String(255), nullable=True)
    notes = Column(Text, nullable=True)

    # Timestamp
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    booking_request = relationship("BookingRequest", back_populates="booking_history")
    user = relationship("User", foreign_keys=[changed_by_user_id])

    def __repr__(self):
        return f"<BookingHistory(id={self.id}, action='{self.action}', field='{self.field_changed}')>"


class BookingReminder(Base):
    """
    Automated reminders for service appointments.
    """

    __tablename__ = "booking_reminders"

    id = Column(Integer, primary_key=True, index=True)
    booking_request_id = Column(
        Integer, ForeignKey("booking_requests.id"), nullable=False
    )
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Reminder Configuration
    reminder_type = Column(
        String(50), nullable=False
    )  # confirmation, reminder, follow_up
    send_method = Column(String(50), nullable=False)  # sms, email, phone, push

    # Timing
    scheduled_time = Column(DateTime(timezone=True), nullable=False, index=True)
    hours_before_appointment = Column(Integer, nullable=True)

    # Content
    message_template = Column(String(100), nullable=True)
    custom_message = Column(Text, nullable=True)

    # Status
    status = Column(String(50), default="pending")  # pending, sent, failed, cancelled
    sent_at = Column(DateTime(timezone=True), nullable=True)
    delivery_status = Column(String(50), nullable=True)

    # Response Tracking
    customer_response = Column(
        String(50), nullable=True
    )  # confirmed, rescheduled, cancelled
    response_received_at = Column(DateTime(timezone=True), nullable=True)

    # Metadata
    provider_message_id = Column(String(255), nullable=True)
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    booking_request = relationship("BookingRequest", back_populates="reminders")
    company = relationship("Company")

    def __repr__(self):
        return f"<BookingReminder(id={self.id}, type='{self.reminder_type}', status='{self.status}')>"


class AvailabilitySlot(Base):
    """
    Available time slots for booking appointments.
    """

    __tablename__ = "availability_slots"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    technician_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Slot Information
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    start_time = Column(String(10), nullable=False)  # "09:00"
    end_time = Column(String(10), nullable=False)  # "10:00"
    duration_minutes = Column(Integer, nullable=False)

    # Availability
    is_available = Column(Boolean, default=True, index=True)
    max_bookings = Column(Integer, default=1)
    current_bookings = Column(Integer, default=0)

    # Slot Configuration
    service_types = Column(JSON, nullable=True, default=list)  # Allowed service types
    buffer_time_before = Column(Integer, default=0)  # minutes
    buffer_time_after = Column(Integer, default=0)  # minutes

    # Pricing
    slot_price_modifier = Column(Float, default=1.0)  # Multiplier for pricing

    # Recurrence
    is_recurring = Column(Boolean, default=False)
    recurrence_pattern = Column(JSON, nullable=True)  # Weekly, monthly patterns

    # Status
    is_active = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    company = relationship("Company")
    technician = relationship("User")

    def __repr__(self):
        return f"<AvailabilitySlot(id={self.id}, date='{self.date}', time='{self.start_time}-{self.end_time}')>"


class BookingAnalytics(Base):
    """
    Analytics for booking performance and trends.
    """

    __tablename__ = "booking_analytics"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Time Period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False, default="daily")

    # Booking Volume
    total_requests = Column(Integer, default=0)
    confirmed_bookings = Column(Integer, default=0)
    completed_bookings = Column(Integer, default=0)
    cancelled_bookings = Column(Integer, default=0)
    no_show_bookings = Column(Integer, default=0)

    # Conversion Metrics
    inquiry_to_booking_rate = Column(Float, default=0.0)  # percentage
    booking_completion_rate = Column(Float, default=0.0)  # percentage
    cancellation_rate = Column(Float, default=0.0)  # percentage

    # Revenue Metrics
    total_revenue = Column(DECIMAL(12, 2), default=0.0)
    average_booking_value = Column(DECIMAL(10, 2), default=0.0)
    revenue_per_technician = Column(DECIMAL(10, 2), default=0.0)

    # Efficiency Metrics
    average_booking_duration = Column(Integer, default=0)  # minutes
    technician_utilization = Column(Float, default=0.0)  # percentage
    schedule_efficiency = Column(Float, default=0.0)  # percentage

    # Customer Metrics
    new_customers = Column(Integer, default=0)
    repeat_customers = Column(Integer, default=0)
    customer_satisfaction = Column(Float, nullable=True)  # average rating

    # Popular Services
    top_service_types = Column(JSON, nullable=True, default=list)
    peak_booking_hours = Column(JSON, nullable=True, default=list)

    # Geographic Data
    service_areas = Column(JSON, nullable=True, default=list)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    company = relationship("Company")

    def __repr__(self):
        return f"<BookingAnalytics(company_id={self.company_id}, date='{self.date}')>"


class BookingTemplate(Base):
    """
    Templates for common booking types and configurations.
    """

    __tablename__ = "booking_templates"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Template Information
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    service_type = Column(String(100), nullable=False)

    # Default Values
    default_duration = Column(Integer, nullable=False)  # minutes
    default_price = Column(DECIMAL(10, 2), nullable=True)

    # Requirements
    required_fields = Column(JSON, nullable=True, default=list)
    optional_fields = Column(JSON, nullable=True, default=list)

    # Configuration
    allow_online_booking = Column(Boolean, default=True)
    require_approval = Column(Boolean, default=False)
    advance_booking_days = Column(Integer, default=30)

    # Automation
    auto_assign_technician = Column(Boolean, default=False)
    auto_send_confirmation = Column(Boolean, default=True)
    auto_send_reminders = Column(Boolean, default=True)

    # Status
    is_active = Column(Boolean, default=True)

    # Usage Statistics
    times_used = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    company = relationship("Company")

    def __repr__(self):
        return f"<BookingTemplate(id={self.id}, name='{self.name}', service_type='{self.service_type}')>"
