"""
Web Tools API Router

Provides HTTP endpoints for web tools that can be called by Dify agents.
"""

import logging
from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from core.tools.web_tools import WEB_TOOLS, get_tool_descriptions
from modules.auth.service import get_current_active_user
from modules.users.models import User as DBUser

logger = logging.getLogger(__name__)

router = APIRouter(tags=["tools"])


class ToolCallRequest(BaseModel):
    tool_name: str
    parameters: Dict[str, Any]


class ToolCallResponse(BaseModel):
    success: bool
    result: Dict[str, Any]
    error: Optional[str] = None


@router.get("/tools/descriptions")
async def get_tools_descriptions():
    """
    Get descriptions of all available web tools.
    This endpoint can be used by <PERSON>fy to understand available tools.
    """
    return {"success": True, "tools": get_tool_descriptions()}


@router.post("/tools/call", response_model=ToolCallResponse)
async def call_tool(
    request: ToolCallRequest, current_user: DBUser = Depends(get_current_active_user)
):
    """
    Call a web tool with the provided parameters.
    This endpoint can be called by Dify agents to execute business logic.
    """
    logger.info(f"Tool call request: {request.tool_name} by user {current_user.id}")

    if request.tool_name not in WEB_TOOLS:
        raise HTTPException(
            status_code=404, detail=f"Tool '{request.tool_name}' not found"
        )

    try:
        # Add user_id to parameters if not provided
        if "user_id" not in request.parameters:
            request.parameters["user_id"] = current_user.id

        # Call the tool
        tool_function = WEB_TOOLS[request.tool_name]
        result = await tool_function(**request.parameters)

        return ToolCallResponse(success=True, result=result)

    except Exception as e:
        logger.error(f"Error calling tool {request.tool_name}: {e}")
        return ToolCallResponse(success=False, result={}, error=str(e))


@router.get("/tools/schedule_job")
async def schedule_job_endpoint(
    customer_id: int,
    service_type: str,
    scheduled_time: str,
    notes: Optional[str] = None,
    current_user: DBUser = Depends(get_current_active_user),
):
    """
    Direct endpoint for scheduling jobs (can be called via GET for simplicity).
    """
    try:
        from core.tools.web_tools import schedule_job_tool

        result = await schedule_job_tool(
            customer_id=customer_id,
            service_type=service_type,
            scheduled_time=scheduled_time,
            notes=notes,
            user_id=current_user.id,
        )
        return result
    except Exception as e:
        logger.error(f"Error in schedule_job_endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tools/customer/{customer_id}/jobs")
async def get_customer_jobs_endpoint(
    customer_id: int, current_user: DBUser = Depends(get_current_active_user)
):
    """
    Direct endpoint for getting customer jobs.
    """
    try:
        from core.tools.web_tools import get_customer_jobs_tool

        result = await get_customer_jobs_tool(
            customer_id=customer_id, user_id=current_user.id
        )
        return result
    except Exception as e:
        logger.error(f"Error in get_customer_jobs_endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tools/customer/{customer_id}")
async def get_customer_details_endpoint(
    customer_id: int, current_user: DBUser = Depends(get_current_active_user)
):
    """
    Direct endpoint for getting customer details.
    """
    try:
        from core.tools.web_tools import get_customer_details_tool

        result = await get_customer_details_tool(
            customer_id=customer_id, user_id=current_user.id
        )
        return result
    except Exception as e:
        logger.error(f"Error in get_customer_details_endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tools/customers")
async def get_all_customers_endpoint(
    skip: int = 0,
    limit: int = 100,
    current_user: DBUser = Depends(get_current_active_user),
):
    """
    Direct endpoint for getting all customers.
    """
    try:
        from core.tools.web_tools import get_all_customers_tool

        result = await get_all_customers_tool(
            user_id=current_user.id, skip=skip, limit=limit
        )
        return result
    except Exception as e:
        logger.error(f"Error in get_all_customers_endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))
