"""
Phone Numbers API Router with enhanced routing and management.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.service import get_current_active_user
from modules.companies import crud as company_crud
from modules.users.models import User as DBUser

from . import crud, schemas

logger = logging.getLogger(__name__)

router = APIRouter(tags=["phone-numbers"])


@router.get("/", response_model=schemas.PhoneNumberListResponse)
async def get_phone_numbers(
    company_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[schemas.PhoneNumberStatus] = None,
    provider: Optional[schemas.Provider] = None,
    agent_id: Optional[int] = None,
    search: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get phone numbers for a company."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    phone_numbers = await crud.get_phone_numbers(
        db=db,
        company_id=company_id,
        skip=skip,
        limit=limit,
        status=status,
        provider=provider,
        agent_id=agent_id,
        search=search,
    )

    total = await crud.get_phone_numbers_count(
        db=db,
        company_id=company_id,
        status=status,
        provider=provider,
        agent_id=agent_id,
        search=search,
    )

    return schemas.PhoneNumberListResponse(
        phone_numbers=phone_numbers,
        total=total,
        page=skip // limit + 1,
        per_page=limit,
        has_next=(skip + limit) < total,
        has_prev=skip > 0,
    )


@router.post("/", response_model=schemas.PhoneNumber)
async def create_phone_number(
    phone_number: schemas.PhoneNumberCreate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a new phone number."""
    # Verify company belongs to user
    company = await company_crud.get_company(
        db, phone_number.company_id, current_user.id
    )
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    try:
        db_phone_number = await crud.create_phone_number(
            db=db, phone_number=phone_number
        )
        return db_phone_number
    except Exception as e:
        logger.error(f"Error creating phone number: {e}")
        raise HTTPException(status_code=400, detail="Failed to create phone number")


@router.get("/{phone_number_id}", response_model=schemas.PhoneNumberWithDetails)
async def get_phone_number(
    phone_number_id: int,
    company_id: int,
    include_details: bool = Query(False),
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get a specific phone number."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    phone_number = await crud.get_phone_number(
        db=db,
        phone_number_id=phone_number_id,
        company_id=company_id,
        include_details=include_details,
    )

    if not phone_number:
        raise HTTPException(status_code=404, detail="Phone number not found")

    return phone_number


@router.put("/{phone_number_id}", response_model=schemas.PhoneNumber)
async def update_phone_number(
    phone_number_id: int,
    company_id: int,
    phone_number_update: schemas.PhoneNumberUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Update a phone number."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    phone_number = await crud.update_phone_number(
        db=db,
        phone_number_id=phone_number_id,
        company_id=company_id,
        phone_number_update=phone_number_update,
    )

    if not phone_number:
        raise HTTPException(status_code=404, detail="Phone number not found")

    return phone_number


@router.delete("/{phone_number_id}")
async def delete_phone_number(
    phone_number_id: int,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Delete a phone number."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    success = await crud.delete_phone_number(
        db=db, phone_number_id=phone_number_id, company_id=company_id
    )

    if not success:
        raise HTTPException(
            status_code=400, detail="Phone number not found or cannot be deleted"
        )

    return {"message": "Phone number deleted successfully"}


# Call Queue Endpoints
@router.get("/{phone_number_id}/queues", response_model=List[schemas.CallQueue])
async def get_call_queues(
    phone_number_id: int,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get call queues for a phone number."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    queues = await crud.get_call_queues(
        db=db, company_id=company_id, phone_number_id=phone_number_id
    )

    return queues


@router.post("/{phone_number_id}/queues", response_model=schemas.CallQueue)
async def create_call_queue(
    phone_number_id: int,
    company_id: int,
    queue: schemas.CallQueueCreate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a call queue for a phone number."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Ensure the phone_number_id and company_id match
    queue.phone_number_id = phone_number_id
    queue.company_id = company_id

    db_queue = await crud.create_call_queue(db=db, queue=queue)
    return db_queue


# Routing Rules Endpoints
@router.get(
    "/{phone_number_id}/routing-rules", response_model=List[schemas.CallRoutingRule]
)
async def get_routing_rules(
    phone_number_id: int,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get routing rules for a phone number."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    rules = await crud.get_call_routing_rules(
        db=db, company_id=company_id, phone_number_id=phone_number_id
    )

    return rules


@router.post("/{phone_number_id}/routing-rules", response_model=schemas.CallRoutingRule)
async def create_routing_rule(
    phone_number_id: int,
    company_id: int,
    rule: schemas.CallRoutingRuleCreate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a routing rule for a phone number."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Ensure the phone_number_id and company_id match
    rule.phone_number_id = phone_number_id
    rule.company_id = company_id

    db_rule = await crud.create_call_routing_rule(db=db, rule=rule)
    return db_rule


# Blacklist Endpoints
@router.get(
    "/{phone_number_id}/blacklist", response_model=List[schemas.PhoneNumberBlacklist]
)
async def get_blacklist_entries(
    phone_number_id: int,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get blacklist entries for a phone number."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    entries = await crud.get_blacklist_entries(
        db=db, company_id=company_id, phone_number_id=phone_number_id
    )

    return entries


@router.post(
    "/{phone_number_id}/blacklist", response_model=schemas.PhoneNumberBlacklist
)
async def create_blacklist_entry(
    phone_number_id: int,
    company_id: int,
    blacklist: schemas.PhoneNumberBlacklistCreate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Add a number to the blacklist."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Ensure the phone_number_id and company_id match
    blacklist.phone_number_id = phone_number_id
    blacklist.company_id = company_id

    db_blacklist = await crud.create_blacklist_entry(
        db=db, blacklist=blacklist, created_by=current_user.id
    )
    return db_blacklist


# Test Phone Number
@router.post("/{phone_number_id}/test", response_model=schemas.PhoneNumberTestResult)
async def test_phone_number(
    phone_number_id: int,
    company_id: int,
    test_request: schemas.PhoneNumberTestRequest,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Test a phone number configuration."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Get phone number
    phone_number = await crud.get_phone_number(db, phone_number_id, company_id)
    if not phone_number:
        raise HTTPException(status_code=404, detail="Phone number not found")

    # TODO: Implement actual phone number testing logic
    # This would involve calling the provider's test API

    import time
    from datetime import datetime

    start_time = time.time()

    # Simulate test execution
    test_result = {
        "routing_test": True,
        "agent_connection": True,
        "provider_status": "active",
    }

    response_time = (time.time() - start_time) * 1000

    return schemas.PhoneNumberTestResult(
        test_type=test_request.test_type,
        success=True,
        response_time_ms=response_time,
        result_data=test_result,
        timestamp=datetime.utcnow(),
    )
