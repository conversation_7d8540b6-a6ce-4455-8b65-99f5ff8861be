"""
Phone Number Schemas for API requests and responses.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field


class PhoneNumberStatus(str, Enum):
    """Phone number status options."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PORTING = "porting"
    MAINTENANCE = "maintenance"


class CallRoutingType(str, Enum):
    """Call routing types."""

    DIRECT = "direct"
    ROUND_ROBIN = "round_robin"
    SKILL_BASED = "skill_based"
    BUSINESS_HOURS = "business_hours"
    QUEUE = "queue"
    VOICEMAIL = "voicemail"


class AfterHoursAction(str, Enum):
    """After hours actions."""

    VOICEMAIL = "voicemail"
    FORWARD = "forward"
    HANGUP = "hangup"
    QUEUE = "queue"


class Provider(str, Enum):
    """Phone number providers."""

    TWILIO = "twilio"
    ELEVENLABS = "elevenlabs"
    OTHER = "other"


# Phone Number Base Schemas
class PhoneNumberBase(BaseModel):
    phone_number: str = Field(..., max_length=20)
    formatted_number: Optional[str] = Field(None, max_length=25)
    country_code: str = Field(default="+1", max_length=5)
    provider: Provider = Provider.TWILIO
    provider_config: Optional[Dict[str, Any]] = None
    primary_agent_id: Optional[int] = None
    backup_agent_ids: Optional[List[int]] = None
    routing_type: CallRoutingType = CallRoutingType.DIRECT
    routing_rules: Optional[Dict[str, Any]] = None
    business_hours: Optional[Dict[str, Any]] = None
    timezone: str = Field(default="UTC", max_length=50)
    holiday_schedule: Optional[Dict[str, Any]] = None
    after_hours_action: AfterHoursAction = AfterHoursAction.VOICEMAIL
    max_ring_time: int = Field(default=30, ge=10, le=120)
    max_queue_time: int = Field(default=300, ge=30, le=1800)
    queue_music_url: Optional[str] = Field(None, max_length=500)
    voicemail_greeting_url: Optional[str] = Field(None, max_length=500)
    call_recording_enabled: bool = False
    call_transcription_enabled: bool = False
    sms_enabled: bool = True
    mms_enabled: bool = False
    webhook_url: Optional[str] = Field(None, max_length=500)
    webhook_events: Optional[List[str]] = None
    friendly_name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    tags: Optional[List[str]] = None


class PhoneNumberCreate(PhoneNumberBase):
    company_id: int
    user_id: int


class PhoneNumberUpdate(BaseModel):
    formatted_number: Optional[str] = Field(None, max_length=25)
    provider_config: Optional[Dict[str, Any]] = None
    primary_agent_id: Optional[int] = None
    backup_agent_ids: Optional[List[int]] = None
    routing_type: Optional[CallRoutingType] = None
    routing_rules: Optional[Dict[str, Any]] = None
    business_hours: Optional[Dict[str, Any]] = None
    timezone: Optional[str] = Field(None, max_length=50)
    holiday_schedule: Optional[Dict[str, Any]] = None
    after_hours_action: Optional[AfterHoursAction] = None
    max_ring_time: Optional[int] = Field(None, ge=10, le=120)
    max_queue_time: Optional[int] = Field(None, ge=30, le=1800)
    queue_music_url: Optional[str] = Field(None, max_length=500)
    voicemail_greeting_url: Optional[str] = Field(None, max_length=500)
    call_recording_enabled: Optional[bool] = None
    call_transcription_enabled: Optional[bool] = None
    sms_enabled: Optional[bool] = None
    mms_enabled: Optional[bool] = None
    webhook_url: Optional[str] = Field(None, max_length=500)
    webhook_events: Optional[List[str]] = None
    status: Optional[PhoneNumberStatus] = None
    friendly_name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    tags: Optional[List[str]] = None


class PhoneNumber(PhoneNumberBase):
    id: int
    company_id: int
    user_id: int
    area_code: Optional[str] = None
    provider_phone_sid: Optional[str] = None
    webhook_auth_token: Optional[str] = None
    status: PhoneNumberStatus
    is_verified: bool = False
    total_calls_received: int = 0
    total_calls_answered: int = 0
    total_sms_received: int = 0
    total_sms_sent: int = 0
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_call_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class PhoneNumberWithDetails(PhoneNumber):
    """Phone number with additional details."""

    agent_name: Optional[str] = None
    backup_agent_names: Optional[List[str]] = None
    recent_calls_count: int = 0
    today_calls_count: int = 0
    queue_info: Optional[Dict[str, Any]] = None


# Call Queue Schemas
class CallQueueBase(BaseModel):
    name: str = Field(..., max_length=100)
    description: Optional[str] = None
    max_queue_size: int = Field(default=10, ge=1, le=100)
    max_wait_time: int = Field(default=300, ge=30, le=1800)
    priority_routing: bool = False
    agent_ids: Optional[List[int]] = None
    skill_requirements: Optional[List[str]] = None
    overflow_action: str = Field(default="voicemail", max_length=50)
    overflow_destination: Optional[str] = Field(None, max_length=100)
    hold_music_url: Optional[str] = Field(None, max_length=500)
    welcome_message_url: Optional[str] = Field(None, max_length=500)
    position_announcement: bool = True
    estimated_wait_announcement: bool = True
    is_active: bool = True


class CallQueueCreate(CallQueueBase):
    phone_number_id: int
    company_id: int


class CallQueueUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    max_queue_size: Optional[int] = Field(None, ge=1, le=100)
    max_wait_time: Optional[int] = Field(None, ge=30, le=1800)
    priority_routing: Optional[bool] = None
    agent_ids: Optional[List[int]] = None
    skill_requirements: Optional[List[str]] = None
    overflow_action: Optional[str] = Field(None, max_length=50)
    overflow_destination: Optional[str] = Field(None, max_length=100)
    hold_music_url: Optional[str] = Field(None, max_length=500)
    welcome_message_url: Optional[str] = Field(None, max_length=500)
    position_announcement: Optional[bool] = None
    estimated_wait_announcement: Optional[bool] = None
    is_active: Optional[bool] = None


class CallQueue(CallQueueBase):
    id: int
    phone_number_id: int
    company_id: int
    total_calls_queued: int = 0
    total_calls_answered: int = 0
    total_calls_abandoned: int = 0
    average_wait_time: int = 0
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Call Routing Rule Schemas
class CallRoutingRuleBase(BaseModel):
    name: str = Field(..., max_length=100)
    description: Optional[str] = None
    priority: int = Field(default=0, ge=0, le=100)
    conditions: Dict[str, Any]
    action_type: str = Field(..., max_length=50)
    action_config: Dict[str, Any]
    is_active: bool = True


class CallRoutingRuleCreate(CallRoutingRuleBase):
    phone_number_id: int
    company_id: int


class CallRoutingRuleUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    priority: Optional[int] = Field(None, ge=0, le=100)
    conditions: Optional[Dict[str, Any]] = None
    action_type: Optional[str] = Field(None, max_length=50)
    action_config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class CallRoutingRule(CallRoutingRuleBase):
    id: int
    phone_number_id: int
    company_id: int
    times_matched: int = 0
    last_matched_at: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Phone Number Analytics Schemas
class PhoneNumberAnalytics(BaseModel):
    phone_number_id: int
    company_id: int
    date: datetime
    period_type: str
    total_calls: int = 0
    answered_calls: int = 0
    missed_calls: int = 0
    abandoned_calls: int = 0
    total_call_duration: int = 0
    average_call_duration: int = 0
    longest_call_duration: int = 0
    total_queued_calls: int = 0
    average_queue_time: int = 0
    max_queue_time: int = 0
    queue_abandonment_rate: int = 0
    total_sms_received: int = 0
    total_sms_sent: int = 0
    sms_response_rate: int = 0
    agent_utilization: int = 0
    first_call_resolution: int = 0
    customer_satisfaction_score: Optional[int] = None
    total_satisfaction_responses: int = 0
    leads_generated: int = 0
    appointments_scheduled: int = 0
    conversion_rate: int = 0

    model_config = ConfigDict(from_attributes=True)


# Blacklist Schemas
class PhoneNumberBlacklistBase(BaseModel):
    blocked_number: str = Field(..., max_length=20)
    blocked_number_formatted: Optional[str] = Field(None, max_length=25)
    reason: Optional[str] = Field(None, max_length=255)
    block_type: str = Field(default="permanent", max_length=50)
    block_until: Optional[datetime] = None
    is_active: bool = True


class PhoneNumberBlacklistCreate(PhoneNumberBlacklistBase):
    phone_number_id: int
    company_id: int


class PhoneNumberBlacklistUpdate(BaseModel):
    reason: Optional[str] = Field(None, max_length=255)
    block_type: Optional[str] = Field(None, max_length=50)
    block_until: Optional[datetime] = None
    is_active: Optional[bool] = None


class PhoneNumberBlacklist(PhoneNumberBlacklistBase):
    id: int
    phone_number_id: int
    company_id: int
    total_blocked_attempts: int = 0
    last_attempt_at: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)


# Business Hours Schema
class BusinessHours(BaseModel):
    """Business hours configuration for phone numbers."""

    monday: Optional[Dict[str, str]] = None  # {"open": "09:00", "close": "17:00"}
    tuesday: Optional[Dict[str, str]] = None
    wednesday: Optional[Dict[str, str]] = None
    thursday: Optional[Dict[str, str]] = None
    friday: Optional[Dict[str, str]] = None
    saturday: Optional[Dict[str, str]] = None
    sunday: Optional[Dict[str, str]] = None
    timezone: str = "UTC"
    holidays: Optional[List[str]] = None


# Phone Number Testing Schemas
class PhoneNumberTestRequest(BaseModel):
    test_type: str = Field(..., max_length=50)  # "call", "sms", "routing"
    test_config: Optional[Dict[str, Any]] = None
    caller_id: Optional[str] = None
    message: Optional[str] = None


class PhoneNumberTestResult(BaseModel):
    test_type: str
    success: bool
    response_time_ms: float
    result_data: Dict[str, Any]
    error_message: Optional[str] = None
    timestamp: datetime


# Response Models
class PhoneNumberListResponse(BaseModel):
    phone_numbers: List[PhoneNumber]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class CallQueueListResponse(BaseModel):
    queues: List[CallQueue]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class CallRoutingRuleListResponse(BaseModel):
    rules: List[CallRoutingRule]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class ApiResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None
    errors: Optional[List[str]] = None


# Phone Number Purchase/Provisioning Schemas
class PhoneNumberPurchaseRequest(BaseModel):
    area_code: Optional[str] = None
    contains: Optional[str] = None  # Number should contain these digits
    country_code: str = "+1"
    provider: Provider = Provider.TWILIO
    friendly_name: Optional[str] = None


class AvailablePhoneNumber(BaseModel):
    phone_number: str
    formatted_number: str
    locality: Optional[str] = None
    region: Optional[str] = None
    country_code: str
    capabilities: List[str]  # ["voice", "sms", "mms"]
    monthly_cost: Optional[float] = None
