"""
Phone Number CRUD operations with enhanced routing and management.
"""

import logging
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload

from modules.agents.models import Agent
from modules.companies.models import Company

from . import models, schemas

logger = logging.getLogger(__name__)


# Phone Number CRUD
async def get_phone_number(
    db: AsyncSession,
    phone_number_id: int,
    company_id: int,
    include_details: bool = False,
) -> Optional[models.PhoneNumber]:
    """Get a single phone number by ID."""
    query = select(models.PhoneNumber).filter(
        and_(
            models.PhoneNumber.id == phone_number_id,
            models.PhoneNumber.company_id == company_id,
        )
    )

    if include_details:
        query = query.options(
            joinedload(models.PhoneNumber.agent),
            selectinload(models.PhoneNumber.call_history),
            selectinload(models.PhoneNumber.conversations),
        )

    result = await db.execute(query)
    return result.scalars().first()


async def get_phone_numbers(
    db: AsyncSession,
    company_id: int,
    skip: int = 0,
    limit: int = 100,
    status: Optional[schemas.PhoneNumberStatus] = None,
    provider: Optional[schemas.Provider] = None,
    agent_id: Optional[int] = None,
    search: Optional[str] = None,
) -> List[models.PhoneNumber]:
    """Get phone numbers with filtering and pagination."""
    query = select(models.PhoneNumber).filter(
        models.PhoneNumber.company_id == company_id
    )

    # Apply filters
    if status:
        query = query.filter(models.PhoneNumber.status == status.value)

    if provider:
        query = query.filter(models.PhoneNumber.provider == provider.value)

    if agent_id:
        query = query.filter(models.PhoneNumber.primary_agent_id == agent_id)

    if search:
        query = query.filter(
            or_(
                models.PhoneNumber.phone_number.ilike(f"%{search}%"),
                models.PhoneNumber.formatted_number.ilike(f"%{search}%"),
                models.PhoneNumber.friendly_name.ilike(f"%{search}%"),
            )
        )

    # Apply pagination and ordering
    query = (
        query.order_by(desc(models.PhoneNumber.created_at)).offset(skip).limit(limit)
    )

    result = await db.execute(query)
    return result.scalars().all()


async def get_phone_numbers_count(
    db: AsyncSession,
    company_id: int,
    status: Optional[schemas.PhoneNumberStatus] = None,
    provider: Optional[schemas.Provider] = None,
    agent_id: Optional[int] = None,
    search: Optional[str] = None,
) -> int:
    """Get total count of phone numbers with filters."""
    query = select(func.count(models.PhoneNumber.id)).filter(
        models.PhoneNumber.company_id == company_id
    )

    if status:
        query = query.filter(models.PhoneNumber.status == status.value)

    if provider:
        query = query.filter(models.PhoneNumber.provider == provider.value)

    if agent_id:
        query = query.filter(models.PhoneNumber.primary_agent_id == agent_id)

    if search:
        query = query.filter(
            or_(
                models.PhoneNumber.phone_number.ilike(f"%{search}%"),
                models.PhoneNumber.formatted_number.ilike(f"%{search}%"),
                models.PhoneNumber.friendly_name.ilike(f"%{search}%"),
            )
        )

    result = await db.execute(query)
    return result.scalar()


async def create_phone_number(
    db: AsyncSession, phone_number: schemas.PhoneNumberCreate
) -> models.PhoneNumber:
    """Create a new phone number."""
    # Extract area code from phone number if not provided
    area_code = None
    if (
        phone_number.phone_number.startswith("+1")
        and len(phone_number.phone_number) >= 5
    ):
        area_code = phone_number.phone_number[2:5]

    db_phone_number = models.PhoneNumber(
        **phone_number.model_dump(), area_code=area_code
    )

    db.add(db_phone_number)
    await db.commit()
    await db.refresh(db_phone_number)

    logger.info(
        f"Created phone number {db_phone_number.id} for company {phone_number.company_id}"
    )
    return db_phone_number


async def update_phone_number(
    db: AsyncSession,
    phone_number_id: int,
    company_id: int,
    phone_number_update: schemas.PhoneNumberUpdate,
) -> Optional[models.PhoneNumber]:
    """Update an existing phone number."""
    db_phone_number = await get_phone_number(db, phone_number_id, company_id)
    if not db_phone_number:
        return None

    # Update fields
    update_data = phone_number_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_phone_number, field, value)

    await db.commit()
    await db.refresh(db_phone_number)

    logger.info(f"Updated phone number {phone_number_id}")
    return db_phone_number


async def delete_phone_number(
    db: AsyncSession, phone_number_id: int, company_id: int
) -> bool:
    """Delete a phone number."""
    db_phone_number = await get_phone_number(db, phone_number_id, company_id)
    if not db_phone_number:
        return False

    # Check if phone number has active calls or conversations
    active_calls_count = await db.execute(
        select(func.count())
        .select_from(
            select(models.PhoneNumber.id)
            .join(models.PhoneNumber.conversations)
            .subquery()
        )
        .filter(models.PhoneNumber.id == phone_number_id)
    )

    if active_calls_count.scalar() > 0:
        # Don't delete if there are active conversations
        logger.warning(
            f"Cannot delete phone number {phone_number_id} - has active conversations"
        )
        return False

    await db.delete(db_phone_number)
    await db.commit()

    logger.info(f"Deleted phone number {phone_number_id}")
    return True


# Call Queue CRUD
async def get_call_queue(
    db: AsyncSession, queue_id: int, company_id: int
) -> Optional[models.CallQueue]:
    """Get a single call queue by ID."""
    result = await db.execute(
        select(models.CallQueue).filter(
            and_(
                models.CallQueue.id == queue_id,
                models.CallQueue.company_id == company_id,
            )
        )
    )
    return result.scalars().first()


async def get_call_queues(
    db: AsyncSession,
    company_id: int,
    phone_number_id: Optional[int] = None,
    is_active: Optional[bool] = None,
) -> List[models.CallQueue]:
    """Get call queues for a company."""
    query = select(models.CallQueue).filter(models.CallQueue.company_id == company_id)

    if phone_number_id:
        query = query.filter(models.CallQueue.phone_number_id == phone_number_id)

    if is_active is not None:
        query = query.filter(models.CallQueue.is_active == is_active)

    query = query.order_by(models.CallQueue.name)

    result = await db.execute(query)
    return result.scalars().all()


async def create_call_queue(
    db: AsyncSession, queue: schemas.CallQueueCreate
) -> models.CallQueue:
    """Create a new call queue."""
    db_queue = models.CallQueue(**queue.model_dump())

    db.add(db_queue)
    await db.commit()
    await db.refresh(db_queue)

    logger.info(
        f"Created call queue {db_queue.id} for phone number {queue.phone_number_id}"
    )
    return db_queue


async def update_call_queue(
    db: AsyncSession,
    queue_id: int,
    company_id: int,
    queue_update: schemas.CallQueueUpdate,
) -> Optional[models.CallQueue]:
    """Update an existing call queue."""
    db_queue = await get_call_queue(db, queue_id, company_id)
    if not db_queue:
        return None

    # Update fields
    update_data = queue_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_queue, field, value)

    await db.commit()
    await db.refresh(db_queue)

    logger.info(f"Updated call queue {queue_id}")
    return db_queue


async def delete_call_queue(db: AsyncSession, queue_id: int, company_id: int) -> bool:
    """Delete a call queue."""
    db_queue = await get_call_queue(db, queue_id, company_id)
    if not db_queue:
        return False

    await db.delete(db_queue)
    await db.commit()

    logger.info(f"Deleted call queue {queue_id}")
    return True


# Call Routing Rule CRUD
async def get_call_routing_rule(
    db: AsyncSession, rule_id: int, company_id: int
) -> Optional[models.CallRoutingRule]:
    """Get a single call routing rule by ID."""
    result = await db.execute(
        select(models.CallRoutingRule).filter(
            and_(
                models.CallRoutingRule.id == rule_id,
                models.CallRoutingRule.company_id == company_id,
            )
        )
    )
    return result.scalars().first()


async def get_call_routing_rules(
    db: AsyncSession,
    company_id: int,
    phone_number_id: Optional[int] = None,
    is_active: Optional[bool] = None,
) -> List[models.CallRoutingRule]:
    """Get call routing rules for a company."""
    query = select(models.CallRoutingRule).filter(
        models.CallRoutingRule.company_id == company_id
    )

    if phone_number_id:
        query = query.filter(models.CallRoutingRule.phone_number_id == phone_number_id)

    if is_active is not None:
        query = query.filter(models.CallRoutingRule.is_active == is_active)

    # Order by priority (higher first), then by name
    query = query.order_by(
        desc(models.CallRoutingRule.priority), models.CallRoutingRule.name
    )

    result = await db.execute(query)
    return result.scalars().all()


async def create_call_routing_rule(
    db: AsyncSession, rule: schemas.CallRoutingRuleCreate
) -> models.CallRoutingRule:
    """Create a new call routing rule."""
    db_rule = models.CallRoutingRule(**rule.model_dump())

    db.add(db_rule)
    await db.commit()
    await db.refresh(db_rule)

    logger.info(
        f"Created call routing rule {db_rule.id} for phone number {rule.phone_number_id}"
    )
    return db_rule


async def update_call_routing_rule(
    db: AsyncSession,
    rule_id: int,
    company_id: int,
    rule_update: schemas.CallRoutingRuleUpdate,
) -> Optional[models.CallRoutingRule]:
    """Update an existing call routing rule."""
    db_rule = await get_call_routing_rule(db, rule_id, company_id)
    if not db_rule:
        return None

    # Update fields
    update_data = rule_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_rule, field, value)

    await db.commit()
    await db.refresh(db_rule)

    logger.info(f"Updated call routing rule {rule_id}")
    return db_rule


async def delete_call_routing_rule(
    db: AsyncSession, rule_id: int, company_id: int
) -> bool:
    """Delete a call routing rule."""
    db_rule = await get_call_routing_rule(db, rule_id, company_id)
    if not db_rule:
        return False

    await db.delete(db_rule)
    await db.commit()

    logger.info(f"Deleted call routing rule {rule_id}")
    return True


# Phone Number Blacklist CRUD
async def get_blacklist_entries(
    db: AsyncSession,
    company_id: int,
    phone_number_id: Optional[int] = None,
    is_active: Optional[bool] = None,
) -> List[models.PhoneNumberBlacklist]:
    """Get blacklist entries for a company."""
    query = select(models.PhoneNumberBlacklist).filter(
        models.PhoneNumberBlacklist.company_id == company_id
    )

    if phone_number_id:
        query = query.filter(
            models.PhoneNumberBlacklist.phone_number_id == phone_number_id
        )

    if is_active is not None:
        query = query.filter(models.PhoneNumberBlacklist.is_active == is_active)

    query = query.order_by(desc(models.PhoneNumberBlacklist.created_at))

    result = await db.execute(query)
    return result.scalars().all()


async def create_blacklist_entry(
    db: AsyncSession,
    blacklist: schemas.PhoneNumberBlacklistCreate,
    created_by: Optional[int] = None,
) -> models.PhoneNumberBlacklist:
    """Create a new blacklist entry."""
    db_blacklist = models.PhoneNumberBlacklist(
        **blacklist.model_dump(), created_by=created_by
    )

    db.add(db_blacklist)
    await db.commit()
    await db.refresh(db_blacklist)

    logger.info(f"Created blacklist entry for {blacklist.blocked_number}")
    return db_blacklist


async def is_number_blacklisted(
    db: AsyncSession, phone_number_id: int, caller_number: str
) -> bool:
    """Check if a caller number is blacklisted for a phone number."""
    result = await db.execute(
        select(models.PhoneNumberBlacklist).filter(
            and_(
                models.PhoneNumberBlacklist.phone_number_id == phone_number_id,
                models.PhoneNumberBlacklist.blocked_number == caller_number,
                models.PhoneNumberBlacklist.is_active == True,
            )
        )
    )

    blacklist_entry = result.scalars().first()

    if blacklist_entry:
        # Check if temporary block has expired
        if blacklist_entry.block_type == "temporary" and blacklist_entry.block_until:
            from datetime import datetime

            if datetime.utcnow() > blacklist_entry.block_until:
                # Block has expired, deactivate it
                blacklist_entry.is_active = False
                await db.commit()
                return False

        # Update attempt count
        blacklist_entry.total_blocked_attempts += 1
        blacklist_entry.last_attempt_at = func.now()
        await db.commit()

        return True

    return False
