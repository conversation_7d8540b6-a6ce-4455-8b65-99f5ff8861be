"""
Enhanced Phone Number Models for Home Service Platform

Manages phone numbers with agent assignment, routing rules, and business hours.
"""

from enum import Enum as PyEnum

from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class PhoneNumberStatus(PyEnum):
    """Phone number status options."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PORTING = "porting"
    MAINTENANCE = "maintenance"


class CallRoutingType(PyEnum):
    """Call routing types."""

    DIRECT = "direct"  # Direct to specific agent
    ROUND_ROBIN = "round_robin"  # Rotate between agents
    SKILL_BASED = "skill_based"  # Route based on agent skills
    BUSINESS_HOURS = "business_hours"  # Route based on business hours
    QUEUE = "queue"  # Queue calls when agents busy
    VOICEMAIL = "voicemail"  # Send to voicemail


class PhoneNumber(Base):
    """
    Enhanced phone number model with agent assignment and routing.
    """

    __tablename__ = "phone_numbers"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)  # Owner
    # Customer information is stored in conversation records

    # Phone Number Information
    phone_number = Column(String(20), nullable=False, unique=True, index=True)
    formatted_number = Column(String(25), nullable=True)  # +****************
    country_code = Column(String(5), nullable=True, default="+1")
    area_code = Column(String(5), nullable=True)

    # Provider Information
    provider = Column(
        String(50), nullable=True, default="twilio"
    )  # twilio, elevenlabs, etc.
    provider_phone_sid = Column(
        String(100), nullable=True
    )  # Provider's phone number ID
    provider_config = Column(JSON, nullable=True)  # Provider-specific configuration

    # Assignment & Routing
    primary_agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True)
    backup_agent_ids = Column(
        JSON, nullable=True, default=list
    )  # List of backup agent IDs
    routing_type = Column(
        String(50), nullable=False, default=CallRoutingType.DIRECT.value
    )
    routing_rules = Column(JSON, nullable=True, default=dict)  # Custom routing logic

    # Business Hours & Availability
    business_hours = Column(JSON, nullable=True)  # Operating hours for this number
    timezone = Column(String(50), nullable=True, default="UTC")
    holiday_schedule = Column(JSON, nullable=True)  # Holiday routing
    after_hours_action = Column(String(50), nullable=True, default="voicemail")

    # Call Handling Settings
    max_ring_time = Column(Integer, default=30)  # seconds
    max_queue_time = Column(Integer, default=300)  # seconds
    queue_music_url = Column(String(500), nullable=True)
    voicemail_greeting_url = Column(String(500), nullable=True)

    # Features & Capabilities
    call_recording_enabled = Column(Boolean, default=False)
    call_transcription_enabled = Column(Boolean, default=False)
    sms_enabled = Column(Boolean, default=True)
    mms_enabled = Column(Boolean, default=False)

    # Webhook Configuration
    webhook_url = Column(String(500), nullable=True)
    webhook_events = Column(JSON, nullable=True, default=list)  # Events to send
    webhook_auth_token = Column(String(255), nullable=True)

    # Status & Control
    status = Column(String(50), nullable=False, default=PhoneNumberStatus.ACTIVE.value)
    is_verified = Column(Boolean, default=False)

    # Usage & Analytics
    total_calls_received = Column(Integer, default=0)
    total_calls_answered = Column(Integer, default=0)
    total_sms_received = Column(Integer, default=0)
    total_sms_sent = Column(Integer, default=0)

    # Metadata
    friendly_name = Column(String(100), nullable=True)  # Human-readable name
    description = Column(Text, nullable=True)
    tags = Column(JSON, nullable=True, default=list)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_call_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    company = relationship("Company", back_populates="phone_numbers")
    # Customer relationships are handled through conversations
    user = relationship("User")
    agent = relationship(
        "Agent", back_populates="phone_numbers", foreign_keys=[primary_agent_id]
    )
    call_history = relationship("CallHistory", back_populates="phone_number")
    conversations = relationship("Conversation", back_populates="phone_number")

    def __repr__(self):
        return f"<PhoneNumber(id={self.id}, number='{self.phone_number}', agent_id={self.primary_agent_id})>"


class CallQueue(Base):
    """
    Call queue management for phone numbers.
    """

    __tablename__ = "call_queues"

    id = Column(Integer, primary_key=True, index=True)
    phone_number_id = Column(Integer, ForeignKey("phone_numbers.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Queue Information
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)

    # Queue Settings
    max_queue_size = Column(Integer, default=10)
    max_wait_time = Column(Integer, default=300)  # seconds
    priority_routing = Column(Boolean, default=False)

    # Agent Assignment
    agent_ids = Column(JSON, nullable=True, default=list)  # Agents in this queue
    skill_requirements = Column(JSON, nullable=True, default=list)  # Required skills

    # Queue Behavior
    overflow_action = Column(
        String(50), default="voicemail"
    )  # voicemail, transfer, hangup
    overflow_destination = Column(String(100), nullable=True)

    # Music & Messages
    hold_music_url = Column(String(500), nullable=True)
    welcome_message_url = Column(String(500), nullable=True)
    position_announcement = Column(Boolean, default=True)
    estimated_wait_announcement = Column(Boolean, default=True)

    # Status
    is_active = Column(Boolean, default=True)

    # Statistics
    total_calls_queued = Column(Integer, default=0)
    total_calls_answered = Column(Integer, default=0)
    total_calls_abandoned = Column(Integer, default=0)
    average_wait_time = Column(Integer, default=0)  # seconds

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    phone_number = relationship("PhoneNumber")
    company = relationship("Company")

    def __repr__(self):
        return f"<CallQueue(id={self.id}, name='{self.name}', phone_number_id={self.phone_number_id})>"


class CallRoutingRule(Base):
    """
    Advanced call routing rules for phone numbers.
    """

    __tablename__ = "call_routing_rules"

    id = Column(Integer, primary_key=True, index=True)
    phone_number_id = Column(Integer, ForeignKey("phone_numbers.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Rule Information
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    priority = Column(Integer, default=0)  # Higher number = higher priority

    # Conditions
    conditions = Column(JSON, nullable=False)  # Routing conditions
    # Example: {"time_range": {"start": "09:00", "end": "17:00"}, "caller_id": "+1555*"}

    # Actions
    action_type = Column(
        String(50), nullable=False
    )  # route_to_agent, queue, voicemail, etc.
    action_config = Column(JSON, nullable=False)  # Action configuration
    # Example: {"agent_id": 123} or {"queue_id": 456}

    # Status & Control
    is_active = Column(Boolean, default=True)

    # Usage Statistics
    times_matched = Column(Integer, default=0)
    last_matched_at = Column(DateTime(timezone=True), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    phone_number = relationship("PhoneNumber")
    company = relationship("Company")

    def __repr__(self):
        return f"<CallRoutingRule(id={self.id}, name='{self.name}', priority={self.priority})>"


class PhoneNumberAnalytics(Base):
    """
    Analytics for phone number usage and performance.
    """

    __tablename__ = "phone_number_analytics"

    id = Column(Integer, primary_key=True, index=True)
    phone_number_id = Column(Integer, ForeignKey("phone_numbers.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Time Period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(
        String(20), nullable=False, default="daily"
    )  # hourly, daily, weekly, monthly

    # Call Metrics
    total_calls = Column(Integer, default=0)
    answered_calls = Column(Integer, default=0)
    missed_calls = Column(Integer, default=0)
    abandoned_calls = Column(Integer, default=0)

    # Call Duration Metrics
    total_call_duration = Column(Integer, default=0)  # in seconds
    average_call_duration = Column(Integer, default=0)  # in seconds
    longest_call_duration = Column(Integer, default=0)  # in seconds

    # Queue Metrics
    total_queued_calls = Column(Integer, default=0)
    average_queue_time = Column(Integer, default=0)  # in seconds
    max_queue_time = Column(Integer, default=0)  # in seconds
    queue_abandonment_rate = Column(Integer, default=0)  # percentage

    # SMS Metrics
    total_sms_received = Column(Integer, default=0)
    total_sms_sent = Column(Integer, default=0)
    sms_response_rate = Column(Integer, default=0)  # percentage

    # Agent Performance
    agent_utilization = Column(Integer, default=0)  # percentage
    first_call_resolution = Column(Integer, default=0)  # percentage

    # Customer Satisfaction
    customer_satisfaction_score = Column(Integer, nullable=True)  # 1-5 rating
    total_satisfaction_responses = Column(Integer, default=0)

    # Business Metrics
    leads_generated = Column(Integer, default=0)
    appointments_scheduled = Column(Integer, default=0)
    conversion_rate = Column(Integer, default=0)  # percentage

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    phone_number = relationship("PhoneNumber")
    company = relationship("Company")

    def __repr__(self):
        return f"<PhoneNumberAnalytics(phone_number_id={self.phone_number_id}, date='{self.date}')>"


class PhoneNumberBlacklist(Base):
    """
    Blacklist for blocking unwanted callers.
    """

    __tablename__ = "phone_number_blacklist"

    id = Column(Integer, primary_key=True, index=True)
    phone_number_id = Column(Integer, ForeignKey("phone_numbers.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Blocked Number
    blocked_number = Column(String(20), nullable=False, index=True)
    blocked_number_formatted = Column(String(25), nullable=True)

    # Block Information
    reason = Column(String(255), nullable=True)
    block_type = Column(String(50), default="permanent")  # permanent, temporary

    # Temporary Block Settings
    block_until = Column(DateTime(timezone=True), nullable=True)

    # Statistics
    total_blocked_attempts = Column(Integer, default=0)
    last_attempt_at = Column(DateTime(timezone=True), nullable=True)

    # Status
    is_active = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationships
    phone_number = relationship("PhoneNumber")
    company = relationship("Company")

    def __repr__(self):
        return f"<PhoneNumberBlacklist(id={self.id}, blocked_number='{self.blocked_number}')>"
