"""
A/B Testing API Router

FastAPI routes for managing A/B tests and analyzing results.
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.service import get_current_user
from modules.users.models import User
from modules.campaigns import crud
from .service import ab_testing_service

router = APIRouter(prefix="/ab-testing", tags=["ab-testing"])


@router.post("/generate-variants")
async def generate_ad_variants(
    business_profile_id: int,
    campaign_goal: str,
    num_variants: int = 3,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Generate multiple ad variants for A/B testing."""
    
    # Get business profile
    business_profile = await crud.get_business_profile(db, business_profile_id, current_user.id)
    if not business_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Business profile not found"
        )
    
    # Convert to dict for service
    profile_dict = {
        "business_type": business_profile.business_type,
        "business_name": business_profile.business_name,
        "services_offered": business_profile.services_offered,
        "target_audience": business_profile.target_audience,
        "unique_selling_points": business_profile.unique_selling_points,
        "city": business_profile.city,
        "country": business_profile.country
    }
    
    try:
        variants = await ab_testing_service.generate_ad_variants(
            business_profile=profile_dict,
            campaign_goal=campaign_goal,
            num_variants=min(num_variants, 5)  # Limit to 5 variants max
        )
        
        return variants
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate ad variants: {str(e)}"
        )


@router.post("/create-test")
async def create_ab_test(
    campaign_id: int,
    ab_test_config: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create A/B test campaigns based on generated variants."""
    
    # Get base campaign
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    if campaign.status != 'draft':
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only create A/B tests for draft campaigns"
        )
    
    try:
        # Create A/B test campaigns
        test_campaigns = await ab_testing_service.create_ab_test_campaigns(
            db=db,
            base_campaign=campaign,
            ab_test_config=ab_test_config,
            platform_configs={}  # Platform-specific configs would go here
        )
        
        # Update original campaign to mark as A/B test parent
        await crud.update_campaign(db, campaign_id, {
            "ab_test_config": {
                "is_ab_test_parent": True,
                "child_campaign_ids": [c.id for c in test_campaigns],
                "test_config": ab_test_config
            }
        })
        
        return {
            "success": True,
            "parent_campaign_id": campaign_id,
            "test_campaigns": [
                {
                    "id": c.id,
                    "name": c.name,
                    "variant_name": c.ab_test_config.get("variant_name"),
                    "budget": float(c.budget),
                    "traffic_allocation": c.ab_test_config.get("traffic_allocation")
                }
                for c in test_campaigns
            ],
            "message": f"Created {len(test_campaigns)} A/B test variants"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create A/B test: {str(e)}"
        )


@router.get("/results/{campaign_id}")
async def get_ab_test_results(
    campaign_id: int,
    test_duration_days: int = 7,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get A/B test results and analysis."""
    
    # Get parent campaign
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    ab_config = campaign.ab_test_config or {}
    if not ab_config.get("is_ab_test_parent"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Campaign is not an A/B test parent"
        )
    
    child_campaign_ids = ab_config.get("child_campaign_ids", [])
    if not child_campaign_ids:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No A/B test variants found"
        )
    
    try:
        # Analyze A/B test results
        results = await ab_testing_service.analyze_ab_test_results(
            db=db,
            campaign_ids=child_campaign_ids,
            test_duration_days=test_duration_days
        )
        
        return results
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze A/B test results: {str(e)}"
        )


@router.post("/declare-winner/{campaign_id}")
async def declare_ab_test_winner(
    campaign_id: int,
    winning_variant_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Declare a winning variant and pause other variants."""
    
    # Get parent campaign
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    ab_config = campaign.ab_test_config or {}
    child_campaign_ids = ab_config.get("child_campaign_ids", [])
    
    if winning_variant_id not in child_campaign_ids:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid winning variant ID"
        )
    
    try:
        # Pause all variants except the winner
        for variant_id in child_campaign_ids:
            if variant_id != winning_variant_id:
                await crud.update_campaign(db, variant_id, {"status": "paused"})
        
        # Keep winner active and allocate full budget
        winning_campaign = await crud.get_campaign(db, winning_variant_id, current_user.id)
        await crud.update_campaign(db, winning_variant_id, {
            "status": "active",
            "budget": campaign.budget,  # Allocate full original budget
            "daily_budget": campaign.budget / (campaign.duration_days or 30)
        })
        
        # Update parent campaign
        await crud.update_campaign(db, campaign_id, {
            "ab_test_config": {
                **ab_config,
                "test_completed": True,
                "winning_variant_id": winning_variant_id,
                "winner_declared_at": datetime.now().isoformat()
            }
        })
        
        return {
            "success": True,
            "winning_variant_id": winning_variant_id,
            "winning_campaign_name": winning_campaign.name,
            "message": "A/B test winner declared successfully"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to declare A/B test winner: {str(e)}"
        )


@router.get("/active-tests")
async def get_active_ab_tests(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get all active A/B tests for the current user."""
    
    try:
        # Get all campaigns for user
        campaigns = await crud.get_campaigns_by_user(db, current_user.id)
        
        # Filter for A/B test parent campaigns
        ab_test_campaigns = []
        for campaign in campaigns:
            ab_config = campaign.ab_test_config or {}
            if ab_config.get("is_ab_test_parent") and not ab_config.get("test_completed"):
                
                # Get child campaign details
                child_campaign_ids = ab_config.get("child_campaign_ids", [])
                child_campaigns = []
                
                for child_id in child_campaign_ids:
                    child_campaign = await crud.get_campaign(db, child_id, current_user.id)
                    if child_campaign:
                        child_campaigns.append({
                            "id": child_campaign.id,
                            "name": child_campaign.name,
                            "status": child_campaign.status,
                            "variant_name": child_campaign.ab_test_config.get("variant_name"),
                            "budget": float(child_campaign.budget),
                            "spent": 0  # This would come from analytics data
                        })
                
                ab_test_campaigns.append({
                    "id": campaign.id,
                    "name": campaign.name,
                    "created_at": campaign.created_at.isoformat(),
                    "variants": child_campaigns,
                    "test_config": ab_config.get("test_config", {})
                })
        
        return {
            "active_tests": ab_test_campaigns,
            "total_tests": len(ab_test_campaigns)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get active A/B tests: {str(e)}"
        )


@router.delete("/test/{campaign_id}")
async def stop_ab_test(
    campaign_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Stop an A/B test and pause all variants."""
    
    # Get parent campaign
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    ab_config = campaign.ab_test_config or {}
    child_campaign_ids = ab_config.get("child_campaign_ids", [])
    
    try:
        # Pause all variant campaigns
        for variant_id in child_campaign_ids:
            await crud.update_campaign(db, variant_id, {"status": "paused"})
        
        # Update parent campaign
        await crud.update_campaign(db, campaign_id, {
            "ab_test_config": {
                **ab_config,
                "test_stopped": True,
                "stopped_at": datetime.now().isoformat()
            }
        })
        
        return {
            "success": True,
            "message": f"A/B test stopped. {len(child_campaign_ids)} variants paused."
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stop A/B test: {str(e)}"
        )


@router.get("/performance-comparison/{campaign_id}")
async def get_performance_comparison(
    campaign_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get detailed performance comparison between A/B test variants."""
    
    # Get parent campaign
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    ab_config = campaign.ab_test_config or {}
    child_campaign_ids = ab_config.get("child_campaign_ids", [])
    
    try:
        # Get detailed performance data for each variant
        variant_performance = []
        
        for variant_id in child_campaign_ids:
            variant_campaign = await crud.get_campaign(db, variant_id, current_user.id)
            if not variant_campaign:
                continue
            
            # Get analytics data (last 30 days)
            analytics = await crud.get_campaign_analytics(db, variant_id, days=30)
            
            # Calculate performance metrics
            total_spend = sum(a.total_spend for a in analytics)
            total_impressions = sum(a.impressions for a in analytics)
            total_clicks = sum(a.clicks for a in analytics)
            total_conversions = sum(a.appointments_booked for a in analytics)
            
            variant_performance.append({
                "campaign_id": variant_id,
                "variant_name": variant_campaign.ab_test_config.get("variant_name"),
                "strategy": variant_campaign.ab_test_config.get("strategy", {}),
                "performance": {
                    "spend": float(total_spend),
                    "impressions": total_impressions,
                    "clicks": total_clicks,
                    "conversions": total_conversions,
                    "ctr": (total_clicks / total_impressions * 100) if total_impressions > 0 else 0,
                    "conversion_rate": (total_conversions / total_clicks * 100) if total_clicks > 0 else 0,
                    "cost_per_conversion": float(total_spend / total_conversions) if total_conversions > 0 else 0
                },
                "status": variant_campaign.status
            })
        
        return {
            "campaign_id": campaign_id,
            "campaign_name": campaign.name,
            "variants": variant_performance,
            "comparison_date": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get performance comparison: {str(e)}"
        )
