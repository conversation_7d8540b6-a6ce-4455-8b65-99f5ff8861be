"""
AI-Powered A/B Testing Service

Automatically generates multiple ad variants and manages split testing
to determine the best performing creative combinations.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
import json
import random

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from modules.campaigns import crud
from modules.campaigns.models import Campaign, AnalyticsData
from modules.campaigns.ai_service import ai_service

logger = logging.getLogger(__name__)


class ABTestStatus(str, Enum):
    """A/B test status options."""
    DRAFT = "draft"
    RUNNING = "running"
    COMPLETED = "completed"
    PAUSED = "paused"


class VariantType(str, Enum):
    """Types of ad variants."""
    HEADLINE = "headline"
    DESCRIPTION = "description"
    AD_COPY = "ad_copy"
    CREATIVE = "creative"
    TARGETING = "targeting"


class ABTestingService:
    """Service for AI-powered A/B testing of ad campaigns."""
    
    def __init__(self):
        self.min_test_duration_days = 7
        self.min_impressions_per_variant = 1000
        self.confidence_threshold = 0.95
        self.max_variants_per_test = 5
    
    async def generate_ad_variants(
        self, 
        business_profile: Dict[str, Any],
        campaign_goal: str,
        num_variants: int = 3,
        variant_types: List[VariantType] = None
    ) -> Dict[str, Any]:
        """Generate multiple ad variants for A/B testing."""
        
        try:
            if variant_types is None:
                variant_types = [VariantType.HEADLINE, VariantType.DESCRIPTION, VariantType.AD_COPY]
            
            # Prepare AI generation request with variant instructions
            ai_request = {
                "business_type": business_profile.get("business_type"),
                "business_name": business_profile.get("business_name"),
                "services": business_profile.get("services_offered", []),
                "target_audience": business_profile.get("target_audience"),
                "unique_selling_points": business_profile.get("unique_selling_points", []),
                "goal": campaign_goal,
                "location": f"{business_profile.get('city')}, {business_profile.get('country')}",
                "num_variants": num_variants,
                "variant_focus": [vt.value for vt in variant_types]
            }
            
            # Generate base content
            base_content = await ai_service.generate_ad_content(ai_request)
            
            # Generate additional variants with different approaches
            variants = []
            
            for i in range(num_variants):
                variant = await self._generate_single_variant(
                    ai_request, base_content, i, variant_types
                )
                variants.append(variant)
            
            # Create A/B test configuration
            ab_test_config = {
                "variants": variants,
                "test_settings": {
                    "traffic_split": self._calculate_traffic_split(num_variants),
                    "duration_days": self.min_test_duration_days,
                    "success_metrics": ["cost_per_appointment", "conversion_rate", "roi"],
                    "confidence_threshold": self.confidence_threshold
                },
                "variant_types": [vt.value for vt in variant_types],
                "created_at": datetime.now().isoformat()
            }
            
            return ab_test_config
            
        except Exception as e:
            logger.error(f"Error generating ad variants: {str(e)}")
            raise
    
    async def _generate_single_variant(
        self,
        base_request: Dict[str, Any],
        base_content: Dict[str, Any],
        variant_index: int,
        variant_types: List[VariantType]
    ) -> Dict[str, Any]:
        """Generate a single ad variant with specific focus."""
        
        # Define variant strategies
        strategies = [
            {
                "name": "Benefit-Focused",
                "headline_style": "benefit-driven",
                "description_style": "problem-solution",
                "tone": "professional"
            },
            {
                "name": "Urgency-Driven",
                "headline_style": "urgency",
                "description_style": "time-sensitive",
                "tone": "urgent"
            },
            {
                "name": "Trust-Building",
                "headline_style": "credibility",
                "description_style": "testimonial-style",
                "tone": "trustworthy"
            },
            {
                "name": "Value-Proposition",
                "headline_style": "value-focused",
                "description_style": "competitive-advantage",
                "tone": "confident"
            },
            {
                "name": "Local-Focused",
                "headline_style": "location-specific",
                "description_style": "community-oriented",
                "tone": "friendly"
            }
        ]
        
        strategy = strategies[variant_index % len(strategies)]
        
        # Generate variant-specific content
        variant_request = {
            **base_request,
            "strategy": strategy,
            "variant_name": f"Variant {variant_index + 1}: {strategy['name']}"
        }
        
        # Use base content as starting point and modify based on strategy
        variant_content = await self._apply_variant_strategy(
            base_content, strategy, variant_types
        )
        
        return {
            "id": f"variant_{variant_index + 1}",
            "name": strategy["name"],
            "strategy": strategy,
            "content": variant_content,
            "performance": {
                "impressions": 0,
                "clicks": 0,
                "conversions": 0,
                "cost": 0,
                "ctr": 0,
                "conversion_rate": 0,
                "cost_per_conversion": 0
            }
        }
    
    async def _apply_variant_strategy(
        self,
        base_content: Dict[str, Any],
        strategy: Dict[str, Any],
        variant_types: List[VariantType]
    ) -> Dict[str, Any]:
        """Apply variant strategy to modify base content."""
        
        variant_content = base_content.copy()
        
        # Modify headlines based on strategy
        if VariantType.HEADLINE in variant_types:
            variant_content["headlines"] = await self._generate_strategy_headlines(
                base_content.get("headlines", []), strategy
            )
        
        # Modify descriptions based on strategy
        if VariantType.DESCRIPTION in variant_types:
            variant_content["descriptions"] = await self._generate_strategy_descriptions(
                base_content.get("descriptions", []), strategy
            )
        
        # Modify ad copy based on strategy
        if VariantType.AD_COPY in variant_types:
            variant_content["ad_copy_variations"] = await self._generate_strategy_ad_copy(
                base_content.get("ad_copy_variations", []), strategy
            )
        
        return variant_content
    
    async def _generate_strategy_headlines(
        self, base_headlines: List[str], strategy: Dict[str, Any]
    ) -> List[str]:
        """Generate headlines based on specific strategy."""
        
        strategy_templates = {
            "benefit-driven": [
                "Get {benefit} in {location}",
                "{benefit} - Professional {service}",
                "Experience {benefit} Today"
            ],
            "urgency": [
                "Book Now - Limited Availability",
                "Same Day {service} Available",
                "Emergency {service} - Call Now"
            ],
            "credibility": [
                "Trusted {service} Since {year}",
                "Licensed & Insured {service}",
                "5-Star Rated {service}"
            ],
            "value-focused": [
                "Affordable {service} Solutions",
                "Best Value {service} in {location}",
                "Quality {service} - Fair Prices"
            ],
            "location-specific": [
                "{location}'s Premier {service}",
                "Local {service} Experts",
                "Serving {location} Since {year}"
            ]
        }
        
        # For now, return modified base headlines
        # In a real implementation, this would use AI to generate strategy-specific headlines
        return base_headlines[:3]  # Return top 3 headlines
    
    async def _generate_strategy_descriptions(
        self, base_descriptions: List[str], strategy: Dict[str, Any]
    ) -> List[str]:
        """Generate descriptions based on specific strategy."""
        
        # For now, return modified base descriptions
        # In a real implementation, this would use AI to generate strategy-specific descriptions
        return base_descriptions[:3]  # Return top 3 descriptions
    
    async def _generate_strategy_ad_copy(
        self, base_ad_copy: List[str], strategy: Dict[str, Any]
    ) -> List[str]:
        """Generate ad copy based on specific strategy."""
        
        # For now, return modified base ad copy
        # In a real implementation, this would use AI to generate strategy-specific ad copy
        return base_ad_copy[:3]  # Return top 3 ad copy variations
    
    def _calculate_traffic_split(self, num_variants: int) -> Dict[str, float]:
        """Calculate equal traffic split for variants."""
        
        split_percentage = 100.0 / num_variants
        
        return {
            f"variant_{i+1}": split_percentage
            for i in range(num_variants)
        }
    
    async def create_ab_test_campaigns(
        self,
        db: AsyncSession,
        base_campaign: Campaign,
        ab_test_config: Dict[str, Any],
        platform_configs: Dict[str, Any]
    ) -> List[Campaign]:
        """Create multiple campaigns for A/B testing."""
        
        try:
            created_campaigns = []
            variants = ab_test_config["variants"]
            traffic_split = ab_test_config["test_settings"]["traffic_split"]
            
            for i, variant in enumerate(variants):
                # Calculate budget allocation based on traffic split
                variant_budget = base_campaign.budget * Decimal(str(traffic_split[f"variant_{i+1}"] / 100))
                
                # Create campaign data for variant
                variant_campaign_data = {
                    "name": f"{base_campaign.name} - {variant['name']}",
                    "description": f"A/B Test Variant: {variant['name']}",
                    "business_profile_id": base_campaign.business_profile_id,
                    "budget": variant_budget,
                    "daily_budget": variant_budget / (base_campaign.duration_days or 30),
                    "duration_days": base_campaign.duration_days,
                    "start_date": base_campaign.start_date,
                    "end_date": base_campaign.end_date,
                    "primary_goal": base_campaign.primary_goal,
                    "target_appointments": int((base_campaign.target_appointments or 0) * traffic_split[f"variant_{i+1}"] / 100),
                    "target_cost_per_appointment": base_campaign.target_cost_per_appointment,
                    "ad_platform": base_campaign.ad_platform,
                    
                    # A/B test specific fields
                    "selected_ad_copy": variant["content"]["ad_copy_variations"][0] if variant["content"].get("ad_copy_variations") else None,
                    "selected_headline": variant["content"]["headlines"][0] if variant["content"].get("headlines") else None,
                    "selected_description": variant["content"]["descriptions"][0] if variant["content"].get("descriptions") else None,
                    "selected_targeting": variant["content"].get("targeting_suggestions"),
                    
                    # Store A/B test metadata
                    "ab_test_config": {
                        "is_ab_test": True,
                        "parent_campaign_id": base_campaign.id,
                        "variant_id": variant["id"],
                        "variant_name": variant["name"],
                        "strategy": variant["strategy"],
                        "traffic_allocation": traffic_split[f"variant_{i+1}"]
                    }
                }
                
                # Create campaign in database
                campaign = await crud.create_campaign(db, variant_campaign_data, base_campaign.user_id)
                created_campaigns.append(campaign)
            
            return created_campaigns
            
        except Exception as e:
            logger.error(f"Error creating A/B test campaigns: {str(e)}")
            raise
    
    async def analyze_ab_test_results(
        self,
        db: AsyncSession,
        campaign_ids: List[int],
        test_duration_days: int = 7
    ) -> Dict[str, Any]:
        """Analyze A/B test results and determine winning variant."""
        
        try:
            # Get performance data for all variants
            variant_results = []
            
            for campaign_id in campaign_ids:
                campaign = await crud.get_campaign(db, campaign_id)
                if not campaign:
                    continue
                
                # Get analytics data
                end_date = datetime.now()
                start_date = end_date - timedelta(days=test_duration_days)
                
                result = await db.execute(
                    select(AnalyticsData).where(
                        and_(
                            AnalyticsData.campaign_id == campaign_id,
                            AnalyticsData.date >= start_date,
                            AnalyticsData.date <= end_date
                        )
                    )
                )
                analytics_data = result.scalars().all()
                
                # Calculate variant performance
                variant_performance = self._calculate_variant_performance(analytics_data)
                variant_performance["campaign_id"] = campaign_id
                variant_performance["campaign_name"] = campaign.name
                variant_performance["variant_config"] = campaign.ab_test_config
                
                variant_results.append(variant_performance)
            
            # Determine winning variant
            winning_variant = self._determine_winning_variant(variant_results)
            
            # Calculate statistical significance
            significance_results = self._calculate_statistical_significance(variant_results)
            
            return {
                "test_results": variant_results,
                "winning_variant": winning_variant,
                "statistical_significance": significance_results,
                "recommendations": self._generate_ab_test_recommendations(variant_results, winning_variant),
                "test_duration_days": test_duration_days,
                "analyzed_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing A/B test results: {str(e)}")
            raise
    
    def _calculate_variant_performance(self, analytics_data: List[AnalyticsData]) -> Dict[str, Any]:
        """Calculate performance metrics for a variant."""
        
        if not analytics_data:
            return {
                "impressions": 0,
                "clicks": 0,
                "conversions": 0,
                "spend": 0,
                "ctr": 0,
                "conversion_rate": 0,
                "cost_per_conversion": 0,
                "roi": 0
            }
        
        total_impressions = sum(data.impressions for data in analytics_data)
        total_clicks = sum(data.clicks for data in analytics_data)
        total_conversions = sum(data.appointments_booked for data in analytics_data)
        total_spend = sum(data.total_spend for data in analytics_data)
        
        ctr = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0
        conversion_rate = (total_conversions / total_clicks * 100) if total_clicks > 0 else 0
        cost_per_conversion = (total_spend / total_conversions) if total_conversions > 0 else 0
        
        # Calculate ROI (assuming average appointment value)
        avg_appointment_value = Decimal('120.00')
        total_revenue = total_conversions * avg_appointment_value
        roi = ((total_revenue - total_spend) / total_spend * 100) if total_spend > 0 else 0
        
        return {
            "impressions": total_impressions,
            "clicks": total_clicks,
            "conversions": total_conversions,
            "spend": float(total_spend),
            "ctr": float(ctr),
            "conversion_rate": float(conversion_rate),
            "cost_per_conversion": float(cost_per_conversion),
            "roi": float(roi)
        }
    
    def _determine_winning_variant(self, variant_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Determine the winning variant based on performance metrics."""
        
        if not variant_results:
            return None
        
        # Score variants based on multiple metrics
        for variant in variant_results:
            score = 0
            
            # ROI weight: 40%
            score += variant["roi"] * 0.4
            
            # Conversion rate weight: 30%
            score += variant["conversion_rate"] * 0.3
            
            # CTR weight: 20%
            score += variant["ctr"] * 0.2
            
            # Cost efficiency weight: 10% (inverse of cost per conversion)
            if variant["cost_per_conversion"] > 0:
                score += (1 / variant["cost_per_conversion"]) * 100 * 0.1
            
            variant["composite_score"] = score
        
        # Return variant with highest composite score
        winning_variant = max(variant_results, key=lambda x: x["composite_score"])
        return winning_variant
    
    def _calculate_statistical_significance(self, variant_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate statistical significance of A/B test results."""
        
        # Simplified statistical significance calculation
        # In a real implementation, you'd use proper statistical tests
        
        if len(variant_results) < 2:
            return {"is_significant": False, "confidence": 0}
        
        # Check if we have enough data
        min_conversions = min(variant["conversions"] for variant in variant_results)
        min_impressions = min(variant["impressions"] for variant in variant_results)
        
        has_sufficient_data = (
            min_conversions >= 10 and
            min_impressions >= self.min_impressions_per_variant
        )
        
        # Calculate confidence based on data volume and performance difference
        best_variant = max(variant_results, key=lambda x: x["conversion_rate"])
        worst_variant = min(variant_results, key=lambda x: x["conversion_rate"])
        
        performance_difference = best_variant["conversion_rate"] - worst_variant["conversion_rate"]
        
        # Simplified confidence calculation
        confidence = min(0.95, (performance_difference / 100) * 2 + 0.5) if has_sufficient_data else 0.3
        
        return {
            "is_significant": confidence >= self.confidence_threshold,
            "confidence": confidence,
            "has_sufficient_data": has_sufficient_data,
            "performance_difference": performance_difference
        }
    
    def _generate_ab_test_recommendations(
        self, 
        variant_results: List[Dict[str, Any]], 
        winning_variant: Dict[str, Any]
    ) -> List[str]:
        """Generate recommendations based on A/B test results."""
        
        recommendations = []
        
        if not winning_variant:
            recommendations.append("Insufficient data to determine a clear winner. Continue testing.")
            return recommendations
        
        # Performance-based recommendations
        if winning_variant["roi"] > 200:
            recommendations.append(f"Scale the winning variant '{winning_variant['campaign_name']}' - it shows excellent ROI of {winning_variant['roi']:.1f}%")
        
        if winning_variant["conversion_rate"] > 5:
            recommendations.append(f"The winning variant has a strong conversion rate of {winning_variant['conversion_rate']:.2f}% - consider applying its strategy to other campaigns")
        
        # Strategy-based recommendations
        winning_strategy = winning_variant.get("variant_config", {}).get("strategy", {})
        if winning_strategy:
            strategy_name = winning_strategy.get("name", "Unknown")
            recommendations.append(f"The '{strategy_name}' approach performed best - consider using this strategy for future campaigns")
        
        # Budget allocation recommendations
        total_spend = sum(variant["spend"] for variant in variant_results)
        winning_spend = winning_variant["spend"]
        
        if winning_spend / total_spend < 0.4:  # Winner used less than 40% of budget
            recommendations.append("Reallocate more budget to the winning variant to maximize results")
        
        return recommendations


# Global service instance
ab_testing_service = ABTestingService()
