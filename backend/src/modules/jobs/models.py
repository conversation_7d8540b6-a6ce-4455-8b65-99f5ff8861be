from sqlalchemy import (
    Column,
    DateTime,
    Foreign<PERSON><PERSON>,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base
from modules.users.models import User  # noqa: F401, needed for relationship


class Job(Base):
    __tablename__ = "jobs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    service_type = Column(String, nullable=False)
    scheduled_time = Column(DateTime(timezone=True), nullable=False)
    status = Column(
        String, default="scheduled"
    )  # e.g., "scheduled", "completed", "cancelled"
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    customer = relationship("Customer", back_populates="jobs")
    user = relationship("User", back_populates="jobs")

    def __repr__(self):
        return f"<Job(id={self.id}, service_type='{self.service_type}', status='{self.status}')>"
