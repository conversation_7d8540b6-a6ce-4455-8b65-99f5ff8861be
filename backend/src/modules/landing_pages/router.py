"""
Landing Pages API Router

FastAPI routes for generating and managing AI-powered landing pages.
"""

from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Form
from fastapi.responses import HTMLResponse
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.service import get_current_user
from modules.users.models import User
from modules.campaigns import crud
from .service import landing_page_service

router = APIRouter(prefix="/landing-pages", tags=["landing-pages"])


@router.post("/generate")
async def generate_landing_page(
    campaign_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Generate an AI-powered landing page for a campaign."""
    
    # Get campaign
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    # Get business profile
    business_profile = await crud.get_business_profile(db, campaign.business_profile_id, current_user.id)
    if not business_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Business profile not found"
        )
    
    try:
        # Get ad content (from campaign or generate new)
        ad_content = {
            "headlines": [campaign.selected_headline] if campaign.selected_headline else ["Professional Service"],
            "descriptions": [campaign.selected_description] if campaign.selected_description else ["Quality service you can trust"],
            "ad_copy_variations": [campaign.selected_ad_copy] if campaign.selected_ad_copy else ["Contact us today"]
        }
        
        # Generate landing page
        landing_page = await landing_page_service.generate_landing_page(
            campaign=campaign,
            business_profile=business_profile,
            ad_content=ad_content
        )
        
        # Update campaign with landing page URL
        await crud.update_campaign(db, campaign_id, {
            "landing_page_url": landing_page["url"],
            "landing_page_id": landing_page["page_id"]
        })
        
        return landing_page
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate landing page: {str(e)}"
        )


@router.get("/preview/{page_id}")
async def preview_landing_page(
    page_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Preview a generated landing page."""
    
    try:
        # In a real implementation, you'd verify the user owns this page
        # For now, we'll just try to read the file
        
        import os
        file_path = os.path.join(landing_page_service.storage_path, f"{page_id}.html")
        
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Landing page not found"
            )
        
        with open(file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        return HTMLResponse(content=html_content)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to preview landing page: {str(e)}"
        )


@router.get("/analytics/{page_id}")
async def get_landing_page_analytics(
    page_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get analytics data for a landing page."""
    
    try:
        # Verify user owns this page (in a real implementation)
        analytics = await landing_page_service.get_page_analytics(page_id)
        return analytics
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get landing page analytics: {str(e)}"
        )


@router.post("/submit/{campaign_id}")
async def submit_landing_page_form(
    campaign_id: int,
    name: str = Form(...),
    email: str = Form(...),
    phone: str = Form(...),
    service: str = Form(...),
    message: str = Form(None),
    company: str = Form(None),
    db: AsyncSession = Depends(get_db)
):
    """Handle landing page form submissions."""
    
    try:
        # Get campaign
        campaign = await crud.get_campaign_by_id(db, campaign_id)
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        # Create lead/appointment record
        lead_data = {
            "campaign_id": campaign_id,
            "customer_name": name,
            "customer_email": email,
            "customer_phone": phone,
            "service_type": service,
            "message": message,
            "company": company,
            "source": "landing_page",
            "status": "new"
        }
        
        # In a real implementation, this would create a Lead record
        # For now, we'll just log it
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"New lead from landing page: {lead_data}")
        
        # Return success response (could redirect to thank you page)
        return HTMLResponse(content="""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Thank You</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                .container { max-width: 600px; margin: 0 auto; }
                h1 { color: #2c3e50; }
                p { font-size: 1.1rem; margin: 20px 0; }
                .cta { background: #e74c3c; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 20px; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Thank You!</h1>
                <p>We've received your request and will contact you within 24 hours.</p>
                <p>A member of our team will reach out to discuss your needs and provide a personalized quote.</p>
                <a href="/" class="cta">Return to Website</a>
            </div>
        </body>
        </html>
        """)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit form: {str(e)}"
        )


@router.get("/user-pages")
async def get_user_landing_pages(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get all landing pages for the current user."""
    
    try:
        # Get user's campaigns with landing pages
        campaigns = await crud.get_campaigns_by_user(db, current_user.id)
        
        landing_pages = []
        for campaign in campaigns:
            if campaign.landing_page_id:
                # Get analytics for each page
                analytics = await landing_page_service.get_page_analytics(campaign.landing_page_id)
                
                landing_pages.append({
                    "page_id": campaign.landing_page_id,
                    "url": campaign.landing_page_url,
                    "campaign_id": campaign.id,
                    "campaign_name": campaign.name,
                    "created_at": campaign.created_at.isoformat(),
                    "analytics": analytics
                })
        
        return {
            "landing_pages": landing_pages,
            "total_pages": len(landing_pages)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get landing pages: {str(e)}"
        )


@router.delete("/{page_id}")
async def delete_landing_page(
    page_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete a landing page."""
    
    try:
        # Find campaign with this landing page
        campaigns = await crud.get_campaigns_by_user(db, current_user.id)
        campaign_to_update = None
        
        for campaign in campaigns:
            if campaign.landing_page_id == page_id:
                campaign_to_update = campaign
                break
        
        if not campaign_to_update:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Landing page not found"
            )
        
        # Delete file
        import os
        file_path = os.path.join(landing_page_service.storage_path, f"{page_id}.html")
        if os.path.exists(file_path):
            os.remove(file_path)
        
        # Update campaign to remove landing page reference
        await crud.update_campaign(db, campaign_to_update.id, {
            "landing_page_url": None,
            "landing_page_id": None
        })
        
        return {"success": True, "message": "Landing page deleted successfully"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete landing page: {str(e)}"
        )


@router.put("/{page_id}/regenerate")
async def regenerate_landing_page(
    page_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Regenerate a landing page with updated content."""
    
    try:
        # Find campaign with this landing page
        campaigns = await crud.get_campaigns_by_user(db, current_user.id)
        campaign = None
        
        for c in campaigns:
            if c.landing_page_id == page_id:
                campaign = c
                break
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Landing page not found"
            )
        
        # Get business profile
        business_profile = await crud.get_business_profile(db, campaign.business_profile_id, current_user.id)
        
        # Regenerate page
        ad_content = {
            "headlines": [campaign.selected_headline] if campaign.selected_headline else ["Professional Service"],
            "descriptions": [campaign.selected_description] if campaign.selected_description else ["Quality service you can trust"],
            "ad_copy_variations": [campaign.selected_ad_copy] if campaign.selected_ad_copy else ["Contact us today"]
        }
        
        # Delete old file
        import os
        old_file_path = os.path.join(landing_page_service.storage_path, f"{page_id}.html")
        if os.path.exists(old_file_path):
            os.remove(old_file_path)
        
        # Generate new page with same ID
        new_page = await landing_page_service.generate_landing_page(
            campaign=campaign,
            business_profile=business_profile,
            ad_content=ad_content
        )
        
        # Update the file with the same page_id
        import shutil
        shutil.move(new_page["file_path"], old_file_path)
        
        return {
            "success": True,
            "page_id": page_id,
            "url": campaign.landing_page_url,
            "message": "Landing page regenerated successfully"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to regenerate landing page: {str(e)}"
        )
