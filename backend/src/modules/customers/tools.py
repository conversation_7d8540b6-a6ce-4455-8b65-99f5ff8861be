import logging

from langchain.tools import tool

from core.db.database import get_db
from core.utils.helpers import model_to_dict
from modules.customers import crud as customer_crud

logger = logging.getLogger(__name__)


@tool
async def get_customer_details(customer_id: int) -> dict:
    """Get details for a specific customer by their ID."""
    logger.info(f"Langchain Tool: get_customer_details for customer_id: {customer_id}")
    async for db in get_db():
        customer = await customer_crud.get_customer(db, customer_id=customer_id)
        if customer:
            return model_to_dict(customer)
        else:
            logger.error("Customer not found.")
            return {"error": "Customer not found."}
