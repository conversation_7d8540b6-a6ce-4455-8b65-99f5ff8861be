from typing import Any, Dict, Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from . import models, schemas


async def get_customer(db: AsyncSession, customer_id: int, user_id: int):
    result = await db.execute(
        select(models.Customer).filter(
            models.Customer.id == customer_id, models.Customer.user_id == user_id
        )
    )
    return result.scalars().first()


async def get_customer_by_name(db: AsyncSession, name: str, user_id: int):
    result = await db.execute(
        select(models.Customer).filter(
            models.Customer.name == name, models.Customer.user_id == user_id
        )
    )
    return result.scalars().first()


async def get_customer_by_domain(db: AsyncSession, domain: str, user_id: int):
    result = await db.execute(
        select(models.Customer).filter(
            models.Customer.domain == domain, models.Customer.user_id == user_id
        )
    )
    return result.scalars().first()


async def get_customer_by_domain_unfiltered(db: AsyncSession, domain: str):
    result = await db.execute(
        select(models.Customer).filter(models.Customer.domain == domain)
    )
    return result.scalars().first()


async def get_customers(
    db: AsyncSession, user_id: int, skip: int = 0, limit: int = 100
):
    result = await db.execute(
        select(models.Customer)
        .filter(models.Customer.user_id == user_id)
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()


async def create_customer(
    db: AsyncSession, customer: schemas.CustomerCreate, user_id: int
):
    db_customer = models.Customer(**customer.model_dump(), user_id=user_id)
    db.add(db_customer)
    await db.commit()
    await db.refresh(db_customer)
    return db_customer


async def update_customer(
    db: AsyncSession, customer_id: int, customer: schemas.CustomerUpdate, user_id: int
):
    db_customer = await db.get(models.Customer, customer_id)
    if db_customer and db_customer.user_id == user_id:
        update_data = customer.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_customer, key, value)
        await db.commit()
        await db.refresh(db_customer)
    return db_customer


async def delete_customer(db: AsyncSession, customer_id: int, user_id: int):
    db_customer = await db.get(models.Customer, customer_id)
    if db_customer and db_customer.user_id == user_id:
        await db.delete(db_customer)
        await db.commit()
        return True
    return False


async def get_call_context(
    db: AsyncSession,
    customer_id: int,
    user_id: int,
    webhook_data: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    Gathers the full context for a call, including customer details and webhook data.
    """
    customer = await get_customer(db, customer_id, user_id)
    if not customer:
        return {}

    customer_info = {
        "id": customer.id,
        "name": customer.name,
        "phone_number": customer.phone_number,
        "address": customer.address,
        "city": customer.city,
        "state": customer.state,
        "zip_code": customer.zip_code,
    }

    return {
        "customer_info": customer_info,
        "call_context": webhook_data or {},
    }
