from sqlalchemy import (
    Column,
    DateTime,
    <PERSON><PERSON><PERSON>,
    Integer,
    String,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base
from modules.users.models import User  # noqa: F401, needed for relationship


class Customer(Base):
    __tablename__ = "customers"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    name = Column(String, nullable=False, index=True)
    domain = Column(String, unique=True, nullable=False, index=True)
    phone_number = Column(String, nullable=True)
    address = Column(String, nullable=True)
    city = Column(String, nullable=True)
    state = Column(String, nullable=True)
    zip_code = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    jobs = relationship("Job", back_populates="customer")
    call_history = relationship("CallHistory", back_populates="customer")
    user = relationship("User", back_populates="customers")

    def __repr__(self):
        return f"<Customer(id={self.id}, name='{self.name}', domain='{self.domain}')>"
