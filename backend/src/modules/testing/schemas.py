"""
Testing Schemas for API requests and responses.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field


class TestType(str, Enum):
    """Test types."""

    PHONE = "phone"
    WEB = "web"
    TEXT = "text"
    VOICE = "voice"
    CHAT = "chat"


class TestStatus(str, Enum):
    """Test status options."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TestChannel(str, Enum):
    """Test channels."""

    PHONE = "phone"
    WEB_CHAT = "web_chat"
    SMS = "sms"
    EMAIL = "email"
    WEBHOOK = "webhook"


# Test Session Schemas
class TestSessionBase(BaseModel):
    name: str = Field(..., max_length=255)
    description: Optional[str] = None
    test_type: TestType
    channel: TestChannel
    agent_id: int
    test_config: Dict[str, Any] = Field(default_factory=dict)
    expected_outcomes: Optional[List[str]] = None
    timeout_seconds: int = Field(default=300, ge=30, le=3600)


class TestSessionCreate(TestSessionBase):
    company_id: int


class TestSessionUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    test_config: Optional[Dict[str, Any]] = None
    expected_outcomes: Optional[List[str]] = None
    timeout_seconds: Optional[int] = Field(None, ge=30, le=3600)


class TestSession(TestSessionBase):
    id: int
    company_id: int
    status: TestStatus = TestStatus.PENDING
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[int] = None
    success: Optional[bool] = None
    error_message: Optional[str] = None
    results: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Test Message Schemas
class TestMessageBase(BaseModel):
    message_type: str = Field(..., max_length=50)  # user, agent, system
    content: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None


class TestMessageCreate(TestMessageBase):
    test_session_id: int


class TestMessage(TestMessageBase):
    id: int
    test_session_id: int
    sequence_number: int
    response_time_ms: Optional[int] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Test Execution Schemas
class TestExecutionRequest(BaseModel):
    test_messages: List[str] = Field(..., min_items=1)
    wait_for_completion: bool = True
    max_wait_seconds: int = Field(default=300, ge=30, le=3600)
    test_metadata: Optional[Dict[str, Any]] = None


class TestExecutionResponse(BaseModel):
    session_id: int
    status: TestStatus
    started_at: datetime
    messages: List[TestMessage] = []
    current_step: int = 0
    total_steps: int = 0
    estimated_completion: Optional[datetime] = None


# Test Results Schemas
class TestResultSummary(BaseModel):
    total_tests: int
    passed_tests: int
    failed_tests: int
    success_rate: float
    average_response_time: Optional[float] = None
    total_duration: Optional[int] = None


class TestSessionWithMessages(TestSession):
    messages: List[TestMessage] = []


class TestSessionListResponse(BaseModel):
    sessions: List[TestSession]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


# Agent Testing Schemas
class AgentTestRequest(BaseModel):
    message: str
    channel: TestChannel = TestChannel.WEB_CHAT
    context: Optional[Dict[str, Any]] = None
    customer_info: Optional[Dict[str, Any]] = None
    test_metadata: Optional[Dict[str, Any]] = None


class AgentTestResponse(BaseModel):
    response: str
    response_time_ms: int
    success: bool
    error_message: Optional[str] = None
    conversation_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    agent_state: Optional[Dict[str, Any]] = None


# Multi-Channel Test Schemas
class MultiChannelTestConfig(BaseModel):
    channels: List[TestChannel]
    test_scenarios: List[Dict[str, Any]]
    parallel_execution: bool = False
    cross_channel_validation: bool = False
    test_duration_minutes: int = Field(default=30, ge=5, le=120)


class MultiChannelTestRequest(BaseModel):
    agent_id: int
    config: MultiChannelTestConfig
    test_name: str = Field(..., max_length=255)
    description: Optional[str] = None


class MultiChannelTestResult(BaseModel):
    test_id: int
    agent_id: int
    channels_tested: List[TestChannel]
    total_scenarios: int
    passed_scenarios: int
    failed_scenarios: int
    success_rate: float
    channel_results: Dict[str, Dict[str, Any]]
    overall_performance: Dict[str, Any]
    recommendations: List[str] = []


# Performance Test Schemas
class PerformanceTestConfig(BaseModel):
    concurrent_users: int = Field(default=10, ge=1, le=100)
    test_duration_minutes: int = Field(default=15, ge=5, le=60)
    ramp_up_seconds: int = Field(default=60, ge=10, le=300)
    message_interval_seconds: int = Field(default=5, ge=1, le=30)
    test_scenarios: List[str]


class PerformanceTestRequest(BaseModel):
    agent_id: int
    config: PerformanceTestConfig
    test_name: str = Field(..., max_length=255)


class PerformanceTestMetrics(BaseModel):
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_response_time: float
    min_response_time: float
    max_response_time: float
    requests_per_second: float
    error_rate: float
    throughput_metrics: Dict[str, Any]


# Test Template Schemas
class TestTemplateBase(BaseModel):
    name: str = Field(..., max_length=255)
    description: Optional[str] = None
    test_type: TestType
    channel: TestChannel
    template_config: Dict[str, Any] = Field(default_factory=dict)
    test_scenarios: List[Dict[str, Any]] = Field(default_factory=list)
    expected_outcomes: List[str] = Field(default_factory=list)
    is_active: bool = True


class TestTemplateCreate(TestTemplateBase):
    company_id: int


class TestTemplateUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    template_config: Optional[Dict[str, Any]] = None
    test_scenarios: Optional[List[Dict[str, Any]]] = None
    expected_outcomes: Optional[List[str]] = None
    is_active: Optional[bool] = None


class TestTemplate(TestTemplateBase):
    id: int
    company_id: int
    times_used: int = 0
    last_used_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Test Analytics Schemas
class TestAnalytics(BaseModel):
    total_sessions: int
    successful_sessions: int
    failed_sessions: int
    success_rate: float
    average_session_duration: Optional[float] = None
    most_tested_agents: List[Dict[str, Any]] = []
    test_trends: List[Dict[str, Any]] = []
    channel_performance: Dict[str, Dict[str, Any]] = {}
    common_failures: List[Dict[str, Any]] = []


# Validation Schemas
class TestValidationRequest(BaseModel):
    test_config: Dict[str, Any]
    test_type: TestType
    channel: TestChannel


class TestValidationResponse(BaseModel):
    is_valid: bool
    errors: List[str] = []
    warnings: List[str] = []
    suggestions: List[str] = []
