"""
Testing CRUD operations.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload

from . import models, schemas

logger = logging.getLogger(__name__)


async def create_test_session(
    db: AsyncSession, session: schemas.TestSessionCreate
) -> models.TestSession:
    """Create a new test session."""
    db_session = models.TestSession(
        name=session.name,
        description=session.description,
        test_type=session.test_type,
        channel=session.channel,
        company_id=session.company_id,
        agent_id=session.agent_id,
        test_config=session.test_config,
        expected_outcomes=session.expected_outcomes or [],
        timeout_seconds=session.timeout_seconds,
        status=schemas.TestStatus.PENDING,
    )
    db.add(db_session)
    await db.commit()
    await db.refresh(db_session)
    return db_session


async def get_test_session(
    db: AsyncSession, session_id: int, company_id: int, include_messages: bool = False
) -> Optional[models.TestSession]:
    """Get a single test session by ID."""
    query = select(models.TestSession).filter(
        and_(
            models.TestSession.id == session_id,
            models.TestSession.company_id == company_id,
        )
    )

    if include_messages:
        query = query.options(selectinload(models.TestSession.messages))

    result = await db.execute(query)
    return result.scalars().first()


async def get_test_sessions(
    db: AsyncSession,
    company_id: int,
    skip: int = 0,
    limit: int = 100,
    test_type: Optional[schemas.TestType] = None,
    status: Optional[schemas.TestStatus] = None,
    agent_id: Optional[int] = None,
    channel: Optional[schemas.TestChannel] = None,
) -> List[models.TestSession]:
    """Get test sessions with filtering."""
    query = select(models.TestSession).filter(
        models.TestSession.company_id == company_id
    )

    if test_type:
        query = query.filter(models.TestSession.test_type == test_type)

    if status:
        query = query.filter(models.TestSession.status == status)

    if agent_id:
        query = query.filter(models.TestSession.agent_id == agent_id)

    if channel:
        query = query.filter(models.TestSession.channel == channel)

    query = query.order_by(desc(models.TestSession.created_at))
    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    return result.scalars().all()


async def update_test_session(
    db: AsyncSession,
    session_id: int,
    company_id: int,
    session_update: schemas.TestSessionUpdate,
) -> Optional[models.TestSession]:
    """Update a test session."""
    db_session = await get_test_session(db, session_id, company_id)
    if not db_session:
        return None

    update_data = session_update.dict(exclude_unset=True)

    for field, value in update_data.items():
        setattr(db_session, field, value)

    await db.commit()
    await db.refresh(db_session)
    return db_session


async def start_test_session(
    db: AsyncSession, session_id: int, company_id: int
) -> Optional[models.TestSession]:
    """Start a test session."""
    db_session = await get_test_session(db, session_id, company_id)
    if not db_session:
        return None

    db_session.status = schemas.TestStatus.RUNNING
    db_session.started_at = datetime.utcnow()

    await db.commit()
    await db.refresh(db_session)
    return db_session


async def complete_test_session(
    db: AsyncSession,
    session_id: int,
    company_id: int,
    success: bool,
    results: Optional[Dict[str, Any]] = None,
    error_message: Optional[str] = None,
) -> Optional[models.TestSession]:
    """Complete a test session."""
    db_session = await get_test_session(db, session_id, company_id)
    if not db_session:
        return None

    now = datetime.utcnow()
    db_session.status = (
        schemas.TestStatus.COMPLETED if success else schemas.TestStatus.FAILED
    )
    db_session.completed_at = now
    db_session.success = success
    db_session.results = results or {}
    db_session.error_message = error_message

    if db_session.started_at:
        duration = (now - db_session.started_at).total_seconds()
        db_session.duration_seconds = int(duration)

    await db.commit()
    await db.refresh(db_session)
    return db_session


async def delete_test_session(
    db: AsyncSession, session_id: int, company_id: int
) -> bool:
    """Delete a test session."""
    db_session = await get_test_session(db, session_id, company_id)
    if not db_session:
        return False

    await db.delete(db_session)
    await db.commit()
    return True


# Test Message CRUD
async def create_test_message(
    db: AsyncSession, message: schemas.TestMessageCreate
) -> models.TestInteraction:
    """Create a new test message."""
    # Get the next sequence number
    result = await db.execute(
        select(
            func.coalesce(func.max(models.TestInteraction.sequence_number), 0) + 1
        ).filter(models.TestInteraction.test_session_id == message.test_session_id)
    )
    sequence_number = result.scalar()

    db_message = models.TestInteraction(
        test_session_id=message.test_session_id,
        message_type=message.message_type,
        content=message.content,
        timestamp=message.timestamp,
        sequence_number=sequence_number,
        metadata=message.metadata or {},
    )
    db.add(db_message)
    await db.commit()
    await db.refresh(db_message)
    return db_message


async def get_test_messages(
    db: AsyncSession, session_id: int, company_id: int
) -> List[models.TestInteraction]:
    """Get messages for a test session."""
    # First verify the session belongs to the company
    session_exists = await db.execute(
        select(models.TestSession.id).filter(
            and_(
                models.TestSession.id == session_id,
                models.TestSession.company_id == company_id,
            )
        )
    )
    if not session_exists.scalar():
        return []

    query = (
        select(models.TestInteraction)
        .filter(models.TestInteraction.test_session_id == session_id)
        .order_by(models.TestInteraction.sequence_number)
    )

    result = await db.execute(query)
    return result.scalars().all()


async def update_message_response_time(
    db: AsyncSession, message_id: int, response_time_ms: int
) -> Optional[models.TestInteraction]:
    """Update message response time."""
    result = await db.execute(
        select(models.TestInteraction).filter(models.TestInteraction.id == message_id)
    )
    db_message = result.scalars().first()

    if not db_message:
        return None

    db_message.response_time_ms = response_time_ms
    await db.commit()
    await db.refresh(db_message)
    return db_message


# Test Analytics
async def get_test_analytics(
    db: AsyncSession,
    company_id: int,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
) -> schemas.TestAnalytics:
    """Get test analytics for a company."""
    query = select(models.TestSession).filter(
        models.TestSession.company_id == company_id
    )

    if start_date:
        query = query.filter(models.TestSession.created_at >= start_date)
    if end_date:
        query = query.filter(models.TestSession.created_at <= end_date)

    result = await db.execute(query)
    sessions = result.scalars().all()

    total_sessions = len(sessions)
    successful_sessions = len([s for s in sessions if s.success is True])
    failed_sessions = len([s for s in sessions if s.success is False])
    success_rate = (
        (successful_sessions / total_sessions * 100) if total_sessions > 0 else 0
    )

    # Calculate average duration
    completed_sessions = [s for s in sessions if s.duration_seconds is not None]
    average_duration = (
        sum(s.duration_seconds for s in completed_sessions) / len(completed_sessions)
        if completed_sessions
        else None
    )

    # Get most tested agents
    agent_counts = {}
    for session in sessions:
        agent_id = session.agent_id
        if agent_id not in agent_counts:
            agent_counts[agent_id] = 0
        agent_counts[agent_id] += 1

    most_tested_agents = [
        {"agent_id": agent_id, "test_count": count}
        for agent_id, count in sorted(
            agent_counts.items(), key=lambda x: x[1], reverse=True
        )[:5]
    ]

    # Channel performance
    channel_performance = {}
    for session in sessions:
        channel = session.channel
        if channel not in channel_performance:
            channel_performance[channel] = {"total": 0, "successful": 0, "failed": 0}

        channel_performance[channel]["total"] += 1
        if session.success is True:
            channel_performance[channel]["successful"] += 1
        elif session.success is False:
            channel_performance[channel]["failed"] += 1

    # Calculate success rates for channels
    for channel_data in channel_performance.values():
        total = channel_data["total"]
        channel_data["success_rate"] = (
            channel_data["successful"] / total * 100 if total > 0 else 0
        )

    return schemas.TestAnalytics(
        total_sessions=total_sessions,
        successful_sessions=successful_sessions,
        failed_sessions=failed_sessions,
        success_rate=success_rate,
        average_session_duration=average_duration,
        most_tested_agents=most_tested_agents,
        test_trends=[],  # TODO: Implement trend analysis
        channel_performance=channel_performance,
        common_failures=[],  # TODO: Implement failure analysis
    )


async def get_test_session_count(
    db: AsyncSession,
    company_id: int,
    test_type: Optional[schemas.TestType] = None,
    status: Optional[schemas.TestStatus] = None,
    agent_id: Optional[int] = None,
    channel: Optional[schemas.TestChannel] = None,
) -> int:
    """Get total count of test sessions."""
    query = select(func.count(models.TestSession.id)).filter(
        models.TestSession.company_id == company_id
    )

    if test_type:
        query = query.filter(models.TestSession.test_type == test_type)
    if status:
        query = query.filter(models.TestSession.status == status)
    if agent_id:
        query = query.filter(models.TestSession.agent_id == agent_id)
    if channel:
        query = query.filter(models.TestSession.channel == channel)

    result = await db.execute(query)
    return result.scalar() or 0
