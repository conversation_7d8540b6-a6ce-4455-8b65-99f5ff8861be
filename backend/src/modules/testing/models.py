"""
Multi-Channel Testing Models for comprehensive agent testing.

Supports phone, web, SMS, and chat testing with detailed analytics.
"""

from enum import Enum as PyEnum

from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    Float,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class TestType(PyEnum):
    """Test types for different channels."""

    PHONE_CALL = "phone_call"
    WEB_CHAT = "web_chat"
    SMS = "sms"
    VOICE_MESSAGE = "voice_message"
    INTEGRATION = "integration"
    LOAD_TEST = "load_test"
    SCENARIO = "scenario"


class TestStatus(PyEnum):
    """Test execution status."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class TestResult(PyEnum):
    """Test result outcomes."""

    PASS = "pass"
    FAIL = "fail"
    WARNING = "warning"
    ERROR = "error"
    PARTIAL = "partial"


class TestSession(Base):
    """
    Main test session model for multi-channel agent testing.
    """

    __tablename__ = "test_sessions"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=False)

    # Session Information
    session_id = Column(String(100), nullable=False, unique=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)

    # Test Configuration
    test_type = Column(String(50), nullable=False, default=TestType.WEB_CHAT.value)
    test_config = Column(JSON, nullable=False, default=dict)

    # Test Scenario
    scenario_id = Column(Integer, ForeignKey("test_scenarios.id"), nullable=True)
    test_script = Column(JSON, nullable=True, default=list)  # Sequence of test steps

    # Execution
    status = Column(
        String(50), nullable=False, default=TestStatus.PENDING.value, index=True
    )
    result = Column(String(50), nullable=True, index=True)  # TestResult

    # Timing
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    duration_seconds = Column(Integer, nullable=True)
    timeout_seconds = Column(Integer, default=300)

    # Results & Metrics
    total_interactions = Column(Integer, default=0)
    successful_interactions = Column(Integer, default=0)
    failed_interactions = Column(Integer, default=0)

    # Performance Metrics
    average_response_time = Column(Float, nullable=True)  # seconds
    max_response_time = Column(Float, nullable=True)  # seconds
    min_response_time = Column(Float, nullable=True)  # seconds

    # Quality Metrics
    accuracy_score = Column(Float, nullable=True)  # 0-1
    completeness_score = Column(Float, nullable=True)  # 0-1
    user_satisfaction_score = Column(Float, nullable=True)  # 0-1

    # Error Information
    error_count = Column(Integer, default=0)
    error_details = Column(JSON, nullable=True, default=list)

    # Test Environment
    environment = Column(String(50), default="test")  # test, staging, production
    user_agent = Column(String(500), nullable=True)
    ip_address = Column(String(45), nullable=True)

    # Metadata
    tags = Column(JSON, nullable=True, default=list)
    notes = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationships
    company = relationship("Company")
    agent = relationship("Agent")
    scenario = relationship("TestScenario")
    user = relationship("User", foreign_keys=[user_id])
    interactions = relationship(
        "TestInteraction", back_populates="test_session", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<TestSession(id={self.id}, session_id='{self.session_id}', status='{self.status}')>"


class TestScenario(Base):
    """
    Predefined test scenarios for consistent testing.
    """

    __tablename__ = "test_scenarios"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Scenario Information
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String(100), nullable=True)

    # Test Configuration
    test_type = Column(String(50), nullable=False)
    test_steps = Column(JSON, nullable=False, default=list)
    expected_outcomes = Column(JSON, nullable=True, default=list)

    # Validation Rules
    validation_rules = Column(JSON, nullable=True, default=list)
    success_criteria = Column(JSON, nullable=True, default=dict)

    # Configuration
    timeout_seconds = Column(Integer, default=300)
    retry_attempts = Column(Integer, default=1)

    # Usage & Performance
    times_used = Column(Integer, default=0)
    success_rate = Column(Float, nullable=True)  # percentage
    average_duration = Column(Float, nullable=True)  # seconds

    # Status
    is_active = Column(Boolean, default=True)
    is_template = Column(Boolean, default=False)

    # Metadata
    tags = Column(JSON, nullable=True, default=list)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationships
    company = relationship("Company")
    user = relationship("User", foreign_keys=[user_id])
    test_sessions = relationship("TestSession", back_populates="scenario")

    def __repr__(self):
        return (
            f"<TestScenario(id={self.id}, name='{self.name}', type='{self.test_type}')>"
        )


class TestInteraction(Base):
    """
    Individual interactions within a test session.
    """

    __tablename__ = "test_interactions"

    id = Column(Integer, primary_key=True, index=True)
    test_session_id = Column(Integer, ForeignKey("test_sessions.id"), nullable=False)

    # Interaction Information
    sequence_number = Column(Integer, nullable=False)
    interaction_type = Column(String(50), nullable=False)  # message, action, validation

    # Input/Output
    input_data = Column(JSON, nullable=True, default=dict)
    expected_output = Column(JSON, nullable=True, default=dict)
    actual_output = Column(JSON, nullable=True, default=dict)

    # Timing
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    response_time = Column(Float, nullable=True)  # seconds

    # Results
    status = Column(String(50), nullable=False, default=TestStatus.PENDING.value)
    result = Column(String(50), nullable=True)  # TestResult

    # Validation
    validation_results = Column(JSON, nullable=True, default=list)
    assertion_results = Column(JSON, nullable=True, default=list)

    # Error Information
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True, default=dict)

    # Metadata
    extra_metadata = Column(JSON, nullable=True, default=dict)

    # Relationships
    test_session = relationship("TestSession", back_populates="interactions")

    def __repr__(self):
        return f"<TestInteraction(id={self.id}, sequence={self.sequence_number}, type='{self.interaction_type}')>"


class TestReport(Base):
    """
    Comprehensive test reports and analytics.
    """

    __tablename__ = "test_reports"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Report Information
    report_name = Column(String(255), nullable=False)
    report_type = Column(
        String(50), nullable=False
    )  # session, agent, scenario, summary

    # Time Period
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=False)

    # Filters
    agent_ids = Column(JSON, nullable=True, default=list)
    test_types = Column(JSON, nullable=True, default=list)
    scenario_ids = Column(JSON, nullable=True, default=list)

    # Summary Statistics
    total_tests = Column(Integer, default=0)
    passed_tests = Column(Integer, default=0)
    failed_tests = Column(Integer, default=0)
    success_rate = Column(Float, nullable=True)  # percentage

    # Performance Metrics
    average_response_time = Column(Float, nullable=True)
    average_test_duration = Column(Float, nullable=True)
    total_test_time = Column(Float, nullable=True)

    # Quality Metrics
    average_accuracy = Column(Float, nullable=True)
    average_completeness = Column(Float, nullable=True)
    average_satisfaction = Column(Float, nullable=True)

    # Detailed Results
    test_results = Column(JSON, nullable=True, default=dict)
    performance_trends = Column(JSON, nullable=True, default=list)
    error_analysis = Column(JSON, nullable=True, default=dict)

    # Report Status
    status = Column(String(50), default="generating")  # generating, completed, failed
    generated_at = Column(DateTime(timezone=True), nullable=True)

    # File Information
    report_file_path = Column(String(500), nullable=True)
    report_file_size = Column(Integer, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationships
    company = relationship("Company")
    user = relationship("User", foreign_keys=[user_id])

    def __repr__(self):
        return f"<TestReport(id={self.id}, name='{self.report_name}', type='{self.report_type}')>"


class TestEnvironment(Base):
    """
    Test environment configurations and settings.
    """

    __tablename__ = "test_environments"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Environment Information
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    environment_type = Column(String(50), nullable=False)  # test, staging, production

    # Configuration
    base_url = Column(String(500), nullable=True)
    api_endpoints = Column(JSON, nullable=True, default=dict)
    authentication = Column(JSON, nullable=True, default=dict)

    # Phone Testing Configuration
    phone_numbers = Column(JSON, nullable=True, default=list)
    twilio_config = Column(JSON, nullable=True, default=dict)
    elevenlabs_config = Column(JSON, nullable=True, default=dict)

    # Web Testing Configuration
    web_urls = Column(JSON, nullable=True, default=list)
    browser_config = Column(JSON, nullable=True, default=dict)

    # SMS Testing Configuration
    sms_numbers = Column(JSON, nullable=True, default=list)
    sms_config = Column(JSON, nullable=True, default=dict)

    # Environment Variables
    environment_variables = Column(JSON, nullable=True, default=dict)

    # Status & Control
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)

    # Usage Statistics
    total_tests_run = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationships
    company = relationship("Company")
    user = relationship("User", foreign_keys=[user_id])

    def __repr__(self):
        return f"<TestEnvironment(id={self.id}, name='{self.name}', type='{self.environment_type}')>"


class TestAnalytics(Base):
    """
    Analytics for test performance and trends.
    """

    __tablename__ = "test_analytics"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Time Period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False, default="daily")

    # Test Volume
    total_tests = Column(Integer, default=0)
    phone_tests = Column(Integer, default=0)
    web_tests = Column(Integer, default=0)
    sms_tests = Column(Integer, default=0)

    # Test Results
    passed_tests = Column(Integer, default=0)
    failed_tests = Column(Integer, default=0)
    error_tests = Column(Integer, default=0)
    success_rate = Column(Float, default=0.0)

    # Performance Metrics
    average_response_time = Column(Float, default=0.0)
    average_test_duration = Column(Float, default=0.0)
    total_test_time = Column(Float, default=0.0)

    # Quality Metrics
    average_accuracy = Column(Float, default=0.0)
    average_completeness = Column(Float, default=0.0)

    # Agent Performance
    agents_tested = Column(Integer, default=0)
    top_performing_agents = Column(JSON, nullable=True, default=list)

    # Common Issues
    top_error_types = Column(JSON, nullable=True, default=list)
    common_failures = Column(JSON, nullable=True, default=list)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    company = relationship("Company")

    def __repr__(self):
        return f"<TestAnalytics(company_id={self.company_id}, date='{self.date}')>"
