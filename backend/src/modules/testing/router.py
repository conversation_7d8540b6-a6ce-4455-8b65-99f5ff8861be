"""
Testing API Router for multi-channel agent testing.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.service import get_current_active_user
from modules.companies import crud as company_crud
from modules.users.models import User as DBUser

from . import crud, schemas

logger = logging.getLogger(__name__)

router = APIRouter(tags=["testing"])


@router.get("/sessions", response_model=schemas.TestSessionListResponse)
async def get_test_sessions(
    company_id: int = Query(..., description="Company ID"),
    agent_id: Optional[int] = Query(None),
    test_type: Optional[schemas.TestType] = Query(None),
    status: Optional[schemas.TestStatus] = Query(None),
    channel: Optional[schemas.TestChannel] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get test sessions for a company."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Get test sessions and total count
    sessions = await crud.get_test_sessions(
        db, company_id, skip, limit, test_type, status, agent_id, channel
    )
    total = await crud.get_test_session_count(
        db, company_id, test_type, status, agent_id, channel
    )

    return schemas.TestSessionListResponse(
        sessions=sessions,
        total=total,
        page=skip // limit + 1,
        per_page=limit,
        has_next=skip + limit < total,
        has_prev=skip > 0,
    )


@router.post("/sessions", response_model=schemas.TestSession)
async def create_test_session(
    session_data: schemas.TestSessionCreate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a new test session."""
    # Verify company belongs to user
    company = await company_crud.get_company(
        db, session_data.company_id, current_user.id
    )
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Create test session
    session = await crud.create_test_session(db, session_data)
    return session


@router.get("/scenarios")
async def get_test_scenarios(
    company_id: int,
    test_type: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get test scenarios for a company."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement test scenario retrieval
    return []


@router.post("/scenarios")
async def create_test_scenario(
    company_id: int,
    scenario_data: dict,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a new test scenario."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement test scenario creation
    return {
        "scenario_id": "scenario_123",
        "message": "Test scenario created successfully",
    }
