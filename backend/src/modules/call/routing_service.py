"""
Call routing service for handling inbound calls and agent assignment.
"""

import logging
from datetime import datetime, time
from typing import Any, Dict, Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from modules.agents.models import Agent

from . import crud

logger = logging.getLogger(__name__)


class CallRoutingService:
    """Service for routing inbound calls to appropriate agents."""

    def __init__(self):
        self.logger = logger

    async def route_inbound_call(
        self,
        phone_number: str,
        db: AsyncSession,
        caller_info: Optional[Dict[str, Any]] = None,
    ) -> Optional[AgentConfig]:
        """
        Route an inbound call to the appropriate agent based on phone number configuration.

        Args:
            phone_number: The phone number that received the call
            db: Database session
            caller_info: Optional caller information for advanced routing

        Returns:
            AgentConfig if routing successful, None otherwise
        """
        try:
            # Get phone number configuration
            phone_config = await self._get_phone_number_config(phone_number, db)
            if not phone_config:
                self.logger.warning(
                    f"No configuration found for phone number: {phone_number}"
                )
                return None

            if not phone_config.is_active:
                self.logger.warning(f"Phone number {phone_number} is not active")
                return None

            # Determine which agent to use
            agent_id = await self._determine_agent(phone_config, caller_info)
            if not agent_id:
                self.logger.warning(
                    f"No agent determined for phone number: {phone_number}"
                )
                return None

            # Get agent configuration
            agent_config = await self._get_agent_config(agent_id, db)
            if not agent_config:
                self.logger.warning(f"Agent {agent_id} not found or inactive")
                return None

            self.logger.info(f"Routed call from {phone_number} to agent {agent_id}")
            return agent_config

        except Exception as e:
            self.logger.error(f"Error routing inbound call: {e}", exc_info=True)
            return None

    async def _get_phone_number_config(
        self, phone_number: str, db: AsyncSession
    ) -> Optional[PhoneNumber]:
        """Get phone number configuration from database."""
        result = await db.execute(
            select(PhoneNumber).where(PhoneNumber.phone_number == phone_number)
        )
        return result.scalars().first()

    async def _determine_agent(
        self, phone_config: PhoneNumber, caller_info: Optional[Dict[str, Any]] = None
    ) -> Optional[int]:
        """
        Determine which agent should handle the call based on routing rules.

        Args:
            phone_config: Phone number configuration
            caller_info: Optional caller information

        Returns:
            Agent ID or None
        """
        # Check if we're in business hours
        if self._is_business_hours(phone_config.business_hours):
            # Use advanced routing if configured
            if phone_config.routing_config:
                agent_id = await self._apply_routing_rules(
                    phone_config.routing_config, caller_info
                )
                if agent_id:
                    return agent_id

            # Fall back to default agent
            return phone_config.default_agent_id
        else:
            # After hours - use after hours agent or default
            return phone_config.after_hours_agent_id or phone_config.default_agent_id

    def _is_business_hours(self, business_hours: Optional[Dict[str, Any]]) -> bool:
        """Check if current time is within business hours."""
        if not business_hours:
            return True  # Always business hours if not configured

        now = datetime.now()
        current_day = now.strftime("%A").lower()
        current_time = now.time()

        day_config = business_hours.get(current_day)
        if not day_config:
            return False  # No configuration for this day

        if not day_config.get("open", True):
            return False  # Closed on this day

        start_time_str = day_config.get("start", "09:00")
        end_time_str = day_config.get("end", "17:00")

        try:
            start_time = time.fromisoformat(start_time_str)
            end_time = time.fromisoformat(end_time_str)

            return start_time <= current_time <= end_time
        except ValueError:
            self.logger.warning(f"Invalid time format in business hours: {day_config}")
            return True  # Default to business hours if invalid format

    async def _apply_routing_rules(
        self, routing_config: Dict[str, Any], caller_info: Optional[Dict[str, Any]]
    ) -> Optional[int]:
        """
        Apply advanced routing rules to determine agent.

        Args:
            routing_config: Routing configuration
            caller_info: Caller information

        Returns:
            Agent ID or None
        """
        # Example routing rules:
        # - Route by caller location
        # - Route by caller history
        # - Route by call volume
        # - Route by agent availability

        rules = routing_config.get("rules", [])

        for rule in rules:
            rule_type = rule.get("type")

            if rule_type == "caller_location" and caller_info:
                location = caller_info.get("location")
                if location and location in rule.get("locations", []):
                    return rule.get("agent_id")

            elif rule_type == "caller_history" and caller_info:
                # Route returning customers to their previous agent
                previous_agent = caller_info.get("previous_agent_id")
                if previous_agent and rule.get("use_previous_agent", False):
                    return previous_agent

            elif rule_type == "time_based":
                # Route based on time of day
                now = datetime.now().time()
                start_time = time.fromisoformat(rule.get("start_time", "00:00"))
                end_time = time.fromisoformat(rule.get("end_time", "23:59"))

                if start_time <= now <= end_time:
                    return rule.get("agent_id")

        return None

    async def _get_agent_config(
        self, agent_id: int, db: AsyncSession
    ) -> Optional[AgentConfig]:
        """Get agent configuration from database."""
        result = await db.execute(
            select(Agent).where(Agent.id == agent_id, Agent.is_active == True)
        )
        agent = result.scalars().first()

        if not agent:
            return None

        # Convert agent workflow to AgentConfig
        if agent.workflow:
            return AgentConfig(**agent.workflow)

        return None


# Global routing service instance
routing_service = CallRoutingService()
