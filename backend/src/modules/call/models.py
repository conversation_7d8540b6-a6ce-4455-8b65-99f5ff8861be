from typing import List

from sqlalchemy import (
    ARRAY,
    JSON,
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import Mapped, relationship
from sqlalchemy.sql import func

from core.db.database import Base
from modules.phone_numbers.models import PhoneNumber
from modules.users.models import User  # noqa: F401, needed for relationship


class CallHistory(Base):
    __tablename__ = "call_history"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    phone_number_id = Column(Integer, ForeignKey("phone_numbers.id"), nullable=True)
    call_sid = Column(String, unique=True, index=True)
    call_status = Column(
        String
    )  # e.g., "initiated", "ringing", "in-progress", "completed", "failed"
    call_duration = Column(Integer)
    call_metadata = Column(JSON)  # type: ignore[arg-type]
    system_prompt = Column(Text)
    first_message = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))

    customer = relationship("Customer", back_populates="call_history")
    phone_number = relationship(
        "PhoneNumber", back_populates="call_history", foreign_keys=[phone_number_id]
    )
    conversation_logs = relationship("ConversationLog", back_populates="call_history")
    user = relationship("User", back_populates="call_history")


class ConversationLog(Base):
    __tablename__ = "conversation_logs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    call_history_id = Column(Integer, ForeignKey("call_history.id"), nullable=False)
    event_type = Column(String)  # e.g., "user_message", "agent_message", "tool_call"
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    data = Column(JSON)  # type: ignore[arg-type]
    source = Column(String)  # e.g., "twilio_stream", "conversation_manager"

    call_history = relationship("CallHistory", back_populates="conversation_logs")
    user = relationship("User", back_populates="conversation_logs")
