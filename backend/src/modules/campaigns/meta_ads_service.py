"""
Meta Ads Integration Service

Handles OAuth flow, campaign creation, and webhook data processing for Meta Ads platform.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from decimal import Decimal

import httpx
from fastapi import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import get_settings
from . import crud
from .models import Campaign, AnalyticsData
from .schemas import AnalyticsDataCreate

logger = logging.getLogger(__name__)


class MetaAdsService:
    """Service for Meta Ads platform integration."""
    
    def __init__(self):
        self.settings = get_settings()
        self.base_url = "https://graph.facebook.com/v18.0"
        self.client = httpx.AsyncClient()
    
    async def get_oauth_url(self, user_id: int, redirect_uri: str) -> str:
        """Generate OAuth URL for Meta Ads authorization."""
        
        # In production, you would use actual Meta App ID and secret
        # For now, return a mock URL structure
        app_id = "your_meta_app_id"  # Replace with actual Meta App ID
        
        oauth_url = (
            f"https://www.facebook.com/v18.0/dialog/oauth?"
            f"client_id={app_id}&"
            f"redirect_uri={redirect_uri}&"
            f"scope=ads_management,ads_read&"
            f"response_type=code&"
            f"state={user_id}"  # Use user_id as state for security
        )
        
        return oauth_url
    
    async def exchange_code_for_token(self, code: str, redirect_uri: str) -> Dict[str, Any]:
        """Exchange authorization code for access token."""
        
        # Mock implementation - in production, make actual API call to Meta
        # This would typically involve:
        # 1. POST to https://graph.facebook.com/v18.0/oauth/access_token
        # 2. Include app_id, app_secret, code, redirect_uri
        # 3. Return access_token and other details
        
        logger.info(f"Mock: Exchanging code {code[:10]}... for access token")
        
        # Return mock token data
        return {
            "access_token": f"mock_access_token_{code[:10]}",
            "token_type": "bearer",
            "expires_in": 3600,
            "scope": "ads_management,ads_read"
        }
    
    async def create_campaign_on_meta(
        self, 
        campaign: Campaign, 
        access_token: str
    ) -> Dict[str, Any]:
        """Create a campaign on Meta Ads platform."""
        
        try:
            # Prepare campaign data for Meta Ads API
            campaign_data = {
                "name": campaign.name,
                "objective": self._get_meta_objective(campaign.primary_goal),
                "status": "PAUSED",  # Start paused for review
                "special_ad_categories": [],
                "buying_type": "AUCTION"
            }
            
            # Mock API call - in production, make actual request
            logger.info(f"Mock: Creating Meta campaign for {campaign.name}")
            
            # Simulate API response
            mock_response = {
                "id": f"mock_campaign_{campaign.id}_{datetime.now().timestamp()}",
                "name": campaign.name,
                "status": "PAUSED"
            }
            
            # Create ad set
            adset_data = {
                "name": f"{campaign.name} - Ad Set",
                "campaign_id": mock_response["id"],
                "daily_budget": int(campaign.daily_budget * 100) if campaign.daily_budget else int(campaign.budget * 100 / 30),  # Convert to cents
                "billing_event": "IMPRESSIONS",
                "optimization_goal": "LINK_CLICKS",
                "targeting": self._prepare_targeting(campaign),
                "status": "PAUSED"
            }
            
            mock_adset_response = {
                "id": f"mock_adset_{campaign.id}_{datetime.now().timestamp()}",
                "name": adset_data["name"],
                "status": "PAUSED"
            }
            
            # Create ad creative
            creative_data = {
                "name": f"{campaign.name} - Creative",
                "object_story_spec": {
                    "page_id": "mock_page_id",
                    "link_data": {
                        "link": campaign.business_profile.booking_url or campaign.business_profile.website,
                        "message": campaign.selected_ad_copy or "Book your appointment today!",
                        "name": campaign.selected_headline or campaign.name,
                        "description": campaign.selected_description or "Professional wellness services"
                    }
                }
            }
            
            mock_creative_response = {
                "id": f"mock_creative_{campaign.id}_{datetime.now().timestamp()}",
                "name": creative_data["name"]
            }
            
            # Create ad
            ad_data = {
                "name": f"{campaign.name} - Ad",
                "adset_id": mock_adset_response["id"],
                "creative": {"creative_id": mock_creative_response["id"]},
                "status": "PAUSED"
            }
            
            mock_ad_response = {
                "id": f"mock_ad_{campaign.id}_{datetime.now().timestamp()}",
                "name": ad_data["name"],
                "status": "PAUSED"
            }
            
            return {
                "campaign": mock_response,
                "adset": mock_adset_response,
                "creative": mock_creative_response,
                "ad": mock_ad_response,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error creating Meta campaign: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to create Meta campaign: {str(e)}")
    
    def _get_meta_objective(self, goal: Optional[str]) -> str:
        """Map campaign goal to Meta Ads objective."""
        goal_mapping = {
            "appointments": "CONVERSIONS",
            "leads": "LEAD_GENERATION", 
            "awareness": "REACH",
            "traffic": "LINK_CLICKS"
        }
        return goal_mapping.get(goal, "CONVERSIONS")
    
    def _prepare_targeting(self, campaign: Campaign) -> Dict[str, Any]:
        """Prepare targeting data for Meta Ads."""
        
        # Get targeting from AI suggestions or use defaults
        if campaign.selected_targeting:
            targeting = campaign.selected_targeting
        elif campaign.targeting_suggestions:
            targeting = campaign.targeting_suggestions
        else:
            # Default Zurich targeting
            targeting = {
                "geo_locations": {
                    "countries": ["CH"],
                    "regions": [{"key": "3973", "name": "Zurich"}]
                },
                "age_min": 25,
                "age_max": 65,
                "locales": [6, 24]  # German, English
            }
        
        # Convert to Meta Ads format
        meta_targeting = {
            "geo_locations": targeting.get("location", {
                "countries": ["CH"],
                "regions": [{"key": "3973", "name": "Zurich"}]
            }),
            "age_min": targeting.get("age_range", {}).get("min", 25),
            "age_max": targeting.get("age_range", {}).get("max", 65)
        }
        
        # Add interests if available
        if "interests" in targeting:
            meta_targeting["interests"] = [
                {"name": interest} for interest in targeting["interests"]
            ]
        
        return meta_targeting
    
    async def process_webhook_data(
        self, 
        webhook_data: Dict[str, Any], 
        db: AsyncSession
    ) -> List[AnalyticsData]:
        """Process webhook data from Meta Ads and update analytics."""
        
        processed_data = []
        
        try:
            # Parse webhook data (structure depends on Meta's webhook format)
            entries = webhook_data.get("entry", [])
            
            for entry in entries:
                changes = entry.get("changes", [])
                
                for change in changes:
                    if change.get("field") == "ads_insights":
                        # Process insights data
                        insights_data = change.get("value", {})
                        analytics_entry = await self._process_insights_data(insights_data, db)
                        if analytics_entry:
                            processed_data.append(analytics_entry)
            
            return processed_data
            
        except Exception as e:
            logger.error(f"Error processing webhook data: {str(e)}")
            return []
    
    async def _process_insights_data(
        self, 
        insights_data: Dict[str, Any], 
        db: AsyncSession
    ) -> Optional[AnalyticsData]:
        """Process individual insights data entry."""
        
        try:
            # Extract campaign ID and find corresponding campaign
            meta_campaign_id = insights_data.get("campaign_id")
            if not meta_campaign_id:
                return None
            
            # Find campaign by Meta platform ID
            # This would require a database query to match meta_campaign_id to our campaign
            # For now, we'll create mock data
            
            analytics_data = AnalyticsDataCreate(
                campaign_id=1,  # This should be looked up from meta_campaign_id
                date=datetime.now(),
                total_spend=Decimal(str(insights_data.get("spend", "0"))),
                daily_spend=Decimal(str(insights_data.get("spend", "0"))),
                impressions=int(insights_data.get("impressions", 0)),
                clicks=int(insights_data.get("clicks", 0)),
                click_through_rate=float(insights_data.get("ctr", 0)),
                leads=int(insights_data.get("actions", [{}])[0].get("value", 0)),
                cost_per_click=Decimal(str(insights_data.get("cpc", "0"))),
                platform_data=insights_data
            )
            
            # Calculate derived metrics
            if analytics_data.clicks > 0:
                analytics_data.cost_per_click = analytics_data.total_spend / analytics_data.clicks
            
            # Create analytics entry
            return await crud.create_analytics_data(db, analytics_data)
            
        except Exception as e:
            logger.error(f"Error processing insights data: {str(e)}")
            return None
    
    async def fetch_campaign_insights(
        self, 
        meta_campaign_id: str, 
        access_token: str,
        date_range: Optional[Dict[str, datetime]] = None
    ) -> Dict[str, Any]:
        """Fetch campaign insights from Meta Ads API."""
        
        try:
            # Prepare date range
            if not date_range:
                end_date = datetime.now()
                start_date = end_date - timedelta(days=7)
            else:
                start_date = date_range["start"]
                end_date = date_range["end"]
            
            # Mock API call - in production, make actual request to Meta Ads API
            logger.info(f"Mock: Fetching insights for campaign {meta_campaign_id}")
            
            # Simulate insights data
            mock_insights = {
                "data": [{
                    "campaign_id": meta_campaign_id,
                    "date_start": start_date.strftime("%Y-%m-%d"),
                    "date_stop": end_date.strftime("%Y-%m-%d"),
                    "impressions": "1250",
                    "clicks": "45",
                    "spend": "125.50",
                    "ctr": "3.6",
                    "cpc": "2.79",
                    "actions": [
                        {"action_type": "lead", "value": "8"},
                        {"action_type": "link_click", "value": "45"}
                    ]
                }]
            }
            
            return mock_insights
            
        except Exception as e:
            logger.error(f"Error fetching campaign insights: {str(e)}")
            return {"data": []}
    
    async def update_campaign_status(
        self, 
        meta_campaign_id: str, 
        status: str, 
        access_token: str
    ) -> bool:
        """Update campaign status on Meta Ads platform."""
        
        try:
            # Mock API call - in production, make PATCH request to Meta Ads API
            logger.info(f"Mock: Updating campaign {meta_campaign_id} status to {status}")
            
            # Simulate successful update
            return True
            
        except Exception as e:
            logger.error(f"Error updating campaign status: {str(e)}")
            return False
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


# Global service instance
meta_ads_service = MetaAdsService()
