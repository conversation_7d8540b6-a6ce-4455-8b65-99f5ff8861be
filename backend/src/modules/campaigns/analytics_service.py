"""
Analytics Processing Service

Handles ROI calculations, performance metrics, and data aggregation for campaigns.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from decimal import Decimal

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_

from . import crud
from .models import Campaign, AnalyticsData, Appointment
from .schemas import R<PERSON>IReport, CampaignSummary, DashboardData

logger = logging.getLogger(__name__)


class AnalyticsService:
    """Service for campaign analytics and ROI calculations."""
    
    def __init__(self):
        pass
    
    async def calculate_campaign_roi(
        self, 
        campaign_id: int, 
        db: AsyncSession,
        date_range: Optional[Dict[str, datetime]] = None
    ) -> ROIReport:
        """Calculate comprehensive ROI report for a campaign."""
        
        try:
            # Get campaign details
            campaign = await crud.get_campaign(db, campaign_id, campaign.user_id)
            if not campaign:
                raise ValueError(f"Campaign {campaign_id} not found")
            
            # Set date range if not provided
            if not date_range:
                end_date = datetime.now()
                start_date = campaign.created_at
            else:
                start_date = date_range["start"]
                end_date = date_range["end"]
            
            # Get analytics data for the period
            analytics_data = await crud.get_campaign_analytics(db, campaign_id, start_date, end_date)
            
            # Calculate totals
            total_spend = sum(data.total_spend for data in analytics_data)
            total_impressions = sum(data.impressions for data in analytics_data)
            total_clicks = sum(data.clicks for data in analytics_data)
            total_leads = sum(data.leads for data in analytics_data)
            
            # Get appointments for this campaign
            appointments = await crud.get_appointments_by_campaign(db, campaign_id, campaign.user_id)
            
            # Filter appointments by date range
            period_appointments = [
                apt for apt in appointments 
                if start_date <= apt.created_at <= end_date
            ]
            
            total_appointments = len(period_appointments)
            
            # Calculate revenue
            total_revenue = sum(
                apt.actual_value or apt.estimated_value or Decimal('0') 
                for apt in period_appointments
            )
            
            # Calculate metrics
            cost_per_appointment = total_spend / total_appointments if total_appointments > 0 else total_spend
            roi_percentage = ((total_revenue - total_spend) / total_spend * 100) if total_spend > 0 else 0
            roas = (total_revenue / total_spend) if total_spend > 0 else 0
            
            return ROIReport(
                campaign_id=campaign_id,
                campaign_name=campaign.name,
                total_spend=total_spend,
                total_appointments=total_appointments,
                total_revenue=total_revenue,
                roi_percentage=float(roi_percentage),
                roas=float(roas),
                cost_per_appointment=cost_per_appointment,
                date_range={"start": start_date, "end": end_date}
            )
            
        except Exception as e:
            logger.error(f"Error calculating ROI for campaign {campaign_id}: {str(e)}")
            raise
    
    async def get_user_campaign_summary(
        self, 
        user_id: int, 
        db: AsyncSession
    ) -> CampaignSummary:
        """Get campaign summary for a user."""
        
        try:
            # Get basic campaign counts
            campaigns = await crud.get_campaigns_by_user(db, user_id)
            active_campaigns = await crud.get_active_campaigns_by_user(db, user_id)
            
            total_campaigns = len(campaigns)
            active_campaign_count = len(active_campaigns)
            
            # Calculate totals across all campaigns
            total_spend = Decimal('0')
            total_appointments = 0
            total_revenue = Decimal('0')
            
            for campaign in campaigns:
                # Get analytics summary for each campaign
                analytics_summary = await crud.get_campaign_summary_analytics(db, campaign.id)
                total_spend += analytics_summary.get('total_spend', Decimal('0'))
                
                # Get appointments for this campaign
                appointments = await crud.get_appointments_by_campaign(db, campaign.id, user_id)
                total_appointments += len(appointments)
                
                # Calculate revenue from appointments
                campaign_revenue = sum(
                    apt.actual_value or apt.estimated_value or Decimal('0') 
                    for apt in appointments
                )
                total_revenue += campaign_revenue
            
            # Calculate average cost per appointment
            average_cost_per_appointment = (
                total_spend / total_appointments if total_appointments > 0 else Decimal('0')
            )
            
            # Calculate total ROI
            total_roi = (
                ((total_revenue - total_spend) / total_spend * 100) if total_spend > 0 else 0
            )
            
            return CampaignSummary(
                total_campaigns=total_campaigns,
                active_campaigns=active_campaign_count,
                total_spend=total_spend,
                total_appointments=total_appointments,
                average_cost_per_appointment=average_cost_per_appointment,
                total_roi=float(total_roi)
            )
            
        except Exception as e:
            logger.error(f"Error getting campaign summary for user {user_id}: {str(e)}")
            raise
    
    async def get_dashboard_data(
        self, 
        user_id: int, 
        db: AsyncSession
    ) -> DashboardData:
        """Get comprehensive dashboard data for a user."""
        
        try:
            # Get campaign summary
            summary = await self.get_user_campaign_summary(user_id, db)
            
            # Get recent campaigns (last 5)
            recent_campaigns = await crud.get_campaigns_by_user(db, user_id, skip=0, limit=5)
            
            # Get top performing campaigns
            campaigns = await crud.get_campaigns_by_user(db, user_id)
            top_performing = []
            
            for campaign in campaigns[:5]:  # Limit to top 5
                try:
                    roi_report = await self.calculate_campaign_roi(campaign.id, db)
                    top_performing.append(roi_report)
                except Exception as e:
                    logger.warning(f"Could not calculate ROI for campaign {campaign.id}: {str(e)}")
                    continue
            
            # Sort by ROI percentage
            top_performing.sort(key=lambda x: x.roi_percentage, reverse=True)
            
            # Get recent appointments (last 10)
            recent_appointments = await crud.get_appointments_by_user(db, user_id, skip=0, limit=10)
            
            # Get analytics trends (last 30 days)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            analytics_trends = []
            for campaign in campaigns:
                campaign_analytics = await crud.get_campaign_analytics(
                    db, campaign.id, start_date, end_date
                )
                analytics_trends.extend(campaign_analytics)
            
            # Sort by date
            analytics_trends.sort(key=lambda x: x.date, reverse=True)
            analytics_trends = analytics_trends[:30]  # Limit to last 30 entries
            
            return DashboardData(
                summary=summary,
                recent_campaigns=recent_campaigns,
                top_performing_campaigns=top_performing,
                recent_appointments=recent_appointments,
                analytics_trends=analytics_trends
            )
            
        except Exception as e:
            logger.error(f"Error getting dashboard data for user {user_id}: {str(e)}")
            raise
    
    async def calculate_performance_metrics(
        self, 
        campaign_id: int, 
        db: AsyncSession,
        period_days: int = 7
    ) -> Dict[str, Any]:
        """Calculate detailed performance metrics for a campaign."""
        
        try:
            # Get campaign
            campaign = await crud.get_campaign(db, campaign_id, campaign.user_id)
            if not campaign:
                raise ValueError(f"Campaign {campaign_id} not found")
            
            # Set date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=period_days)
            
            # Get analytics data
            analytics_data = await crud.get_campaign_analytics(db, campaign_id, start_date, end_date)
            
            if not analytics_data:
                return self._get_empty_metrics()
            
            # Calculate aggregated metrics
            total_spend = sum(data.total_spend for data in analytics_data)
            total_impressions = sum(data.impressions for data in analytics_data)
            total_clicks = sum(data.clicks for data in analytics_data)
            total_leads = sum(data.leads for data in analytics_data)
            
            # Calculate rates
            ctr = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0
            cpc = (total_spend / total_clicks) if total_clicks > 0 else Decimal('0')
            cpl = (total_spend / total_leads) if total_leads > 0 else Decimal('0')
            
            # Get appointments
            appointments = await crud.get_appointments_by_campaign(db, campaign_id, campaign.user_id)
            period_appointments = [
                apt for apt in appointments 
                if start_date <= apt.created_at <= end_date
            ]
            
            total_appointments = len(period_appointments)
            conversion_rate = (total_appointments / total_clicks * 100) if total_clicks > 0 else 0
            
            # Calculate trends (compare with previous period)
            prev_start = start_date - timedelta(days=period_days)
            prev_analytics = await crud.get_campaign_analytics(db, campaign_id, prev_start, start_date)
            
            trends = self._calculate_trends(analytics_data, prev_analytics)
            
            return {
                "period_days": period_days,
                "total_spend": float(total_spend),
                "total_impressions": total_impressions,
                "total_clicks": total_clicks,
                "total_leads": total_leads,
                "total_appointments": total_appointments,
                "ctr": round(ctr, 2),
                "cpc": float(cpc),
                "cpl": float(cpl),
                "conversion_rate": round(conversion_rate, 2),
                "trends": trends,
                "daily_breakdown": [
                    {
                        "date": data.date.strftime("%Y-%m-%d"),
                        "spend": float(data.daily_spend),
                        "impressions": data.impressions,
                        "clicks": data.clicks,
                        "leads": data.leads
                    }
                    for data in analytics_data
                ]
            }
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics for campaign {campaign_id}: {str(e)}")
            return self._get_empty_metrics()
    
    def _calculate_trends(
        self, 
        current_data: List[AnalyticsData], 
        previous_data: List[AnalyticsData]
    ) -> Dict[str, float]:
        """Calculate trend percentages comparing current vs previous period."""
        
        # Current period totals
        current_spend = sum(data.total_spend for data in current_data)
        current_clicks = sum(data.clicks for data in current_data)
        current_impressions = sum(data.impressions for data in current_data)
        
        # Previous period totals
        prev_spend = sum(data.total_spend for data in previous_data) if previous_data else Decimal('0')
        prev_clicks = sum(data.clicks for data in previous_data) if previous_data else 0
        prev_impressions = sum(data.impressions for data in previous_data) if previous_data else 0
        
        # Calculate percentage changes
        spend_trend = self._calculate_percentage_change(float(prev_spend), float(current_spend))
        clicks_trend = self._calculate_percentage_change(prev_clicks, current_clicks)
        impressions_trend = self._calculate_percentage_change(prev_impressions, current_impressions)
        
        return {
            "spend_trend": spend_trend,
            "clicks_trend": clicks_trend,
            "impressions_trend": impressions_trend
        }
    
    def _calculate_percentage_change(self, old_value: float, new_value: float) -> float:
        """Calculate percentage change between two values."""
        if old_value == 0:
            return 100.0 if new_value > 0 else 0.0
        return round(((new_value - old_value) / old_value) * 100, 1)
    
    def _get_empty_metrics(self) -> Dict[str, Any]:
        """Return empty metrics structure."""
        return {
            "period_days": 7,
            "total_spend": 0,
            "total_impressions": 0,
            "total_clicks": 0,
            "total_leads": 0,
            "total_appointments": 0,
            "ctr": 0,
            "cpc": 0,
            "cpl": 0,
            "conversion_rate": 0,
            "trends": {
                "spend_trend": 0,
                "clicks_trend": 0,
                "impressions_trend": 0
            },
            "daily_breakdown": []
        }


# Global service instance
analytics_service = AnalyticsService()
