"""
Campaign Management CRUD Operations

Database operations for campaigns, business profiles, analytics, and appointments.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from sqlalchemy.orm import selectinload

from .models import BusinessProfile, Campaign, AnalyticsData, Appointment
from .schemas import (
    BusinessProfileCreate, BusinessProfileUpdate,
    CampaignCreate, CampaignUpdate,
    AnalyticsDataCreate,
    AppointmentCreate, AppointmentUpdate
)


# Business Profile CRUD
async def create_business_profile(
    db: AsyncSession, 
    profile_data: BusinessProfileCreate, 
    user_id: int
) -> BusinessProfile:
    """Create a new business profile."""
    db_profile = BusinessProfile(
        user_id=user_id,
        **profile_data.model_dump()
    )
    db.add(db_profile)
    await db.commit()
    await db.refresh(db_profile)
    return db_profile


async def get_business_profile(db: AsyncSession, profile_id: int, user_id: int) -> Optional[BusinessProfile]:
    """Get business profile by ID for specific user."""
    result = await db.execute(
        select(BusinessProfile).where(
            and_(BusinessProfile.id == profile_id, BusinessProfile.user_id == user_id)
        )
    )
    return result.scalar_one_or_none()


async def get_business_profiles_by_user(db: AsyncSession, user_id: int) -> List[BusinessProfile]:
    """Get all business profiles for a user."""
    result = await db.execute(
        select(BusinessProfile).where(BusinessProfile.user_id == user_id)
    )
    return result.scalars().all()


async def update_business_profile(
    db: AsyncSession, 
    profile_id: int, 
    user_id: int, 
    profile_update: BusinessProfileUpdate
) -> Optional[BusinessProfile]:
    """Update business profile."""
    db_profile = await get_business_profile(db, profile_id, user_id)
    if not db_profile:
        return None
    
    update_data = profile_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_profile, field, value)
    
    await db.commit()
    await db.refresh(db_profile)
    return db_profile


async def delete_business_profile(db: AsyncSession, profile_id: int, user_id: int) -> bool:
    """Delete business profile."""
    db_profile = await get_business_profile(db, profile_id, user_id)
    if not db_profile:
        return False
    
    await db.delete(db_profile)
    await db.commit()
    return True


# Campaign CRUD
async def create_campaign(
    db: AsyncSession, 
    campaign_data: CampaignCreate, 
    user_id: int
) -> Campaign:
    """Create a new campaign."""
    db_campaign = Campaign(
        user_id=user_id,
        **campaign_data.model_dump()
    )
    db.add(db_campaign)
    await db.commit()
    await db.refresh(db_campaign)
    return db_campaign


async def get_campaign(db: AsyncSession, campaign_id: int, user_id: int) -> Optional[Campaign]:
    """Get campaign by ID for specific user."""
    result = await db.execute(
        select(Campaign)
        .options(selectinload(Campaign.business_profile))
        .where(and_(Campaign.id == campaign_id, Campaign.user_id == user_id))
    )
    return result.scalar_one_or_none()


async def get_campaigns_by_user(
    db: AsyncSession, 
    user_id: int, 
    skip: int = 0, 
    limit: int = 100
) -> List[Campaign]:
    """Get all campaigns for a user with pagination."""
    result = await db.execute(
        select(Campaign)
        .options(selectinload(Campaign.business_profile))
        .where(Campaign.user_id == user_id)
        .order_by(desc(Campaign.created_at))
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()


async def get_active_campaigns_by_user(db: AsyncSession, user_id: int) -> List[Campaign]:
    """Get active campaigns for a user."""
    result = await db.execute(
        select(Campaign)
        .options(selectinload(Campaign.business_profile))
        .where(and_(Campaign.user_id == user_id, Campaign.status == "active"))
        .order_by(desc(Campaign.created_at))
    )
    return result.scalars().all()


async def update_campaign(
    db: AsyncSession, 
    campaign_id: int, 
    user_id: int, 
    campaign_update: CampaignUpdate
) -> Optional[Campaign]:
    """Update campaign."""
    db_campaign = await get_campaign(db, campaign_id, user_id)
    if not db_campaign:
        return None
    
    update_data = campaign_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_campaign, field, value)
    
    await db.commit()
    await db.refresh(db_campaign)
    return db_campaign


async def delete_campaign(db: AsyncSession, campaign_id: int, user_id: int) -> bool:
    """Delete campaign."""
    db_campaign = await get_campaign(db, campaign_id, user_id)
    if not db_campaign:
        return False
    
    await db.delete(db_campaign)
    await db.commit()
    return True


# Analytics Data CRUD
async def create_analytics_data(
    db: AsyncSession, 
    analytics_data: AnalyticsDataCreate
) -> AnalyticsData:
    """Create analytics data entry."""
    db_analytics = AnalyticsData(**analytics_data.model_dump())
    db.add(db_analytics)
    await db.commit()
    await db.refresh(db_analytics)
    return db_analytics


async def get_campaign_analytics(
    db: AsyncSession, 
    campaign_id: int, 
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None
) -> List[AnalyticsData]:
    """Get analytics data for a campaign within date range."""
    query = select(AnalyticsData).where(AnalyticsData.campaign_id == campaign_id)
    
    if start_date:
        query = query.where(AnalyticsData.date >= start_date)
    if end_date:
        query = query.where(AnalyticsData.date <= end_date)
    
    query = query.order_by(AnalyticsData.date)
    result = await db.execute(query)
    return result.scalars().all()


async def get_campaign_summary_analytics(
    db: AsyncSession, 
    campaign_id: int
) -> Dict[str, Any]:
    """Get summary analytics for a campaign."""
    result = await db.execute(
        select(
            func.sum(AnalyticsData.total_spend).label('total_spend'),
            func.sum(AnalyticsData.impressions).label('total_impressions'),
            func.sum(AnalyticsData.clicks).label('total_clicks'),
            func.sum(AnalyticsData.leads).label('total_leads'),
            func.sum(AnalyticsData.appointments_booked).label('total_appointments'),
            func.avg(AnalyticsData.cost_per_appointment).label('avg_cost_per_appointment'),
            func.avg(AnalyticsData.roi_percentage).label('avg_roi'),
            func.avg(AnalyticsData.roas).label('avg_roas')
        ).where(AnalyticsData.campaign_id == campaign_id)
    )
    
    row = result.first()
    return {
        'total_spend': row.total_spend or Decimal('0'),
        'total_impressions': row.total_impressions or 0,
        'total_clicks': row.total_clicks or 0,
        'total_leads': row.total_leads or 0,
        'total_appointments': row.total_appointments or 0,
        'avg_cost_per_appointment': row.avg_cost_per_appointment or Decimal('0'),
        'avg_roi': row.avg_roi or 0.0,
        'avg_roas': row.avg_roas or 0.0
    }


# Appointment CRUD
async def create_appointment(
    db: AsyncSession, 
    appointment_data: AppointmentCreate, 
    user_id: int
) -> Appointment:
    """Create a new appointment."""
    db_appointment = Appointment(
        user_id=user_id,
        **appointment_data.model_dump()
    )
    db.add(db_appointment)
    await db.commit()
    await db.refresh(db_appointment)
    return db_appointment


async def get_appointment(db: AsyncSession, appointment_id: int, user_id: int) -> Optional[Appointment]:
    """Get appointment by ID for specific user."""
    result = await db.execute(
        select(Appointment).where(
            and_(Appointment.id == appointment_id, Appointment.user_id == user_id)
        )
    )
    return result.scalar_one_or_none()


async def get_appointments_by_user(
    db: AsyncSession, 
    user_id: int, 
    skip: int = 0, 
    limit: int = 100
) -> List[Appointment]:
    """Get all appointments for a user with pagination."""
    result = await db.execute(
        select(Appointment)
        .where(Appointment.user_id == user_id)
        .order_by(desc(Appointment.created_at))
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()


async def get_appointments_by_campaign(
    db: AsyncSession, 
    campaign_id: int, 
    user_id: int
) -> List[Appointment]:
    """Get appointments for a specific campaign."""
    result = await db.execute(
        select(Appointment).where(
            and_(
                Appointment.campaign_id == campaign_id, 
                Appointment.user_id == user_id
            )
        ).order_by(desc(Appointment.created_at))
    )
    return result.scalars().all()


async def update_appointment(
    db: AsyncSession, 
    appointment_id: int, 
    user_id: int, 
    appointment_update: AppointmentUpdate
) -> Optional[Appointment]:
    """Update appointment."""
    db_appointment = await get_appointment(db, appointment_id, user_id)
    if not db_appointment:
        return None
    
    update_data = appointment_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_appointment, field, value)
    
    await db.commit()
    await db.refresh(db_appointment)
    return db_appointment


async def get_user_dashboard_data(db: AsyncSession, user_id: int) -> Dict[str, Any]:
    """Get comprehensive dashboard data for a user."""
    # Get campaign counts and totals
    campaign_result = await db.execute(
        select(
            func.count(Campaign.id).label('total_campaigns'),
            func.count(Campaign.id).filter(Campaign.status == 'active').label('active_campaigns')
        ).where(Campaign.user_id == user_id)
    )
    campaign_stats = campaign_result.first()
    
    # Get appointment counts
    appointment_result = await db.execute(
        select(func.count(Appointment.id)).where(Appointment.user_id == user_id)
    )
    total_appointments = appointment_result.scalar() or 0
    
    # Get spend totals from analytics (join with campaigns)
    spend_result = await db.execute(
        select(func.sum(AnalyticsData.total_spend))
        .join(Campaign, AnalyticsData.campaign_id == Campaign.id)
        .where(Campaign.user_id == user_id)
    )
    total_spend = spend_result.scalar() or Decimal('0')
    
    return {
        'total_campaigns': campaign_stats.total_campaigns or 0,
        'active_campaigns': campaign_stats.active_campaigns or 0,
        'total_appointments': total_appointments,
        'total_spend': total_spend,
        'avg_cost_per_appointment': (
            total_spend / total_appointments if total_appointments > 0 else Decimal('0')
        )
    }
