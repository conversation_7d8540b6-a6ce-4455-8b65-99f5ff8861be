"""
AI Ad Generation Service

Uses OpenAI GPT models to generate ad copy, headlines, descriptions, and targeting suggestions
for wellness and health service businesses.
"""

import json
import logging
from typing import Dict, List, Any, Optional
from decimal import Decimal

import openai
from core.config import get_settings
from .schemas import AIAdGenerationRequest, AIAdGenerationResponse

logger = logging.getLogger(__name__)


class AIAdGenerationService:
    """Service for generating AI-powered ad content."""
    
    def __init__(self):
        settings = get_settings()
        self.client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        self.model = "gpt-4"  # Use GPT-4 for better quality
    
    async def generate_ad_content(self, request: AIAdGenerationRequest) -> AIAdGenerationResponse:
        """Generate comprehensive ad content based on business information."""
        
        try:
            # Create the prompt for ad generation
            prompt = self._create_ad_generation_prompt(request)
            
            # Call OpenAI API
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": self._get_system_prompt()
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.7,
                max_tokens=2000
            )
            
            # Parse the response
            content = response.choices[0].message.content
            parsed_content = self._parse_ai_response(content)
            
            # Create targeting suggestions
            targeting_suggestions = self._generate_targeting_suggestions(request)
            
            # Estimate performance
            estimated_performance = self._estimate_performance(request)
            
            return AIAdGenerationResponse(
                ad_copy_variations=parsed_content.get("ad_copy_variations", []),
                headlines=parsed_content.get("headlines", []),
                descriptions=parsed_content.get("descriptions", []),
                targeting_suggestions=targeting_suggestions,
                creative_ideas=parsed_content.get("creative_ideas", []),
                estimated_performance=estimated_performance
            )
            
        except Exception as e:
            logger.error(f"Error generating ad content: {str(e)}")
            # Return fallback content
            return self._get_fallback_response(request)
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for AI ad generation."""
        return """You are an expert digital marketing specialist focused on wellness and health services in Switzerland, particularly Zurich. You create high-converting ad campaigns that drive appointment bookings.

Your expertise includes:
- Understanding Swiss healthcare and wellness market
- Creating compelling ad copy that converts
- Targeting strategies for local health services
- Compliance with healthcare advertising regulations
- Psychology of wellness service customers

Always provide practical, actionable content that can be used immediately in Meta Ads campaigns.

Respond in JSON format with the following structure:
{
  "ad_copy_variations": ["variation1", "variation2", "variation3"],
  "headlines": ["headline1", "headline2", "headline3"],
  "descriptions": ["description1", "description2", "description3"],
  "creative_ideas": ["idea1", "idea2", "idea3"]
}"""
    
    def _create_ad_generation_prompt(self, request: AIAdGenerationRequest) -> str:
        """Create the user prompt for ad generation."""
        services_text = ", ".join(request.services) if request.services else "general wellness services"
        usps_text = ", ".join(request.unique_selling_points) if request.unique_selling_points else "professional, experienced"
        
        prompt = f"""Create a Meta Ads campaign for a {request.business_type.replace('_', ' ')} business in {request.location}.

Business Details:
- Business Name: {request.business_name}
- Services: {services_text}
- Target Audience: {request.target_audience or 'Adults seeking wellness/health services'}
- Unique Selling Points: {usps_text}
- Primary Goal: {request.goal}
- Budget: {request.budget or 'Not specified'}

Requirements:
1. Create 3 ad copy variations (each 125 characters max) that are compelling and action-oriented
2. Create 3 headlines (30 characters max) that grab attention
3. Create 3 descriptions (90 characters max) that highlight benefits
4. Suggest 3 creative ideas for visuals/videos that would work well

Focus on:
- Local Zurich market appeal
- Professional healthcare tone
- Clear call-to-action for appointments
- Trust and credibility building
- Compliance with healthcare advertising standards

Make the content specific to {request.business_type.replace('_', ' ')} services and emphasize the benefits customers will receive."""
        
        return prompt
    
    def _parse_ai_response(self, content: str) -> Dict[str, Any]:
        """Parse the AI response JSON."""
        try:
            # Try to extract JSON from the response
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = content[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # Fallback parsing if JSON is not properly formatted
                return self._fallback_parse(content)
                
        except json.JSONDecodeError:
            logger.warning("Failed to parse AI response as JSON, using fallback")
            return self._fallback_parse(content)
    
    def _fallback_parse(self, content: str) -> Dict[str, Any]:
        """Fallback parsing when JSON parsing fails."""
        lines = content.split('\n')
        result = {
            "ad_copy_variations": [],
            "headlines": [],
            "descriptions": [],
            "creative_ideas": []
        }
        
        current_section = None
        for line in lines:
            line = line.strip()
            if 'ad_copy' in line.lower():
                current_section = 'ad_copy_variations'
            elif 'headline' in line.lower():
                current_section = 'headlines'
            elif 'description' in line.lower():
                current_section = 'descriptions'
            elif 'creative' in line.lower() or 'idea' in line.lower():
                current_section = 'creative_ideas'
            elif line and current_section and (line.startswith('-') or line.startswith('•')):
                cleaned_line = line.lstrip('-•').strip()
                if cleaned_line:
                    result[current_section].append(cleaned_line)
        
        return result
    
    def _generate_targeting_suggestions(self, request: AIAdGenerationRequest) -> Dict[str, Any]:
        """Generate targeting suggestions based on business type and location."""
        
        # Base targeting for Zurich area
        base_targeting = {
            "location": {
                "countries": ["CH"],
                "regions": ["Zurich"],
                "radius": 25,
                "radius_unit": "kilometer"
            },
            "age_range": {"min": 25, "max": 65},
            "languages": ["German", "English"]
        }
        
        # Business-specific targeting
        business_targeting = self._get_business_specific_targeting(request.business_type)
        
        # Combine targeting
        targeting = {**base_targeting, **business_targeting}
        
        # Add interests based on services
        if request.services:
            targeting["interests"] = self._get_interests_from_services(request.services)
        
        return targeting
    
    def _get_business_specific_targeting(self, business_type: str) -> Dict[str, Any]:
        """Get targeting specific to business type."""
        targeting_map = {
            "physical_therapy": {
                "interests": ["Physical fitness", "Health and wellness", "Sports", "Injury recovery"],
                "behaviors": ["Fitness enthusiasts", "Health-conscious consumers"]
            },
            "dental": {
                "interests": ["Dental health", "Cosmetic dentistry", "Health and wellness"],
                "behaviors": ["Health-conscious consumers", "Premium service seekers"]
            },
            "massage_therapy": {
                "interests": ["Massage therapy", "Stress relief", "Wellness", "Relaxation"],
                "behaviors": ["Wellness enthusiasts", "Stress management seekers"]
            },
            "chiropractic": {
                "interests": ["Chiropractic care", "Back pain relief", "Health and wellness"],
                "behaviors": ["Health-conscious consumers", "Pain management seekers"]
            },
            "nutrition": {
                "interests": ["Nutrition", "Healthy eating", "Weight management", "Fitness"],
                "behaviors": ["Health-conscious consumers", "Fitness enthusiasts"]
            },
            "mental_health": {
                "interests": ["Mental health", "Therapy", "Wellness", "Self-care"],
                "behaviors": ["Mental health awareness", "Wellness seekers"]
            }
        }
        
        return targeting_map.get(business_type, {
            "interests": ["Health and wellness", "Self-care"],
            "behaviors": ["Health-conscious consumers"]
        })
    
    def _get_interests_from_services(self, services: List[str]) -> List[str]:
        """Extract relevant interests from services list."""
        interest_keywords = {
            "massage": ["Massage therapy", "Relaxation", "Stress relief"],
            "therapy": ["Physical therapy", "Rehabilitation", "Health and wellness"],
            "dental": ["Dental health", "Oral care", "Cosmetic dentistry"],
            "nutrition": ["Nutrition", "Healthy eating", "Diet and nutrition"],
            "fitness": ["Physical fitness", "Exercise", "Health and wellness"],
            "wellness": ["Health and wellness", "Self-care", "Holistic health"]
        }
        
        interests = set()
        for service in services:
            service_lower = service.lower()
            for keyword, related_interests in interest_keywords.items():
                if keyword in service_lower:
                    interests.update(related_interests)
        
        return list(interests) if interests else ["Health and wellness", "Self-care"]
    
    def _estimate_performance(self, request: AIAdGenerationRequest) -> Dict[str, Any]:
        """Estimate campaign performance based on business type and budget."""
        
        # Base estimates for Zurich market
        base_cpc = Decimal('2.50')  # Average CPC in CHF
        base_ctr = 0.015  # 1.5% CTR
        base_conversion_rate = 0.08  # 8% conversion rate
        
        # Adjust based on business type
        business_multipliers = {
            "dental": {"cpc": 1.3, "ctr": 0.9, "conversion": 1.1},
            "physical_therapy": {"cpc": 1.1, "ctr": 1.0, "conversion": 1.2},
            "massage_therapy": {"cpc": 0.9, "ctr": 1.1, "conversion": 1.0},
            "nutrition": {"cpc": 1.0, "ctr": 1.0, "conversion": 0.9},
            "mental_health": {"cpc": 1.2, "ctr": 0.95, "conversion": 0.85}
        }
        
        multiplier = business_multipliers.get(request.business_type, {"cpc": 1.0, "ctr": 1.0, "conversion": 1.0})
        
        estimated_cpc = base_cpc * Decimal(str(multiplier["cpc"]))
        estimated_ctr = base_ctr * multiplier["ctr"]
        estimated_conversion_rate = base_conversion_rate * multiplier["conversion"]
        
        # Calculate estimates if budget is provided
        if request.budget:
            estimated_clicks = int(request.budget / estimated_cpc)
            estimated_impressions = int(estimated_clicks / estimated_ctr)
            estimated_conversions = int(estimated_clicks * estimated_conversion_rate)
            estimated_cost_per_conversion = request.budget / estimated_conversions if estimated_conversions > 0 else request.budget
        else:
            estimated_clicks = 100
            estimated_impressions = int(estimated_clicks / estimated_ctr)
            estimated_conversions = int(estimated_clicks * estimated_conversion_rate)
            estimated_cost_per_conversion = estimated_cpc / Decimal(str(estimated_conversion_rate))
        
        return {
            "estimated_cpc": float(estimated_cpc),
            "estimated_ctr": estimated_ctr,
            "estimated_conversion_rate": estimated_conversion_rate,
            "estimated_clicks": estimated_clicks,
            "estimated_impressions": estimated_impressions,
            "estimated_conversions": estimated_conversions,
            "estimated_cost_per_conversion": float(estimated_cost_per_conversion)
        }
    
    def _get_fallback_response(self, request: AIAdGenerationRequest) -> AIAdGenerationResponse:
        """Provide fallback response when AI generation fails."""
        business_name = request.business_name
        service_type = request.business_type.replace('_', ' ').title()
        
        return AIAdGenerationResponse(
            ad_copy_variations=[
                f"Book your {service_type.lower()} appointment in Zurich today! Professional care you can trust.",
                f"Experience premium {service_type.lower()} services in Zurich. Schedule your consultation now.",
                f"Transform your health with expert {service_type.lower()} care. Book online in seconds!"
            ],
            headlines=[
                f"{service_type} Zurich",
                f"Book {business_name}",
                f"Expert {service_type}"
            ],
            descriptions=[
                f"Professional {service_type.lower()} services in Zurich. Book your appointment today.",
                f"Trusted {service_type.lower()} care. Convenient online booking available.",
                f"Expert {service_type.lower()} treatments. Schedule your visit now."
            ],
            targeting_suggestions=self._generate_targeting_suggestions(request),
            creative_ideas=[
                "Professional clinic interior photos",
                "Before/after treatment results",
                "Happy customer testimonials video"
            ],
            estimated_performance=self._estimate_performance(request)
        )


# Global service instance
ai_service = AIAdGenerationService()
