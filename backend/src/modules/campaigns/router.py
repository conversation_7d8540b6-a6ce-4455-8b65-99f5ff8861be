"""
Campaign Management API Router

FastAPI routes for campaign management, business profiles, and analytics.
"""

from typing import List, Optional, Any
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.service import get_current_user
from modules.users.models import User
from . import crud, schemas
from .ai_service import ai_service
from .meta_ads_service import meta_ads_service
from .analytics_service import analytics_service


router = APIRouter(prefix="/campaigns", tags=["campaigns"])


# Business Profile Routes
@router.post("/business-profiles", response_model=schemas.BusinessProfilePublic)
async def create_business_profile(
    profile_data: schemas.BusinessProfileCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new business profile."""
    return await crud.create_business_profile(db, profile_data, current_user.id)


@router.get("/business-profiles", response_model=List[schemas.BusinessProfilePublic])
async def get_business_profiles(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get all business profiles for the current user."""
    return await crud.get_business_profiles_by_user(db, current_user.id)


@router.get("/business-profiles/{profile_id}", response_model=schemas.BusinessProfilePublic)
async def get_business_profile(
    profile_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get a specific business profile."""
    profile = await crud.get_business_profile(db, profile_id, current_user.id)
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Business profile not found"
        )
    return profile


@router.put("/business-profiles/{profile_id}", response_model=schemas.BusinessProfilePublic)
async def update_business_profile(
    profile_id: int,
    profile_update: schemas.BusinessProfileUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update a business profile."""
    profile = await crud.update_business_profile(db, profile_id, current_user.id, profile_update)
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Business profile not found"
        )
    return profile


@router.delete("/business-profiles/{profile_id}")
async def delete_business_profile(
    profile_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete a business profile."""
    success = await crud.delete_business_profile(db, profile_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Business profile not found"
        )
    return {"message": "Business profile deleted successfully"}


# Campaign Routes
@router.post("/", response_model=schemas.CampaignPublic)
async def create_campaign(
    campaign_data: schemas.CampaignCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new campaign."""
    # Verify business profile belongs to user
    profile = await crud.get_business_profile(db, campaign_data.business_profile_id, current_user.id)
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Business profile not found"
        )
    
    return await crud.create_campaign(db, campaign_data, current_user.id)


@router.get("/", response_model=List[schemas.CampaignPublic])
async def get_campaigns(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get all campaigns for the current user."""
    return await crud.get_campaigns_by_user(db, current_user.id, skip, limit)


@router.get("/active", response_model=List[schemas.CampaignPublic])
async def get_active_campaigns(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get active campaigns for the current user."""
    return await crud.get_active_campaigns_by_user(db, current_user.id)


@router.get("/{campaign_id}", response_model=schemas.CampaignPublic)
async def get_campaign(
    campaign_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get a specific campaign."""
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    return campaign


@router.put("/{campaign_id}", response_model=schemas.CampaignPublic)
async def update_campaign(
    campaign_id: int,
    campaign_update: schemas.CampaignUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update a campaign."""
    campaign = await crud.update_campaign(db, campaign_id, current_user.id, campaign_update)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    return campaign


@router.delete("/{campaign_id}")
async def delete_campaign(
    campaign_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete a campaign."""
    success = await crud.delete_campaign(db, campaign_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    return {"message": "Campaign deleted successfully"}


# Analytics Routes
@router.get("/{campaign_id}/analytics", response_model=List[schemas.AnalyticsDataPublic])
async def get_campaign_analytics(
    campaign_id: int,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get analytics data for a campaign."""
    # Verify campaign belongs to user
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    return await crud.get_campaign_analytics(db, campaign_id, start_date, end_date)


@router.get("/{campaign_id}/analytics/summary")
async def get_campaign_analytics_summary(
    campaign_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get summary analytics for a campaign."""
    # Verify campaign belongs to user
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    return await crud.get_campaign_summary_analytics(db, campaign_id)


@router.post("/{campaign_id}/analytics", response_model=schemas.AnalyticsDataPublic)
async def create_analytics_data(
    campaign_id: int,
    analytics_data: schemas.AnalyticsDataCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create analytics data entry (typically called by webhooks)."""
    # Verify campaign belongs to user
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    # Ensure campaign_id matches
    analytics_data.campaign_id = campaign_id
    return await crud.create_analytics_data(db, analytics_data)


# Appointment Routes
@router.post("/appointments", response_model=schemas.AppointmentPublic)
async def create_appointment(
    appointment_data: schemas.AppointmentCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new appointment."""
    # If campaign_id is provided, verify it belongs to user
    if appointment_data.campaign_id:
        campaign = await crud.get_campaign(db, appointment_data.campaign_id, current_user.id)
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
    
    return await crud.create_appointment(db, appointment_data, current_user.id)


@router.get("/appointments", response_model=List[schemas.AppointmentPublic])
async def get_appointments(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get all appointments for the current user."""
    return await crud.get_appointments_by_user(db, current_user.id, skip, limit)


@router.get("/{campaign_id}/appointments", response_model=List[schemas.AppointmentPublic])
async def get_campaign_appointments(
    campaign_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get appointments for a specific campaign."""
    # Verify campaign belongs to user
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    return await crud.get_appointments_by_campaign(db, campaign_id, current_user.id)


@router.put("/appointments/{appointment_id}", response_model=schemas.AppointmentPublic)
async def update_appointment(
    appointment_id: int,
    appointment_update: schemas.AppointmentUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update an appointment."""
    appointment = await crud.update_appointment(db, appointment_id, current_user.id, appointment_update)
    if not appointment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Appointment not found"
        )
    return appointment


# AI Ad Generation Route
@router.post("/ai/generate-ad", response_model=schemas.AIAdGenerationResponse)
async def generate_ad_content(
    request: schemas.AIAdGenerationRequest,
    current_user: User = Depends(get_current_user)
):
    """Generate AI-powered ad content for a campaign."""
    return await ai_service.generate_ad_content(request)


# Meta Ads Integration Routes
@router.get("/integrations/meta/auth-url")
async def get_meta_auth_url(
    redirect_uri: str,
    current_user: User = Depends(get_current_user)
):
    """Get Meta Ads OAuth authorization URL."""
    auth_url = await meta_ads_service.get_oauth_url(current_user.id, redirect_uri)
    return {"auth_url": auth_url}


@router.post("/integrations/meta/callback")
async def handle_meta_callback(
    code: str,
    redirect_uri: str,
    current_user: User = Depends(get_current_user)
):
    """Handle Meta Ads OAuth callback and exchange code for token."""
    token_data = await meta_ads_service.exchange_code_for_token(code, redirect_uri)
    return token_data


@router.post("/{campaign_id}/launch-on-meta")
async def launch_campaign_on_meta(
    campaign_id: int,
    access_token: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Launch a campaign on Meta Ads platform."""
    # Verify campaign belongs to user
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )

    # Create campaign on Meta Ads
    meta_result = await meta_ads_service.create_campaign_on_meta(campaign, access_token)

    # Update campaign with Meta platform ID
    if meta_result.get("success"):
        campaign_update = schemas.CampaignUpdate(
            ad_platform_id=meta_result["campaign"]["id"],
            status="active"
        )
        await crud.update_campaign(db, campaign_id, current_user.id, campaign_update)

    return meta_result


@router.post("/integrations/meta/webhook")
async def handle_meta_webhook(
    webhook_data: dict[str, Any],
    db: AsyncSession = Depends(get_db)
):
    """Handle Meta Ads webhook for real-time data updates."""
    processed_data = await meta_ads_service.process_webhook_data(webhook_data, db)
    return {"processed_entries": len(processed_data)}


# Advanced Analytics Routes
@router.get("/{campaign_id}/roi-report")
async def get_campaign_roi_report(
    campaign_id: int,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get detailed ROI report for a campaign."""
    # Verify campaign belongs to user
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )

    date_range = None
    if start_date and end_date:
        date_range = {"start": start_date, "end": end_date}

    return await analytics_service.calculate_campaign_roi(campaign_id, db, date_range)


@router.get("/{campaign_id}/performance-metrics")
async def get_campaign_performance_metrics(
    campaign_id: int,
    period_days: int = 7,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get detailed performance metrics for a campaign."""
    # Verify campaign belongs to user
    campaign = await crud.get_campaign(db, campaign_id, current_user.id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )

    return await analytics_service.calculate_performance_metrics(campaign_id, db, period_days)


# Dashboard Routes
@router.get("/dashboard/summary")
async def get_dashboard_summary(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get dashboard summary data for the current user."""
    return await analytics_service.get_user_campaign_summary(current_user.id, db)


@router.get("/dashboard/full", response_model=schemas.DashboardData)
async def get_full_dashboard_data(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get comprehensive dashboard data for the current user."""
    return await analytics_service.get_dashboard_data(current_user.id, db)
