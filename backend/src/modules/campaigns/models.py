"""
Campaign Management Models for AI-Powered Ad Platform

Manages ad campaigns, business profiles, analytics data, and appointments for wellness/health services.
"""

from decimal import Decimal
from enum import Enum as PyEnum
from typing import Optional

from sqlalchemy import (
    DECIMAL,
    JSON,
    Boolean,
    Column,
    DateTime,
    Float,
    <PERSON><PERSON><PERSON>,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class BusinessType(PyEnum):
    """Business types for local service businesses."""

    # Health & Wellness
    PHYSICAL_THERAPY = "physical_therapy"
    DENTAL = "dental"
    MASSAGE_THERAPY = "massage_therapy"
    CHIROPRACTIC = "chiropractic"
    ACUPUNCTURE = "acupuncture"
    NUTRITION = "nutrition"
    MENTAL_HEALTH = "mental_health"
    FITNESS = "fitness"
    WELLNESS_SPA = "wellness_spa"
    ALTERNATIVE_MEDICINE = "alternative_medicine"

    # Home Services
    PLUMBING = "plumbing"
    ELECTRICAL = "electrical"
    HVAC = "hvac"
    HANDYMAN = "handyman"
    CLEANING = "cleaning"
    LANDSCAPING = "landscaping"
    ROOFING = "roofing"
    PAINTING = "painting"
    PEST_CONTROL = "pest_control"
    APPLIANCE_REPAIR = "appliance_repair"
    CARPET_CLEANING = "carpet_cleaning"
    WINDOW_CLEANING = "window_cleaning"

    # Professional Services
    LEGAL = "legal"
    ACCOUNTING = "accounting"
    CONSULTING = "consulting"
    REAL_ESTATE = "real_estate"
    INSURANCE = "insurance"
    FINANCIAL_PLANNING = "financial_planning"
    MARKETING = "marketing"
    IT_SERVICES = "it_services"
    ARCHITECTURE = "architecture"
    ENGINEERING = "engineering"
    PHOTOGRAPHY = "photography"
    EVENT_PLANNING = "event_planning"


class CampaignStatus(PyEnum):
    """Campaign status options."""

    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"


class AdPlatform(PyEnum):
    """Supported advertising platforms."""

    META_ADS = "meta_ads"  # Facebook & Instagram
    GOOGLE_ADS = "google_ads"
    LINKEDIN_ADS = "linkedin_ads"
    CANCELLED = "cancelled"


class AdPlatform(PyEnum):
    """Supported ad platforms."""

    META_ADS = "meta_ads"
    GOOGLE_ADS = "google_ads"  # Future expansion
    LINKEDIN_ADS = "linkedin_ads"  # Future expansion


class BusinessProfile(Base):
    """Business profile for wellness/health service providers."""

    __tablename__ = "business_profiles"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Basic Information
    business_name = Column(String(255), nullable=False)
    business_type = Column(String(50), nullable=False)  # BusinessType enum
    description = Column(Text, nullable=True)

    # Location Information
    address = Column(String(500), nullable=True)
    city = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)
    country = Column(String(100), default="Switzerland")

    # Contact Information
    phone = Column(String(50), nullable=True)
    website = Column(String(255), nullable=True)
    email = Column(String(255), nullable=True)

    # Business Details
    services_offered = Column(JSON, nullable=True, default=list)  # List of services
    target_audience = Column(Text, nullable=True)
    unique_selling_points = Column(JSON, nullable=True, default=list)

    # Booking Information
    booking_url = Column(String(500), nullable=True)  # Calendly or similar
    booking_phone = Column(String(50), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="business_profiles")
    campaigns = relationship("Campaign", back_populates="business_profile")

    def __repr__(self):
        return f"<BusinessProfile(id={self.id}, business_name='{self.business_name}')>"


class Campaign(Base):
    """Ad campaign management."""

    __tablename__ = "campaigns"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    business_profile_id = Column(
        Integer, ForeignKey("business_profiles.id"), nullable=False
    )

    # Campaign Basic Info
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    status = Column(String(50), default=CampaignStatus.DRAFT.value)

    # Platform Information
    ad_platform = Column(String(50), default=AdPlatform.META_ADS.value)
    ad_platform_id = Column(String(100), nullable=True)  # External platform campaign ID

    # Budget & Duration
    budget = Column(DECIMAL(10, 2), nullable=False)
    daily_budget = Column(DECIMAL(10, 2), nullable=True)
    duration_days = Column(Integer, nullable=True)
    start_date = Column(DateTime(timezone=True), nullable=True)
    end_date = Column(DateTime(timezone=True), nullable=True)

    # AI Generated Content
    ai_generated_ad_copy = Column(JSON, nullable=True)  # Multiple ad variations
    ai_generated_headlines = Column(JSON, nullable=True)
    ai_generated_descriptions = Column(JSON, nullable=True)
    targeting_suggestions = Column(JSON, nullable=True)
    creative_ideas = Column(JSON, nullable=True)

    # Selected Content (what user chose from AI suggestions)
    selected_ad_copy = Column(Text, nullable=True)
    selected_headline = Column(String(255), nullable=True)
    selected_description = Column(Text, nullable=True)
    selected_targeting = Column(JSON, nullable=True)

    # Campaign Goals
    primary_goal = Column(
        String(100), nullable=True
    )  # "appointments", "leads", "awareness"
    target_appointments = Column(Integer, nullable=True)
    target_cost_per_appointment = Column(DECIMAL(8, 2), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="campaigns")
    business_profile = relationship("BusinessProfile", back_populates="campaigns")
    analytics_data = relationship("AnalyticsData", back_populates="campaign")
    appointments = relationship("Appointment", back_populates="campaign")

    def __repr__(self):
        return f"<Campaign(id={self.id}, name='{self.name}', status='{self.status}')>"


class AnalyticsData(Base):
    """Campaign analytics and performance data."""

    __tablename__ = "analytics_data"

    id = Column(Integer, primary_key=True, index=True)
    campaign_id = Column(Integer, ForeignKey("campaigns.id"), nullable=False)

    # Platform Information
    platform_type = Column(String(50), default=AdPlatform.META_ADS.value)

    # Date for this data point
    date = Column(DateTime(timezone=True), nullable=False)

    # Spend Metrics
    total_spend = Column(DECIMAL(10, 2), default=0)
    daily_spend = Column(DECIMAL(10, 2), default=0)

    # Engagement Metrics
    impressions = Column(Integer, default=0)
    clicks = Column(Integer, default=0)
    click_through_rate = Column(Float, default=0.0)

    # Conversion Metrics
    leads = Column(Integer, default=0)
    appointments_booked = Column(Integer, default=0)
    cost_per_click = Column(DECIMAL(8, 2), default=0)
    cost_per_lead = Column(DECIMAL(8, 2), default=0)
    cost_per_appointment = Column(DECIMAL(8, 2), default=0)

    # ROI Metrics
    estimated_revenue = Column(DECIMAL(10, 2), default=0)
    roi_percentage = Column(Float, default=0.0)
    roas = Column(Float, default=0.0)  # Return on Ad Spend

    # Platform-specific data
    platform_data = Column(JSON, nullable=True)  # Raw data from ad platform

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    campaign = relationship("Campaign", back_populates="analytics_data")

    def __repr__(self):
        return f"<AnalyticsData(id={self.id}, campaign_id={self.campaign_id}, date='{self.date}')>"


class Appointment(Base):
    """Appointments booked through ad campaigns."""

    __tablename__ = "appointments"

    id = Column(Integer, primary_key=True, index=True)
    campaign_id = Column(Integer, ForeignKey("campaigns.id"), nullable=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Customer Information
    customer_name = Column(String(255), nullable=False)
    customer_email = Column(String(255), nullable=True)
    customer_phone = Column(String(50), nullable=True)

    # Appointment Details
    service_type = Column(String(255), nullable=True)
    appointment_date = Column(DateTime(timezone=True), nullable=True)
    appointment_duration = Column(Integer, nullable=True)  # minutes
    notes = Column(Text, nullable=True)

    # Booking Source
    booking_source = Column(
        String(100), nullable=True
    )  # "ad_campaign", "direct", "referral"
    booking_url_used = Column(String(500), nullable=True)

    # Status
    status = Column(
        String(50), default="scheduled"
    )  # scheduled, completed, cancelled, no_show

    # Revenue
    estimated_value = Column(DECIMAL(8, 2), nullable=True)
    actual_value = Column(DECIMAL(8, 2), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    campaign = relationship("Campaign", back_populates="appointments")
    user = relationship("User", back_populates="appointments")

    def __repr__(self):
        return f"<Appointment(id={self.id}, customer_name='{self.customer_name}', status='{self.status}')>"
