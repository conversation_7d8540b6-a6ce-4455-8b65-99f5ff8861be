"""
Campaign Management Schemas for AI-Powered Ad Platform

Pydantic models for request/response validation and serialization.
"""

from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator


# Business Profile Schemas
class BusinessProfileBase(BaseModel):
    business_name: str = Field(..., min_length=1, max_length=255)
    business_type: str
    description: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    postal_code: Optional[str] = None
    country: str = "Switzerland"
    phone: Optional[str] = None
    website: Optional[str] = None
    email: Optional[str] = None
    services_offered: List[str] = []
    target_audience: Optional[str] = None
    unique_selling_points: List[str] = []
    booking_url: Optional[str] = None
    booking_phone: Optional[str] = None


class BusinessProfileCreate(BusinessProfileBase):
    pass


class BusinessProfileUpdate(BaseModel):
    business_name: Optional[str] = None
    business_type: Optional[str] = None
    description: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None
    phone: Optional[str] = None
    website: Optional[str] = None
    email: Optional[str] = None
    services_offered: Optional[List[str]] = None
    target_audience: Optional[str] = None
    unique_selling_points: Optional[List[str]] = None
    booking_url: Optional[str] = None
    booking_phone: Optional[str] = None


class BusinessProfilePublic(BusinessProfileBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Campaign Schemas
class CampaignBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    ad_platform: str = "meta_ads"
    budget: Decimal = Field(..., gt=0)
    daily_budget: Optional[Decimal] = None
    duration_days: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    primary_goal: Optional[str] = None
    target_appointments: Optional[int] = None
    target_cost_per_appointment: Optional[Decimal] = None


class CampaignCreate(CampaignBase):
    business_profile_id: int


class CampaignUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None
    budget: Optional[Decimal] = None
    daily_budget: Optional[Decimal] = None
    duration_days: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    selected_ad_copy: Optional[str] = None
    selected_headline: Optional[str] = None
    selected_description: Optional[str] = None
    selected_targeting: Optional[Dict[str, Any]] = None
    primary_goal: Optional[str] = None
    target_appointments: Optional[int] = None
    target_cost_per_appointment: Optional[Decimal] = None


class CampaignPublic(CampaignBase):
    id: int
    user_id: int
    business_profile_id: int
    status: str
    ad_platform_id: Optional[str] = None
    ai_generated_ad_copy: Optional[Dict[str, Any]] = None
    ai_generated_headlines: Optional[Dict[str, Any]] = None
    ai_generated_descriptions: Optional[Dict[str, Any]] = None
    targeting_suggestions: Optional[Dict[str, Any]] = None
    creative_ideas: Optional[Dict[str, Any]] = None
    selected_ad_copy: Optional[str] = None
    selected_headline: Optional[str] = None
    selected_description: Optional[str] = None
    selected_targeting: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# AI Ad Generation Schemas
class AIAdGenerationRequest(BaseModel):
    business_type: str
    business_name: str
    services: List[str]
    target_audience: Optional[str] = None
    unique_selling_points: List[str] = []
    goal: str = "appointments"  # appointments, leads, awareness
    location: str = "Zurich, Switzerland"
    budget: Optional[Decimal] = None


class AIAdGenerationResponse(BaseModel):
    ad_copy_variations: List[str]
    headlines: List[str]
    descriptions: List[str]
    targeting_suggestions: Dict[str, Any]
    creative_ideas: List[str]
    estimated_performance: Dict[str, Any]


# Analytics Schemas
class AnalyticsDataBase(BaseModel):
    date: datetime
    total_spend: Decimal = 0
    daily_spend: Decimal = 0
    impressions: int = 0
    clicks: int = 0
    click_through_rate: float = 0.0
    leads: int = 0
    appointments_booked: int = 0
    cost_per_click: Decimal = 0
    cost_per_lead: Decimal = 0
    cost_per_appointment: Decimal = 0
    estimated_revenue: Decimal = 0
    roi_percentage: float = 0.0
    roas: float = 0.0


class AnalyticsDataCreate(AnalyticsDataBase):
    campaign_id: int
    platform_data: Optional[Dict[str, Any]] = None


class AnalyticsDataPublic(AnalyticsDataBase):
    id: int
    campaign_id: int
    platform_data: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Appointment Schemas
class AppointmentBase(BaseModel):
    customer_name: str = Field(..., min_length=1, max_length=255)
    customer_email: Optional[str] = None
    customer_phone: Optional[str] = None
    service_type: Optional[str] = None
    appointment_date: Optional[datetime] = None
    appointment_duration: Optional[int] = None
    notes: Optional[str] = None
    booking_source: Optional[str] = None
    booking_url_used: Optional[str] = None
    estimated_value: Optional[Decimal] = None


class AppointmentCreate(AppointmentBase):
    campaign_id: Optional[int] = None


class AppointmentUpdate(BaseModel):
    customer_name: Optional[str] = None
    customer_email: Optional[str] = None
    customer_phone: Optional[str] = None
    service_type: Optional[str] = None
    appointment_date: Optional[datetime] = None
    appointment_duration: Optional[int] = None
    notes: Optional[str] = None
    status: Optional[str] = None
    actual_value: Optional[Decimal] = None


class AppointmentPublic(AppointmentBase):
    id: int
    campaign_id: Optional[int] = None
    user_id: int
    status: str
    actual_value: Optional[Decimal] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Dashboard/Analytics Summary Schemas
class CampaignSummary(BaseModel):
    total_campaigns: int
    active_campaigns: int
    total_spend: Decimal
    total_appointments: int
    average_cost_per_appointment: Decimal
    total_roi: float


class ROIReport(BaseModel):
    campaign_id: int
    campaign_name: str
    total_spend: Decimal
    total_appointments: int
    total_revenue: Decimal
    roi_percentage: float
    roas: float
    cost_per_appointment: Decimal
    date_range: Dict[str, datetime]


class DashboardData(BaseModel):
    summary: CampaignSummary
    recent_campaigns: List[CampaignPublic]
    top_performing_campaigns: List[ROIReport]
    recent_appointments: List[AppointmentPublic]
    analytics_trends: List[AnalyticsDataPublic]
