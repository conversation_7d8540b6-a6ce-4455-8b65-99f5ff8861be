"""
Optimization Notifications Service

Handles email and push notifications for campaign optimization events.
"""

import logging
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

from core.config import get_settings

logger = logging.getLogger(__name__)


class NotificationType(str, Enum):
    """Types of optimization notifications."""
    OPTIMIZATION = "optimization"
    ERROR = "error"
    DAILY_REPORT = "daily_report"
    WEEKLY_SUMMARY = "weekly_summary"
    BUDGET_ALERT = "budget_alert"
    PERFORMANCE_ALERT = "performance_alert"


class NotificationService:
    """Service for sending optimization notifications."""
    
    def __init__(self):
        self.settings = get_settings()
        self.smtp_server = "smtp.gmail.com"  # Configure based on your email provider
        self.smtp_port = 587
    
    async def send_email_notification(
        self,
        to_email: str,
        subject: str,
        message: str,
        notification_type: NotificationType = NotificationType.OPTIMIZATION
    ) -> bool:
        """Send email notification."""
        
        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.settings.SMTP_FROM_EMAIL
            msg['To'] = to_email
            
            # Create HTML and text versions
            html_content = self._create_html_email(message, notification_type, subject)
            text_content = self._create_text_email(message)
            
            # Attach parts
            text_part = MIMEText(text_content, 'plain')
            html_part = MIMEText(html_content, 'html')
            
            msg.attach(text_part)
            msg.attach(html_part)
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.settings.SMTP_USERNAME, self.settings.SMTP_PASSWORD)
                server.send_message(msg)
            
            logger.info(f"Email notification sent to {to_email}: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email notification to {to_email}: {str(e)}")
            return False
    
    def _create_html_email(self, message: str, notification_type: NotificationType, subject: str) -> str:
        """Create HTML email content."""
        
        # Get notification styling based on type
        color_scheme = self._get_color_scheme(notification_type)
        icon = self._get_notification_icon(notification_type)
        
        html_template = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{subject}</title>
            <style>
                body {{
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                .header {{
                    background: linear-gradient(135deg, {color_scheme['primary']}, {color_scheme['secondary']});
                    color: white;
                    padding: 30px 20px;
                    border-radius: 8px 8px 0 0;
                    text-align: center;
                }}
                .header h1 {{
                    margin: 0;
                    font-size: 24px;
                    font-weight: 600;
                }}
                .icon {{
                    font-size: 48px;
                    margin-bottom: 10px;
                }}
                .content {{
                    background: white;
                    padding: 30px 20px;
                    border: 1px solid #e5e7eb;
                    border-top: none;
                }}
                .message {{
                    white-space: pre-line;
                    margin-bottom: 30px;
                }}
                .cta {{
                    text-align: center;
                    margin: 30px 0;
                }}
                .cta a {{
                    background: {color_scheme['primary']};
                    color: white;
                    padding: 12px 24px;
                    text-decoration: none;
                    border-radius: 6px;
                    font-weight: 500;
                    display: inline-block;
                }}
                .footer {{
                    background: #f9fafb;
                    padding: 20px;
                    border: 1px solid #e5e7eb;
                    border-top: none;
                    border-radius: 0 0 8px 8px;
                    text-align: center;
                    font-size: 14px;
                    color: #6b7280;
                }}
                .stats {{
                    background: #f8fafc;
                    border: 1px solid #e2e8f0;
                    border-radius: 6px;
                    padding: 20px;
                    margin: 20px 0;
                }}
                .stat-item {{
                    display: inline-block;
                    margin: 10px 20px;
                    text-align: center;
                }}
                .stat-value {{
                    font-size: 24px;
                    font-weight: 600;
                    color: {color_scheme['primary']};
                }}
                .stat-label {{
                    font-size: 12px;
                    color: #6b7280;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="icon">{icon}</div>
                <h1>{subject}</h1>
            </div>
            
            <div class="content">
                <div class="message">{message}</div>
                
                <div class="cta">
                    <a href="https://your-app.com/dashboard" target="_blank">
                        View Dashboard
                    </a>
                </div>
            </div>
            
            <div class="footer">
                <p>
                    This is an automated message from your AI Campaign Optimization system.
                    <br>
                    <a href="https://your-app.com/settings/notifications">Manage notification preferences</a>
                </p>
            </div>
        </body>
        </html>
        """
        
        return html_template
    
    def _create_text_email(self, message: str) -> str:
        """Create plain text email content."""
        
        text_template = f"""
AI Campaign Optimization Update

{message}

---

View your dashboard: https://your-app.com/dashboard
Manage notifications: https://your-app.com/settings/notifications

This is an automated message from your AI Campaign Optimization system.
        """
        
        return text_template.strip()
    
    def _get_color_scheme(self, notification_type: NotificationType) -> Dict[str, str]:
        """Get color scheme based on notification type."""
        
        schemes = {
            NotificationType.OPTIMIZATION: {
                'primary': '#3b82f6',
                'secondary': '#8b5cf6'
            },
            NotificationType.ERROR: {
                'primary': '#ef4444',
                'secondary': '#f97316'
            },
            NotificationType.DAILY_REPORT: {
                'primary': '#10b981',
                'secondary': '#06b6d4'
            },
            NotificationType.WEEKLY_SUMMARY: {
                'primary': '#8b5cf6',
                'secondary': '#ec4899'
            },
            NotificationType.BUDGET_ALERT: {
                'primary': '#f59e0b',
                'secondary': '#ef4444'
            },
            NotificationType.PERFORMANCE_ALERT: {
                'primary': '#06b6d4',
                'secondary': '#3b82f6'
            }
        }
        
        return schemes.get(notification_type, schemes[NotificationType.OPTIMIZATION])
    
    def _get_notification_icon(self, notification_type: NotificationType) -> str:
        """Get emoji icon based on notification type."""
        
        icons = {
            NotificationType.OPTIMIZATION: '🤖',
            NotificationType.ERROR: '⚠️',
            NotificationType.DAILY_REPORT: '📊',
            NotificationType.WEEKLY_SUMMARY: '📈',
            NotificationType.BUDGET_ALERT: '💰',
            NotificationType.PERFORMANCE_ALERT: '🎯'
        }
        
        return icons.get(notification_type, '🤖')
    
    async def send_push_notification(
        self,
        user_id: int,
        title: str,
        message: str,
        data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Send push notification (placeholder for future implementation)."""
        
        try:
            # This would integrate with a push notification service like Firebase
            logger.info(f"Push notification would be sent to user {user_id}: {title}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send push notification to user {user_id}: {str(e)}")
            return False
    
    async def send_slack_notification(
        self,
        webhook_url: str,
        message: str,
        notification_type: NotificationType = NotificationType.OPTIMIZATION
    ) -> bool:
        """Send Slack notification (placeholder for future implementation)."""
        
        try:
            # This would send to Slack webhook
            logger.info(f"Slack notification would be sent: {message[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send Slack notification: {str(e)}")
            return False


# Global notification service instance
notification_service = NotificationService()


async def send_optimization_notification(
    user_id: Optional[int],
    subject: str,
    message: str,
    notification_type: NotificationType = NotificationType.OPTIMIZATION
) -> bool:
    """Send optimization notification to user."""
    
    try:
        # In a real implementation, you'd fetch user email from database
        user_email = "<EMAIL>"  # This should be fetched from user_id
        
        # Send email notification
        email_sent = await notification_service.send_email_notification(
            to_email=user_email,
            subject=subject,
            message=message,
            notification_type=notification_type
        )
        
        # Send push notification if user has enabled it
        if user_id:
            push_sent = await notification_service.send_push_notification(
                user_id=user_id,
                title=subject,
                message=message[:100] + "..." if len(message) > 100 else message
            )
        
        return email_sent
        
    except Exception as e:
        logger.error(f"Error sending optimization notification: {str(e)}")
        return False
