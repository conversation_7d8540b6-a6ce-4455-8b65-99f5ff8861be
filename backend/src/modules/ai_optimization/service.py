"""
AI Campaign Optimization Service

Automated optimization engine that analyzes campaign performance and makes
real-time adjustments to improve ROI and reduce cost per acquisition.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from modules.campaigns import crud
from modules.campaigns.models import Campaign, AnalyticsData, AdPlatform
from integrations.google_ads.service import google_ads_service
from integrations.linkedin_ads.service import linkedin_ads_service
from modules.campaigns.meta_ads_service import meta_ads_service

logger = logging.getLogger(__name__)


class OptimizationAction(str, Enum):
    """Types of optimization actions."""
    INCREASE_BUDGET = "increase_budget"
    DECREASE_BUDGET = "decrease_budget"
    PAUSE_CAMPAIGN = "pause_campaign"
    RESUME_CAMPAIGN = "resume_campaign"
    ADJUST_BID = "adjust_bid"
    UPDATE_TARGETING = "update_targeting"
    PAUSE_UNDERPERFORMING_AD = "pause_underperforming_ad"


class OptimizationRule:
    """Defines an optimization rule with conditions and actions."""
    
    def __init__(
        self,
        name: str,
        condition: callable,
        action: OptimizationAction,
        priority: int = 1,
        cooldown_hours: int = 6
    ):
        self.name = name
        self.condition = condition
        self.action = action
        self.priority = priority
        self.cooldown_hours = cooldown_hours


class AIOptimizationService:
    """Service for automated campaign optimization."""
    
    def __init__(self):
        self.optimization_rules = self._initialize_rules()
        self.min_data_points = 3  # Minimum analytics data points before optimization
        self.min_spend_threshold = Decimal('50.00')  # Minimum spend before optimization
    
    def _initialize_rules(self) -> List[OptimizationRule]:
        """Initialize optimization rules."""
        
        rules = [
            # High-performing campaign rules
            OptimizationRule(
                name="Increase budget for high ROI campaigns",
                condition=lambda metrics: (
                    metrics.get('roi_percentage', 0) > 200 and
                    metrics.get('cost_per_appointment', 0) < metrics.get('target_cost_per_appointment', 50) * 0.8 and
                    metrics.get('total_spend', 0) > 100
                ),
                action=OptimizationAction.INCREASE_BUDGET,
                priority=1,
                cooldown_hours=12
            ),
            
            # Underperforming campaign rules
            OptimizationRule(
                name="Pause campaigns with high cost per appointment",
                condition=lambda metrics: (
                    metrics.get('cost_per_appointment', 0) > metrics.get('target_cost_per_appointment', 50) * 2.0 and
                    metrics.get('total_spend', 0) > 200 and
                    metrics.get('appointments', 0) < 3
                ),
                action=OptimizationAction.PAUSE_CAMPAIGN,
                priority=1,
                cooldown_hours=24
            ),
            
            OptimizationRule(
                name="Decrease budget for poor performing campaigns",
                condition=lambda metrics: (
                    metrics.get('cost_per_appointment', 0) > metrics.get('target_cost_per_appointment', 50) * 1.5 and
                    metrics.get('roi_percentage', 0) < 100 and
                    metrics.get('total_spend', 0) > 150
                ),
                action=OptimizationAction.DECREASE_BUDGET,
                priority=2,
                cooldown_hours=8
            ),
            
            # Click-through rate optimization
            OptimizationRule(
                name="Pause low CTR campaigns",
                condition=lambda metrics: (
                    metrics.get('click_through_rate', 0) < 1.0 and
                    metrics.get('impressions', 0) > 1000 and
                    metrics.get('total_spend', 0) > 100
                ),
                action=OptimizationAction.PAUSE_UNDERPERFORMING_AD,
                priority=2,
                cooldown_hours=6
            ),
            
            # Budget utilization rules
            OptimizationRule(
                name="Resume paused campaigns with improved metrics",
                condition=lambda metrics: (
                    metrics.get('status') == 'paused' and
                    metrics.get('days_since_pause', 0) >= 3 and
                    metrics.get('historical_roi', 0) > 150
                ),
                action=OptimizationAction.RESUME_CAMPAIGN,
                priority=3,
                cooldown_hours=24
            )
        ]
        
        return sorted(rules, key=lambda r: r.priority)
    
    async def optimize_campaigns(self, db: AsyncSession, user_id: Optional[int] = None) -> Dict[str, Any]:
        """Run optimization for all active campaigns."""
        
        try:
            # Get active campaigns
            if user_id:
                campaigns = await crud.get_campaigns_by_user(db, user_id)
                campaigns = [c for c in campaigns if c.status == 'active']
            else:
                # Get all active campaigns (for scheduled optimization)
                result = await db.execute(
                    select(Campaign).where(Campaign.status == 'active')
                )
                campaigns = result.scalars().all()
            
            optimization_results = {
                'total_campaigns_analyzed': len(campaigns),
                'optimizations_applied': 0,
                'campaigns_optimized': [],
                'errors': []
            }
            
            for campaign in campaigns:
                try:
                    result = await self.optimize_single_campaign(db, campaign)
                    if result['optimizations_applied'] > 0:
                        optimization_results['optimizations_applied'] += result['optimizations_applied']
                        optimization_results['campaigns_optimized'].append({
                            'campaign_id': campaign.id,
                            'campaign_name': campaign.name,
                            'actions': result['actions']
                        })
                except Exception as e:
                    logger.error(f"Error optimizing campaign {campaign.id}: {str(e)}")
                    optimization_results['errors'].append({
                        'campaign_id': campaign.id,
                        'error': str(e)
                    })
            
            logger.info(f"Optimization completed: {optimization_results['optimizations_applied']} actions applied across {len(optimization_results['campaigns_optimized'])} campaigns")
            
            return optimization_results
            
        except Exception as e:
            logger.error(f"Error in optimize_campaigns: {str(e)}")
            raise
    
    async def optimize_single_campaign(self, db: AsyncSession, campaign: Campaign) -> Dict[str, Any]:
        """Optimize a single campaign based on performance metrics."""
        
        try:
            # Get campaign performance metrics
            metrics = await self._get_campaign_metrics(db, campaign)
            
            # Check if we have enough data for optimization
            if not self._has_sufficient_data(metrics):
                return {
                    'campaign_id': campaign.id,
                    'optimizations_applied': 0,
                    'actions': [],
                    'reason': 'Insufficient data for optimization'
                }
            
            # Check cooldown periods
            if await self._is_in_cooldown(db, campaign.id):
                return {
                    'campaign_id': campaign.id,
                    'optimizations_applied': 0,
                    'actions': [],
                    'reason': 'Campaign in cooldown period'
                }
            
            # Apply optimization rules
            actions_taken = []
            
            for rule in self.optimization_rules:
                if rule.condition(metrics):
                    success = await self._apply_optimization_action(
                        db, campaign, rule.action, metrics
                    )
                    
                    if success:
                        actions_taken.append({
                            'rule': rule.name,
                            'action': rule.action.value,
                            'timestamp': datetime.now().isoformat()
                        })
                        
                        # Log the optimization action
                        await self._log_optimization_action(
                            db, campaign.id, rule.action, rule.name, metrics
                        )
                        
                        # Only apply one optimization per run to avoid conflicts
                        break
            
            return {
                'campaign_id': campaign.id,
                'optimizations_applied': len(actions_taken),
                'actions': actions_taken
            }
            
        except Exception as e:
            logger.error(f"Error optimizing campaign {campaign.id}: {str(e)}")
            raise
    
    async def _get_campaign_metrics(self, db: AsyncSession, campaign: Campaign) -> Dict[str, Any]:
        """Get comprehensive metrics for a campaign."""
        
        # Get analytics data for the last 7 days
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        result = await db.execute(
            select(AnalyticsData).where(
                and_(
                    AnalyticsData.campaign_id == campaign.id,
                    AnalyticsData.date >= start_date,
                    AnalyticsData.date <= end_date
                )
            ).order_by(AnalyticsData.date.desc())
        )
        analytics_data = result.scalars().all()
        
        if not analytics_data:
            return {'data_points': 0}
        
        # Calculate aggregated metrics
        total_spend = sum(data.total_spend for data in analytics_data)
        total_impressions = sum(data.impressions for data in analytics_data)
        total_clicks = sum(data.clicks for data in analytics_data)
        total_leads = sum(data.leads for data in analytics_data)
        total_appointments = sum(data.appointments_booked for data in analytics_data)
        
        # Calculate rates
        ctr = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0
        conversion_rate = (total_appointments / total_clicks * 100) if total_clicks > 0 else 0
        cost_per_appointment = (total_spend / total_appointments) if total_appointments > 0 else float('inf')
        
        # Calculate ROI (assuming average appointment value)
        avg_appointment_value = Decimal('120.00')  # This should come from business profile
        total_revenue = total_appointments * avg_appointment_value
        roi_percentage = ((total_revenue - total_spend) / total_spend * 100) if total_spend > 0 else 0
        
        return {
            'data_points': len(analytics_data),
            'total_spend': float(total_spend),
            'total_impressions': total_impressions,
            'total_clicks': total_clicks,
            'total_leads': total_leads,
            'appointments': total_appointments,
            'click_through_rate': float(ctr),
            'conversion_rate': float(conversion_rate),
            'cost_per_appointment': float(cost_per_appointment),
            'roi_percentage': float(roi_percentage),
            'target_cost_per_appointment': float(campaign.target_cost_per_appointment or 50),
            'budget': float(campaign.budget),
            'daily_budget': float(campaign.daily_budget or campaign.budget / 30),
            'status': campaign.status,
            'platform': campaign.ad_platform,
            'days_running': (datetime.now() - campaign.created_at).days
        }
    
    def _has_sufficient_data(self, metrics: Dict[str, Any]) -> bool:
        """Check if campaign has sufficient data for optimization."""
        return (
            metrics.get('data_points', 0) >= self.min_data_points and
            metrics.get('total_spend', 0) >= float(self.min_spend_threshold)
        )
    
    async def _is_in_cooldown(self, db: AsyncSession, campaign_id: int) -> bool:
        """Check if campaign is in cooldown period from last optimization."""
        
        # This would check a optimization_log table in a real implementation
        # For now, return False to allow optimization
        return False
    
    async def _apply_optimization_action(
        self, 
        db: AsyncSession, 
        campaign: Campaign, 
        action: OptimizationAction,
        metrics: Dict[str, Any]
    ) -> bool:
        """Apply the optimization action to the campaign."""
        
        try:
            if action == OptimizationAction.INCREASE_BUDGET:
                return await self._increase_budget(db, campaign, metrics)
            elif action == OptimizationAction.DECREASE_BUDGET:
                return await self._decrease_budget(db, campaign, metrics)
            elif action == OptimizationAction.PAUSE_CAMPAIGN:
                return await self._pause_campaign(db, campaign, metrics)
            elif action == OptimizationAction.RESUME_CAMPAIGN:
                return await self._resume_campaign(db, campaign, metrics)
            elif action == OptimizationAction.PAUSE_UNDERPERFORMING_AD:
                return await self._pause_underperforming_ad(db, campaign, metrics)
            else:
                logger.warning(f"Unknown optimization action: {action}")
                return False
                
        except Exception as e:
            logger.error(f"Error applying optimization action {action} to campaign {campaign.id}: {str(e)}")
            return False
    
    async def _increase_budget(self, db: AsyncSession, campaign: Campaign, metrics: Dict[str, Any]) -> bool:
        """Increase campaign budget by 20%."""
        
        new_budget = campaign.budget * Decimal('1.2')
        new_daily_budget = (campaign.daily_budget or campaign.budget / 30) * Decimal('1.2')
        
        # Update in database
        await crud.update_campaign(db, campaign.id, {
            'budget': new_budget,
            'daily_budget': new_daily_budget
        })
        
        # Update on ad platform
        if campaign.ad_platform == AdPlatform.GOOGLE_ADS.value:
            await google_ads_service.update_campaign_budget(
                campaign.ad_platform_id, float(new_daily_budget), "mock_access_token", "mock_customer_id"
            )
        elif campaign.ad_platform == AdPlatform.LINKEDIN_ADS.value:
            await linkedin_ads_service.update_campaign_budget(
                campaign.ad_platform_id, float(new_daily_budget), "mock_access_token"
            )
        elif campaign.ad_platform == AdPlatform.META_ADS.value:
            await meta_ads_service.update_campaign_budget(
                campaign.ad_platform_id, float(new_daily_budget), "mock_access_token"
            )
        
        logger.info(f"Increased budget for campaign {campaign.id} from {campaign.budget} to {new_budget}")
        return True
    
    async def _decrease_budget(self, db: AsyncSession, campaign: Campaign, metrics: Dict[str, Any]) -> bool:
        """Decrease campaign budget by 30%."""
        
        new_budget = campaign.budget * Decimal('0.7')
        new_daily_budget = (campaign.daily_budget or campaign.budget / 30) * Decimal('0.7')
        
        # Don't decrease below minimum threshold
        min_budget = Decimal('50.00')
        if new_budget < min_budget:
            new_budget = min_budget
            new_daily_budget = min_budget / 30
        
        # Update in database
        await crud.update_campaign(db, campaign.id, {
            'budget': new_budget,
            'daily_budget': new_daily_budget
        })
        
        logger.info(f"Decreased budget for campaign {campaign.id} from {campaign.budget} to {new_budget}")
        return True
    
    async def _pause_campaign(self, db: AsyncSession, campaign: Campaign, metrics: Dict[str, Any]) -> bool:
        """Pause underperforming campaign."""
        
        # Update in database
        await crud.update_campaign(db, campaign.id, {'status': 'paused'})
        
        # Update on ad platform
        if campaign.ad_platform == AdPlatform.GOOGLE_ADS.value:
            await google_ads_service.update_campaign_status(
                campaign.ad_platform_id, "PAUSED", "mock_access_token", "mock_customer_id"
            )
        elif campaign.ad_platform == AdPlatform.LINKEDIN_ADS.value:
            await linkedin_ads_service.update_campaign_status(
                campaign.ad_platform_id, "PAUSED", "mock_access_token"
            )
        elif campaign.ad_platform == AdPlatform.META_ADS.value:
            await meta_ads_service.update_campaign_status(
                campaign.ad_platform_id, "PAUSED", "mock_access_token"
            )
        
        logger.info(f"Paused underperforming campaign {campaign.id}")
        return True
    
    async def _resume_campaign(self, db: AsyncSession, campaign: Campaign, metrics: Dict[str, Any]) -> bool:
        """Resume paused campaign with improved metrics."""
        
        # Update in database
        await crud.update_campaign(db, campaign.id, {'status': 'active'})
        
        logger.info(f"Resumed campaign {campaign.id}")
        return True
    
    async def _pause_underperforming_ad(self, db: AsyncSession, campaign: Campaign, metrics: Dict[str, Any]) -> bool:
        """Pause underperforming ad creative."""
        
        # This would pause specific ad creatives within the campaign
        # For now, just log the action
        logger.info(f"Would pause underperforming ads for campaign {campaign.id}")
        return True
    
    async def _log_optimization_action(
        self, 
        db: AsyncSession, 
        campaign_id: int, 
        action: OptimizationAction,
        rule_name: str,
        metrics: Dict[str, Any]
    ):
        """Log optimization action for tracking and cooldown purposes."""
        
        # In a real implementation, this would insert into an optimization_log table
        logger.info(f"Optimization action logged: {action.value} for campaign {campaign_id} (rule: {rule_name})")


# Global service instance
ai_optimization_service = AIOptimizationService()
