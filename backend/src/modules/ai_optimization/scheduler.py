"""
Campaign Optimization Scheduler

Background task scheduler for automated campaign optimization.
Uses APScheduler for scheduling optimization runs every 6 hours.
"""

import logging
import asyncio
from datetime import datetime
from typing import Dict, Any

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import Cron<PERSON>rigger
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import AsyncSessionLocal
from .service import ai_optimization_service
from .notifications import send_optimization_notification

logger = logging.getLogger(__name__)


class OptimizationScheduler:
    """Scheduler for automated campaign optimization."""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
    
    def start(self):
        """Start the optimization scheduler."""
        
        if self.is_running:
            logger.warning("Optimization scheduler is already running")
            return
        
        # Schedule optimization every 6 hours
        self.scheduler.add_job(
            func=self.run_optimization,
            trigger=IntervalTrigger(hours=6),
            id='campaign_optimization',
            name='Campaign Optimization',
            replace_existing=True,
            max_instances=1  # Prevent overlapping runs
        )
        
        # Schedule daily performance reports
        self.scheduler.add_job(
            func=self.send_daily_reports,
            trigger=CronTrigger(hour=9, minute=0),  # 9 AM daily
            id='daily_reports',
            name='Daily Performance Reports',
            replace_existing=True
        )
        
        # Schedule weekly optimization summary
        self.scheduler.add_job(
            func=self.send_weekly_summary,
            trigger=CronTrigger(day_of_week='mon', hour=10, minute=0),  # Monday 10 AM
            id='weekly_summary',
            name='Weekly Optimization Summary',
            replace_existing=True
        )
        
        self.scheduler.start()
        self.is_running = True
        logger.info("Optimization scheduler started successfully")
    
    def stop(self):
        """Stop the optimization scheduler."""
        
        if not self.is_running:
            logger.warning("Optimization scheduler is not running")
            return
        
        self.scheduler.shutdown()
        self.is_running = False
        logger.info("Optimization scheduler stopped")
    
    async def run_optimization(self):
        """Run optimization for all active campaigns."""
        
        logger.info("Starting scheduled campaign optimization")
        start_time = datetime.now()
        
        try:
            async with AsyncSessionLocal() as db:
                # Run optimization for all campaigns
                results = await ai_optimization_service.optimize_campaigns(db)
                
                # Log results
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                logger.info(
                    f"Optimization completed in {duration:.2f}s: "
                    f"{results['optimizations_applied']} actions applied "
                    f"across {results['total_campaigns_analyzed']} campaigns"
                )
                
                # Send notifications for significant optimizations
                if results['optimizations_applied'] > 0:
                    await self.send_optimization_notifications(results)
                
                return results
                
        except Exception as e:
            logger.error(f"Error in scheduled optimization: {str(e)}")
            # Send error notification
            await send_optimization_notification(
                user_id=None,
                subject="Campaign Optimization Error",
                message=f"Scheduled optimization failed: {str(e)}",
                notification_type="error"
            )
    
    async def send_optimization_notifications(self, results: Dict[str, Any]):
        """Send notifications about optimization actions."""
        
        try:
            # Group optimizations by user
            user_optimizations = {}
            
            for campaign_opt in results['campaigns_optimized']:
                # In a real implementation, you'd get the user_id from the campaign
                # For now, we'll use a mock user_id
                user_id = 1  # This should be fetched from the campaign
                
                if user_id not in user_optimizations:
                    user_optimizations[user_id] = []
                
                user_optimizations[user_id].append(campaign_opt)
            
            # Send notifications to each user
            for user_id, optimizations in user_optimizations.items():
                await send_optimization_notification(
                    user_id=user_id,
                    subject="Campaign Optimization Update",
                    message=self._format_optimization_message(optimizations),
                    notification_type="optimization"
                )
                
        except Exception as e:
            logger.error(f"Error sending optimization notifications: {str(e)}")
    
    def _format_optimization_message(self, optimizations: list) -> str:
        """Format optimization results into a user-friendly message."""
        
        message_parts = [
            "🤖 AI Optimization Update",
            "",
            f"We've optimized {len(optimizations)} of your campaigns:",
            ""
        ]
        
        for opt in optimizations:
            campaign_name = opt['campaign_name']
            actions = opt['actions']
            
            message_parts.append(f"📊 {campaign_name}:")
            for action in actions:
                action_text = self._format_action_text(action['action'])
                message_parts.append(f"  • {action_text}")
            message_parts.append("")
        
        message_parts.extend([
            "These optimizations are designed to improve your ROI and reduce costs.",
            "You can view detailed performance data in your dashboard.",
            "",
            "Questions? Reply to this email or contact our support team."
        ])
        
        return "\n".join(message_parts)
    
    def _format_action_text(self, action: str) -> str:
        """Format action type into user-friendly text."""
        
        action_texts = {
            'increase_budget': 'Increased budget for better performance',
            'decrease_budget': 'Reduced budget to control costs',
            'pause_campaign': 'Paused underperforming campaign',
            'resume_campaign': 'Resumed improved campaign',
            'adjust_bid': 'Adjusted bidding strategy',
            'pause_underperforming_ad': 'Paused low-performing ads'
        }
        
        return action_texts.get(action, f'Applied optimization: {action}')
    
    async def send_daily_reports(self):
        """Send daily performance reports to users."""
        
        logger.info("Sending daily performance reports")
        
        try:
            async with AsyncSessionLocal() as db:
                # Get performance data for all users
                # This would fetch user-specific performance data
                
                # For now, just log that reports would be sent
                logger.info("Daily reports would be sent to all users")
                
        except Exception as e:
            logger.error(f"Error sending daily reports: {str(e)}")
    
    async def send_weekly_summary(self):
        """Send weekly optimization summary to users."""
        
        logger.info("Sending weekly optimization summary")
        
        try:
            async with AsyncSessionLocal() as db:
                # Get weekly optimization summary data
                # This would compile optimization actions from the past week
                
                # For now, just log that summaries would be sent
                logger.info("Weekly summaries would be sent to all users")
                
        except Exception as e:
            logger.error(f"Error sending weekly summary: {str(e)}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get scheduler status and job information."""
        
        jobs = []
        if self.scheduler:
            for job in self.scheduler.get_jobs():
                jobs.append({
                    'id': job.id,
                    'name': job.name,
                    'next_run': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger)
                })
        
        return {
            'is_running': self.is_running,
            'jobs': jobs,
            'scheduler_state': self.scheduler.state if self.scheduler else None
        }


# Global scheduler instance
optimization_scheduler = OptimizationScheduler()
