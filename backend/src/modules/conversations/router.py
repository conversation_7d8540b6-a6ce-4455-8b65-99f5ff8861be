"""
Conversations API Router for comprehensive logging and analytics.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.service import get_current_active_user
from modules.companies import crud as company_crud
from modules.users.models import User as DBUser

from . import crud, schemas

logger = logging.getLogger(__name__)

router = APIRouter(tags=["conversations"])


@router.get("/", response_model=List[schemas.Conversation])
async def get_conversations(
    company_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    channel: Optional[schemas.ConversationChannel] = None,
    status: Optional[schemas.ConversationStatus] = None,
    agent_id: Optional[int] = None,
    phone_number_id: Optional[int] = None,
    search: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get conversations for a company."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    conversations = await crud.get_conversations(
        db=db,
        company_id=company_id,
        skip=skip,
        limit=limit,
        channel=channel,
        status=status,
        agent_id=agent_id,
        phone_number_id=phone_number_id,
        search=search,
    )
    return conversations


@router.get("/{conversation_id}", response_model=schemas.ConversationWithDetails)
async def get_conversation(
    conversation_id: int,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get a specific conversation with messages."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement get conversation with details
    raise HTTPException(status_code=404, detail="Conversation not found")


@router.get(
    "/{conversation_id}/messages", response_model=List[schemas.ConversationMessage]
)
async def get_conversation_messages(
    conversation_id: int,
    company_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get messages for a conversation."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement get conversation messages
    return []


@router.post("/{conversation_id}/feedback", response_model=schemas.ConversationFeedback)
async def create_conversation_feedback(
    conversation_id: int,
    company_id: int,
    feedback: schemas.ConversationFeedbackCreate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create feedback for a conversation."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement create conversation feedback
    raise HTTPException(status_code=501, detail="Not implemented yet")


@router.get("/analytics/summary")
async def get_conversation_analytics(
    company_id: int,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    channel: Optional[schemas.ConversationChannel] = None,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get conversation analytics summary."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement conversation analytics
    return {
        "total_conversations": 0,
        "completed_conversations": 0,
        "abandoned_conversations": 0,
        "average_duration": 0,
        "customer_satisfaction": 0.0,
        "channel_breakdown": {},
        "agent_performance": [],
    }
