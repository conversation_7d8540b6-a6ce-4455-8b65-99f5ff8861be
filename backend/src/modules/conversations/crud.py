"""
Conversation CRUD operations for comprehensive logging and analytics.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, or_, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload

from modules.agents.models import Agent
from modules.companies.models import Company
from modules.phone_numbers.models import PhoneNumber

from . import models, schemas

logger = logging.getLogger(__name__)


async def create_conversation(
    db: AsyncSession, conversation: schemas.ConversationCreate, company_id: int
) -> models.Conversation:
    """Create a new conversation."""
    db_conversation = models.Conversation(
        **conversation.dict(), company_id=company_id, created_at=datetime.utcnow()
    )
    db.add(db_conversation)
    await db.commit()
    await db.refresh(db_conversation)
    return db_conversation


async def get_conversation(
    db: AsyncSession,
    conversation_id: int,
    company_id: int,
    include_messages: bool = False,
) -> Optional[models.Conversation]:
    """Get a single conversation by ID."""
    query = select(models.Conversation).filter(
        and_(
            models.Conversation.id == conversation_id,
            models.Conversation.company_id == company_id,
        )
    )

    if include_messages:
        query = query.options(
            selectinload(models.Conversation.messages),
            joinedload(models.Conversation.agent),
            joinedload(models.Conversation.phone_number),
        )

    result = await db.execute(query)
    return result.scalars().first()


async def get_conversations(
    db: AsyncSession,
    company_id: int,
    skip: int = 0,
    limit: int = 100,
    channel: Optional[schemas.ConversationChannel] = None,
    status: Optional[schemas.ConversationStatus] = None,
    agent_id: Optional[int] = None,
    phone_number_id: Optional[int] = None,
    search: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
) -> List[models.Conversation]:
    """Get conversations with filtering."""
    query = select(models.Conversation).filter(
        models.Conversation.company_id == company_id
    )

    # Apply filters
    if channel:
        query = query.filter(models.Conversation.channel == channel)

    if status:
        query = query.filter(models.Conversation.status == status)

    if agent_id:
        query = query.filter(models.Conversation.agent_id == agent_id)

    if phone_number_id:
        query = query.filter(models.Conversation.phone_number_id == phone_number_id)

    if search:
        query = query.filter(
            or_(
                models.Conversation.customer_phone.ilike(f"%{search}%"),
                models.Conversation.customer_name.ilike(f"%{search}%"),
                models.Conversation.summary.ilike(f"%{search}%"),
            )
        )

    if start_date:
        query = query.filter(models.Conversation.created_at >= start_date)

    if end_date:
        query = query.filter(models.Conversation.created_at <= end_date)

    # Order by most recent first
    query = query.order_by(desc(models.Conversation.created_at))
    query = query.offset(skip).limit(limit)

    # Include related data
    query = query.options(
        joinedload(models.Conversation.agent),
        joinedload(models.Conversation.phone_number),
    )

    result = await db.execute(query)
    return result.scalars().all()


async def update_conversation(
    db: AsyncSession,
    conversation_id: int,
    company_id: int,
    conversation_update: schemas.ConversationUpdate,
) -> Optional[models.Conversation]:
    """Update a conversation."""
    db_conversation = await get_conversation(db, conversation_id, company_id)
    if not db_conversation:
        return None

    update_data = conversation_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_conversation, field, value)

    db_conversation.updated_at = datetime.utcnow()
    await db.commit()
    await db.refresh(db_conversation)
    return db_conversation


async def delete_conversation(
    db: AsyncSession, conversation_id: int, company_id: int
) -> bool:
    """Delete a conversation."""
    db_conversation = await get_conversation(db, conversation_id, company_id)
    if not db_conversation:
        return False

    await db.delete(db_conversation)
    await db.commit()
    return True


async def add_message_to_conversation(
    db: AsyncSession,
    conversation_id: int,
    company_id: int,
    message: schemas.ConversationMessageCreate,
) -> Optional[models.ConversationMessage]:
    """Add a message to a conversation."""
    # Verify conversation exists and belongs to company
    conversation = await get_conversation(db, conversation_id, company_id)
    if not conversation:
        return None

    db_message = models.ConversationMessage(
        **message.dict(), conversation_id=conversation_id, created_at=datetime.utcnow()
    )

    db.add(db_message)

    # Update conversation's last activity
    conversation.updated_at = datetime.utcnow()
    if message.role == "user":
        conversation.last_customer_message_at = datetime.utcnow()
    elif message.role == "assistant":
        conversation.last_agent_message_at = datetime.utcnow()

    await db.commit()
    await db.refresh(db_message)
    return db_message


async def get_conversation_analytics(
    db: AsyncSession,
    company_id: int,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    agent_id: Optional[int] = None,
    phone_number_id: Optional[int] = None,
) -> Dict[str, Any]:
    """Get conversation analytics."""
    if not start_date:
        start_date = datetime.utcnow() - timedelta(days=30)
    if not end_date:
        end_date = datetime.utcnow()

    base_query = select(models.Conversation).filter(
        and_(
            models.Conversation.company_id == company_id,
            models.Conversation.created_at >= start_date,
            models.Conversation.created_at <= end_date,
        )
    )

    if agent_id:
        base_query = base_query.filter(models.Conversation.agent_id == agent_id)

    if phone_number_id:
        base_query = base_query.filter(
            models.Conversation.phone_number_id == phone_number_id
        )

    # Total conversations
    total_result = await db.execute(
        select(func.count()).select_from(base_query.subquery())
    )
    total_conversations = total_result.scalar()

    # Conversations by status
    status_result = await db.execute(
        select(models.Conversation.status, func.count(models.Conversation.id))
        .filter(
            and_(
                models.Conversation.company_id == company_id,
                models.Conversation.created_at >= start_date,
                models.Conversation.created_at <= end_date,
            )
        )
        .group_by(models.Conversation.status)
    )
    status_breakdown = {row[0]: row[1] for row in status_result.fetchall()}

    # Conversations by channel
    channel_result = await db.execute(
        select(models.Conversation.channel, func.count(models.Conversation.id))
        .filter(
            and_(
                models.Conversation.company_id == company_id,
                models.Conversation.created_at >= start_date,
                models.Conversation.created_at <= end_date,
            )
        )
        .group_by(models.Conversation.channel)
    )
    channel_breakdown = {row[0]: row[1] for row in channel_result.fetchall()}

    # Average duration (for completed conversations)
    duration_result = await db.execute(
        select(func.avg(models.Conversation.duration_seconds)).filter(
            and_(
                models.Conversation.company_id == company_id,
                models.Conversation.created_at >= start_date,
                models.Conversation.created_at <= end_date,
                models.Conversation.status == schemas.ConversationStatus.COMPLETED,
                models.Conversation.duration_seconds.isnot(None),
            )
        )
    )
    avg_duration = duration_result.scalar() or 0

    # Daily conversation counts
    daily_result = await db.execute(
        select(
            func.date(models.Conversation.created_at).label("date"),
            func.count(models.Conversation.id).label("count"),
        )
        .filter(
            and_(
                models.Conversation.company_id == company_id,
                models.Conversation.created_at >= start_date,
                models.Conversation.created_at <= end_date,
            )
        )
        .group_by(func.date(models.Conversation.created_at))
        .order_by(func.date(models.Conversation.created_at))
    )
    daily_counts = [
        {"date": row[0].isoformat(), "count": row[1]} for row in daily_result.fetchall()
    ]

    return {
        "total_conversations": total_conversations,
        "status_breakdown": status_breakdown,
        "channel_breakdown": channel_breakdown,
        "average_duration_seconds": round(avg_duration, 2),
        "daily_counts": daily_counts,
        "period": {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
        },
    }
