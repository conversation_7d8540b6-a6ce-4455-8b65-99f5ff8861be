"""
Conversation Schemas for API requests and responses.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field


class ConversationStatus(str, Enum):
    """Conversation status options."""

    ACTIVE = "active"
    COMPLETED = "completed"
    ABANDONED = "abandoned"
    FAILED = "failed"
    TRANSFERRED = "transferred"
    QUEUED = "queued"


class ConversationChannel(str, Enum):
    """Communication channels."""

    PHONE = "phone"
    WEB = "web"
    SMS = "sms"
    CHAT = "chat"
    EMAIL = "email"
    WHATSAPP = "whatsapp"


class MessageRole(str, Enum):
    """Message roles in conversation."""

    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    AGENT = "agent"


class ConversationOutcome(str, Enum):
    """Conversation outcomes."""

    APPOINTMENT_SCHEDULED = "appointment_scheduled"
    QUOTE_PROVIDED = "quote_provided"
    INFORMATION_PROVIDED = "information_provided"
    ISSUE_RESOLVED = "issue_resolved"
    TRANSFERRED_TO_HUMAN = "transferred_to_human"
    CALLBACK_REQUESTED = "callback_requested"
    NO_RESOLUTION = "no_resolution"
    SPAM_DETECTED = "spam_detected"


# Base Schemas
class ConversationBase(BaseModel):
    conversation_id: str = Field(..., max_length=100)
    session_id: Optional[str] = Field(None, max_length=100)
    channel: ConversationChannel = ConversationChannel.PHONE
    customer_phone: Optional[str] = Field(None, max_length=20)
    customer_email: Optional[str] = Field(None, max_length=255)
    customer_name: Optional[str] = Field(None, max_length=255)
    customer_info: Optional[Dict[str, Any]] = None
    status: ConversationStatus = ConversationStatus.ACTIVE
    outcome: Optional[ConversationOutcome] = None
    priority: int = Field(default=0, ge=0, le=2)


class ConversationCreate(ConversationBase):
    company_id: int
    phone_number_id: Optional[int] = None
    agent_id: Optional[int] = None


class ConversationUpdate(BaseModel):
    status: Optional[ConversationStatus] = None
    outcome: Optional[ConversationOutcome] = None
    customer_name: Optional[str] = Field(None, max_length=255)
    customer_email: Optional[str] = Field(None, max_length=255)
    customer_info: Optional[Dict[str, Any]] = None
    agent_id: Optional[int] = None


class Conversation(ConversationBase):
    id: int
    company_id: int
    phone_number_id: Optional[int] = None
    agent_id: Optional[int] = None
    started_at: datetime
    ended_at: Optional[datetime] = None
    duration_seconds: Optional[int] = None
    first_response_time: Optional[int] = None
    resolution_time: Optional[int] = None
    agent_response_count: int = 0
    customer_message_count: int = 0
    customer_satisfaction: Optional[int] = None
    agent_rating: Optional[int] = None
    quality_score: Optional[float] = None
    lead_generated: bool = False
    appointment_scheduled: bool = False
    quote_provided: bool = False
    sale_amount: Optional[float] = None
    sentiment_score: Optional[float] = None
    topics: Optional[List[str]] = None
    keywords: Optional[List[str]] = None
    summary: Optional[str] = None
    transferred_from_agent_id: Optional[int] = None
    transfer_reason: Optional[str] = None
    escalated_to_human: bool = False
    escalation_reason: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class ConversationWithDetails(Conversation):
    """Conversation with additional details."""

    messages: Optional[List["ConversationMessage"]] = None
    events: Optional[List["ConversationEvent"]] = None
    agent_name: Optional[str] = None
    phone_number: Optional[str] = None


# Message Schemas
class ConversationMessageBase(BaseModel):
    role: MessageRole = MessageRole.USER
    content: str
    content_type: str = Field(default="text", max_length=50)
    message_id: Optional[str] = Field(None, max_length=100)
    sequence_number: int
    response_time: Optional[int] = None
    sentiment_score: Optional[float] = None
    confidence_score: Optional[float] = None
    intent: Optional[str] = Field(None, max_length=100)
    entities: Optional[List[Dict[str, Any]]] = None
    media_url: Optional[str] = Field(None, max_length=500)
    media_type: Optional[str] = Field(None, max_length=50)
    media_duration: Optional[int] = None
    flagged: bool = False
    flag_reason: Optional[str] = Field(None, max_length=255)


class ConversationMessageCreate(ConversationMessageBase):
    conversation_id: int


class ConversationMessage(ConversationMessageBase):
    id: int
    conversation_id: int
    timestamp: datetime
    provider_message_id: Optional[str] = None
    provider_metadata: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Event Schemas
class ConversationEventBase(BaseModel):
    event_type: str = Field(..., max_length=50)
    event_data: Optional[Dict[str, Any]] = None
    description: Optional[str] = Field(None, max_length=500)
    actor_type: Optional[str] = Field(None, max_length=50)
    actor_id: Optional[str] = Field(None, max_length=100)
    duration: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None


class ConversationEventCreate(ConversationEventBase):
    conversation_id: int


class ConversationEvent(ConversationEventBase):
    id: int
    conversation_id: int
    timestamp: datetime

    model_config = ConfigDict(from_attributes=True)


# Feedback Schemas
class ConversationFeedbackBase(BaseModel):
    rating: int = Field(..., ge=1, le=5)
    comment: Optional[str] = None
    helpfulness_rating: Optional[int] = Field(None, ge=1, le=5)
    speed_rating: Optional[int] = Field(None, ge=1, le=5)
    professionalism_rating: Optional[int] = Field(None, ge=1, le=5)
    feedback_channel: Optional[str] = Field(None, max_length=50)
    feedback_prompt: Optional[str] = Field(None, max_length=255)
    customer_phone: Optional[str] = Field(None, max_length=20)
    customer_email: Optional[str] = Field(None, max_length=255)


class ConversationFeedbackCreate(ConversationFeedbackBase):
    conversation_id: int
    company_id: int


class ConversationFeedback(ConversationFeedbackBase):
    id: int
    conversation_id: int
    company_id: int
    sentiment_score: Optional[float] = None
    topics: Optional[List[str]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Analytics Schemas
class ConversationAnalytics(BaseModel):
    company_id: int
    date: datetime
    period_type: str
    total_conversations: int = 0
    completed_conversations: int = 0
    abandoned_conversations: int = 0
    transferred_conversations: int = 0
    phone_conversations: int = 0
    web_conversations: int = 0
    sms_conversations: int = 0
    chat_conversations: int = 0
    average_duration: int = 0
    average_first_response_time: int = 0
    average_resolution_time: int = 0
    average_customer_satisfaction: Optional[float] = None
    average_agent_rating: Optional[float] = None
    average_quality_score: Optional[float] = None
    leads_generated: int = 0
    appointments_scheduled: int = 0
    quotes_provided: int = 0
    total_revenue: float = 0.0
    agent_utilization: float = 0.0
    escalation_rate: float = 0.0
    top_topics: Optional[List[str]] = None
    top_intents: Optional[List[str]] = None

    model_config = ConfigDict(from_attributes=True)


# Response Models
class ConversationListResponse(BaseModel):
    conversations: List[Conversation]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class ApiResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None
    errors: Optional[List[str]] = None
