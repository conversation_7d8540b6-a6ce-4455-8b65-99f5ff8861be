"""
Conversation Models for comprehensive logging and analytics.

Tracks all conversations across phone, web, SMS, and chat channels.
"""

from enum import Enum as PyEnum

from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    Float,
    <PERSON><PERSON>ey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class ConversationStatus(PyEnum):
    """Conversation status options."""

    ACTIVE = "active"
    COMPLETED = "completed"
    ABANDONED = "abandoned"
    FAILED = "failed"
    TRANSFERRED = "transferred"
    QUEUED = "queued"


class ConversationChannel(PyEnum):
    """Communication channels."""

    PHONE = "phone"
    WEB = "web"
    SMS = "sms"
    CHAT = "chat"
    EMAIL = "email"
    WHATSAPP = "whatsapp"


class MessageRole(PyEnum):
    """Message roles in conversation."""

    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    AGENT = "agent"


class ConversationOutcome(PyEnum):
    """Conversation outcomes."""

    APPOINTMENT_SCHEDULED = "appointment_scheduled"
    QUOTE_PROVIDED = "quote_provided"
    INFORMATION_PROVIDED = "information_provided"
    ISSUE_RESOLVED = "issue_resolved"
    TRANSFERRED_TO_HUMAN = "transferred_to_human"
    CALLBACK_REQUESTED = "callback_requested"
    NO_RESOLUTION = "no_resolution"
    SPAM_DETECTED = "spam_detected"


class Conversation(Base):
    """
    Main conversation model tracking all customer interactions.
    """

    __tablename__ = "conversations"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    phone_number_id = Column(Integer, ForeignKey("phone_numbers.id"), nullable=True)
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True)

    # Conversation Identification
    conversation_id = Column(
        String(100), nullable=False, unique=True, index=True
    )  # External ID
    session_id = Column(String(100), nullable=True, index=True)  # Session tracking

    # Channel & Contact Information
    channel = Column(
        String(50), nullable=False, default=ConversationChannel.PHONE.value
    )
    customer_phone = Column(String(20), nullable=True, index=True)
    customer_email = Column(String(255), nullable=True, index=True)
    customer_name = Column(String(255), nullable=True)
    customer_info = Column(
        JSON, nullable=True, default=dict
    )  # Additional customer data

    # Conversation Metadata
    status = Column(String(50), nullable=False, default=ConversationStatus.ACTIVE.value)
    outcome = Column(String(50), nullable=True)  # ConversationOutcome
    priority = Column(Integer, default=0)  # 0=normal, 1=high, 2=urgent

    # Timing Information
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    ended_at = Column(DateTime(timezone=True), nullable=True)
    duration_seconds = Column(Integer, nullable=True)

    # Agent Performance Metrics
    first_response_time = Column(Integer, nullable=True)  # seconds to first response
    resolution_time = Column(Integer, nullable=True)  # seconds to resolution
    agent_response_count = Column(Integer, default=0)
    customer_message_count = Column(Integer, default=0)

    # Quality Metrics
    customer_satisfaction = Column(Integer, nullable=True)  # 1-5 rating
    agent_rating = Column(Integer, nullable=True)  # 1-5 rating
    quality_score = Column(Float, nullable=True)  # AI-generated quality score

    # Business Outcomes
    lead_generated = Column(Boolean, default=False)
    appointment_scheduled = Column(Boolean, default=False)
    quote_provided = Column(Boolean, default=False)
    sale_amount = Column(Float, nullable=True)  # Revenue generated

    # Technical Information
    provider_conversation_id = Column(String(255), nullable=True)  # Provider's ID
    provider_metadata = Column(JSON, nullable=True, default=dict)

    # Analysis & Insights
    sentiment_score = Column(Float, nullable=True)  # -1 to 1
    topics = Column(JSON, nullable=True, default=list)  # Extracted topics
    keywords = Column(JSON, nullable=True, default=list)  # Key phrases
    summary = Column(Text, nullable=True)  # AI-generated summary

    # Transfer & Escalation
    transferred_from_agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True)
    transfer_reason = Column(String(255), nullable=True)
    escalated_to_human = Column(Boolean, default=False)
    escalation_reason = Column(String(255), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    company = relationship("Company")
    phone_number = relationship("PhoneNumber", back_populates="conversations")
    agent = relationship(
        "Agent", back_populates="conversations", foreign_keys=[agent_id]
    )
    transferred_from_agent = relationship(
        "Agent", foreign_keys=[transferred_from_agent_id]
    )
    messages = relationship(
        "ConversationMessage",
        back_populates="conversation",
        cascade="all, delete-orphan",
    )
    events = relationship(
        "ConversationEvent", back_populates="conversation", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<Conversation(id={self.id}, conversation_id='{self.conversation_id}', channel='{self.channel}')>"


class ConversationMessage(Base):
    """
    Individual messages within a conversation.
    """

    __tablename__ = "conversation_messages"

    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False)

    # Message Content
    role = Column(String(50), nullable=False, default=MessageRole.USER.value)
    content = Column(Text, nullable=False)
    content_type = Column(String(50), default="text")  # text, audio, image, file

    # Message Metadata
    message_id = Column(String(100), nullable=True, index=True)  # External message ID
    sequence_number = Column(Integer, nullable=False)

    # Timing
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    response_time = Column(
        Integer, nullable=True
    )  # Time to generate response (for agent messages)

    # Technical Details
    provider_message_id = Column(String(255), nullable=True)
    provider_metadata = Column(JSON, nullable=True, default=dict)

    # Content Analysis
    sentiment_score = Column(Float, nullable=True)
    confidence_score = Column(Float, nullable=True)
    intent = Column(String(100), nullable=True)  # Detected intent
    entities = Column(JSON, nullable=True, default=list)  # Extracted entities

    # Media & Attachments
    media_url = Column(String(500), nullable=True)
    media_type = Column(String(50), nullable=True)  # audio, image, video, document
    media_duration = Column(Integer, nullable=True)  # For audio/video in seconds

    # Quality & Moderation
    flagged = Column(Boolean, default=False)
    flag_reason = Column(String(255), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    conversation = relationship("Conversation", back_populates="messages")

    def __repr__(self):
        return f"<ConversationMessage(id={self.id}, role='{self.role}', sequence={self.sequence_number})>"


class ConversationEvent(Base):
    """
    Events and actions that occur during conversations.
    """

    __tablename__ = "conversation_events"

    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False)

    # Event Information
    event_type = Column(
        String(50), nullable=False
    )  # call_started, agent_joined, transferred, etc.
    event_data = Column(JSON, nullable=True, default=dict)  # Event-specific data

    # Event Details
    description = Column(String(500), nullable=True)
    actor_type = Column(String(50), nullable=True)  # user, agent, system
    actor_id = Column(String(100), nullable=True)  # ID of the actor

    # Timing
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    duration = Column(Integer, nullable=True)  # Duration in seconds if applicable

    # Metadata
    extra_metadata = Column(JSON, nullable=True, default=dict)

    # Relationships
    conversation = relationship("Conversation", back_populates="events")

    def __repr__(self):
        return f"<ConversationEvent(id={self.id}, type='{self.event_type}', timestamp='{self.timestamp}')>"


class ConversationAnalytics(Base):
    """
    Aggregated analytics for conversations.
    """

    __tablename__ = "conversation_analytics"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Time Period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(
        String(20), nullable=False, default="daily"
    )  # hourly, daily, weekly, monthly

    # Volume Metrics
    total_conversations = Column(Integer, default=0)
    completed_conversations = Column(Integer, default=0)
    abandoned_conversations = Column(Integer, default=0)
    transferred_conversations = Column(Integer, default=0)

    # Channel Breakdown
    phone_conversations = Column(Integer, default=0)
    web_conversations = Column(Integer, default=0)
    sms_conversations = Column(Integer, default=0)
    chat_conversations = Column(Integer, default=0)

    # Performance Metrics
    average_duration = Column(Integer, default=0)  # seconds
    average_first_response_time = Column(Integer, default=0)  # seconds
    average_resolution_time = Column(Integer, default=0)  # seconds

    # Quality Metrics
    average_customer_satisfaction = Column(Float, nullable=True)
    average_agent_rating = Column(Float, nullable=True)
    average_quality_score = Column(Float, nullable=True)

    # Business Metrics
    leads_generated = Column(Integer, default=0)
    appointments_scheduled = Column(Integer, default=0)
    quotes_provided = Column(Integer, default=0)
    total_revenue = Column(Float, default=0.0)

    # Agent Performance
    agent_utilization = Column(Float, default=0.0)  # percentage
    escalation_rate = Column(Float, default=0.0)  # percentage

    # Popular Topics & Intents
    top_topics = Column(JSON, nullable=True, default=list)
    top_intents = Column(JSON, nullable=True, default=list)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    company = relationship("Company")

    def __repr__(self):
        return (
            f"<ConversationAnalytics(company_id={self.company_id}, date='{self.date}')>"
        )


class ConversationFeedback(Base):
    """
    Customer feedback and ratings for conversations.
    """

    __tablename__ = "conversation_feedback"

    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Feedback Content
    rating = Column(Integer, nullable=False)  # 1-5 stars
    comment = Column(Text, nullable=True)

    # Feedback Categories
    helpfulness_rating = Column(Integer, nullable=True)  # 1-5
    speed_rating = Column(Integer, nullable=True)  # 1-5
    professionalism_rating = Column(Integer, nullable=True)  # 1-5

    # Feedback Metadata
    feedback_channel = Column(String(50), nullable=True)  # sms, email, web, phone
    feedback_prompt = Column(String(255), nullable=True)  # How feedback was requested

    # Customer Information
    customer_phone = Column(String(20), nullable=True)
    customer_email = Column(String(255), nullable=True)

    # Analysis
    sentiment_score = Column(Float, nullable=True)
    topics = Column(JSON, nullable=True, default=list)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    conversation = relationship("Conversation")
    company = relationship("Company")

    def __repr__(self):
        return f"<ConversationFeedback(id={self.id}, rating={self.rating}, conversation_id={self.conversation_id})>"
