"""
Agent Schemas for API requests and responses.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field


class AgentProvider(str, Enum):
    """Supported agent providers."""

    ELEVENLABS = "elevenlabs"
    DIFY = "dify"


class AgentStatus(str, Enum):
    """Agent status options."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    TESTING = "testing"
    MAINTENANCE = "maintenance"


class TestType(str, Enum):
    """Agent test types."""

    PHONE = "phone"
    WEB = "web"
    TEXT = "text"


# Agent Variable Schemas
class AgentVariableBase(BaseModel):
    key: str = Field(..., max_length=100)
    value: Optional[str] = None
    data_type: str = Field(default="string", max_length=50)
    description: Optional[str] = None
    is_required: bool = False
    default_value: Optional[str] = None


class AgentVariableCreate(AgentVariableBase):
    pass


class AgentVariableUpdate(BaseModel):
    value: Optional[str] = None
    description: Optional[str] = None
    is_required: Optional[bool] = None
    default_value: Optional[str] = None


class AgentVariable(AgentVariableBase):
    id: int
    agent_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Agent Schemas
class AgentBase(BaseModel):
    name: str = Field(..., max_length=255)
    description: Optional[str] = None
    instructions: Optional[str] = None
    provider: AgentProvider = AgentProvider.DIFY
    provider_config: Optional[Dict[str, Any]] = None
    variables: Optional[Dict[str, Any]] = None
    status: AgentStatus = AgentStatus.ACTIVE
    is_default: bool = False
    business_hours: Optional[Dict[str, Any]] = None
    routing_rules: Optional[Dict[str, Any]] = None


class AgentCreate(AgentBase):
    company_id: int


class AgentUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    instructions: Optional[str] = None
    provider: Optional[AgentProvider] = None
    provider_config: Optional[Dict[str, Any]] = None
    variables: Optional[Dict[str, Any]] = None
    status: Optional[AgentStatus] = None
    is_default: Optional[bool] = None
    business_hours: Optional[Dict[str, Any]] = None
    routing_rules: Optional[Dict[str, Any]] = None


class Agent(AgentBase):
    id: int
    company_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)


class AgentWithDetails(Agent):
    """Agent with additional details like variables and phone numbers."""

    agent_variables: List[AgentVariable] = []
    phone_numbers: List[Dict[str, Any]] = []
    performance_metrics: Optional[Dict[str, Any]] = None


# Agent Template Schemas
class AgentTemplateBase(BaseModel):
    name: str = Field(..., max_length=255)
    description: Optional[str] = None
    category: Optional[str] = Field(None, max_length=100)
    provider: AgentProvider
    instructions: Optional[str] = None
    default_variables: Optional[Dict[str, Any]] = None
    provider_config: Optional[Dict[str, Any]] = None
    is_public: bool = True


class AgentTemplateCreate(AgentTemplateBase):
    created_by_company: Optional[int] = None


class AgentTemplate(AgentTemplateBase):
    id: int
    created_by_company: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class AgentFromTemplateRequest(BaseModel):
    template_id: int
    name: str = Field(..., max_length=255)
    description: Optional[str] = None
    variable_overrides: Optional[Dict[str, Any]] = None
    provider_config_overrides: Optional[Dict[str, Any]] = None


# Agent Testing Schemas
class AgentTestRequest(BaseModel):
    test_type: TestType
    test_input: str
    expected_output: Optional[str] = None
    test_config: Optional[Dict[str, Any]] = None


class AgentTestResult(BaseModel):
    id: int
    agent_id: int
    test_type: TestType
    status: str
    test_input: str
    expected_output: Optional[str] = None
    actual_output: Optional[str] = None
    results: Optional[Dict[str, Any]] = None
    logs: Optional[Dict[str, Any]] = None
    started_at: datetime
    completed_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Agent Performance Schemas
class AgentPerformanceMetrics(BaseModel):
    agent_id: int
    date: datetime
    period_type: str
    total_conversations: int = 0
    successful_conversations: int = 0
    failed_conversations: int = 0
    average_duration: int = 0
    bookings_created: int = 0
    leads_generated: int = 0
    customer_satisfaction: Optional[int] = None
    response_time_avg: int = 0
    error_rate: int = 0
    uptime_percentage: int = 100

    model_config = ConfigDict(from_attributes=True)


# Provider Configuration Schemas
class ElevenLabsConfig(BaseModel):
    """ElevenLabs provider configuration."""

    agent_id: str
    voice_id: Optional[str] = None
    voice_settings: Optional[Dict[str, Any]] = None
    conversation_config: Optional[Dict[str, Any]] = None
    webhook_url: Optional[str] = None


class DifyConfig(BaseModel):
    """Dify provider configuration."""

    app_id: Optional[str] = None
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    dify_model_config: Optional[Dict[str, Any]] = None
    workflow_config: Optional[Dict[str, Any]] = None


# Agent Execution Schemas
class AgentExecutionRequest(BaseModel):
    """Request to execute an agent conversation."""

    message: str
    channel: str = "web"  # phone, web, sms, chat
    customer_info: Optional[Dict[str, Any]] = None
    context: Optional[Dict[str, Any]] = None
    variables: Optional[Dict[str, Any]] = None


class AgentExecutionResponse(BaseModel):
    """Response from agent execution."""

    response: str
    conversation_id: Optional[str] = None
    agent_id: int
    channel: str
    metadata: Optional[Dict[str, Any]] = None
    next_actions: Optional[List[Dict[str, Any]]] = None


# Agent Analytics Schemas
class AgentAnalytics(BaseModel):
    """Agent analytics summary."""

    agent_id: int
    agent_name: str
    total_conversations: int
    success_rate: float
    average_response_time: float
    customer_satisfaction: Optional[float] = None
    bookings_generated: int
    leads_generated: int
    uptime_percentage: float
    last_active: Optional[datetime] = None


class AgentListResponse(BaseModel):
    """Response for agent list with pagination."""

    agents: List[Agent]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


# Bulk Operations
class BulkAgentUpdate(BaseModel):
    """Bulk update multiple agents."""

    agent_ids: List[int]
    updates: AgentUpdate


class BulkAgentStatusUpdate(BaseModel):
    """Bulk status update for multiple agents."""

    agent_ids: List[int]
    status: AgentStatus


# Response Models
class ApiResponse(BaseModel):
    """Standard API response wrapper."""

    success: bool
    message: str
    data: Optional[Any] = None
    errors: Optional[List[str]] = None
