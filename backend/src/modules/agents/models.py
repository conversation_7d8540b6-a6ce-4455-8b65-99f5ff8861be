"""
Agent Models for Home Service Platform

Supports both ElevenLabs and Dify providers with dynamic configuration.
"""

from enum import Enum as PyEnum

from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    Enum,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class AgentProvider(PyEnum):
    """Supported agent providers."""

    ELEVENLABS = "elevenlabs"
    DIFY = "dify"


class AgentStatus(PyEnum):
    """Agent status options."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    TESTING = "testing"
    MAINTENANCE = "maintenance"


class Agent(Base):
    """
    Agent model supporting multiple providers (ElevenLabs, Dify).
    Each agent can be assigned to phone numbers and configured with variables.
    """

    __tablename__ = "agents"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Basic Information
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    instructions = Column(Text, nullable=True)  # System prompt/instructions

    # Provider Configuration
    provider = Column(Enum(AgentProvider), nullable=False, default=AgentProvider.DIFY)
    provider_config = Column(JSON, nullable=True)  # Provider-specific configuration

    # Agent Variables (dynamic configuration)
    variables = Column(
        JSON, nullable=True, default=dict
    )  # Key-value pairs for agent customization

    # Status and Control
    status = Column(Enum(AgentStatus), nullable=False, default=AgentStatus.ACTIVE)
    is_default = Column(Boolean, default=False)  # Default agent for company

    # Business Rules
    business_hours = Column(JSON, nullable=True)  # Operating hours for this agent
    routing_rules = Column(JSON, nullable=True)  # Custom routing logic

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationships
    company = relationship("Company", back_populates="agents")
    phone_numbers = relationship("PhoneNumber", back_populates="agent")
    conversations = relationship(
        "Conversation", back_populates="agent", foreign_keys="[Conversation.agent_id]"
    )
    user = relationship("User", back_populates="agents", foreign_keys=[user_id])

    def __repr__(self):
        return f"<Agent(id={self.id}, name='{self.name}', provider='{self.provider.value}')>"


class AgentVariable(Base):
    """
    Agent variables for dynamic configuration.
    Allows runtime customization of agent behavior.
    """

    __tablename__ = "agent_variables"

    id = Column(Integer, primary_key=True, index=True)
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=False)

    # Variable Definition
    key = Column(String(100), nullable=False, index=True)
    value = Column(Text, nullable=True)
    data_type = Column(
        String(50), nullable=False, default="string"
    )  # string, number, boolean, json

    # Metadata
    description = Column(Text, nullable=True)
    is_required = Column(Boolean, default=False)
    default_value = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    agent = relationship("Agent")

    def __repr__(self):
        return f"<AgentVariable(agent_id={self.agent_id}, key='{self.key}', value='{self.value}')>"


class AgentTemplate(Base):
    """
    Agent templates for quick agent creation.
    Predefined configurations for common use cases.
    """

    __tablename__ = "agent_templates"

    id = Column(Integer, primary_key=True, index=True)

    # Template Information
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    category = Column(
        String(100), nullable=True
    )  # e.g., "customer_service", "booking", "support"

    # Template Configuration
    provider = Column(Enum(AgentProvider), nullable=False)
    instructions = Column(Text, nullable=True)
    default_variables = Column(JSON, nullable=True, default=dict)
    provider_config = Column(JSON, nullable=True)

    # Template Metadata
    is_public = Column(Boolean, default=True)  # Available to all companies
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<AgentTemplate(id={self.id}, name='{self.name}', provider='{self.provider.value}')>"


class AgentTestSession(Base):
    """
    Agent testing sessions for phone, web, and text testing.
    """

    __tablename__ = "agent_test_sessions"

    id = Column(Integer, primary_key=True, index=True)
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Test Configuration
    test_type = Column(String(50), nullable=False)  # "phone", "web", "text"
    test_config = Column(JSON, nullable=True)  # Test-specific configuration

    # Test Results
    status = Column(
        String(50), nullable=False, default="pending"
    )  # pending, running, completed, failed
    results = Column(JSON, nullable=True)  # Test results and metrics
    logs = Column(JSON, nullable=True)  # Detailed test logs

    # Test Data
    test_input = Column(Text, nullable=True)  # Input message/scenario
    expected_output = Column(Text, nullable=True)  # Expected response
    actual_output = Column(Text, nullable=True)  # Actual response

    # Timestamps
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    agent = relationship("Agent")
    company = relationship("Company")

    def __repr__(self):
        return f"<AgentTestSession(id={self.id}, agent_id={self.agent_id}, test_type='{self.test_type}')>"


class AgentPerformanceMetrics(Base):
    """
    Agent performance metrics and analytics.
    """

    __tablename__ = "agent_performance_metrics"

    id = Column(Integer, primary_key=True, index=True)
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=False)

    # Time Period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(
        String(20), nullable=False, default="daily"
    )  # hourly, daily, weekly, monthly

    # Performance Metrics
    total_conversations = Column(Integer, default=0)
    successful_conversations = Column(Integer, default=0)
    failed_conversations = Column(Integer, default=0)
    average_duration = Column(Integer, default=0)  # in seconds

    # Business Metrics
    bookings_created = Column(Integer, default=0)
    leads_generated = Column(Integer, default=0)
    customer_satisfaction = Column(Integer, nullable=True)  # 1-5 rating

    # Technical Metrics
    response_time_avg = Column(Integer, default=0)  # in milliseconds
    error_rate = Column(Integer, default=0)  # percentage
    uptime_percentage = Column(Integer, default=100)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    agent = relationship("Agent")

    def __repr__(self):
        return (
            f"<AgentPerformanceMetrics(agent_id={self.agent_id}, date='{self.date}')>"
        )
