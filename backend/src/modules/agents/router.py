"""
Agent API Router with dual provider support (ElevenLabs/Dify).
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import func

from core.agents.providers import (
    AgentProviderFactory,
    ProviderType,
    get_status,
    provider_manager,
    validate_config,
)
from core.db.database import get_db
from modules.auth.service import get_current_active_user
from modules.companies import crud as company_crud
from modules.users.models import User as DBUser

from . import crud, models, schemas

logger = logging.getLogger(__name__)

router = APIRouter(tags=["agents"])


@router.get("/", response_model=schemas.AgentListResponse)
async def get_agents(
    company_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    provider: Optional[schemas.AgentProvider] = None,
    status: Optional[schemas.AgentStatus] = None,
    search: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get list of agents for a company."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    agents = await crud.get_agents(
        db=db,
        company_id=company_id,
        skip=skip,
        limit=limit,
        provider=provider,
        status=status,
        search=search,
    )

    total = await crud.get_agents_count(
        db=db, company_id=company_id, provider=provider, status=status, search=search
    )

    return schemas.AgentListResponse(
        agents=agents,
        total=total,
        page=skip // limit + 1,
        per_page=limit,
        has_next=(skip + limit) < total,
        has_prev=skip > 0,
    )


@router.post("/", response_model=schemas.Agent)
async def create_agent(
    agent: schemas.AgentCreate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a new agent."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, agent.company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Validate provider configuration if provided
    if agent.provider_config:
        validation = await validate_config(
            ProviderType(agent.provider.value), agent.provider_config
        )
        if not validation["valid"]:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid provider configuration: {', '.join(validation['errors'])}",
            )

    try:
        db_agent = await crud.create_agent(
            db=db, agent=agent, created_by=current_user.id
        )
        return db_agent
    except Exception as e:
        logger.error(f"Error creating agent: {e}")
        raise HTTPException(status_code=400, detail="Failed to create agent")


@router.get("/{agent_id}", response_model=schemas.AgentWithDetails)
async def get_agent(
    agent_id: int,
    company_id: int,
    include_details: bool = Query(False),
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get a specific agent by ID."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    db_agent = await crud.get_agent(
        db=db, agent_id=agent_id, company_id=company_id, include_details=include_details
    )

    if not db_agent:
        raise HTTPException(status_code=404, detail="Agent not found")

    if include_details:
        # Get agent variables
        variables = await crud.get_agent_variables(db, agent_id, company_id)

        return schemas.AgentWithDetails(
            **db_agent.__dict__,
            agent_variables=variables,
            phone_numbers=[],  # Would be populated with actual phone numbers
            performance_metrics={},  # Would be populated with actual metrics
        )

    return db_agent


@router.put("/{agent_id}", response_model=schemas.Agent)
async def update_agent(
    agent_id: int,
    company_id: int,
    agent_update: schemas.AgentUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Update an agent."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Validate provider configuration if being updated
    if agent_update.provider_config:
        provider_type = agent_update.provider or schemas.AgentProvider.DIFY
        validation = await validate_config(
            ProviderType(provider_type.value), agent_update.provider_config
        )
        if not validation["valid"]:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid provider configuration: {', '.join(validation['errors'])}",
            )

    db_agent = await crud.update_agent(
        db=db, agent_id=agent_id, company_id=company_id, agent_update=agent_update
    )

    if not db_agent:
        raise HTTPException(status_code=404, detail="Agent not found")

    return db_agent


@router.delete("/{agent_id}")
async def delete_agent(
    agent_id: int,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Delete an agent."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    success = await crud.delete_agent(db=db, agent_id=agent_id, company_id=company_id)

    if not success:
        raise HTTPException(
            status_code=400,
            detail="Agent not found or cannot be deleted (may have assigned phone numbers)",
        )

    return {"message": "Agent deleted successfully"}


# Agent Variables Endpoints
@router.get("/{agent_id}/variables", response_model=List[schemas.AgentVariable])
async def get_agent_variables(
    agent_id: int,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get agent variables."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    variables = await crud.get_agent_variables(db, agent_id, company_id)
    return variables


@router.post("/{agent_id}/variables", response_model=schemas.AgentVariable)
async def create_agent_variable(
    agent_id: int,
    company_id: int,
    variable: schemas.AgentVariableCreate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create an agent variable."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    db_variable = await crud.create_agent_variable(
        db=db, agent_id=agent_id, company_id=company_id, variable=variable
    )

    if not db_variable:
        raise HTTPException(status_code=404, detail="Agent not found")

    return db_variable


@router.put("/{agent_id}/variables/{variable_id}", response_model=schemas.AgentVariable)
async def update_agent_variable(
    agent_id: int,
    variable_id: int,
    company_id: int,
    variable_update: schemas.AgentVariableUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Update an agent variable."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    db_variable = await crud.update_agent_variable(
        db=db,
        variable_id=variable_id,
        agent_id=agent_id,
        company_id=company_id,
        variable_update=variable_update,
    )

    if not db_variable:
        raise HTTPException(status_code=404, detail="Variable not found")

    return db_variable


@router.delete("/{agent_id}/variables/{variable_id}")
async def delete_agent_variable(
    agent_id: int,
    variable_id: int,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Delete an agent variable."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    success = await crud.delete_agent_variable(
        db=db, variable_id=variable_id, agent_id=agent_id, company_id=company_id
    )

    if not success:
        raise HTTPException(status_code=404, detail="Variable not found")

    return {"message": "Variable deleted successfully"}


# Agent Testing Endpoints
@router.post("/{agent_id}/test", response_model=schemas.AgentTestResult)
async def test_agent(
    agent_id: int,
    company_id: int,
    test_request: schemas.AgentTestRequest,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Test an agent with a message."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Get agent
    agent = await crud.get_agent(db, agent_id, company_id)
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")

    try:
        # Get provider
        provider = await provider_manager.get_provider(
            ProviderType(agent.provider.value),
            agent.provider_config or {},
            cache_key=f"agent_{agent_id}",
        )

        # Prepare agent config
        agent_config = {
            "name": agent.name,
            "instructions": agent.instructions,
            "provider_config": agent.provider_config or {},
            "variables": agent.variables or {},
        }

        # Run test
        test_result = await provider.test_agent(
            agent_config=agent_config,
            test_message=test_request.test_input,
            test_type=test_request.test_type.value,
        )

        # Create test session record
        test_session = models.AgentTestSession(
            agent_id=agent_id,
            company_id=company_id,
            test_type=test_request.test_type.value,
            test_config=test_request.test_config,
            test_input=test_request.test_input,
            expected_output=test_request.expected_output,
            actual_output=test_result.get("response", ""),
            status="completed" if test_result.get("success") else "failed",
            results=test_result,
            completed_at=func.now(),
        )

        db.add(test_session)
        await db.commit()
        await db.refresh(test_session)

        return schemas.AgentTestResult(
            id=test_session.id,
            agent_id=agent_id,
            test_type=test_request.test_type,
            status=test_session.status,
            test_input=test_request.test_input,
            expected_output=test_request.expected_output,
            actual_output=test_session.actual_output,
            results=test_result,
            started_at=test_session.started_at,
            completed_at=test_session.completed_at,
        )

    except Exception as e:
        logger.error(f"Error testing agent {agent_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Agent test failed: {str(e)}")


# Provider Status Endpoints
@router.get("/{agent_id}/provider-status")
async def get_agent_provider_status(
    agent_id: int,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get the status of the agent's provider."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Get agent
    agent = await crud.get_agent(db, agent_id, company_id)
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")

    try:
        status = await get_status(
            ProviderType(agent.provider.value), agent.provider_config or {}
        )
        return status
    except Exception as e:
        logger.error(f"Error getting provider status for agent {agent_id}: {e}")
        return {"status": "error", "error": str(e)}


# Bulk Operations
@router.put("/bulk-update")
async def bulk_update_agents(
    company_id: int,
    bulk_update: schemas.BulkAgentUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Bulk update multiple agents."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    updated_agents = []
    for agent_id in bulk_update.agent_ids:
        agent = await crud.update_agent(
            db=db,
            agent_id=agent_id,
            company_id=company_id,
            agent_update=bulk_update.updates,
        )
        if agent:
            updated_agents.append(agent)

    return {
        "message": f"Updated {len(updated_agents)} agents",
        "updated_agents": updated_agents,
    }
