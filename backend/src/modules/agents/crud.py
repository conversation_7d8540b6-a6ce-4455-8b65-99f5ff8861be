"""
Agent CRUD operations with support for ElevenLabs and Dify providers.
"""

import logging
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload

from modules.companies.models import Company

from . import models, schemas

logger = logging.getLogger(__name__)


async def get_agent(
    db: AsyncSession, agent_id: int, company_id: int, include_details: bool = False
) -> Optional[models.Agent]:
    """Get a single agent by ID."""
    query = select(models.Agent).filter(
        and_(models.Agent.id == agent_id, models.Agent.company_id == company_id)
    )

    if include_details:
        query = query.options(
            selectinload(models.Agent.phone_numbers),
            selectinload(models.Agent.conversations),
        )

    result = await db.execute(query)
    return result.scalars().first()


async def get_agents(
    db: AsyncSession,
    company_id: int,
    skip: int = 0,
    limit: int = 100,
    provider: Optional[schemas.AgentProvider] = None,
    status: Optional[schemas.AgentStatus] = None,
    search: Optional[str] = None,
) -> List[models.Agent]:
    """Get agents with filtering and pagination."""
    query = select(models.Agent).filter(models.Agent.company_id == company_id)

    # Apply filters
    if provider:
        query = query.filter(models.Agent.provider == provider)

    if status:
        query = query.filter(models.Agent.status == status)

    if search:
        query = query.filter(
            or_(
                models.Agent.name.ilike(f"%{search}%"),
                models.Agent.description.ilike(f"%{search}%"),
            )
        )

    # Apply pagination and ordering
    query = query.order_by(desc(models.Agent.created_at)).offset(skip).limit(limit)

    result = await db.execute(query)
    return result.scalars().all()


async def get_agents_count(
    db: AsyncSession,
    company_id: int,
    provider: Optional[schemas.AgentProvider] = None,
    status: Optional[schemas.AgentStatus] = None,
    search: Optional[str] = None,
) -> int:
    """Get total count of agents with filters."""
    query = select(func.count(models.Agent.id)).filter(
        models.Agent.company_id == company_id
    )

    if provider:
        query = query.filter(models.Agent.provider == provider)

    if status:
        query = query.filter(models.Agent.status == status)

    if search:
        query = query.filter(
            or_(
                models.Agent.name.ilike(f"%{search}%"),
                models.Agent.description.ilike(f"%{search}%"),
            )
        )

    result = await db.execute(query)
    return result.scalar()


async def create_agent(
    db: AsyncSession, agent: schemas.AgentCreate, created_by: Optional[int] = None
) -> models.Agent:
    """Create a new agent."""
    # Check if this is set as default and update existing default
    if agent.is_default:
        await _unset_default_agent(db, agent.company_id)

    db_agent = models.Agent(**agent.model_dump(), created_by=created_by)

    db.add(db_agent)
    await db.commit()
    await db.refresh(db_agent)

    logger.info(f"Created agent {db_agent.id} for company {agent.company_id}")
    return db_agent


async def update_agent(
    db: AsyncSession, agent_id: int, company_id: int, agent_update: schemas.AgentUpdate
) -> Optional[models.Agent]:
    """Update an existing agent."""
    db_agent = await get_agent(db, agent_id, company_id)
    if not db_agent:
        return None

    # Handle default agent logic
    if agent_update.is_default is True:
        await _unset_default_agent(db, company_id)

    # Update fields
    update_data = agent_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_agent, field, value)

    await db.commit()
    await db.refresh(db_agent)

    logger.info(f"Updated agent {agent_id}")
    return db_agent


async def delete_agent(db: AsyncSession, agent_id: int, company_id: int) -> bool:
    """Delete an agent."""
    db_agent = await get_agent(db, agent_id, company_id)
    if not db_agent:
        return False

    # Check if agent has active phone numbers or conversations
    phone_count = await db.execute(
        select(func.count())
        .select_from(
            select(models.Agent.id).join(models.Agent.phone_numbers).subquery()
        )
        .filter(models.Agent.id == agent_id)
    )

    if phone_count.scalar() > 0:
        # Don't delete if agent has assigned phone numbers
        logger.warning(f"Cannot delete agent {agent_id} - has assigned phone numbers")
        return False

    await db.delete(db_agent)
    await db.commit()

    logger.info(f"Deleted agent {agent_id}")
    return True


async def get_default_agent(
    db: AsyncSession, company_id: int
) -> Optional[models.Agent]:
    """Get the default agent for a company."""
    result = await db.execute(
        select(models.Agent).filter(
            and_(
                models.Agent.company_id == company_id,
                models.Agent.is_default == True,
                models.Agent.status == schemas.AgentStatus.ACTIVE,
            )
        )
    )
    return result.scalars().first()


async def _unset_default_agent(db: AsyncSession, company_id: int):
    """Unset the current default agent for a company."""
    result = await db.execute(
        select(models.Agent).filter(
            and_(models.Agent.company_id == company_id, models.Agent.is_default == True)
        )
    )
    current_default = result.scalars().first()

    if current_default:
        current_default.is_default = False
        await db.commit()


# Agent Variables CRUD
async def get_agent_variables(
    db: AsyncSession, agent_id: int, company_id: int
) -> List[models.AgentVariable]:
    """Get all variables for an agent."""
    # Verify agent belongs to company
    agent = await get_agent(db, agent_id, company_id)
    if not agent:
        return []

    result = await db.execute(
        select(models.AgentVariable)
        .filter(models.AgentVariable.agent_id == agent_id)
        .order_by(models.AgentVariable.key)
    )
    return result.scalars().all()


async def create_agent_variable(
    db: AsyncSession,
    agent_id: int,
    company_id: int,
    variable: schemas.AgentVariableCreate,
) -> Optional[models.AgentVariable]:
    """Create a new agent variable."""
    # Verify agent belongs to company
    agent = await get_agent(db, agent_id, company_id)
    if not agent:
        return None

    db_variable = models.AgentVariable(agent_id=agent_id, **variable.model_dump())

    db.add(db_variable)
    await db.commit()
    await db.refresh(db_variable)

    return db_variable


async def update_agent_variable(
    db: AsyncSession,
    variable_id: int,
    agent_id: int,
    company_id: int,
    variable_update: schemas.AgentVariableUpdate,
) -> Optional[models.AgentVariable]:
    """Update an agent variable."""
    # Verify agent belongs to company
    agent = await get_agent(db, agent_id, company_id)
    if not agent:
        return None

    result = await db.execute(
        select(models.AgentVariable).filter(
            and_(
                models.AgentVariable.id == variable_id,
                models.AgentVariable.agent_id == agent_id,
            )
        )
    )
    db_variable = result.scalars().first()

    if not db_variable:
        return None

    update_data = variable_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_variable, field, value)

    await db.commit()
    await db.refresh(db_variable)

    return db_variable


async def delete_agent_variable(
    db: AsyncSession, variable_id: int, agent_id: int, company_id: int
) -> bool:
    """Delete an agent variable."""
    # Verify agent belongs to company
    agent = await get_agent(db, agent_id, company_id)
    if not agent:
        return False

    result = await db.execute(
        select(models.AgentVariable).filter(
            and_(
                models.AgentVariable.id == variable_id,
                models.AgentVariable.agent_id == agent_id,
            )
        )
    )
    db_variable = result.scalars().first()

    if not db_variable:
        return False

    await db.delete(db_variable)
    await db.commit()

    return True
