import logging

from fastapi import APIRouter, HTTPException, status

from core.schemas.error import FrontendError

router = APIRouter()

logger = logging.getLogger(__name__)


@router.post("/errors", status_code=status.HTTP_204_NO_CONTENT)
async def capture_frontend_error(error: FrontendError):
    """Receives and logs frontend errors."""
    logger.error(
        f"Frontend Error: {error.message}\nStack: {error.stack}\nComponent: {error.component}\nSeverity: {error.severity}\nContext: {error.context}"
    )
    # In a real application, you might send this to a dedicated error tracking service
    # e.g., Sentry, Datadog, ELK stack
    return
