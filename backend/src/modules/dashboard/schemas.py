from datetime import datetime
from typing import Any, Optional

from pydantic import BaseModel, ConfigDict


class DashboardSummary(BaseModel):
    total_customers: int
    recent_calls_24h: int
    total_numbers: int


class CustomerInDB(BaseModel):
    id: int
    name: str
    domain: str
    phone_number: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class CallHistoryInDB(BaseModel):
    id: int
    customer_id: int
    number_id: Optional[int] = None
    call_sid: str
    call_status: str
    call_duration: Optional[int] = None
    call_metadata: Optional[dict[str, Any]] = None
    system_prompt: Optional[str] = None
    first_message: Optional[str] = None
    conversation_log: Optional[dict[str, Any]] = None
    created_at: datetime
    completed_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)
