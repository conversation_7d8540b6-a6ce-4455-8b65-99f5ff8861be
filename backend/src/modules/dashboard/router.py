from typing import List

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.service import get_current_active_user, get_current_user
from modules.users.models import User as DBUser

from . import schemas, service

router = APIRouter(prefix="/dashboard", tags=["Dashboard"])


@router.get("/summary", response_model=schemas.DashboardSummary)
async def get_summary(
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    summary = await service.get_dashboard_summary(db, current_user.id)
    return summary


@router.get("/recent-customers", response_model=List[schemas.CustomerInDB])
async def get_recent_customers(
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    customers = await service.get_recent_customers(db, user_id=current_user.id, limit=5)
    return customers


@router.get("/recent-calls", response_model=List[schemas.CallHistoryInDB])
async def get_recent_calls(
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    calls = await service.get_recent_calls(db, user_id=current_user.id, limit=5)
    return calls
