from datetime import datetime, timedelta

from sqlalchemy import func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from modules.call.models import CallHistory
from modules.customers.models import Customer
from modules.dashboard import schemas


async def get_dashboard_summary(db: AsyncSession, user_id: int):
    total_customers_result = await db.execute(
        select(func.count(Customer.id)).filter(Customer.user_id == user_id)
    )
    total_customers = total_customers_result.scalar_one()

    twenty_four_hours_ago = datetime.utcnow() - timedelta(hours=24)
    recent_calls_result = await db.execute(
        select(func.count(CallHistory.id)).filter(
            CallHistory.created_at >= twenty_four_hours_ago,
            CallHistory.user_id == user_id,
        )
    )
    recent_calls_24h = recent_calls_result.scalar_one()

    total_numbers_result = await db.execute(
        select(func.count(PhoneNumber.id)).filter(PhoneNumber.user_id == user_id)
    )
    total_numbers = total_numbers_result.scalar_one()

    return schemas.DashboardSummary(
        total_customers=total_customers,
        recent_calls_24h=recent_calls_24h,
        total_numbers=total_numbers,
    )


async def get_recent_customers(db: AsyncSession, user_id: int, limit: int = 5):
    result = await db.execute(
        select(Customer)
        .filter(Customer.user_id == user_id)
        .order_by(Customer.created_at.desc())
        .limit(limit)
    )
    return result.scalars().all()


async def get_recent_calls(db: AsyncSession, user_id: int, limit: int = 5):
    result = await db.execute(
        select(CallHistory)
        .filter(CallHistory.user_id == user_id)
        .order_by(CallHistory.created_at.desc())
        .limit(limit)
    )
    return result.scalars().all()
