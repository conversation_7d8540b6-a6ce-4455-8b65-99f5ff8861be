"""
Analytics CRUD operations for comprehensive business intelligence.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, or_, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from modules.agents.models import Agent
from modules.bookings.models import BookingRequest
from modules.call.models import CallHistory
from modules.conversations.models import Conversation, ConversationMessage
from modules.phone_numbers.models import PhoneNumber

from . import schemas

logger = logging.getLogger(__name__)


async def get_call_analytics(
    db: AsyncSession,
    company_id: int,
    start_date: datetime,
    end_date: datetime,
    agent_ids: Optional[List[int]] = None,
    phone_number_ids: Optional[List[int]] = None,
) -> schemas.CallAnalytics:
    """Get comprehensive call analytics."""

    # Base query for calls
    base_query = select(Call).filter(
        and_(
            Call.company_id == company_id,
            Call.created_at >= start_date,
            Call.created_at <= end_date,
        )
    )

    if agent_ids:
        base_query = base_query.filter(Call.agent_id.in_(agent_ids))

    if phone_number_ids:
        base_query = base_query.filter(Call.phone_number_id.in_(phone_number_ids))

    # Total calls
    total_calls_result = await db.execute(
        select(func.count()).select_from(base_query.subquery())
    )
    total_calls = total_calls_result.scalar()

    # Answered calls
    answered_calls_result = await db.execute(
        select(func.count()).select_from(
            base_query.filter(Call.status == "completed").subquery()
        )
    )
    answered_calls = answered_calls_result.scalar()

    # Missed calls
    missed_calls = total_calls - answered_calls

    # Average duration
    avg_duration_result = await db.execute(
        select(func.avg(Call.duration_seconds)).filter(
            and_(
                Call.company_id == company_id,
                Call.created_at >= start_date,
                Call.created_at <= end_date,
                Call.status == "completed",
                Call.duration_seconds.isnot(None),
            )
        )
    )
    average_duration = avg_duration_result.scalar() or 0

    # Answer rate
    answer_rate = (answered_calls / total_calls * 100) if total_calls > 0 else 0

    # Call volume by hour
    hourly_calls_result = await db.execute(
        select(
            func.extract("hour", Call.created_at).label("hour"),
            func.count(Call.id).label("count"),
        )
        .filter(
            and_(
                Call.company_id == company_id,
                Call.created_at >= start_date,
                Call.created_at <= end_date,
            )
        )
        .group_by(func.extract("hour", Call.created_at))
        .order_by(func.extract("hour", Call.created_at))
    )

    call_volume_by_hour = [
        schemas.TimeSeriesDataPoint(
            timestamp=datetime.now().replace(
                hour=int(row[0]), minute=0, second=0, microsecond=0
            ),
            value=float(row[1]),
            label=f"{int(row[0])}:00",
        )
        for row in hourly_calls_result.fetchall()
    ]

    # Call outcomes
    outcomes_result = await db.execute(
        select(Call.status, func.count(Call.id))
        .filter(
            and_(
                Call.company_id == company_id,
                Call.created_at >= start_date,
                Call.created_at <= end_date,
            )
        )
        .group_by(Call.status)
    )
    call_outcomes = {row[0]: row[1] for row in outcomes_result.fetchall()}

    return schemas.CallAnalytics(
        total_calls=total_calls,
        answered_calls=answered_calls,
        missed_calls=missed_calls,
        average_duration=round(average_duration, 2),
        answer_rate=round(answer_rate, 2),
        call_volume_by_hour=call_volume_by_hour,
        call_outcomes=call_outcomes,
    )


async def get_conversation_analytics(
    db: AsyncSession,
    company_id: int,
    start_date: datetime,
    end_date: datetime,
    agent_ids: Optional[List[int]] = None,
    phone_number_ids: Optional[List[int]] = None,
) -> schemas.ConversationAnalytics:
    """Get comprehensive conversation analytics."""

    base_query = select(Conversation).filter(
        and_(
            Conversation.company_id == company_id,
            Conversation.created_at >= start_date,
            Conversation.created_at <= end_date,
        )
    )

    if agent_ids:
        base_query = base_query.filter(Conversation.agent_id.in_(agent_ids))

    if phone_number_ids:
        base_query = base_query.filter(
            Conversation.phone_number_id.in_(phone_number_ids)
        )

    # Total conversations
    total_result = await db.execute(
        select(func.count()).select_from(base_query.subquery())
    )
    total_conversations = total_result.scalar()

    # Completed conversations
    completed_result = await db.execute(
        select(func.count()).select_from(
            base_query.filter(Conversation.status == "completed").subquery()
        )
    )
    completed_conversations = completed_result.scalar()

    # Average duration
    avg_duration_result = await db.execute(
        select(func.avg(Conversation.duration_seconds)).filter(
            and_(
                Conversation.company_id == company_id,
                Conversation.created_at >= start_date,
                Conversation.created_at <= end_date,
                Conversation.status == "completed",
                Conversation.duration_seconds.isnot(None),
            )
        )
    )
    average_duration = avg_duration_result.scalar() or 0

    # Completion rate
    completion_rate = (
        (completed_conversations / total_conversations * 100)
        if total_conversations > 0
        else 0
    )

    # Conversations by channel
    channel_result = await db.execute(
        select(Conversation.channel, func.count(Conversation.id))
        .filter(
            and_(
                Conversation.company_id == company_id,
                Conversation.created_at >= start_date,
                Conversation.created_at <= end_date,
            )
        )
        .group_by(Conversation.channel)
    )
    conversations_by_channel = {row[0]: row[1] for row in channel_result.fetchall()}

    # Daily conversation counts
    daily_result = await db.execute(
        select(
            func.date(Conversation.created_at).label("date"),
            func.count(Conversation.id).label("count"),
        )
        .filter(
            and_(
                Conversation.company_id == company_id,
                Conversation.created_at >= start_date,
                Conversation.created_at <= end_date,
            )
        )
        .group_by(func.date(Conversation.created_at))
        .order_by(func.date(Conversation.created_at))
    )

    daily_conversation_counts = [
        schemas.TimeSeriesDataPoint(
            timestamp=datetime.combine(row[0], datetime.min.time()),
            value=float(row[1]),
            label=row[0].strftime("%Y-%m-%d"),
        )
        for row in daily_result.fetchall()
    ]

    return schemas.ConversationAnalytics(
        total_conversations=total_conversations,
        completed_conversations=completed_conversations,
        average_duration=round(average_duration, 2),
        completion_rate=round(completion_rate, 2),
        conversations_by_channel=conversations_by_channel,
        daily_conversation_counts=daily_conversation_counts,
    )


async def get_booking_analytics(
    db: AsyncSession,
    company_id: int,
    start_date: datetime,
    end_date: datetime,
    agent_ids: Optional[List[int]] = None,
) -> schemas.BookingAnalytics:
    """Get comprehensive booking analytics."""

    base_query = select(Booking).filter(
        and_(
            Booking.company_id == company_id,
            Booking.created_at >= start_date,
            Booking.created_at <= end_date,
        )
    )

    if agent_ids:
        base_query = base_query.filter(Booking.agent_id.in_(agent_ids))

    # Total bookings
    total_result = await db.execute(
        select(func.count()).select_from(base_query.subquery())
    )
    total_bookings = total_result.scalar()

    # Confirmed bookings
    confirmed_result = await db.execute(
        select(func.count()).select_from(
            base_query.filter(Booking.status == "confirmed").subquery()
        )
    )
    confirmed_bookings = confirmed_result.scalar()

    # Cancelled bookings
    cancelled_result = await db.execute(
        select(func.count()).select_from(
            base_query.filter(Booking.status == "cancelled").subquery()
        )
    )
    cancelled_bookings = cancelled_result.scalar()

    # Booking rate (confirmed / total)
    booking_rate = (
        (confirmed_bookings / total_bookings * 100) if total_bookings > 0 else 0
    )

    # Revenue generated
    revenue_result = await db.execute(
        select(func.sum(Booking.estimated_price)).filter(
            and_(
                Booking.company_id == company_id,
                Booking.created_at >= start_date,
                Booking.created_at <= end_date,
                Booking.status == "confirmed",
            )
        )
    )
    revenue_generated = revenue_result.scalar() or 0

    # Bookings by service
    service_result = await db.execute(
        select(Booking.service_type, func.count(Booking.id))
        .filter(
            and_(
                Booking.company_id == company_id,
                Booking.created_at >= start_date,
                Booking.created_at <= end_date,
            )
        )
        .group_by(Booking.service_type)
    )
    bookings_by_service = {row[0]: row[1] for row in service_result.fetchall()}

    # Booking trends (daily)
    trends_result = await db.execute(
        select(
            func.date(Booking.created_at).label("date"),
            func.count(Booking.id).label("count"),
        )
        .filter(
            and_(
                Booking.company_id == company_id,
                Booking.created_at >= start_date,
                Booking.created_at <= end_date,
            )
        )
        .group_by(func.date(Booking.created_at))
        .order_by(func.date(Booking.created_at))
    )

    booking_trends = [
        schemas.TimeSeriesDataPoint(
            timestamp=datetime.combine(row[0], datetime.min.time()),
            value=float(row[1]),
            label=row[0].strftime("%Y-%m-%d"),
        )
        for row in trends_result.fetchall()
    ]

    return schemas.BookingAnalytics(
        total_bookings=total_bookings,
        confirmed_bookings=confirmed_bookings,
        cancelled_bookings=cancelled_bookings,
        booking_rate=round(booking_rate, 2),
        revenue_generated=float(revenue_generated),
        bookings_by_service=bookings_by_service,
        booking_trends=booking_trends,
    )
