"""
Analytics schemas for API responses.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel


class AnalyticsTimeframe(str, Enum):
    """Analytics timeframe options."""

    HOUR = "hour"
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    QUARTER = "quarter"
    YEAR = "year"


class MetricType(str, Enum):
    """Metric types for analytics."""

    CALLS = "calls"
    CONVERSATIONS = "conversations"
    BOOKINGS = "bookings"
    REVENUE = "revenue"
    CUSTOMER_SATISFACTION = "customer_satisfaction"
    AGENT_PERFORMANCE = "agent_performance"


class TimeSeriesDataPoint(BaseModel):
    """A single data point in a time series."""

    timestamp: datetime
    value: float
    label: Optional[str] = None


class MetricSummary(BaseModel):
    """Summary statistics for a metric."""

    current_value: float
    previous_value: Optional[float] = None
    change_percent: Optional[float] = None
    trend: Optional[str] = None  # "up", "down", "stable"


class CallAnalytics(BaseModel):
    """Call analytics data."""

    total_calls: int
    answered_calls: int
    missed_calls: int
    average_duration: float
    answer_rate: float
    call_volume_by_hour: List[TimeSeriesDataPoint]
    call_outcomes: Dict[str, int]


class ConversationAnalytics(BaseModel):
    """Conversation analytics data."""

    total_conversations: int
    completed_conversations: int
    average_duration: float
    completion_rate: float
    conversations_by_channel: Dict[str, int]
    daily_conversation_counts: List[TimeSeriesDataPoint]


class BookingAnalytics(BaseModel):
    """Booking analytics data."""

    total_bookings: int
    confirmed_bookings: int
    cancelled_bookings: int
    booking_rate: float
    revenue_generated: float
    bookings_by_service: Dict[str, int]
    booking_trends: List[TimeSeriesDataPoint]


class AgentPerformance(BaseModel):
    """Agent performance metrics."""

    agent_id: int
    agent_name: str
    total_calls: int
    total_conversations: int
    average_call_duration: float
    customer_satisfaction_score: Optional[float] = None
    bookings_generated: int
    revenue_generated: float


class CustomerSatisfactionMetrics(BaseModel):
    """Customer satisfaction metrics."""

    average_rating: float
    total_ratings: int
    rating_distribution: Dict[str, int]  # "1": count, "2": count, etc.
    satisfaction_trends: List[TimeSeriesDataPoint]


class RevenueAnalytics(BaseModel):
    """Revenue analytics data."""

    total_revenue: float
    revenue_by_service: Dict[str, float]
    revenue_trends: List[TimeSeriesDataPoint]
    average_booking_value: float


class ComprehensiveAnalytics(BaseModel):
    """Comprehensive analytics dashboard data."""

    timeframe: AnalyticsTimeframe
    start_date: datetime
    end_date: datetime

    # Summary metrics
    total_calls: MetricSummary
    total_conversations: MetricSummary
    total_bookings: MetricSummary
    total_revenue: MetricSummary

    # Detailed analytics
    call_analytics: CallAnalytics
    conversation_analytics: ConversationAnalytics
    booking_analytics: BookingAnalytics
    revenue_analytics: RevenueAnalytics

    # Performance data
    agent_performance: List[AgentPerformance]
    customer_satisfaction: CustomerSatisfactionMetrics

    # Trends and insights
    key_insights: List[str]
    recommendations: List[str]


class AnalyticsFilter(BaseModel):
    """Filters for analytics queries."""

    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    agent_ids: Optional[List[int]] = None
    phone_number_ids: Optional[List[int]] = None
    service_types: Optional[List[str]] = None
    channels: Optional[List[str]] = None


class CustomReport(BaseModel):
    """Custom analytics report configuration."""

    name: str
    description: Optional[str] = None
    metrics: List[MetricType]
    filters: AnalyticsFilter
    timeframe: AnalyticsTimeframe
    visualization_type: str  # "line", "bar", "pie", "table"


class CustomReportCreate(BaseModel):
    """Schema for creating custom reports."""

    name: str
    description: Optional[str] = None
    metrics: List[MetricType]
    filters: AnalyticsFilter
    timeframe: AnalyticsTimeframe
    visualization_type: str


class CustomReportUpdate(BaseModel):
    """Schema for updating custom reports."""

    name: Optional[str] = None
    description: Optional[str] = None
    metrics: Optional[List[MetricType]] = None
    filters: Optional[AnalyticsFilter] = None
    timeframe: Optional[AnalyticsTimeframe] = None
    visualization_type: Optional[str] = None


class AnalyticsExport(BaseModel):
    """Analytics export configuration."""

    format: str  # "csv", "xlsx", "pdf"
    metrics: List[MetricType]
    filters: AnalyticsFilter
    include_charts: bool = False
