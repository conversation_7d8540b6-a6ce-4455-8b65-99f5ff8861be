"""
Analytics & Reporting Models for comprehensive business intelligence.

Provides insights into call analytics, booking trends, and agent performance.
"""

from enum import Enum as PyEnum

from sqlalchemy import (
    DECIMAL,
    JSON,
    Boolean,
    Column,
    DateTime,
    Float,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class ReportType(PyEnum):
    """Types of analytics reports."""

    DASHBOARD = "dashboard"
    CALLS = "calls"
    BOOKINGS = "bookings"
    AGENTS = "agents"
    REVENUE = "revenue"
    CUSTOMERS = "customers"
    PERFORMANCE = "performance"
    CUSTOM = "custom"


class ReportStatus(PyEnum):
    """Report generation status."""

    PENDING = "pending"
    GENERATING = "generating"
    COMPLETED = "completed"
    FAILED = "failed"
    EXPIRED = "expired"


class MetricType(PyEnum):
    """Types of metrics."""

    COUNT = "count"
    SUM = "sum"
    AVERAGE = "average"
    PERCENTAGE = "percentage"
    RATIO = "ratio"
    TREND = "trend"


class BusinessReport(Base):
    """
    Main business intelligence reports.
    """

    __tablename__ = "business_reports"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Report Information
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    report_type = Column(String(50), nullable=False, default=ReportType.DASHBOARD.value)

    # Report Configuration
    date_range_start = Column(DateTime(timezone=True), nullable=False)
    date_range_end = Column(DateTime(timezone=True), nullable=False)
    filters = Column(JSON, nullable=True, default=dict)
    grouping = Column(JSON, nullable=True, default=list)

    # Report Data
    metrics = Column(JSON, nullable=True, default=dict)
    charts = Column(JSON, nullable=True, default=list)
    tables = Column(JSON, nullable=True, default=list)
    insights = Column(JSON, nullable=True, default=list)

    # Report Status
    status = Column(String(50), nullable=False, default=ReportStatus.PENDING.value)
    generation_started_at = Column(DateTime(timezone=True), nullable=True)
    generation_completed_at = Column(DateTime(timezone=True), nullable=True)
    generation_time_seconds = Column(Integer, nullable=True)

    # File Information
    report_file_path = Column(String(500), nullable=True)
    report_file_size = Column(Integer, nullable=True)
    report_format = Column(String(20), nullable=True)  # pdf, excel, csv

    # Sharing & Access
    is_public = Column(Boolean, default=False)
    shared_with = Column(JSON, nullable=True, default=list)
    access_token = Column(String(100), nullable=True, unique=True)

    # Scheduling
    is_scheduled = Column(Boolean, default=False)
    schedule_frequency = Column(String(50), nullable=True)  # daily, weekly, monthly
    schedule_config = Column(JSON, nullable=True, default=dict)
    next_run_at = Column(DateTime(timezone=True), nullable=True)

    # Error Information
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True, default=dict)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationships
    company = relationship("Company")
    user = relationship("User", foreign_keys=[user_id])

    def __repr__(self):
        return f"<BusinessReport(id={self.id}, name='{self.name}', type='{self.report_type}')>"


class DashboardWidget(Base):
    """
    Configurable dashboard widgets for real-time analytics.
    """

    __tablename__ = "dashboard_widgets"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Widget Information
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    widget_type = Column(String(50), nullable=False)  # metric, chart, table, list

    # Widget Configuration
    data_source = Column(String(100), nullable=False)  # calls, bookings, agents, etc.
    metric_type = Column(String(50), nullable=False, default=MetricType.COUNT.value)
    filters = Column(JSON, nullable=True, default=dict)

    # Display Configuration
    chart_type = Column(String(50), nullable=True)  # line, bar, pie, donut, etc.
    display_options = Column(JSON, nullable=True, default=dict)
    refresh_interval = Column(Integer, default=300)  # seconds

    # Layout
    position_x = Column(Integer, default=0)
    position_y = Column(Integer, default=0)
    width = Column(Integer, default=4)
    height = Column(Integer, default=3)

    # Data Cache
    cached_data = Column(JSON, nullable=True, default=dict)
    cache_updated_at = Column(DateTime(timezone=True), nullable=True)

    # Status
    is_active = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationships
    company = relationship("Company")
    user = relationship("User", foreign_keys=[user_id])

    def __repr__(self):
        return f"<DashboardWidget(id={self.id}, name='{self.name}', type='{self.widget_type}')>"


class CallAnalytics(Base):
    """
    Detailed call analytics and performance metrics.
    """

    __tablename__ = "call_analytics"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    phone_number_id = Column(Integer, ForeignKey("phone_numbers.id"), nullable=True)
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True)

    # Time Period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    hour = Column(Integer, nullable=True)  # 0-23 for hourly analytics
    day_of_week = Column(Integer, nullable=True)  # 0-6 for weekly patterns

    # Call Volume Metrics
    total_calls = Column(Integer, default=0)
    inbound_calls = Column(Integer, default=0)
    outbound_calls = Column(Integer, default=0)
    answered_calls = Column(Integer, default=0)
    missed_calls = Column(Integer, default=0)
    abandoned_calls = Column(Integer, default=0)

    # Call Duration Metrics
    total_talk_time = Column(Integer, default=0)  # seconds
    average_call_duration = Column(Integer, default=0)  # seconds
    longest_call_duration = Column(Integer, default=0)  # seconds
    shortest_call_duration = Column(Integer, nullable=True)  # seconds

    # Response Time Metrics
    average_answer_time = Column(Integer, default=0)  # seconds
    average_queue_time = Column(Integer, default=0)  # seconds
    first_call_resolution_rate = Column(Float, default=0.0)  # percentage

    # Quality Metrics
    customer_satisfaction_avg = Column(Float, nullable=True)  # 1-5 rating
    call_quality_score = Column(Float, nullable=True)  # 0-100
    agent_performance_score = Column(Float, nullable=True)  # 0-100

    # Business Outcomes
    leads_generated = Column(Integer, default=0)
    appointments_scheduled = Column(Integer, default=0)
    quotes_provided = Column(Integer, default=0)
    sales_closed = Column(Integer, default=0)
    total_revenue = Column(DECIMAL(10, 2), default=0.0)

    # Call Types & Categories
    service_calls = Column(Integer, default=0)
    sales_calls = Column(Integer, default=0)
    support_calls = Column(Integer, default=0)
    emergency_calls = Column(Integer, default=0)

    # Geographic Data
    call_locations = Column(JSON, nullable=True, default=dict)  # City/state breakdown

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    company = relationship("Company")
    phone_number = relationship("PhoneNumber")
    agent = relationship("Agent")

    def __repr__(self):
        return f"<CallAnalytics(company_id={self.company_id}, date='{self.date}')>"


class AgentPerformance(Base):
    """
    Agent performance analytics and KPIs.
    """

    __tablename__ = "agent_performance"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=False)

    # Time Period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False, default="daily")

    # Activity Metrics
    total_interactions = Column(Integer, default=0)
    calls_handled = Column(Integer, default=0)
    chats_handled = Column(Integer, default=0)
    emails_handled = Column(Integer, default=0)

    # Performance Metrics
    average_response_time = Column(Float, default=0.0)  # seconds
    average_resolution_time = Column(Float, default=0.0)  # seconds
    first_contact_resolution_rate = Column(Float, default=0.0)  # percentage

    # Quality Metrics
    customer_satisfaction_avg = Column(Float, nullable=True)  # 1-5 rating
    quality_score = Column(Float, nullable=True)  # 0-100
    accuracy_score = Column(Float, nullable=True)  # 0-100

    # Efficiency Metrics
    utilization_rate = Column(Float, default=0.0)  # percentage
    idle_time_minutes = Column(Integer, default=0)
    break_time_minutes = Column(Integer, default=0)

    # Business Impact
    leads_generated = Column(Integer, default=0)
    appointments_scheduled = Column(Integer, default=0)
    revenue_generated = Column(DECIMAL(10, 2), default=0.0)
    upsells_achieved = Column(Integer, default=0)

    # Error & Issue Tracking
    errors_made = Column(Integer, default=0)
    escalations_received = Column(Integer, default=0)
    complaints_received = Column(Integer, default=0)

    # Training & Development
    training_hours = Column(Float, default=0.0)
    certifications_earned = Column(Integer, default=0)
    skill_assessments = Column(JSON, nullable=True, default=dict)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    company = relationship("Company")
    agent = relationship("Agent")

    def __repr__(self):
        return f"<AgentPerformance(agent_id={self.agent_id}, date='{self.date}')>"


class RevenueAnalytics(Base):
    """
    Revenue and financial performance analytics.
    """

    __tablename__ = "revenue_analytics"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Time Period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False, default="daily")

    # Revenue Metrics
    total_revenue = Column(DECIMAL(12, 2), default=0.0)
    recurring_revenue = Column(DECIMAL(12, 2), default=0.0)
    one_time_revenue = Column(DECIMAL(12, 2), default=0.0)

    # Service Breakdown
    service_revenue = Column(DECIMAL(12, 2), default=0.0)
    product_revenue = Column(DECIMAL(12, 2), default=0.0)
    maintenance_revenue = Column(DECIMAL(12, 2), default=0.0)

    # Customer Metrics
    new_customer_revenue = Column(DECIMAL(12, 2), default=0.0)
    existing_customer_revenue = Column(DECIMAL(12, 2), default=0.0)
    average_order_value = Column(DECIMAL(10, 2), default=0.0)

    # Conversion Metrics
    quote_to_sale_rate = Column(Float, default=0.0)  # percentage
    lead_to_customer_rate = Column(Float, default=0.0)  # percentage
    upsell_rate = Column(Float, default=0.0)  # percentage

    # Cost Metrics
    cost_of_goods_sold = Column(DECIMAL(12, 2), default=0.0)
    labor_costs = Column(DECIMAL(12, 2), default=0.0)
    overhead_costs = Column(DECIMAL(12, 2), default=0.0)
    gross_profit = Column(DECIMAL(12, 2), default=0.0)
    gross_margin = Column(Float, default=0.0)  # percentage

    # Payment Metrics
    cash_payments = Column(DECIMAL(12, 2), default=0.0)
    credit_payments = Column(DECIMAL(12, 2), default=0.0)
    pending_payments = Column(DECIMAL(12, 2), default=0.0)
    overdue_payments = Column(DECIMAL(12, 2), default=0.0)

    # Geographic Breakdown
    revenue_by_location = Column(JSON, nullable=True, default=dict)

    # Service Category Breakdown
    revenue_by_category = Column(JSON, nullable=True, default=dict)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    company = relationship("Company")

    def __repr__(self):
        return f"<RevenueAnalytics(company_id={self.company_id}, date='{self.date}')>"


class CustomerAnalytics(Base):
    """
    Customer behavior and lifecycle analytics.
    """

    __tablename__ = "customer_analytics"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Time Period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False, default="daily")

    # Customer Volume
    total_customers = Column(Integer, default=0)
    new_customers = Column(Integer, default=0)
    returning_customers = Column(Integer, default=0)
    lost_customers = Column(Integer, default=0)

    # Customer Lifecycle
    customer_acquisition_cost = Column(DECIMAL(10, 2), default=0.0)
    customer_lifetime_value = Column(DECIMAL(10, 2), default=0.0)
    average_customer_lifespan = Column(Integer, default=0)  # days

    # Engagement Metrics
    average_interactions_per_customer = Column(Float, default=0.0)
    customer_satisfaction_avg = Column(Float, nullable=True)  # 1-5 rating
    net_promoter_score = Column(Integer, nullable=True)  # -100 to 100

    # Retention Metrics
    retention_rate = Column(Float, default=0.0)  # percentage
    churn_rate = Column(Float, default=0.0)  # percentage
    repeat_purchase_rate = Column(Float, default=0.0)  # percentage

    # Service Utilization
    services_per_customer = Column(Float, default=0.0)
    most_popular_services = Column(JSON, nullable=True, default=list)

    # Communication Preferences
    preferred_contact_methods = Column(JSON, nullable=True, default=dict)
    response_rates_by_channel = Column(JSON, nullable=True, default=dict)

    # Geographic Distribution
    customers_by_location = Column(JSON, nullable=True, default=dict)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    company = relationship("Company")

    def __repr__(self):
        return f"<CustomerAnalytics(company_id={self.company_id}, date='{self.date}')>"


class AnalyticsAlert(Base):
    """
    Automated alerts for important business metrics.
    """

    __tablename__ = "analytics_alerts"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Alert Configuration
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    metric_name = Column(String(100), nullable=False)

    # Trigger Conditions
    condition_type = Column(String(50), nullable=False)  # threshold, trend, anomaly
    threshold_value = Column(Float, nullable=True)
    comparison_operator = Column(String(10), nullable=True)  # >, <, =, >=, <=

    # Alert Settings
    is_active = Column(Boolean, default=True)
    check_frequency = Column(String(50), default="hourly")  # hourly, daily, weekly

    # Notification Settings
    notification_methods = Column(
        JSON, nullable=True, default=list
    )  # email, sms, webhook
    notification_recipients = Column(JSON, nullable=True, default=list)

    # Alert History
    last_triggered_at = Column(DateTime(timezone=True), nullable=True)
    trigger_count = Column(Integer, default=0)

    # Status
    current_value = Column(Float, nullable=True)
    last_checked_at = Column(DateTime(timezone=True), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationships
    company = relationship("Company")
    user = relationship("User", foreign_keys=[user_id])

    def __repr__(self):
        return f"<AnalyticsAlert(id={self.id}, name='{self.name}', metric='{self.metric_name}')>"
