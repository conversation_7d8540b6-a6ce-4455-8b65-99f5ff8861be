"""
Web Tools for Dify Integration

These tools can be exposed as web tools for Dify agents to use.
They provide access to business logic like job scheduling and customer management.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from core.utils.helpers import model_to_dict
from modules.customers import crud as customer_crud
from modules.jobs import crud as job_crud, schemas as job_schemas

logger = logging.getLogger(__name__)


async def schedule_job_tool(
    customer_id: int,
    service_type: str,
    scheduled_time: str,
    notes: Optional[str] = None,
    user_id: int = 1,  # Default user for now
) -> Dict[str, Any]:
    """
    Schedule a new job for a customer.

    Args:
        customer_id: ID of the customer
        service_type: Type of service to schedule
        scheduled_time: ISO format datetime string
        notes: Optional notes for the job
        user_id: User ID (default to 1 for now)

    Returns:
        Dictionary containing the created job details
    """
    logger.info(
        f"Web Tool: schedule_job for customer_id: {customer_id}, service_type: {service_type}"
    )

    try:
        # Convert scheduled_time string to datetime object
        scheduled_datetime = datetime.fromisoformat(
            scheduled_time.replace("Z", "+00:00")
        )

        async for db in get_db():
            job_schema = job_schemas.JobCreate(
                customer_id=customer_id,
                service_type=service_type,
                scheduled_time=scheduled_datetime,
                notes=notes,
            )
            job = await job_crud.create_job(db, job_schema, user_id=user_id)
            return {
                "success": True,
                "job": model_to_dict(job),
                "message": f"Job scheduled successfully for {scheduled_datetime}",
            }
    except Exception as e:
        logger.error(f"Error scheduling job: {e}")
        return {"success": False, "error": str(e), "message": "Failed to schedule job"}


async def get_customer_jobs_tool(customer_id: int, user_id: int = 1) -> Dict[str, Any]:
    """
    Get a list of all jobs for a customer.

    Args:
        customer_id: ID of the customer
        user_id: User ID (default to 1 for now)

    Returns:
        Dictionary containing the list of jobs
    """
    logger.info(f"Web Tool: get_customer_jobs for customer_id: {customer_id}")

    try:
        async for db in get_db():
            jobs = await job_crud.get_jobs_by_customer(db, customer_id, user_id=user_id)
            return {
                "success": True,
                "jobs": [model_to_dict(job) for job in jobs],
                "count": len(jobs),
            }
    except Exception as e:
        logger.error(f"Error getting customer jobs: {e}")
        return {"success": False, "error": str(e), "jobs": []}


async def get_customer_details_tool(
    customer_id: int, user_id: int = 1
) -> Dict[str, Any]:
    """
    Get details for a specific customer by their ID.

    Args:
        customer_id: ID of the customer
        user_id: User ID (default to 1 for now)

    Returns:
        Dictionary containing customer details
    """
    logger.info(f"Web Tool: get_customer_details for customer_id: {customer_id}")

    try:
        async for db in get_db():
            customer = await customer_crud.get_customer(
                db, customer_id=customer_id, user_id=user_id
            )
            if customer:
                return {"success": True, "customer": model_to_dict(customer)}
            else:
                return {
                    "success": False,
                    "error": "Customer not found",
                    "customer": None,
                }
    except Exception as e:
        logger.error(f"Error getting customer details: {e}")
        return {"success": False, "error": str(e), "customer": None}


async def get_all_customers_tool(
    user_id: int = 1, skip: int = 0, limit: int = 100
) -> Dict[str, Any]:
    """
    Get a list of all customers for a user.

    Args:
        user_id: User ID (default to 1 for now)
        skip: Number of records to skip
        limit: Maximum number of records to return

    Returns:
        Dictionary containing the list of customers
    """
    logger.info(f"Web Tool: get_all_customers for user_id: {user_id}")

    try:
        async for db in get_db():
            customers = await customer_crud.get_customers(
                db, user_id=user_id, skip=skip, limit=limit
            )
            return {
                "success": True,
                "customers": [model_to_dict(customer) for customer in customers],
                "count": len(customers),
            }
    except Exception as e:
        logger.error(f"Error getting customers: {e}")
        return {"success": False, "error": str(e), "customers": []}


# Tool registry for easy access
WEB_TOOLS = {
    "schedule_job": schedule_job_tool,
    "get_customer_jobs": get_customer_jobs_tool,
    "get_customer_details": get_customer_details_tool,
    "get_all_customers": get_all_customers_tool,
}


def get_tool_descriptions() -> Dict[str, Dict[str, Any]]:
    """
    Get descriptions of all available web tools for Dify configuration.

    Returns:
        Dictionary containing tool descriptions and parameters
    """
    return {
        "schedule_job": {
            "description": "Schedule a new job for a customer",
            "parameters": {
                "customer_id": {"type": "integer", "description": "ID of the customer"},
                "service_type": {
                    "type": "string",
                    "description": "Type of service to schedule",
                },
                "scheduled_time": {
                    "type": "string",
                    "description": "ISO format datetime string",
                },
                "notes": {
                    "type": "string",
                    "description": "Optional notes for the job",
                    "required": False,
                },
            },
        },
        "get_customer_jobs": {
            "description": "Get all jobs for a specific customer",
            "parameters": {
                "customer_id": {"type": "integer", "description": "ID of the customer"}
            },
        },
        "get_customer_details": {
            "description": "Get details for a specific customer",
            "parameters": {
                "customer_id": {"type": "integer", "description": "ID of the customer"}
            },
        },
        "get_all_customers": {
            "description": "Get list of all customers",
            "parameters": {
                "skip": {
                    "type": "integer",
                    "description": "Number of records to skip",
                    "required": False,
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of records to return",
                    "required": False,
                },
            },
        },
    }
