import logging
import time
from datetime import datetime
from typing import Any, AsyncGenerator, <PERSON>wai<PERSON>, Callable, Dict, List, Optional

from sqlalchemy.ext.asyncio import AsyncSession

from core.services.dify_service import dify_service
from core.utils.events import _log_and_send_event
from core.websocket.connection_manager import connection_manager
from modules.call import crud as call_crud
from modules.call.schemas import ConversationLogEvent
from modules.customers import crud as customer_crud

logger = logging.getLogger(__name__)


class ConversationManager:
    def __init__(self, db_session_factory: Callable[[], AsyncSession]):
        self.db_session_factory = db_session_factory
        self.start_time = time.time()

    async def handle_conversation(
        self,
        call_history_id: int,
        input_handler: Callable[[Any, Any], Awaitable[str | None]],
        output_handler: Callable[[AsyncGenerator[str, None]], Awaitable[str]],
        first_message_handler: Callable[[str], Awaitable[None]],
        get_init_message: Callable[[], Awaitable[dict]],
        websocket: Any,
        agent_config_data: Optional[Dict[str, Any]] = None,
    ):
        logger.info(f"Starting conversation for call history ID: {call_history_id}")

        # Wait for the frontend to establish its update WebSocket connection
        await connection_manager.wait_for_connection(call_history_id)

        async with self.db_session_factory() as db:
            try:
                init_message = await get_init_message()
                logger.info(f"Received init message: {init_message}")

                call_history = await call_crud.get_call_history(db, call_history_id)
                if not call_history:
                    raise Exception(f"Call history {call_history_id} not found.")

                call_context = await customer_crud.get_call_context(
                    db, call_history.customer_id, init_message.get("webhook_data")
                )
                if not call_context:
                    raise Exception(
                        f"Could not generate context for customer {call_history.customer_id}"
                    )

                # Prepare context for Dify
                user_id = str(call_history.customer_id)
                conversation_inputs = {
                    "customer_name": call_context.get("customer_info", {}).get(
                        "name", "Customer"
                    ),
                    "customer_context": call_context,
                    "system_prompt": call_history.system_prompt
                    or "You are a helpful assistant.",
                }

                first_message = (
                    call_history.first_message
                    or "Hello, {customer_name}! How can I help you today?"
                ).format(
                    customer_name=call_context.get("customer_info", {}).get(
                        "name", "there"
                    ),
                )

                # Send first message through Dify
                await first_message_handler(first_message)

                await _log_and_send_event(
                    db,
                    call_history_id,
                    ConversationLogEvent(
                        call_history_id=call_history_id,
                        event_type="agent_message",
                        timestamp=datetime.now(),
                        data={"content": first_message},
                        source="conversation_manager",
                    ),
                )

                # Create Dify conversation
                dify_conversation = await dify_service.create_conversation(
                    user_id=user_id,
                    inputs=conversation_inputs,
                    query=first_message,
                    response_mode="streaming",
                )

                conversation_id = dify_conversation.get("conversation_id")
                logger.info(f"Created Dify conversation: {conversation_id}")

                # Handle conversation loop with Dify
                while True:
                    try:
                        # Get user input
                        user_input = await input_handler(websocket, db)
                        if not user_input:
                            break

                        logger.info(f"User input: {user_input}")

                        # Log user message
                        await _log_and_send_event(
                            db,
                            call_history_id,
                            ConversationLogEvent(
                                call_history_id=call_history_id,
                                event_type="user_message",
                                timestamp=datetime.now(),
                                data={"content": user_input},
                                source="conversation_manager",
                            ),
                        )

                        # Send message to Dify and stream response
                        response_chunks = []
                        async for chunk in dify_service.send_message(
                            user_id=user_id,
                            query=user_input,
                            conversation_id=conversation_id,
                            inputs=conversation_inputs,
                            response_mode="streaming",
                        ):
                            if chunk.get("event") == "message":
                                content = chunk.get("answer", "")
                                if content:
                                    response_chunks.append(content)

                        # Combine response chunks
                        full_response = "".join(response_chunks)

                        if full_response:
                            # Send response through output handler
                            async def response_generator():
                                yield full_response

                            await output_handler(response_generator())

                            # Log agent response
                            await _log_and_send_event(
                                db,
                                call_history_id,
                                ConversationLogEvent(
                                    call_history_id=call_history_id,
                                    event_type="agent_message",
                                    timestamp=datetime.now(),
                                    data={"content": full_response},
                                    source="conversation_manager",
                                ),
                            )

                    except Exception as e:
                        logger.error(f"Error in conversation loop: {e}", exc_info=True)
                        break

            except Exception as e:
                logger.error(f"Error in conversation: {e}", exc_info=True)
            finally:
                duration = int(time.time() - self.start_time)
                await call_crud.update_call_history(
                    db, call_history_id, call_duration=duration, status="completed"
                )
                await db.commit()
                logger.info(
                    f"Conversation ended for call history ID: {call_history_id}"
                )
