"""
Dify Service Layer

This service handles all interactions with <PERSON>fy for agent orchestration,
replacing the internal agent builder and orchestration system.
"""

import asyncio
import logging
from typing import Any, AsyncGenerator, Dict, List, Optional

from dify_client import DifyClient

# ChatMessage will be imported from the appropriate module when needed
from core.config import get_settings

logger = logging.getLogger(__name__)


class DifyService:
    """Service for handling Dify API interactions."""

    def __init__(self):
        settings = get_settings()
        self.client = DifyClient(api_key=settings.DIFY_API_KEY)
        self.base_url = getattr(settings, "DIFY_BASE_URL", "https://api.dify.ai/v1")

    async def create_conversation(
        self,
        user_id: str,
        inputs: Optional[Dict[str, Any]] = None,
        query: Optional[str] = None,
        response_mode: str = "streaming",
        conversation_id: Optional[str] = None,
        files: Optional[List[Dict[str, Any]]] = None,
        auto_generate_name: bool = True,
    ) -> Dict[str, Any]:
        """
        Create a new conversation with <PERSON><PERSON>.

        Args:
            user_id: Unique identifier for the user
            inputs: Input variables for the conversation
            query: Initial query/message
            response_mode: "streaming" or "blocking"
            conversation_id: Existing conversation ID to continue
            files: List of files to include
            auto_generate_name: Whether to auto-generate conversation name

        Returns:
            Conversation response from Dify
        """
        try:
            # Prepare the request data
            data = {
                "inputs": inputs or {},
                "query": query or "",
                "response_mode": response_mode,
                "user": user_id,
                "auto_generate_name": auto_generate_name,
            }

            if conversation_id:
                data["conversation_id"] = conversation_id

            if files:
                data["files"] = files

            # Make the API call to Dify
            response = await self._make_async_request("chat-messages", data)

            logger.info(f"Created Dify conversation for user {user_id}")
            return response

        except Exception as e:
            logger.error(f"Error creating Dify conversation: {e}")
            raise

    async def send_message(
        self,
        user_id: str,
        query: str,
        conversation_id: str,
        inputs: Optional[Dict[str, Any]] = None,
        response_mode: str = "streaming",
        files: Optional[List[Dict[str, Any]]] = None,
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Send a message to an existing Dify conversation.

        Args:
            user_id: Unique identifier for the user
            query: Message to send
            conversation_id: Existing conversation ID
            inputs: Additional input variables
            response_mode: "streaming" or "blocking"
            files: List of files to include

        Yields:
            Streaming response chunks from Dify
        """
        try:
            data = {
                "inputs": inputs or {},
                "query": query,
                "response_mode": response_mode,
                "conversation_id": conversation_id,
                "user": user_id,
            }

            if files:
                data["files"] = files

            # Handle streaming response
            if response_mode == "streaming":
                async for chunk in self._stream_response("chat-messages", data):
                    yield chunk
            else:
                response = await self._make_async_request("chat-messages", data)
                yield response

        except Exception as e:
            logger.error(f"Error sending message to Dify: {e}")
            raise

    async def get_conversation_messages(
        self,
        user_id: str,
        conversation_id: str,
        first_id: Optional[str] = None,
        limit: int = 20,
    ) -> Dict[str, Any]:
        """
        Get messages from a Dify conversation.

        Args:
            user_id: Unique identifier for the user
            conversation_id: Conversation ID
            first_id: First message ID for pagination
            limit: Number of messages to retrieve

        Returns:
            Messages from the conversation
        """
        try:
            params = {
                "user": user_id,
                "conversation_id": conversation_id,
                "limit": limit,
            }

            if first_id:
                params["first_id"] = first_id

            response = await self._make_async_request(
                f"messages", method="GET", params=params
            )

            return response

        except Exception as e:
            logger.error(f"Error getting conversation messages: {e}")
            raise

    async def get_conversations(
        self,
        user_id: str,
        last_id: Optional[str] = None,
        limit: int = 20,
        pinned: Optional[bool] = None,
    ) -> Dict[str, Any]:
        """
        Get user's conversations from Dify.

        Args:
            user_id: Unique identifier for the user
            last_id: Last conversation ID for pagination
            limit: Number of conversations to retrieve
            pinned: Filter for pinned conversations

        Returns:
            List of conversations
        """
        try:
            params = {"user": user_id, "limit": limit}

            if last_id:
                params["last_id"] = last_id

            if pinned is not None:
                params["pinned"] = pinned

            response = await self._make_async_request(
                "conversations", method="GET", params=params
            )

            return response

        except Exception as e:
            logger.error(f"Error getting conversations: {e}")
            raise

    async def rename_conversation(
        self, user_id: str, conversation_id: str, name: str
    ) -> Dict[str, Any]:
        """
        Rename a Dify conversation.

        Args:
            user_id: Unique identifier for the user
            conversation_id: Conversation ID to rename
            name: New name for the conversation

        Returns:
            Updated conversation info
        """
        try:
            data = {"name": name, "user": user_id}

            response = await self._make_async_request(
                f"conversations/{conversation_id}/name", data, method="POST"
            )

            return response

        except Exception as e:
            logger.error(f"Error renaming conversation: {e}")
            raise

    async def delete_conversation(self, user_id: str, conversation_id: str) -> bool:
        """
        Delete a Dify conversation.

        Args:
            user_id: Unique identifier for the user
            conversation_id: Conversation ID to delete

        Returns:
            True if successful
        """
        try:
            data = {"user": user_id}

            await self._make_async_request(
                f"conversations/{conversation_id}", data, method="DELETE"
            )

            return True

        except Exception as e:
            logger.error(f"Error deleting conversation: {e}")
            raise

    async def _make_async_request(
        self,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        method: str = "POST",
        params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Make an async HTTP request to Dify API."""
        import httpx

        url = f"{self.base_url}/{endpoint}"
        headers = {
            "Authorization": f"Bearer {settings.DIFY_API_KEY}",
            "Content-Type": "application/json",
        }

        async with httpx.AsyncClient() as client:
            if method == "GET":
                response = await client.get(url, headers=headers, params=params)
            elif method == "POST":
                response = await client.post(url, headers=headers, json=data)
            elif method == "DELETE":
                response = await client.delete(url, headers=headers, json=data)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            response.raise_for_status()
            return response.json()

    async def _stream_response(
        self, endpoint: str, data: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream response from Dify API."""
        import json

        import httpx

        url = f"{self.base_url}/{endpoint}"
        headers = {
            "Authorization": f"Bearer {settings.DIFY_API_KEY}",
            "Content-Type": "application/json",
            "Accept": "text/event-stream",
        }

        async with httpx.AsyncClient() as client:
            async with client.stream(
                "POST", url, headers=headers, json=data
            ) as response:
                response.raise_for_status()

                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        try:
                            data_str = line[6:]  # Remove "data: " prefix
                            if data_str.strip() == "[DONE]":
                                break
                            chunk_data = json.loads(data_str)
                            yield chunk_data
                        except json.JSONDecodeError:
                            continue


# Global instance
dify_service = DifyService()
