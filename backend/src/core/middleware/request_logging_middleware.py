import json
import logging
import time

from starlette.requests import Request

logger = logging.getLogger(__name__)


async def request_logging_middleware(request: Request, call_next):
    if request.url.path == "/health":
        return await call_next(request)

    start_time = time.time()

    response = await call_next(request)

    process_time = time.time() - start_time

    response_body = b""
    async for chunk in response.body_iterator:
        response_body += chunk

    log_dict = {
        "request": {
            "method": request.method,
            "url": str(request.url),
            "headers": dict(request.headers),
        },
        "response": {
            "status_code": response.status_code,
            "process_time_seconds": f"{process_time:.4f}",
            "body": response_body.decode(),
        },
    }

    logger.info(json.dumps(log_dict))

    # After reading the body, we need to create a new iterator for the response
    async def new_body_iterator():
        yield response_body

    response.body_iterator = new_body_iterator()

    return response
