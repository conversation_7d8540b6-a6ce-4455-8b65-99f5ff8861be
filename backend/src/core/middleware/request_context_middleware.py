import uuid

from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request

from core.utils.request_context import request_id_var


class RequestContextMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        request_id = request.headers.get("X-Request-ID")
        if not request_id:
            request_id = str(uuid.uuid4())

        token = request_id_var.set(request_id)

        response = await call_next(request)
        response.headers["X-Request-ID"] = request_id_var.get()

        request_id_var.reset(token)

        return response
