from functools import lru_cache

from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """
    Manages application settings with validation.
    Reads from a .env file and environment variables.
    """

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )

    # Core application settings
    BASE_URL: str
    ENVIRONMENT: str = "dev"
    PORT: int = 8000

    # Database
    DATABASE_URL: str
    TEST_DATABASE_URL: str = "sqlite+aiosqlite:///./test.db"

    # Security
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Redis
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0

    # Twilio
    TWILIO_ACCOUNT_SID: str
    TWILIO_AUTH_TOKEN: str
    TWILIO_PHONE_NUMBER: str

    # ElevenLabs
    ELEVENLABS_API_KEY: str
    ELEVENLABS_VOICE_ID: str
    ELEVENLABS_AGENT_ID: str
    ELEVENLABS_PHONE_NUMBER_ID: str

    # LLM Providers
    LLM_PROVIDER: str = "gemini"
    OPENAI_API_KEY: str
    GEMINI_API_KEY: str
    USE_LANGCHAIN_AGENT: bool = False

    # Dify Configuration
    DIFY_API_KEY: str
    DIFY_BASE_URL: str = "https://api.dify.ai/v1"

    # Frontend
    FRONTEND_URLS: str = "http://localhost:5173,http://localhost:3000"


@lru_cache()
def get_settings() -> Settings:
    settings = Settings()
    # Manually parse the FRONTEND_URLS string into a list
    if isinstance(settings.FRONTEND_URLS, str):
        settings.FRONTEND_URLS = [
            url.strip() for url in settings.FRONTEND_URLS.split(",")
        ]
    return settings
