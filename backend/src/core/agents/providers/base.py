"""
Base agent provider interface for ElevenLabs and Dify integration.
"""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, AsyncGenerator, Dict, List, Optional

from pydantic import BaseModel


class ChannelType(str, Enum):
    """Communication channel types."""

    PHONE = "phone"
    WEB = "web"
    SMS = "sms"
    CHAT = "chat"
    EMAIL = "email"


class ConversationStatus(str, Enum):
    """Conversation status options."""

    ACTIVE = "active"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"


class AgentMessage(BaseModel):
    """Standard message format across providers."""

    role: str  # "user", "assistant", "system"
    content: str
    timestamp: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class ConversationContext(BaseModel):
    """Conversation context passed to agents."""

    conversation_id: Optional[str] = None
    customer_info: Optional[Dict[str, Any]] = None
    channel: ChannelType
    session_data: Optional[Dict[str, Any]] = None
    business_context: Optional[Dict[str, Any]] = None


class AgentResponse(BaseModel):
    """Standard agent response format."""

    message: str
    conversation_id: Optional[str] = None
    status: ConversationStatus = ConversationStatus.ACTIVE
    metadata: Optional[Dict[str, Any]] = None
    next_actions: Optional[List[Dict[str, Any]]] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None


class StreamingResponse(BaseModel):
    """Streaming response chunk."""

    chunk: str
    is_final: bool = False
    metadata: Optional[Dict[str, Any]] = None


class BaseAgentProvider(ABC):
    """
    Abstract base class for agent providers.

    This defines the interface that all agent providers (ElevenLabs, Dify)
    must implement to ensure consistent behavior across the platform.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.provider_name = self.__class__.__name__.lower().replace("provider", "")

    @abstractmethod
    async def initialize(self) -> bool:
        """
        Initialize the provider with configuration.

        Returns:
            bool: True if initialization successful, False otherwise
        """
        pass

    @abstractmethod
    async def create_conversation(
        self,
        agent_config: Dict[str, Any],
        context: ConversationContext,
        initial_message: Optional[str] = None,
    ) -> str:
        """
        Create a new conversation with the agent.

        Args:
            agent_config: Agent-specific configuration
            context: Conversation context
            initial_message: Optional initial message

        Returns:
            str: Conversation ID
        """
        pass

    @abstractmethod
    async def send_message(
        self,
        conversation_id: str,
        message: str,
        context: Optional[ConversationContext] = None,
    ) -> AgentResponse:
        """
        Send a message to the agent and get response.

        Args:
            conversation_id: Conversation ID
            message: User message
            context: Optional conversation context

        Returns:
            AgentResponse: Agent's response
        """
        pass

    @abstractmethod
    async def stream_message(
        self,
        conversation_id: str,
        message: str,
        context: Optional[ConversationContext] = None,
    ) -> AsyncGenerator[StreamingResponse, None]:
        """
        Send a message and stream the response.

        Args:
            conversation_id: Conversation ID
            message: User message
            context: Optional conversation context

        Yields:
            StreamingResponse: Response chunks
        """
        pass

    @abstractmethod
    async def end_conversation(
        self, conversation_id: str, reason: Optional[str] = None
    ) -> bool:
        """
        End a conversation.

        Args:
            conversation_id: Conversation ID
            reason: Optional reason for ending

        Returns:
            bool: True if successful
        """
        pass

    @abstractmethod
    async def get_conversation_history(
        self, conversation_id: str
    ) -> List[AgentMessage]:
        """
        Get conversation message history.

        Args:
            conversation_id: Conversation ID

        Returns:
            List[AgentMessage]: Message history
        """
        pass

    @abstractmethod
    async def test_agent(
        self, agent_config: Dict[str, Any], test_message: str, test_type: str = "text"
    ) -> Dict[str, Any]:
        """
        Test an agent configuration.

        Args:
            agent_config: Agent configuration
            test_message: Test message
            test_type: Type of test (text, phone, web)

        Returns:
            Dict[str, Any]: Test results
        """
        pass

    @abstractmethod
    async def validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate provider-specific configuration.

        Args:
            config: Configuration to validate

        Returns:
            Dict[str, Any]: Validation results with errors if any
        """
        pass

    @abstractmethod
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """
        Get list of available models/agents from the provider.

        Returns:
            List[Dict[str, Any]]: Available models
        """
        pass

    @abstractmethod
    async def get_provider_status(self) -> Dict[str, Any]:
        """
        Get provider service status and health.

        Returns:
            Dict[str, Any]: Provider status information
        """
        pass

    # Helper methods that can be overridden
    def supports_channel(self, channel: ChannelType) -> bool:
        """
        Check if provider supports a specific channel.

        Args:
            channel: Channel type to check

        Returns:
            bool: True if supported
        """
        return True  # Default: support all channels

    def get_supported_channels(self) -> List[ChannelType]:
        """
        Get list of supported channels.

        Returns:
            List[ChannelType]: Supported channels
        """
        return [ChannelType.PHONE, ChannelType.WEB, ChannelType.SMS, ChannelType.CHAT]

    def format_agent_config(self, agent_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format agent configuration for this provider.

        Args:
            agent_data: Raw agent data

        Returns:
            Dict[str, Any]: Provider-specific configuration
        """
        return agent_data

    def extract_metadata(self, response: Any) -> Dict[str, Any]:
        """
        Extract metadata from provider response.

        Args:
            response: Provider response

        Returns:
            Dict[str, Any]: Extracted metadata
        """
        return {}

    async def cleanup(self):
        """Clean up provider resources."""
        pass

    def __repr__(self):
        return f"<{self.__class__.__name__}(provider='{self.provider_name}')>"
