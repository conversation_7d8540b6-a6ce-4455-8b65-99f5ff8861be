"""
Dify Agent Provider

Integrates with Dify platform for text/chat-based agents with workflow capabilities.
"""

import json
import logging
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional

import httpx

from core.config import get_settings

from .base import (
    AgentMessage,
    AgentResponse,
    BaseAgentProvider,
    ChannelType,
    ConversationContext,
    ConversationStatus,
    StreamingResponse,
)

logger = logging.getLogger(__name__)
settings = get_settings()


class DifyProvider(BaseAgentProvider):
    """
    Dify platform provider for text/chat agents with workflow capabilities.

    Supports web chat, SMS, email, and text-based interactions.
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config.get("api_key") or settings.DIFY_API_KEY
        self.base_url = config.get("base_url") or settings.DIFY_BASE_URL
        self.app_id = config.get("app_id")
        self.client = None

    async def initialize(self) -> bool:
        """Initialize Dify client and validate configuration."""
        try:
            self.client = httpx.AsyncClient(
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json",
                },
                timeout=30.0,
            )

            # Test API connection by getting app info
            if self.app_id:
                response = await self.client.get(f"{self.base_url}/apps/{self.app_id}")
                if response.status_code == 200:
                    logger.info("Dify provider initialized successfully")
                    return True
                else:
                    logger.error(f"Dify API test failed: {response.status_code}")
                    return False
            else:
                logger.info("Dify provider initialized (no app_id specified)")
                return True

        except Exception as e:
            logger.error(f"Failed to initialize Dify provider: {e}")
            return False

    async def create_conversation(
        self,
        agent_config: Dict[str, Any],
        context: ConversationContext,
        initial_message: Optional[str] = None,
    ) -> str:
        """Create a new conversation with Dify agent."""
        try:
            # Extract Dify-specific config
            dify_config = agent_config.get("provider_config", {})
            app_id = dify_config.get("app_id", self.app_id)

            if not app_id:
                raise ValueError("Dify app_id is required")

            # Prepare conversation inputs
            inputs = {
                "customer_info": context.customer_info or {},
                "channel": context.channel.value,
                "session_data": context.session_data or {},
                "business_context": context.business_context or {},
            }

            # Add agent variables to inputs
            variables = agent_config.get("variables", {})
            inputs.update(variables)

            # Create conversation
            conversation_data = {
                "inputs": inputs,
                "query": initial_message or "Hello",
                "response_mode": "streaming",
                "user": str(context.customer_info.get("id", "anonymous")),
                "auto_generate_name": True,
            }

            response = await self.client.post(
                f"{self.base_url}/chat-messages", json=conversation_data
            )

            if response.status_code == 200:
                data = response.json()
                conversation_id = data.get("conversation_id")
                logger.info(f"Created Dify conversation: {conversation_id}")
                return conversation_id
            else:
                logger.error(
                    f"Failed to create Dify conversation: {response.status_code}"
                )
                raise Exception(f"Dify API error: {response.status_code}")

        except Exception as e:
            logger.error(f"Failed to create Dify conversation: {e}")
            raise

    async def send_message(
        self,
        conversation_id: str,
        message: str,
        context: Optional[ConversationContext] = None,
    ) -> AgentResponse:
        """Send message to Dify agent."""
        try:
            # Prepare message data
            message_data = {
                "inputs": {},
                "query": message,
                "response_mode": "blocking",
                "conversation_id": conversation_id,
                "user": str(context.customer_info.get("id", "anonymous"))
                if context
                else "anonymous",
            }

            response = await self.client.post(
                f"{self.base_url}/chat-messages", json=message_data
            )

            if response.status_code == 200:
                data = response.json()
                return AgentResponse(
                    message=data.get("answer", ""),
                    conversation_id=conversation_id,
                    status=ConversationStatus.ACTIVE,
                    metadata={
                        "message_id": data.get("id"),
                        "usage": data.get("metadata", {}).get("usage", {}),
                        "retriever_resources": data.get("metadata", {}).get(
                            "retriever_resources", []
                        ),
                    },
                )
            else:
                logger.error(f"Dify message failed: {response.status_code}")
                raise Exception(f"Dify API error: {response.status_code}")

        except Exception as e:
            logger.error(f"Failed to send message to Dify: {e}")
            raise

    async def stream_message(
        self,
        conversation_id: str,
        message: str,
        context: Optional[ConversationContext] = None,
    ) -> AsyncGenerator[StreamingResponse, None]:
        """Stream response from Dify agent."""
        try:
            # Prepare message data for streaming
            message_data = {
                "inputs": {},
                "query": message,
                "response_mode": "streaming",
                "conversation_id": conversation_id,
                "user": str(context.customer_info.get("id", "anonymous"))
                if context
                else "anonymous",
            }

            async with self.client.stream(
                "POST",
                f"{self.base_url}/chat-messages",
                json=message_data,
                headers={"Accept": "text/event-stream"},
            ) as response:
                if response.status_code == 200:
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            try:
                                data_str = line[6:]  # Remove "data: " prefix
                                if data_str.strip() == "[DONE]":
                                    yield StreamingResponse(
                                        chunk="",
                                        is_final=True,
                                        metadata={"status": "completed"},
                                    )
                                    break

                                chunk_data = json.loads(data_str)
                                event_type = chunk_data.get("event")

                                if event_type == "message":
                                    yield StreamingResponse(
                                        chunk=chunk_data.get("answer", ""),
                                        is_final=False,
                                        metadata=chunk_data.get("metadata", {}),
                                    )
                                elif event_type == "message_end":
                                    yield StreamingResponse(
                                        chunk="",
                                        is_final=True,
                                        metadata=chunk_data.get("metadata", {}),
                                    )

                            except json.JSONDecodeError:
                                continue
                else:
                    logger.error(f"Dify streaming failed: {response.status_code}")

        except Exception as e:
            logger.error(f"Failed to stream from Dify: {e}")
            raise

    async def end_conversation(
        self, conversation_id: str, reason: Optional[str] = None
    ) -> bool:
        """End Dify conversation."""
        try:
            # Dify doesn't have explicit conversation ending
            # We can optionally send a final message or just log
            logger.info(f"Ending Dify conversation {conversation_id}, reason: {reason}")
            return True

        except Exception as e:
            logger.error(f"Failed to end Dify conversation: {e}")
            return False

    async def get_conversation_history(
        self, conversation_id: str
    ) -> List[AgentMessage]:
        """Get conversation history from Dify."""
        try:
            response = await self.client.get(
                f"{self.base_url}/messages",
                params={
                    "conversation_id": conversation_id,
                    "user": "system",  # Use system user for history retrieval
                    "limit": 100,
                },
            )

            if response.status_code == 200:
                data = response.json()
                messages = []
                for msg in data.get("data", []):
                    messages.append(
                        AgentMessage(
                            role="user"
                            if msg.get("from_source") == "user"
                            else "assistant",
                            content=msg.get("query") or msg.get("answer", ""),
                            timestamp=msg.get("created_at"),
                            metadata={
                                "message_id": msg.get("id"),
                                "from_source": msg.get("from_source"),
                            },
                        )
                    )
                return messages
            else:
                logger.error(f"Failed to get Dify history: {response.status_code}")
                return []

        except Exception as e:
            logger.error(f"Failed to get Dify conversation history: {e}")
            return []

    async def test_agent(
        self, agent_config: Dict[str, Any], test_message: str, test_type: str = "text"
    ) -> Dict[str, Any]:
        """Test Dify agent configuration."""
        try:
            start_time = datetime.utcnow()

            # Create test context
            context = ConversationContext(
                channel=ChannelType.WEB,
                customer_info={"id": "test_user", "test": True},
                session_data={"test_type": test_type},
            )

            # Create conversation
            conversation_id = await self.create_conversation(
                agent_config, context, test_message
            )

            # Send test message (if different from initial)
            if test_message != "Hello":
                response = await self.send_message(
                    conversation_id, test_message, context
                )
            else:
                # Get the initial response
                response = AgentResponse(
                    message="Test conversation created successfully",
                    conversation_id=conversation_id,
                    status=ConversationStatus.ACTIVE,
                )

            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()

            return {
                "success": True,
                "response": response.message,
                "conversation_id": conversation_id,
                "duration_seconds": duration,
                "test_type": test_type,
                "metadata": response.metadata,
            }

        except Exception as e:
            logger.error(f"Dify agent test failed: {e}")
            return {"success": False, "error": str(e), "test_type": test_type}

    async def validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate Dify configuration."""
        errors = []
        warnings = []

        # Check API key
        if not config.get("api_key") and not self.api_key:
            errors.append("api_key is required for Dify provider")

        # Check app_id for specific app usage
        if not config.get("app_id") and not self.app_id:
            warnings.append("app_id not specified - using default Dify configuration")

        # Validate workflow config if present
        workflow_config = config.get("workflow_config", {})
        if workflow_config and not isinstance(workflow_config, dict):
            errors.append("workflow_config must be a dictionary")

        return {"valid": len(errors) == 0, "errors": errors, "warnings": warnings}

    async def get_available_models(self) -> List[Dict[str, Any]]:
        """Get available Dify apps/models."""
        try:
            # Get available apps
            response = await self.client.get(f"{self.base_url}/apps")
            apps = []

            if response.status_code == 200:
                data = response.json()
                for app in data.get("data", []):
                    apps.append(
                        {
                            "id": app.get("id"),
                            "name": app.get("name"),
                            "category": "workflow",
                            "description": app.get("description"),
                            "mode": app.get("mode"),
                        }
                    )

            return apps

        except Exception as e:
            logger.error(f"Failed to get Dify models: {e}")
            return []

    async def get_provider_status(self) -> Dict[str, Any]:
        """Get Dify service status."""
        try:
            # Test basic API connectivity
            response = await self.client.get(f"{self.base_url}/apps")

            if response.status_code == 200:
                return {
                    "status": "healthy",
                    "api_accessible": True,
                    "base_url": self.base_url,
                }
            else:
                return {
                    "status": "unhealthy",
                    "api_accessible": False,
                    "error": f"API returned {response.status_code}",
                }

        except Exception as e:
            return {"status": "error", "api_accessible": False, "error": str(e)}

    def supports_channel(self, channel: ChannelType) -> bool:
        """Check if Dify supports the channel."""
        return channel in [
            ChannelType.WEB,
            ChannelType.SMS,
            ChannelType.CHAT,
            ChannelType.EMAIL,
        ]

    def get_supported_channels(self) -> List[ChannelType]:
        """Get supported channels for Dify."""
        return [ChannelType.WEB, ChannelType.SMS, ChannelType.CHAT, ChannelType.EMAIL]

    async def cleanup(self):
        """Clean up Dify provider resources."""
        if self.client:
            await self.client.aclose()
            logger.info("Dify provider cleaned up")
