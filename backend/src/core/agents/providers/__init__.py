"""
Agent Provider Factory and Manager

Handles creation and management of different agent providers (ElevenLabs, Dify).
"""

import logging
from enum import Enum
from typing import Any, Dict, Optional, Type

from .base import BaseAgentProvider
from .dify_provider import DifyProvider
from .elevenlabs_provider import ElevenLabsProvider

logger = logging.getLogger(__name__)


class ProviderType(str, Enum):
    """Available agent provider types."""

    ELEVENLABS = "elevenlabs"
    DIFY = "dify"


class AgentProviderFactory:
    """Factory for creating agent providers."""

    _providers: Dict[ProviderType, Type[BaseAgentProvider]] = {
        ProviderType.ELEVENLABS: ElevenLabsProvider,
        ProviderType.DIFY: DifyProvider,
    }

    @classmethod
    def create_provider(
        cls, provider_type: ProviderType, config: Dict[str, Any]
    ) -> BaseAgentProvider:
        """
        Create an agent provider instance.

        Args:
            provider_type: Type of provider to create
            config: Provider configuration

        Returns:
            BaseAgentProvider: Provider instance

        Raises:
            ValueError: If provider type is not supported
        """
        if provider_type not in cls._providers:
            raise ValueError(f"Unsupported provider type: {provider_type}")

        provider_class = cls._providers[provider_type]
        return provider_class(config)

    @classmethod
    def get_supported_providers(cls) -> list[ProviderType]:
        """Get list of supported provider types."""
        return list(cls._providers.keys())

    @classmethod
    def register_provider(
        cls, provider_type: ProviderType, provider_class: Type[BaseAgentProvider]
    ):
        """Register a new provider type."""
        cls._providers[provider_type] = provider_class
        logger.info(f"Registered provider: {provider_type}")


class AgentProviderManager:
    """
    Manager for agent providers with caching and lifecycle management.
    """

    def __init__(self):
        self._provider_cache: Dict[str, BaseAgentProvider] = {}
        self._initialized_providers: Dict[str, bool] = {}

    async def get_provider(
        self,
        provider_type: ProviderType,
        config: Dict[str, Any],
        cache_key: Optional[str] = None,
    ) -> BaseAgentProvider:
        """
        Get or create a provider instance.

        Args:
            provider_type: Type of provider
            config: Provider configuration
            cache_key: Optional cache key for reusing providers

        Returns:
            BaseAgentProvider: Provider instance
        """
        # Generate cache key if not provided
        if cache_key is None:
            cache_key = f"{provider_type.value}_{hash(str(sorted(config.items())))}"

        # Check cache first
        if cache_key in self._provider_cache:
            provider = self._provider_cache[cache_key]

            # Ensure provider is initialized
            if cache_key not in self._initialized_providers:
                await self._initialize_provider(provider, cache_key)

            return provider

        # Create new provider
        provider = AgentProviderFactory.create_provider(provider_type, config)

        # Cache the provider
        self._provider_cache[cache_key] = provider

        # Initialize the provider
        await self._initialize_provider(provider, cache_key)

        return provider

    async def _initialize_provider(self, provider: BaseAgentProvider, cache_key: str):
        """Initialize a provider and mark as initialized."""
        try:
            success = await provider.initialize()
            if success:
                self._initialized_providers[cache_key] = True
                logger.info(f"Provider {cache_key} initialized successfully")
            else:
                logger.error(f"Failed to initialize provider {cache_key}")
                # Remove from cache if initialization failed
                self._provider_cache.pop(cache_key, None)
                raise Exception(f"Provider initialization failed: {cache_key}")

        except Exception as e:
            logger.error(f"Error initializing provider {cache_key}: {e}")
            # Clean up on error
            self._provider_cache.pop(cache_key, None)
            self._initialized_providers.pop(cache_key, None)
            raise

    async def remove_provider(self, cache_key: str):
        """Remove a provider from cache and clean up resources."""
        if cache_key in self._provider_cache:
            provider = self._provider_cache[cache_key]
            try:
                await provider.cleanup()
            except Exception as e:
                logger.error(f"Error cleaning up provider {cache_key}: {e}")

            # Remove from cache
            self._provider_cache.pop(cache_key, None)
            self._initialized_providers.pop(cache_key, None)

            logger.info(f"Removed provider from cache: {cache_key}")

    async def cleanup_all(self):
        """Clean up all cached providers."""
        for cache_key in list(self._provider_cache.keys()):
            await self.remove_provider(cache_key)

        logger.info("All providers cleaned up")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "cached_providers": len(self._provider_cache),
            "initialized_providers": len(self._initialized_providers),
            "provider_types": list(
                set(key.split("_")[0] for key in self._provider_cache.keys())
            ),
        }

    async def validate_provider_config(
        self, provider_type: ProviderType, config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate provider configuration without creating/caching the provider.

        Args:
            provider_type: Type of provider
            config: Configuration to validate

        Returns:
            Dict[str, Any]: Validation results
        """
        try:
            # Create temporary provider for validation
            provider = AgentProviderFactory.create_provider(provider_type, config)
            return await provider.validate_config(config)

        except Exception as e:
            return {
                "valid": False,
                "errors": [f"Provider creation failed: {str(e)}"],
                "warnings": [],
            }

    async def get_provider_status(
        self, provider_type: ProviderType, config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Get provider status without caching.

        Args:
            provider_type: Type of provider
            config: Provider configuration

        Returns:
            Dict[str, Any]: Provider status
        """
        try:
            provider = AgentProviderFactory.create_provider(provider_type, config)
            await provider.initialize()
            status = await provider.get_provider_status()
            await provider.cleanup()
            return status

        except Exception as e:
            return {"status": "error", "error": str(e)}


# Global provider manager instance
provider_manager = AgentProviderManager()


# Convenience functions
async def get_provider(
    provider_type: ProviderType, config: Dict[str, Any], cache_key: Optional[str] = None
) -> BaseAgentProvider:
    """Get a provider instance using the global manager."""
    return await provider_manager.get_provider(provider_type, config, cache_key)


async def validate_config(
    provider_type: ProviderType, config: Dict[str, Any]
) -> Dict[str, Any]:
    """Validate provider configuration using the global manager."""
    return await provider_manager.validate_provider_config(provider_type, config)


async def get_status(
    provider_type: ProviderType, config: Dict[str, Any]
) -> Dict[str, Any]:
    """Get provider status using the global manager."""
    return await provider_manager.get_provider_status(provider_type, config)
