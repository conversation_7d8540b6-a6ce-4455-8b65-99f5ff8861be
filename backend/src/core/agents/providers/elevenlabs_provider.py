"""
ElevenLabs Agent Provider

Integrates with ElevenLabs Conversational AI for voice-based agents.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional

import httpx

from core.config import get_settings

from .base import (
    AgentMessage,
    AgentResponse,
    BaseAgentProvider,
    ChannelType,
    ConversationContext,
    ConversationStatus,
    StreamingResponse,
)

logger = logging.getLogger(__name__)
settings = get_settings()


class ElevenLabsProvider(BaseAgentProvider):
    """
    ElevenLabs Conversational AI provider for voice agents.

    Supports phone calls, web voice chat, and voice-based interactions.
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config.get("api_key") or settings.ELEVENLABS_API_KEY
        self.base_url = "https://api.elevenlabs.io/v1"
        self.agent_id = config.get("agent_id")
        self.phone_number_id = config.get("phone_number_id")
        self.voice_id = config.get("voice_id")
        self.client = None

    async def initialize(self) -> bool:
        """Initialize ElevenLabs client and validate configuration."""
        try:
            self.client = httpx.AsyncClient(
                headers={
                    "xi-api-key": self.api_key,
                    "Content-Type": "application/json",
                },
                timeout=30.0,
            )

            # Test API connection
            response = await self.client.get(f"{self.base_url}/user")
            if response.status_code == 200:
                logger.info("ElevenLabs provider initialized successfully")
                return True
            else:
                logger.error(f"ElevenLabs API test failed: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"Failed to initialize ElevenLabs provider: {e}")
            return False

    async def create_conversation(
        self,
        agent_config: Dict[str, Any],
        context: ConversationContext,
        initial_message: Optional[str] = None,
    ) -> str:
        """Create a new conversation with ElevenLabs agent."""
        try:
            # Extract ElevenLabs-specific config
            elevenlabs_config = agent_config.get("provider_config", {})
            agent_id = elevenlabs_config.get("agent_id", self.agent_id)

            if not agent_id:
                raise ValueError("ElevenLabs agent_id is required")

            # Prepare conversation data
            conversation_data = {
                "agent_id": agent_id,
                "customer_info": context.customer_info or {},
                "channel": context.channel.value,
                "session_data": context.session_data or {},
            }

            if initial_message:
                conversation_data["initial_message"] = initial_message

            # For phone calls, use the phone number integration
            if context.channel == ChannelType.PHONE:
                return await self._create_phone_conversation(conversation_data)
            else:
                return await self._create_web_conversation(conversation_data)

        except Exception as e:
            logger.error(f"Failed to create ElevenLabs conversation: {e}")
            raise

    async def send_message(
        self,
        conversation_id: str,
        message: str,
        context: Optional[ConversationContext] = None,
    ) -> AgentResponse:
        """Send message to ElevenLabs agent."""
        try:
            # For phone conversations, handle differently
            if context and context.channel == ChannelType.PHONE:
                return await self._handle_phone_message(
                    conversation_id, message, context
                )

            # For web/text conversations
            response = await self.client.post(
                f"{self.base_url}/convai/conversations/{conversation_id}/messages",
                json={"message": message, "timestamp": datetime.utcnow().isoformat()},
            )

            if response.status_code == 200:
                data = response.json()
                return AgentResponse(
                    message=data.get("response", ""),
                    conversation_id=conversation_id,
                    status=ConversationStatus.ACTIVE,
                    metadata=data.get("metadata", {}),
                )
            else:
                logger.error(f"ElevenLabs message failed: {response.status_code}")
                raise Exception(f"ElevenLabs API error: {response.status_code}")

        except Exception as e:
            logger.error(f"Failed to send message to ElevenLabs: {e}")
            raise

    async def stream_message(
        self,
        conversation_id: str,
        message: str,
        context: Optional[ConversationContext] = None,
    ) -> AsyncGenerator[StreamingResponse, None]:
        """Stream response from ElevenLabs agent."""
        try:
            async with self.client.stream(
                "POST",
                f"{self.base_url}/convai/conversations/{conversation_id}/stream",
                json={"message": message, "timestamp": datetime.utcnow().isoformat()},
            ) as response:
                if response.status_code == 200:
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            try:
                                data = json.loads(line[6:])
                                if data.get("type") == "message_chunk":
                                    yield StreamingResponse(
                                        chunk=data.get("content", ""),
                                        is_final=data.get("is_final", False),
                                        metadata=data.get("metadata", {}),
                                    )
                            except json.JSONDecodeError:
                                continue
                else:
                    logger.error(f"ElevenLabs streaming failed: {response.status_code}")

        except Exception as e:
            logger.error(f"Failed to stream from ElevenLabs: {e}")
            raise

    async def end_conversation(
        self, conversation_id: str, reason: Optional[str] = None
    ) -> bool:
        """End ElevenLabs conversation."""
        try:
            response = await self.client.post(
                f"{self.base_url}/convai/conversations/{conversation_id}/end",
                json={"reason": reason or "user_ended"},
            )
            return response.status_code == 200

        except Exception as e:
            logger.error(f"Failed to end ElevenLabs conversation: {e}")
            return False

    async def get_conversation_history(
        self, conversation_id: str
    ) -> List[AgentMessage]:
        """Get conversation history from ElevenLabs."""
        try:
            response = await self.client.get(
                f"{self.base_url}/convai/conversations/{conversation_id}/history"
            )

            if response.status_code == 200:
                data = response.json()
                messages = []
                for msg in data.get("messages", []):
                    messages.append(
                        AgentMessage(
                            role=msg.get("role", "user"),
                            content=msg.get("content", ""),
                            timestamp=msg.get("timestamp"),
                            metadata=msg.get("metadata", {}),
                        )
                    )
                return messages
            else:
                logger.error(
                    f"Failed to get ElevenLabs history: {response.status_code}"
                )
                return []

        except Exception as e:
            logger.error(f"Failed to get ElevenLabs conversation history: {e}")
            return []

    async def test_agent(
        self, agent_config: Dict[str, Any], test_message: str, test_type: str = "text"
    ) -> Dict[str, Any]:
        """Test ElevenLabs agent configuration."""
        try:
            start_time = datetime.utcnow()

            # Create test context
            context = ConversationContext(
                channel=ChannelType.WEB if test_type == "web" else ChannelType.PHONE,
                customer_info={"test": True},
                session_data={"test_type": test_type},
            )

            # Create conversation
            conversation_id = await self.create_conversation(agent_config, context)

            # Send test message
            response = await self.send_message(conversation_id, test_message, context)

            # End conversation
            await self.end_conversation(conversation_id, "test_completed")

            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()

            return {
                "success": True,
                "response": response.message,
                "conversation_id": conversation_id,
                "duration_seconds": duration,
                "test_type": test_type,
                "metadata": response.metadata,
            }

        except Exception as e:
            logger.error(f"ElevenLabs agent test failed: {e}")
            return {"success": False, "error": str(e), "test_type": test_type}

    async def validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate ElevenLabs configuration."""
        errors = []
        warnings = []

        # Check required fields
        if not config.get("agent_id"):
            errors.append("agent_id is required for ElevenLabs provider")

        # Check API key
        if not config.get("api_key") and not self.api_key:
            errors.append("api_key is required for ElevenLabs provider")

        # Validate voice settings
        voice_settings = config.get("voice_settings", {})
        if voice_settings:
            if (
                voice_settings.get("stability", 0) < 0
                or voice_settings.get("stability", 0) > 1
            ):
                warnings.append("voice_settings.stability should be between 0 and 1")

            if (
                voice_settings.get("similarity_boost", 0) < 0
                or voice_settings.get("similarity_boost", 0) > 1
            ):
                warnings.append(
                    "voice_settings.similarity_boost should be between 0 and 1"
                )

        return {"valid": len(errors) == 0, "errors": errors, "warnings": warnings}

    async def get_available_models(self) -> List[Dict[str, Any]]:
        """Get available ElevenLabs agents/voices."""
        try:
            # Get available voices
            response = await self.client.get(f"{self.base_url}/voices")
            voices = []

            if response.status_code == 200:
                data = response.json()
                for voice in data.get("voices", []):
                    voices.append(
                        {
                            "id": voice.get("voice_id"),
                            "name": voice.get("name"),
                            "category": voice.get("category", "voice"),
                            "language": voice.get("labels", {}).get("language"),
                            "description": voice.get("description"),
                        }
                    )

            return voices

        except Exception as e:
            logger.error(f"Failed to get ElevenLabs models: {e}")
            return []

    async def get_provider_status(self) -> Dict[str, Any]:
        """Get ElevenLabs service status."""
        try:
            response = await self.client.get(f"{self.base_url}/user")

            if response.status_code == 200:
                user_data = response.json()
                return {
                    "status": "healthy",
                    "api_accessible": True,
                    "user_info": {
                        "subscription": user_data.get("subscription"),
                        "character_count": user_data.get("character_count"),
                        "character_limit": user_data.get("character_limit"),
                    },
                }
            else:
                return {
                    "status": "unhealthy",
                    "api_accessible": False,
                    "error": f"API returned {response.status_code}",
                }

        except Exception as e:
            return {"status": "error", "api_accessible": False, "error": str(e)}

    def supports_channel(self, channel: ChannelType) -> bool:
        """Check if ElevenLabs supports the channel."""
        return channel in [ChannelType.PHONE, ChannelType.WEB, ChannelType.CHAT]

    def get_supported_channels(self) -> List[ChannelType]:
        """Get supported channels for ElevenLabs."""
        return [ChannelType.PHONE, ChannelType.WEB, ChannelType.CHAT]

    async def _create_phone_conversation(
        self, conversation_data: Dict[str, Any]
    ) -> str:
        """Create phone conversation with ElevenLabs."""
        # Implementation for phone-specific conversation creation
        # This would integrate with ElevenLabs phone API
        conversation_id = f"phone_{datetime.utcnow().timestamp()}"
        logger.info(f"Created ElevenLabs phone conversation: {conversation_id}")
        return conversation_id

    async def _create_web_conversation(self, conversation_data: Dict[str, Any]) -> str:
        """Create web conversation with ElevenLabs."""
        # Implementation for web-specific conversation creation
        conversation_id = f"web_{datetime.utcnow().timestamp()}"
        logger.info(f"Created ElevenLabs web conversation: {conversation_id}")
        return conversation_id

    async def _handle_phone_message(
        self, conversation_id: str, message: str, context: ConversationContext
    ) -> AgentResponse:
        """Handle phone-specific message processing."""
        # Phone-specific logic would go here
        # For now, return a basic response
        return AgentResponse(
            message="Phone response from ElevenLabs agent",
            conversation_id=conversation_id,
            status=ConversationStatus.ACTIVE,
            metadata={"channel": "phone"},
        )

    async def cleanup(self):
        """Clean up ElevenLabs provider resources."""
        if self.client:
            await self.client.aclose()
            logger.info("ElevenLabs provider cleaned up")
