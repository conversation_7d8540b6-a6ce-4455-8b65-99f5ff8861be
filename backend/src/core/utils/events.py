import json
import logging

from sqlalchemy.ext.asyncio import AsyncSession

from core.websocket.connection_manager import connection_manager
from modules.call import crud as call_crud
from modules.call.schemas import ConversationLogEvent

logger = logging.getLogger(__name__)


async def _log_and_send_event(
    db: AsyncSession, call_history_id: int, event_data: ConversationLogEvent
):
    try:
        await call_crud.create_conversation_log(db, event_data)
        await connection_manager.send_personal_message(
            json.dumps(
                {"type": "conversation_log", "log": event_data.model_dump(mode="json")}
            ),
            call_history_id,
        )
    except Exception as e:
        logger.error(f"Error logging and sending event: {e}", exc_info=True)
        await db.rollback()
