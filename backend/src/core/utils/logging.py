import logging
import sys

from pythonjsonlogger import jsonlogger


def setup_logging():
    """
    Sets up structured JSON logging.
    """
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Use a handler that outputs to stdout
    logHandler = logging.StreamHandler(sys.stdout)

    # Create a JSON formatter
    formatter = jsonlogger.JsonFormatter(
        "%(asctime)s %(name)s %(levelname)s %(pathname)s %(lineno)d %(message)s"
    )

    logHandler.setFormatter(formatter)

    # Clear existing handlers and add the new one
    if logger.hasHandlers():
        logger.handlers.clear()
    logger.addHandler(logHandler)
