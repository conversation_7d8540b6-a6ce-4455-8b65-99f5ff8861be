from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class FrontendError(BaseModel):
    message: str = Field(..., description="Error message")
    stack: Optional[str] = Field(None, description="Error stack trace")
    component: Optional[str] = Field(
        None, description="Frontend component where error occurred"
    )
    user_info: Optional[Dict[str, Any]] = Field(
        None, description="User-related information"
    )
    timestamp: Optional[str] = Field(None, description="Timestamp of the error")
    severity: Optional[str] = Field(
        "error",
        description="Severity level of the error (e.g., 'error', 'warning', 'info')",
    )
    context: Optional[Dict[str, Any]] = Field(
        None, description="Additional context for the error"
    )
