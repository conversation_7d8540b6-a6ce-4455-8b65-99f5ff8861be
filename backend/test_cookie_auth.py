#!/usr/bin/env python3
"""
Test script to verify cookie-based authentication is working.
"""

import sys

import requests


def test_cookie_authentication():
    """Test the complete cookie authentication flow."""
    base_url = "http://localhost:8000"

    print("🔐 Testing Cookie Authentication Flow")
    print("=" * 50)

    # Create a session to maintain cookies
    session = requests.Session()

    # Step 1: Try to access protected endpoint without authentication
    print("\n1. Testing protected endpoint without authentication...")
    try:
        response = session.post(f"{base_url}/api/agents/")
        print(f"   Status: {response.status_code}")
        if response.status_code == 401:
            print("   ✅ Correctly returns 401 Unauthorized")
        else:
            print(f"   ❌ Expected 401, got {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

    # Step 2: Login to get authentication cookie
    print("\n2. Testing login to get authentication cookie...")
    login_data = {
        "username": "<EMAIL>",  # Default test user
        "password": "testpassword123",
    }

    try:
        response = session.post(
            f"{base_url}/api/auth/token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )
        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            print("   ✅ Login successful")

            # Check if cookie was set
            cookies = session.cookies
            if "access_token" in cookies:
                print("   ✅ access_token cookie set")
                cookie = cookies["access_token"]
                print(
                    f"   Cookie value: {cookie[:20]}..."
                    if len(cookie) > 20
                    else f"   Cookie value: {cookie}"
                )
            else:
                print("   ❌ access_token cookie not found")
                print(f"   Available cookies: {list(cookies.keys())}")
                return False

        elif response.status_code == 401:
            print("   ❌ Login failed - Invalid credentials")
            print("   Note: Make sure test user exists or create one first")
            return False
        else:
            print(f"   ❌ Unexpected status code: {response.status_code}")
            print(f"   Response: {response.text}")
            return False

    except Exception as e:
        print(f"   ❌ Login error: {e}")
        return False

    # Step 3: Try to access protected endpoint with authentication cookie
    print("\n3. Testing protected endpoint with authentication cookie...")
    try:
        response = session.post(f"{base_url}/api/agents/")
        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            print("   ✅ Successfully accessed protected endpoint")
            data = response.json()
            print(f"   Response type: {type(data)}")
            if isinstance(data, list):
                print(f"   Number of agents: {len(data)}")
            return True
        elif response.status_code == 401:
            print("   ❌ Still getting 401 - Cookie authentication not working")
            return False
        else:
            print(f"   ❌ Unexpected status code: {response.status_code}")
            print(f"   Response: {response.text}")
            return False

    except Exception as e:
        print(f"   ❌ Protected endpoint error: {e}")
        return False


def test_cookie_properties():
    """Test that cookies have the correct properties."""
    print("\n4. Testing cookie properties...")

    session = requests.Session()

    # Login first
    login_data = {"username": "<EMAIL>", "password": "testpassword123"}

    try:
        response = session.post(
            "http://localhost:8000/api/auth/token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )

        if response.status_code != 200:
            print("   ❌ Cannot test cookie properties - login failed")
            return False

        # Check cookie properties
        cookies = session.cookies
        if "access_token" in cookies:
            cookie = cookies["access_token"]
            print(f"   ✅ Cookie found: access_token")
            print(f"   Path: {getattr(cookie, 'path', 'Not set')}")
            print(f"   Domain: {getattr(cookie, 'domain', 'Not set')}")
            print(f"   Secure: {getattr(cookie, 'secure', 'Not set')}")
            print(
                f"   HttpOnly: {getattr(cookie, 'has_nonstandard_attr', lambda x: False)('HttpOnly')}"
            )
            return True
        else:
            print("   ❌ No access_token cookie found")
            return False

    except Exception as e:
        print(f"   ❌ Cookie properties test error: {e}")
        return False


def main():
    """Run all authentication tests."""
    print("Starting authentication tests...")
    print("Make sure the backend server is running on http://localhost:8000")
    print()

    # Test basic authentication flow
    auth_test = test_cookie_authentication()

    # Test cookie properties
    cookie_test = test_cookie_properties()

    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"✅ Authentication Flow: {'PASS' if auth_test else 'FAIL'}")
    print(f"✅ Cookie Properties: {'PASS' if cookie_test else 'FAIL'}")

    if auth_test and cookie_test:
        print("\n🎉 All authentication tests passed!")
        print("\n💡 Troubleshooting tips if frontend still has issues:")
        print("   1. Check browser dev tools for cookie in Application tab")
        print("   2. Verify CORS settings allow credentials")
        print("   3. Check if frontend is sending withCredentials: true")
        print("   4. Ensure frontend and backend are on same domain/port for dev")
        return 0
    else:
        print("\n⚠️  Some authentication tests failed.")
        print("\n🔧 Common fixes:")
        print("   1. Create test user: POST /api/auth/register")
        print("   2. Check backend server is running")
        print("   3. Verify database connection")
        print("   4. Check authentication service configuration")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
