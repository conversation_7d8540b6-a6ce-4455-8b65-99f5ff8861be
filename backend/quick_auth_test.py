#!/usr/bin/env python3
"""
Quick authentication test - verifies the complete auth flow works.
"""

import sys

import requests


def quick_auth_test():
    """Quick test of authentication flow."""
    print("🔐 Quick Authentication Test")
    print("=" * 40)

    base_url = "http://localhost:8000"
    session = requests.Session()

    # Test 1: Login
    print("1. Testing login...")
    login_data = {"username": "<EMAIL>", "password": "testpassword123"}

    try:
        response = session.post(
            f"{base_url}/api/auth/token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )

        if response.status_code == 200:
            print("   ✅ Login successful")
            if "access_token" in session.cookies:
                print("   ✅ Cookie set")
            else:
                print("   ❌ No cookie found")
                return False
        else:
            print(f"   ❌ Login failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False

    except Exception as e:
        print(f"   ❌ Login error: {e}")
        return False

    # Test 2: Access protected endpoint
    print("2. Testing protected endpoint...")
    try:
        response = session.get(f"{base_url}/api/agents/?limit=100")

        if response.status_code == 200:
            print("   ✅ Protected endpoint accessible")
            return True
        elif response.status_code == 401:
            print("   ❌ Still getting 401 - auth not working")
            return False
        else:
            print(f"   ❌ Unexpected status: {response.status_code}")
            return False

    except Exception as e:
        print(f"   ❌ Protected endpoint error: {e}")
        return False


def main():
    """Run the quick test."""
    print("Make sure backend server is running on http://localhost:8000\n")

    success = quick_auth_test()

    print("\n" + "=" * 40)
    if success:
        print("🎉 Authentication is working!")
        print("\n✅ What this means:")
        print("   • Login sets HTTP-only cookie")
        print("   • Cookie is sent with subsequent requests")
        print("   • Protected endpoints are accessible")
        print("   • Frontend should work with authentication")

        print("\n🔧 If frontend still has issues:")
        print("   • Check browser dev tools for cookies")
        print("   • Verify frontend uses withCredentials: true")
        print("   • Check CORS settings")
        return 0
    else:
        print("❌ Authentication test failed!")
        print("\n🔧 Troubleshooting:")
        print("   • Make sure backend server is running")
        print("   • Check if test user was created in startup logs")
        print("   • Verify database connection")
        print("   • Check authentication service configuration")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
