from datetime import datetime
from typing import List, Optional, Dict, Any

from pydantic import BaseModel


class StoreConnectionTest(BaseModel):
    success: bool
    message: str
    store_info: Optional[dict] = None


class StoreBase(BaseModel):
    name: str
    platform: str = "shopify"
    api_key: Optional[str] = None
    api_secret_key: Optional[str] = None
    admin_access_token: Optional[str] = None
    storefront_access_token: Optional[str] = None
    shop_domain: Optional[str] = None
    shop_id: Optional[str] = None
    shop_name: Optional[str] = None
    is_active: bool = True


class StoreCreate(StoreBase):
    pass


class Store(StoreBase):
    id: int
    owner_id: int
    last_sync: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class FieldDetail(BaseModel):
    name: str
    type: str

class ActionDetail(BaseModel):
    name: str
    payload_type: str
    input_type: Optional[str] = None
    payload_fields: List[FieldDetail] = []
    input_fields: List[FieldDetail] = []

class StoreMappingResponse(BaseModel):
    store_type: str
    queries: List[ActionDetail]
    mutations: List[ActionDetail]
