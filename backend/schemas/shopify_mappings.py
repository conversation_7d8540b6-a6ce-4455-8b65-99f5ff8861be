# This file is auto-generated by generate_shopify_api.py
# Do not modify it directly.

from typing import Dict, Any, Type, List
from models import shopify_models
from schemas import shopify_sgqlc_schema

MUTATION_PAYLOAD_MAP: Dict[str, Dict[str, Any]] = {
    'abandonmentEmailStateUpdate': {'payload_type': shopify_sgqlc_schema.AbandonmentEmailStateUpdatePayload, 'input_type': None},
    'abandonmentUpdateActivitiesDeliveryStatuses': {'payload_type': shopify_sgqlc_schema.AbandonmentUpdateActivitiesDeliveryStatusesPayload, 'input_type': None},
    'appPurchaseOneTimeCreate': {'payload_type': shopify_sgqlc_schema.AppPurchaseOneTimeCreatePayload, 'input_type': None},
    'appRevokeAccessScopes': {'payload_type': shopify_sgqlc_schema.AppRevokeAccessScopesPayload, 'input_type': None},
    'appSubscriptionCancel': {'payload_type': shopify_sgqlc_schema.AppSubscriptionCancelPayload, 'input_type': None},
    'appSubscriptionCreate': {'payload_type': shopify_sgqlc_schema.AppSubscriptionCreatePayload, 'input_type': None},
    'appSubscriptionLineItemUpdate': {'payload_type': shopify_sgqlc_schema.AppSubscriptionLineItemUpdatePayload, 'input_type': None},
    'appSubscriptionTrialExtend': {'payload_type': shopify_sgqlc_schema.AppSubscriptionTrialExtendPayload, 'input_type': None},
    'appUsageRecordCreate': {'payload_type': shopify_sgqlc_schema.AppUsageRecordCreatePayload, 'input_type': None},
    'articleCreate': {'payload_type': shopify_sgqlc_schema.ArticleCreatePayload, 'input_type': None},
    'articleDelete': {'payload_type': shopify_sgqlc_schema.ArticleDeletePayload, 'input_type': None},
    'articleUpdate': {'payload_type': shopify_sgqlc_schema.ArticleUpdatePayload, 'input_type': None},
    'blogCreate': {'payload_type': shopify_sgqlc_schema.BlogCreatePayload, 'input_type': None},
    'blogDelete': {'payload_type': shopify_sgqlc_schema.BlogDeletePayload, 'input_type': None},
    'blogUpdate': {'payload_type': shopify_sgqlc_schema.BlogUpdatePayload, 'input_type': None},
    'bulkOperationCancel': {'payload_type': shopify_sgqlc_schema.BulkOperationCancelPayload, 'input_type': None},
    'bulkOperationRunMutation': {'payload_type': shopify_sgqlc_schema.BulkOperationRunMutationPayload, 'input_type': None},
    'bulkOperationRunQuery': {'payload_type': shopify_sgqlc_schema.BulkOperationRunQueryPayload, 'input_type': None},
    'bulkProductResourceFeedbackCreate': {'payload_type': shopify_sgqlc_schema.BulkProductResourceFeedbackCreatePayload, 'input_type': None},
    'carrierServiceCreate': {'payload_type': shopify_sgqlc_schema.CarrierServiceCreatePayload, 'input_type': shopify_sgqlc_schema.DeliveryCarrierServiceCreateInput},
    'carrierServiceDelete': {'payload_type': shopify_sgqlc_schema.CarrierServiceDeletePayload, 'input_type': None},
    'carrierServiceUpdate': {'payload_type': shopify_sgqlc_schema.CarrierServiceUpdatePayload, 'input_type': shopify_sgqlc_schema.DeliveryCarrierServiceUpdateInput},
    'cartTransformCreate': {'payload_type': shopify_sgqlc_schema.CartTransformCreatePayload, 'input_type': None},
    'cartTransformDelete': {'payload_type': shopify_sgqlc_schema.CartTransformDeletePayload, 'input_type': None},
    'catalogContextUpdate': {'payload_type': shopify_sgqlc_schema.CatalogContextUpdatePayload, 'input_type': None},
    'catalogCreate': {'payload_type': shopify_sgqlc_schema.CatalogCreatePayload, 'input_type': shopify_sgqlc_schema.CatalogCreateInput},
    'catalogDelete': {'payload_type': shopify_sgqlc_schema.CatalogDeletePayload, 'input_type': None},
    'catalogUpdate': {'payload_type': shopify_sgqlc_schema.CatalogUpdatePayload, 'input_type': shopify_sgqlc_schema.CatalogUpdateInput},
    'checkoutBrandingUpsert': {'payload_type': shopify_sgqlc_schema.CheckoutBrandingUpsertPayload, 'input_type': None},
    'collectionAddProducts': {'payload_type': shopify_sgqlc_schema.CollectionAddProductsPayload, 'input_type': None},
    'collectionAddProductsV2': {'payload_type': shopify_sgqlc_schema.CollectionAddProductsV2Payload, 'input_type': None},
    'collectionCreate': {'payload_type': shopify_sgqlc_schema.CollectionCreatePayload, 'input_type': shopify_sgqlc_schema.CollectionInput},
    'collectionDelete': {'payload_type': shopify_sgqlc_schema.CollectionDeletePayload, 'input_type': shopify_sgqlc_schema.CollectionDeleteInput},
    'collectionPublish': {'payload_type': shopify_sgqlc_schema.CollectionPublishPayload, 'input_type': shopify_sgqlc_schema.CollectionPublishInput},
    'collectionRemoveProducts': {'payload_type': shopify_sgqlc_schema.CollectionRemoveProductsPayload, 'input_type': None},
    'collectionReorderProducts': {'payload_type': shopify_sgqlc_schema.CollectionReorderProductsPayload, 'input_type': None},
    'collectionUnpublish': {'payload_type': shopify_sgqlc_schema.CollectionUnpublishPayload, 'input_type': shopify_sgqlc_schema.CollectionUnpublishInput},
    'collectionUpdate': {'payload_type': shopify_sgqlc_schema.CollectionUpdatePayload, 'input_type': shopify_sgqlc_schema.CollectionInput},
    'combinedListingUpdate': {'payload_type': shopify_sgqlc_schema.CombinedListingUpdatePayload, 'input_type': None},
    'commentApprove': {'payload_type': shopify_sgqlc_schema.CommentApprovePayload, 'input_type': None},
    'commentDelete': {'payload_type': shopify_sgqlc_schema.CommentDeletePayload, 'input_type': None},
    'commentNotSpam': {'payload_type': shopify_sgqlc_schema.CommentNotSpamPayload, 'input_type': None},
    'commentSpam': {'payload_type': shopify_sgqlc_schema.CommentSpamPayload, 'input_type': None},
    'companiesDelete': {'payload_type': shopify_sgqlc_schema.CompaniesDeletePayload, 'input_type': None},
    'companyAddressDelete': {'payload_type': shopify_sgqlc_schema.CompanyAddressDeletePayload, 'input_type': None},
    'companyAssignCustomerAsContact': {'payload_type': shopify_sgqlc_schema.CompanyAssignCustomerAsContactPayload, 'input_type': None},
    'companyAssignMainContact': {'payload_type': shopify_sgqlc_schema.CompanyAssignMainContactPayload, 'input_type': None},
    'companyContactAssignRole': {'payload_type': shopify_sgqlc_schema.CompanyContactAssignRolePayload, 'input_type': None},
    'companyContactAssignRoles': {'payload_type': shopify_sgqlc_schema.CompanyContactAssignRolesPayload, 'input_type': None},
    'companyContactCreate': {'payload_type': shopify_sgqlc_schema.CompanyContactCreatePayload, 'input_type': shopify_sgqlc_schema.CompanyContactInput},
    'companyContactDelete': {'payload_type': shopify_sgqlc_schema.CompanyContactDeletePayload, 'input_type': None},
    'companyContactRemoveFromCompany': {'payload_type': shopify_sgqlc_schema.CompanyContactRemoveFromCompanyPayload, 'input_type': None},
    'companyContactRevokeRole': {'payload_type': shopify_sgqlc_schema.CompanyContactRevokeRolePayload, 'input_type': None},
    'companyContactRevokeRoles': {'payload_type': shopify_sgqlc_schema.CompanyContactRevokeRolesPayload, 'input_type': None},
    'companyContactSendWelcomeEmail': {'payload_type': shopify_sgqlc_schema.CompanyContactSendWelcomeEmailPayload, 'input_type': None},
    'companyContactUpdate': {'payload_type': shopify_sgqlc_schema.CompanyContactUpdatePayload, 'input_type': shopify_sgqlc_schema.CompanyContactInput},
    'companyContactsDelete': {'payload_type': shopify_sgqlc_schema.CompanyContactsDeletePayload, 'input_type': None},
    'companyCreate': {'payload_type': shopify_sgqlc_schema.CompanyCreatePayload, 'input_type': shopify_sgqlc_schema.CompanyCreateInput},
    'companyDelete': {'payload_type': shopify_sgqlc_schema.CompanyDeletePayload, 'input_type': None},
    'companyLocationAssignAddress': {'payload_type': shopify_sgqlc_schema.CompanyLocationAssignAddressPayload, 'input_type': None},
    'companyLocationAssignRoles': {'payload_type': shopify_sgqlc_schema.CompanyLocationAssignRolesPayload, 'input_type': None},
    'companyLocationAssignStaffMembers': {'payload_type': shopify_sgqlc_schema.CompanyLocationAssignStaffMembersPayload, 'input_type': None},
    'companyLocationAssignTaxExemptions': {'payload_type': shopify_sgqlc_schema.CompanyLocationAssignTaxExemptionsPayload, 'input_type': None},
    'companyLocationCreate': {'payload_type': shopify_sgqlc_schema.CompanyLocationCreatePayload, 'input_type': shopify_sgqlc_schema.CompanyLocationInput},
    'companyLocationCreateTaxRegistration': {'payload_type': shopify_sgqlc_schema.CompanyLocationCreateTaxRegistrationPayload, 'input_type': None},
    'companyLocationDelete': {'payload_type': shopify_sgqlc_schema.CompanyLocationDeletePayload, 'input_type': None},
    'companyLocationRemoveStaffMembers': {'payload_type': shopify_sgqlc_schema.CompanyLocationRemoveStaffMembersPayload, 'input_type': None},
    'companyLocationRevokeRoles': {'payload_type': shopify_sgqlc_schema.CompanyLocationRevokeRolesPayload, 'input_type': None},
    'companyLocationRevokeTaxExemptions': {'payload_type': shopify_sgqlc_schema.CompanyLocationRevokeTaxExemptionsPayload, 'input_type': None},
    'companyLocationRevokeTaxRegistration': {'payload_type': shopify_sgqlc_schema.CompanyLocationRevokeTaxRegistrationPayload, 'input_type': None},
    'companyLocationUpdate': {'payload_type': shopify_sgqlc_schema.CompanyLocationUpdatePayload, 'input_type': shopify_sgqlc_schema.CompanyLocationUpdateInput},
    'companyLocationsDelete': {'payload_type': shopify_sgqlc_schema.CompanyLocationsDeletePayload, 'input_type': None},
    'companyRevokeMainContact': {'payload_type': shopify_sgqlc_schema.CompanyRevokeMainContactPayload, 'input_type': None},
    'companyUpdate': {'payload_type': shopify_sgqlc_schema.CompanyUpdatePayload, 'input_type': shopify_sgqlc_schema.CompanyInput},
    'customerAddTaxExemptions': {'payload_type': shopify_sgqlc_schema.CustomerAddTaxExemptionsPayload, 'input_type': None},
    'customerCancelDataErasure': {'payload_type': shopify_sgqlc_schema.CustomerCancelDataErasurePayload, 'input_type': None},
    'customerCreate': {'payload_type': shopify_sgqlc_schema.CustomerCreatePayload, 'input_type': shopify_sgqlc_schema.CustomerInput},
    'customerDelete': {'payload_type': shopify_sgqlc_schema.CustomerDeletePayload, 'input_type': shopify_sgqlc_schema.CustomerDeleteInput},
    'customerEmailMarketingConsentUpdate': {'payload_type': shopify_sgqlc_schema.CustomerEmailMarketingConsentUpdatePayload, 'input_type': shopify_sgqlc_schema.CustomerEmailMarketingConsentUpdateInput},
    'customerGenerateAccountActivationUrl': {'payload_type': shopify_sgqlc_schema.CustomerGenerateAccountActivationUrlPayload, 'input_type': None},
    'customerMerge': {'payload_type': shopify_sgqlc_schema.CustomerMergePayload, 'input_type': None},
    'customerPaymentMethodCreateFromDuplicationData': {'payload_type': shopify_sgqlc_schema.CustomerPaymentMethodCreateFromDuplicationDataPayload, 'input_type': None},
    'customerPaymentMethodCreditCardCreate': {'payload_type': shopify_sgqlc_schema.CustomerPaymentMethodCreditCardCreatePayload, 'input_type': None},
    'customerPaymentMethodCreditCardUpdate': {'payload_type': shopify_sgqlc_schema.CustomerPaymentMethodCreditCardUpdatePayload, 'input_type': None},
    'customerPaymentMethodGetDuplicationData': {'payload_type': shopify_sgqlc_schema.CustomerPaymentMethodGetDuplicationDataPayload, 'input_type': None},
    'customerPaymentMethodGetUpdateUrl': {'payload_type': shopify_sgqlc_schema.CustomerPaymentMethodGetUpdateUrlPayload, 'input_type': None},
    'customerPaymentMethodPaypalBillingAgreementCreate': {'payload_type': shopify_sgqlc_schema.CustomerPaymentMethodPaypalBillingAgreementCreatePayload, 'input_type': None},
    'customerPaymentMethodPaypalBillingAgreementUpdate': {'payload_type': shopify_sgqlc_schema.CustomerPaymentMethodPaypalBillingAgreementUpdatePayload, 'input_type': None},
    'customerPaymentMethodRemoteCreate': {'payload_type': shopify_sgqlc_schema.CustomerPaymentMethodRemoteCreatePayload, 'input_type': None},
    'customerPaymentMethodRemoteCreditCardCreate': {'payload_type': shopify_sgqlc_schema.CustomerPaymentMethodRemoteCreditCardCreatePayload, 'input_type': None},
    'customerPaymentMethodRevoke': {'payload_type': shopify_sgqlc_schema.CustomerPaymentMethodRevokePayload, 'input_type': None},
    'customerPaymentMethodSendUpdateEmail': {'payload_type': shopify_sgqlc_schema.CustomerPaymentMethodSendUpdateEmailPayload, 'input_type': None},
    'customerRemoveTaxExemptions': {'payload_type': shopify_sgqlc_schema.CustomerRemoveTaxExemptionsPayload, 'input_type': None},
    'customerReplaceTaxExemptions': {'payload_type': shopify_sgqlc_schema.CustomerReplaceTaxExemptionsPayload, 'input_type': None},
    'customerRequestDataErasure': {'payload_type': shopify_sgqlc_schema.CustomerRequestDataErasurePayload, 'input_type': None},
    'customerSegmentMembersQueryCreate': {'payload_type': shopify_sgqlc_schema.CustomerSegmentMembersQueryCreatePayload, 'input_type': shopify_sgqlc_schema.CustomerSegmentMembersQueryInput},
    'customerSendAccountInviteEmail': {'payload_type': shopify_sgqlc_schema.CustomerSendAccountInviteEmailPayload, 'input_type': None},
    'customerSmsMarketingConsentUpdate': {'payload_type': shopify_sgqlc_schema.CustomerSmsMarketingConsentUpdatePayload, 'input_type': shopify_sgqlc_schema.CustomerSmsMarketingConsentUpdateInput},
    'customerUpdate': {'payload_type': shopify_sgqlc_schema.CustomerUpdatePayload, 'input_type': shopify_sgqlc_schema.CustomerInput},
    'customerUpdateDefaultAddress': {'payload_type': shopify_sgqlc_schema.CustomerUpdateDefaultAddressPayload, 'input_type': None},
    'dataSaleOptOut': {'payload_type': shopify_sgqlc_schema.DataSaleOptOutPayload, 'input_type': None},
    'delegateAccessTokenCreate': {'payload_type': shopify_sgqlc_schema.DelegateAccessTokenCreatePayload, 'input_type': shopify_sgqlc_schema.DelegateAccessTokenInput},
    'delegateAccessTokenDestroy': {'payload_type': shopify_sgqlc_schema.DelegateAccessTokenDestroyPayload, 'input_type': None},
    'deliveryCustomizationActivation': {'payload_type': shopify_sgqlc_schema.DeliveryCustomizationActivationPayload, 'input_type': None},
    'deliveryCustomizationCreate': {'payload_type': shopify_sgqlc_schema.DeliveryCustomizationCreatePayload, 'input_type': None},
    'deliveryCustomizationDelete': {'payload_type': shopify_sgqlc_schema.DeliveryCustomizationDeletePayload, 'input_type': None},
    'deliveryCustomizationUpdate': {'payload_type': shopify_sgqlc_schema.DeliveryCustomizationUpdatePayload, 'input_type': None},
    'deliveryProfileCreate': {'payload_type': shopify_sgqlc_schema.DeliveryProfileCreatePayload, 'input_type': None},
    'deliveryProfileRemove': {'payload_type': shopify_sgqlc_schema.DeliveryProfileRemovePayload, 'input_type': None},
    'deliveryProfileUpdate': {'payload_type': shopify_sgqlc_schema.DeliveryProfileUpdatePayload, 'input_type': None},
    'deliveryPromiseProviderUpsert': {'payload_type': shopify_sgqlc_schema.DeliveryPromiseProviderUpsertPayload, 'input_type': None},
    'deliverySettingUpdate': {'payload_type': shopify_sgqlc_schema.DeliverySettingUpdatePayload, 'input_type': None},
    'deliveryShippingOriginAssign': {'payload_type': shopify_sgqlc_schema.DeliveryShippingOriginAssignPayload, 'input_type': None},
    'discountAutomaticActivate': {'payload_type': shopify_sgqlc_schema.DiscountAutomaticActivatePayload, 'input_type': None},
    'discountAutomaticAppCreate': {'payload_type': shopify_sgqlc_schema.DiscountAutomaticAppCreatePayload, 'input_type': None},
    'discountAutomaticAppUpdate': {'payload_type': shopify_sgqlc_schema.DiscountAutomaticAppUpdatePayload, 'input_type': None},
    'discountAutomaticBasicCreate': {'payload_type': shopify_sgqlc_schema.DiscountAutomaticBasicCreatePayload, 'input_type': None},
    'discountAutomaticBasicUpdate': {'payload_type': shopify_sgqlc_schema.DiscountAutomaticBasicUpdatePayload, 'input_type': None},
    'discountAutomaticBulkDelete': {'payload_type': shopify_sgqlc_schema.DiscountAutomaticBulkDeletePayload, 'input_type': None},
    'discountAutomaticBxgyCreate': {'payload_type': shopify_sgqlc_schema.DiscountAutomaticBxgyCreatePayload, 'input_type': None},
    'discountAutomaticBxgyUpdate': {'payload_type': shopify_sgqlc_schema.DiscountAutomaticBxgyUpdatePayload, 'input_type': None},
    'discountAutomaticDeactivate': {'payload_type': shopify_sgqlc_schema.DiscountAutomaticDeactivatePayload, 'input_type': None},
    'discountAutomaticDelete': {'payload_type': shopify_sgqlc_schema.DiscountAutomaticDeletePayload, 'input_type': None},
    'discountAutomaticFreeShippingCreate': {'payload_type': shopify_sgqlc_schema.DiscountAutomaticFreeShippingCreatePayload, 'input_type': None},
    'discountAutomaticFreeShippingUpdate': {'payload_type': shopify_sgqlc_schema.DiscountAutomaticFreeShippingUpdatePayload, 'input_type': None},
    'discountCodeActivate': {'payload_type': shopify_sgqlc_schema.DiscountCodeActivatePayload, 'input_type': None},
    'discountCodeAppCreate': {'payload_type': shopify_sgqlc_schema.DiscountCodeAppCreatePayload, 'input_type': None},
    'discountCodeAppUpdate': {'payload_type': shopify_sgqlc_schema.DiscountCodeAppUpdatePayload, 'input_type': None},
    'discountCodeBasicCreate': {'payload_type': shopify_sgqlc_schema.DiscountCodeBasicCreatePayload, 'input_type': None},
    'discountCodeBasicUpdate': {'payload_type': shopify_sgqlc_schema.DiscountCodeBasicUpdatePayload, 'input_type': None},
    'discountCodeBulkActivate': {'payload_type': shopify_sgqlc_schema.DiscountCodeBulkActivatePayload, 'input_type': None},
    'discountCodeBulkDeactivate': {'payload_type': shopify_sgqlc_schema.DiscountCodeBulkDeactivatePayload, 'input_type': None},
    'discountCodeBulkDelete': {'payload_type': shopify_sgqlc_schema.DiscountCodeBulkDeletePayload, 'input_type': None},
    'discountCodeBxgyCreate': {'payload_type': shopify_sgqlc_schema.DiscountCodeBxgyCreatePayload, 'input_type': None},
    'discountCodeBxgyUpdate': {'payload_type': shopify_sgqlc_schema.DiscountCodeBxgyUpdatePayload, 'input_type': None},
    'discountCodeDeactivate': {'payload_type': shopify_sgqlc_schema.DiscountCodeDeactivatePayload, 'input_type': None},
    'discountCodeDelete': {'payload_type': shopify_sgqlc_schema.DiscountCodeDeletePayload, 'input_type': None},
    'discountCodeFreeShippingCreate': {'payload_type': shopify_sgqlc_schema.DiscountCodeFreeShippingCreatePayload, 'input_type': None},
    'discountCodeFreeShippingUpdate': {'payload_type': shopify_sgqlc_schema.DiscountCodeFreeShippingUpdatePayload, 'input_type': None},
    'discountCodeRedeemCodeBulkDelete': {'payload_type': shopify_sgqlc_schema.DiscountCodeRedeemCodeBulkDeletePayload, 'input_type': None},
    'discountRedeemCodeBulkAdd': {'payload_type': shopify_sgqlc_schema.DiscountRedeemCodeBulkAddPayload, 'input_type': None},
    'disputeEvidenceUpdate': {'payload_type': shopify_sgqlc_schema.DisputeEvidenceUpdatePayload, 'input_type': shopify_sgqlc_schema.ShopifyPaymentsDisputeEvidenceUpdateInput},
    'draftOrderBulkAddTags': {'payload_type': shopify_sgqlc_schema.DraftOrderBulkAddTagsPayload, 'input_type': None},
    'draftOrderBulkDelete': {'payload_type': shopify_sgqlc_schema.DraftOrderBulkDeletePayload, 'input_type': None},
    'draftOrderBulkRemoveTags': {'payload_type': shopify_sgqlc_schema.DraftOrderBulkRemoveTagsPayload, 'input_type': None},
    'draftOrderCalculate': {'payload_type': shopify_sgqlc_schema.DraftOrderCalculatePayload, 'input_type': shopify_sgqlc_schema.DraftOrderInput},
    'draftOrderComplete': {'payload_type': shopify_sgqlc_schema.DraftOrderCompletePayload, 'input_type': None},
    'draftOrderCreate': {'payload_type': shopify_sgqlc_schema.DraftOrderCreatePayload, 'input_type': shopify_sgqlc_schema.DraftOrderInput},
    'draftOrderCreateFromOrder': {'payload_type': shopify_sgqlc_schema.DraftOrderCreateFromOrderPayload, 'input_type': None},
    'draftOrderCreateMerchantCheckout': {'payload_type': shopify_sgqlc_schema.DraftOrderCreateMerchantCheckoutPayload, 'input_type': None},
    'draftOrderDelete': {'payload_type': shopify_sgqlc_schema.DraftOrderDeletePayload, 'input_type': shopify_sgqlc_schema.DraftOrderDeleteInput},
    'draftOrderDuplicate': {'payload_type': shopify_sgqlc_schema.DraftOrderDuplicatePayload, 'input_type': None},
    'draftOrderInvoicePreview': {'payload_type': shopify_sgqlc_schema.DraftOrderInvoicePreviewPayload, 'input_type': None},
    'draftOrderInvoiceSend': {'payload_type': shopify_sgqlc_schema.DraftOrderInvoiceSendPayload, 'input_type': None},
    'draftOrderUpdate': {'payload_type': shopify_sgqlc_schema.DraftOrderUpdatePayload, 'input_type': shopify_sgqlc_schema.DraftOrderInput},
    'eventBridgeServerPixelUpdate': {'payload_type': shopify_sgqlc_schema.EventBridgeServerPixelUpdatePayload, 'input_type': None},
    'eventBridgeWebhookSubscriptionCreate': {'payload_type': shopify_sgqlc_schema.EventBridgeWebhookSubscriptionCreatePayload, 'input_type': None},
    'eventBridgeWebhookSubscriptionUpdate': {'payload_type': shopify_sgqlc_schema.EventBridgeWebhookSubscriptionUpdatePayload, 'input_type': None},
    'fileAcknowledgeUpdateFailed': {'payload_type': shopify_sgqlc_schema.FileAcknowledgeUpdateFailedPayload, 'input_type': None},
    'fileCreate': {'payload_type': shopify_sgqlc_schema.FileCreatePayload, 'input_type': None},
    'fileDelete': {'payload_type': shopify_sgqlc_schema.FileDeletePayload, 'input_type': None},
    'fileUpdate': {'payload_type': shopify_sgqlc_schema.FileUpdatePayload, 'input_type': None},
    'flowGenerateSignature': {'payload_type': shopify_sgqlc_schema.FlowGenerateSignaturePayload, 'input_type': None},
    'flowTriggerReceive': {'payload_type': shopify_sgqlc_schema.FlowTriggerReceivePayload, 'input_type': None},
    'fulfillmentCancel': {'payload_type': shopify_sgqlc_schema.FulfillmentCancelPayload, 'input_type': None},
    'fulfillmentConstraintRuleCreate': {'payload_type': shopify_sgqlc_schema.FulfillmentConstraintRuleCreatePayload, 'input_type': None},
    'fulfillmentConstraintRuleDelete': {'payload_type': shopify_sgqlc_schema.FulfillmentConstraintRuleDeletePayload, 'input_type': None},
    'fulfillmentConstraintRuleUpdate': {'payload_type': shopify_sgqlc_schema.FulfillmentConstraintRuleUpdatePayload, 'input_type': None},
    'fulfillmentCreate': {'payload_type': shopify_sgqlc_schema.FulfillmentCreatePayload, 'input_type': None},
    'fulfillmentCreateV2': {'payload_type': shopify_sgqlc_schema.FulfillmentCreateV2Payload, 'input_type': None},
    'fulfillmentEventCreate': {'payload_type': shopify_sgqlc_schema.FulfillmentEventCreatePayload, 'input_type': None},
    'fulfillmentOrderAcceptCancellationRequest': {'payload_type': shopify_sgqlc_schema.FulfillmentOrderAcceptCancellationRequestPayload, 'input_type': None},
    'fulfillmentOrderAcceptFulfillmentRequest': {'payload_type': shopify_sgqlc_schema.FulfillmentOrderAcceptFulfillmentRequestPayload, 'input_type': None},
    'fulfillmentOrderCancel': {'payload_type': shopify_sgqlc_schema.FulfillmentOrderCancelPayload, 'input_type': None},
    'fulfillmentOrderClose': {'payload_type': shopify_sgqlc_schema.FulfillmentOrderClosePayload, 'input_type': None},
    'fulfillmentOrderHold': {'payload_type': shopify_sgqlc_schema.FulfillmentOrderHoldPayload, 'input_type': None},
    'fulfillmentOrderLineItemsPreparedForPickup': {'payload_type': shopify_sgqlc_schema.FulfillmentOrderLineItemsPreparedForPickupPayload, 'input_type': shopify_sgqlc_schema.FulfillmentOrderLineItemsPreparedForPickupInput},
    'fulfillmentOrderMerge': {'payload_type': shopify_sgqlc_schema.FulfillmentOrderMergePayload, 'input_type': None},
    'fulfillmentOrderMove': {'payload_type': shopify_sgqlc_schema.FulfillmentOrderMovePayload, 'input_type': None},
    'fulfillmentOrderOpen': {'payload_type': shopify_sgqlc_schema.FulfillmentOrderOpenPayload, 'input_type': None},
    'fulfillmentOrderRejectCancellationRequest': {'payload_type': shopify_sgqlc_schema.FulfillmentOrderRejectCancellationRequestPayload, 'input_type': None},
    'fulfillmentOrderRejectFulfillmentRequest': {'payload_type': shopify_sgqlc_schema.FulfillmentOrderRejectFulfillmentRequestPayload, 'input_type': None},
    'fulfillmentOrderReleaseHold': {'payload_type': shopify_sgqlc_schema.FulfillmentOrderReleaseHoldPayload, 'input_type': None},
    'fulfillmentOrderReschedule': {'payload_type': shopify_sgqlc_schema.FulfillmentOrderReschedulePayload, 'input_type': None},
    'fulfillmentOrderSplit': {'payload_type': shopify_sgqlc_schema.FulfillmentOrderSplitPayload, 'input_type': None},
    'fulfillmentOrderSubmitCancellationRequest': {'payload_type': shopify_sgqlc_schema.FulfillmentOrderSubmitCancellationRequestPayload, 'input_type': None},
    'fulfillmentOrderSubmitFulfillmentRequest': {'payload_type': shopify_sgqlc_schema.FulfillmentOrderSubmitFulfillmentRequestPayload, 'input_type': None},
    'fulfillmentOrdersSetFulfillmentDeadline': {'payload_type': shopify_sgqlc_schema.FulfillmentOrdersSetFulfillmentDeadlinePayload, 'input_type': None},
    'fulfillmentServiceCreate': {'payload_type': shopify_sgqlc_schema.FulfillmentServiceCreatePayload, 'input_type': None},
    'fulfillmentServiceDelete': {'payload_type': shopify_sgqlc_schema.FulfillmentServiceDeletePayload, 'input_type': None},
    'fulfillmentServiceUpdate': {'payload_type': shopify_sgqlc_schema.FulfillmentServiceUpdatePayload, 'input_type': None},
    'fulfillmentTrackingInfoUpdate': {'payload_type': shopify_sgqlc_schema.FulfillmentTrackingInfoUpdatePayload, 'input_type': None},
    'fulfillmentTrackingInfoUpdateV2': {'payload_type': shopify_sgqlc_schema.FulfillmentTrackingInfoUpdateV2Payload, 'input_type': None},
    'giftCardCreate': {'payload_type': shopify_sgqlc_schema.GiftCardCreatePayload, 'input_type': shopify_sgqlc_schema.GiftCardCreateInput},
    'giftCardCredit': {'payload_type': shopify_sgqlc_schema.GiftCardCreditPayload, 'input_type': None},
    'giftCardDeactivate': {'payload_type': shopify_sgqlc_schema.GiftCardDeactivatePayload, 'input_type': None},
    'giftCardDebit': {'payload_type': shopify_sgqlc_schema.GiftCardDebitPayload, 'input_type': None},
    'giftCardSendNotificationToCustomer': {'payload_type': shopify_sgqlc_schema.GiftCardSendNotificationToCustomerPayload, 'input_type': None},
    'giftCardSendNotificationToRecipient': {'payload_type': shopify_sgqlc_schema.GiftCardSendNotificationToRecipientPayload, 'input_type': None},
    'giftCardUpdate': {'payload_type': shopify_sgqlc_schema.GiftCardUpdatePayload, 'input_type': shopify_sgqlc_schema.GiftCardUpdateInput},
    'inventoryActivate': {'payload_type': shopify_sgqlc_schema.InventoryActivatePayload, 'input_type': None},
    'inventoryAdjustQuantities': {'payload_type': shopify_sgqlc_schema.InventoryAdjustQuantitiesPayload, 'input_type': shopify_sgqlc_schema.InventoryAdjustQuantitiesInput},
    'inventoryBulkToggleActivation': {'payload_type': shopify_sgqlc_schema.InventoryBulkToggleActivationPayload, 'input_type': None},
    'inventoryDeactivate': {'payload_type': shopify_sgqlc_schema.InventoryDeactivatePayload, 'input_type': None},
    'inventoryItemUpdate': {'payload_type': shopify_sgqlc_schema.InventoryItemUpdatePayload, 'input_type': shopify_sgqlc_schema.InventoryItemInput},
    'inventoryMoveQuantities': {'payload_type': shopify_sgqlc_schema.InventoryMoveQuantitiesPayload, 'input_type': shopify_sgqlc_schema.InventoryMoveQuantitiesInput},
    'inventorySetOnHandQuantities': {'payload_type': shopify_sgqlc_schema.InventorySetOnHandQuantitiesPayload, 'input_type': shopify_sgqlc_schema.InventorySetOnHandQuantitiesInput},
    'inventorySetQuantities': {'payload_type': shopify_sgqlc_schema.InventorySetQuantitiesPayload, 'input_type': shopify_sgqlc_schema.InventorySetQuantitiesInput},
    'inventorySetScheduledChanges': {'payload_type': shopify_sgqlc_schema.InventorySetScheduledChangesPayload, 'input_type': shopify_sgqlc_schema.InventorySetScheduledChangesInput},
    'locationActivate': {'payload_type': shopify_sgqlc_schema.LocationActivatePayload, 'input_type': None},
    'locationAdd': {'payload_type': shopify_sgqlc_schema.LocationAddPayload, 'input_type': shopify_sgqlc_schema.LocationAddInput},
    'locationDeactivate': {'payload_type': shopify_sgqlc_schema.LocationDeactivatePayload, 'input_type': None},
    'locationDelete': {'payload_type': shopify_sgqlc_schema.LocationDeletePayload, 'input_type': None},
    'locationEdit': {'payload_type': shopify_sgqlc_schema.LocationEditPayload, 'input_type': shopify_sgqlc_schema.LocationEditInput},
    'locationLocalPickupDisable': {'payload_type': shopify_sgqlc_schema.LocationLocalPickupDisablePayload, 'input_type': None},
    'locationLocalPickupEnable': {'payload_type': shopify_sgqlc_schema.LocationLocalPickupEnablePayload, 'input_type': None},
    'marketCreate': {'payload_type': shopify_sgqlc_schema.MarketCreatePayload, 'input_type': shopify_sgqlc_schema.MarketCreateInput},
    'marketCurrencySettingsUpdate': {'payload_type': shopify_sgqlc_schema.MarketCurrencySettingsUpdatePayload, 'input_type': shopify_sgqlc_schema.MarketCurrencySettingsUpdateInput},
    'marketDelete': {'payload_type': shopify_sgqlc_schema.MarketDeletePayload, 'input_type': None},
    'marketLocalizationsRegister': {'payload_type': shopify_sgqlc_schema.MarketLocalizationsRegisterPayload, 'input_type': None},
    'marketLocalizationsRemove': {'payload_type': shopify_sgqlc_schema.MarketLocalizationsRemovePayload, 'input_type': None},
    'marketRegionDelete': {'payload_type': shopify_sgqlc_schema.MarketRegionDeletePayload, 'input_type': None},
    'marketRegionsCreate': {'payload_type': shopify_sgqlc_schema.MarketRegionsCreatePayload, 'input_type': None},
    'marketRegionsDelete': {'payload_type': shopify_sgqlc_schema.MarketRegionsDeletePayload, 'input_type': None},
    'marketUpdate': {'payload_type': shopify_sgqlc_schema.MarketUpdatePayload, 'input_type': shopify_sgqlc_schema.MarketUpdateInput},
    'marketWebPresenceCreate': {'payload_type': shopify_sgqlc_schema.MarketWebPresenceCreatePayload, 'input_type': None},
    'marketWebPresenceDelete': {'payload_type': shopify_sgqlc_schema.MarketWebPresenceDeletePayload, 'input_type': None},
    'marketWebPresenceUpdate': {'payload_type': shopify_sgqlc_schema.MarketWebPresenceUpdatePayload, 'input_type': None},
    'marketingActivitiesDeleteAllExternal': {'payload_type': shopify_sgqlc_schema.MarketingActivitiesDeleteAllExternalPayload, 'input_type': None},
    'marketingActivityCreate': {'payload_type': shopify_sgqlc_schema.MarketingActivityCreatePayload, 'input_type': shopify_sgqlc_schema.MarketingActivityCreateInput},
    'marketingActivityCreateExternal': {'payload_type': shopify_sgqlc_schema.MarketingActivityCreateExternalPayload, 'input_type': shopify_sgqlc_schema.MarketingActivityCreateExternalInput},
    'marketingActivityDeleteExternal': {'payload_type': shopify_sgqlc_schema.MarketingActivityDeleteExternalPayload, 'input_type': None},
    'marketingActivityUpdate': {'payload_type': shopify_sgqlc_schema.MarketingActivityUpdatePayload, 'input_type': shopify_sgqlc_schema.MarketingActivityUpdateInput},
    'marketingActivityUpdateExternal': {'payload_type': shopify_sgqlc_schema.MarketingActivityUpdateExternalPayload, 'input_type': shopify_sgqlc_schema.MarketingActivityUpdateExternalInput},
    'marketingActivityUpsertExternal': {'payload_type': shopify_sgqlc_schema.MarketingActivityUpsertExternalPayload, 'input_type': shopify_sgqlc_schema.MarketingActivityUpsertExternalInput},
    'marketingEngagementCreate': {'payload_type': shopify_sgqlc_schema.MarketingEngagementCreatePayload, 'input_type': None},
    'marketingEngagementsDelete': {'payload_type': shopify_sgqlc_schema.MarketingEngagementsDeletePayload, 'input_type': None},
    'menuCreate': {'payload_type': shopify_sgqlc_schema.MenuCreatePayload, 'input_type': None},
    'menuDelete': {'payload_type': shopify_sgqlc_schema.MenuDeletePayload, 'input_type': None},
    'menuUpdate': {'payload_type': shopify_sgqlc_schema.MenuUpdatePayload, 'input_type': None},
    'metafieldDefinitionCreate': {'payload_type': shopify_sgqlc_schema.MetafieldDefinitionCreatePayload, 'input_type': None},
    'metafieldDefinitionDelete': {'payload_type': shopify_sgqlc_schema.MetafieldDefinitionDeletePayload, 'input_type': None},
    'metafieldDefinitionPin': {'payload_type': shopify_sgqlc_schema.MetafieldDefinitionPinPayload, 'input_type': None},
    'metafieldDefinitionUnpin': {'payload_type': shopify_sgqlc_schema.MetafieldDefinitionUnpinPayload, 'input_type': None},
    'metafieldDefinitionUpdate': {'payload_type': shopify_sgqlc_schema.MetafieldDefinitionUpdatePayload, 'input_type': None},
    'metafieldDelete': {'payload_type': shopify_sgqlc_schema.MetafieldDeletePayload, 'input_type': shopify_sgqlc_schema.MetafieldDeleteInput},
    'metafieldStorefrontVisibilityCreate': {'payload_type': shopify_sgqlc_schema.MetafieldStorefrontVisibilityCreatePayload, 'input_type': shopify_sgqlc_schema.MetafieldStorefrontVisibilityInput},
    'metafieldStorefrontVisibilityDelete': {'payload_type': shopify_sgqlc_schema.MetafieldStorefrontVisibilityDeletePayload, 'input_type': None},
    'metafieldsDelete': {'payload_type': shopify_sgqlc_schema.MetafieldsDeletePayload, 'input_type': None},
    'metafieldsSet': {'payload_type': shopify_sgqlc_schema.MetafieldsSetPayload, 'input_type': None},
    'metaobjectBulkDelete': {'payload_type': shopify_sgqlc_schema.MetaobjectBulkDeletePayload, 'input_type': None},
    'metaobjectCreate': {'payload_type': shopify_sgqlc_schema.MetaobjectCreatePayload, 'input_type': None},
    'metaobjectDefinitionCreate': {'payload_type': shopify_sgqlc_schema.MetaobjectDefinitionCreatePayload, 'input_type': None},
    'metaobjectDefinitionDelete': {'payload_type': shopify_sgqlc_schema.MetaobjectDefinitionDeletePayload, 'input_type': None},
    'metaobjectDefinitionUpdate': {'payload_type': shopify_sgqlc_schema.MetaobjectDefinitionUpdatePayload, 'input_type': None},
    'metaobjectDelete': {'payload_type': shopify_sgqlc_schema.MetaobjectDeletePayload, 'input_type': None},
    'metaobjectUpdate': {'payload_type': shopify_sgqlc_schema.MetaobjectUpdatePayload, 'input_type': None},
    'metaobjectUpsert': {'payload_type': shopify_sgqlc_schema.MetaobjectUpsertPayload, 'input_type': None},
    'mobilePlatformApplicationCreate': {'payload_type': shopify_sgqlc_schema.MobilePlatformApplicationCreatePayload, 'input_type': shopify_sgqlc_schema.MobilePlatformApplicationCreateInput},
    'mobilePlatformApplicationDelete': {'payload_type': shopify_sgqlc_schema.MobilePlatformApplicationDeletePayload, 'input_type': None},
    'mobilePlatformApplicationUpdate': {'payload_type': shopify_sgqlc_schema.MobilePlatformApplicationUpdatePayload, 'input_type': shopify_sgqlc_schema.MobilePlatformApplicationUpdateInput},
    'orderCancel': {'payload_type': shopify_sgqlc_schema.OrderCancelPayload, 'input_type': None},
    'orderCapture': {'payload_type': shopify_sgqlc_schema.OrderCapturePayload, 'input_type': shopify_sgqlc_schema.OrderCaptureInput},
    'orderClose': {'payload_type': shopify_sgqlc_schema.OrderClosePayload, 'input_type': shopify_sgqlc_schema.OrderCloseInput},
    'orderCreate': {'payload_type': shopify_sgqlc_schema.OrderCreatePayload, 'input_type': None},
    'orderCreateMandatePayment': {'payload_type': shopify_sgqlc_schema.OrderCreateMandatePaymentPayload, 'input_type': None},
    'orderDelete': {'payload_type': shopify_sgqlc_schema.OrderDeletePayload, 'input_type': None},
    'orderEditAddCustomItem': {'payload_type': shopify_sgqlc_schema.OrderEditAddCustomItemPayload, 'input_type': None},
    'orderEditAddLineItemDiscount': {'payload_type': shopify_sgqlc_schema.OrderEditAddLineItemDiscountPayload, 'input_type': None},
    'orderEditAddShippingLine': {'payload_type': shopify_sgqlc_schema.OrderEditAddShippingLinePayload, 'input_type': None},
    'orderEditAddVariant': {'payload_type': shopify_sgqlc_schema.OrderEditAddVariantPayload, 'input_type': None},
    'orderEditBegin': {'payload_type': shopify_sgqlc_schema.OrderEditBeginPayload, 'input_type': None},
    'orderEditCommit': {'payload_type': shopify_sgqlc_schema.OrderEditCommitPayload, 'input_type': None},
    'orderEditRemoveDiscount': {'payload_type': shopify_sgqlc_schema.OrderEditRemoveDiscountPayload, 'input_type': None},
    'orderEditRemoveLineItemDiscount': {'payload_type': shopify_sgqlc_schema.OrderEditRemoveLineItemDiscountPayload, 'input_type': None},
    'orderEditRemoveShippingLine': {'payload_type': shopify_sgqlc_schema.OrderEditRemoveShippingLinePayload, 'input_type': None},
    'orderEditSetQuantity': {'payload_type': shopify_sgqlc_schema.OrderEditSetQuantityPayload, 'input_type': None},
    'orderEditUpdateDiscount': {'payload_type': shopify_sgqlc_schema.OrderEditUpdateDiscountPayload, 'input_type': None},
    'orderEditUpdateShippingLine': {'payload_type': shopify_sgqlc_schema.OrderEditUpdateShippingLinePayload, 'input_type': None},
    'orderInvoiceSend': {'payload_type': shopify_sgqlc_schema.OrderInvoiceSendPayload, 'input_type': None},
    'orderMarkAsPaid': {'payload_type': shopify_sgqlc_schema.OrderMarkAsPaidPayload, 'input_type': shopify_sgqlc_schema.OrderMarkAsPaidInput},
    'orderOpen': {'payload_type': shopify_sgqlc_schema.OrderOpenPayload, 'input_type': shopify_sgqlc_schema.OrderOpenInput},
    'orderRiskAssessmentCreate': {'payload_type': shopify_sgqlc_schema.OrderRiskAssessmentCreatePayload, 'input_type': None},
    'orderUpdate': {'payload_type': shopify_sgqlc_schema.OrderUpdatePayload, 'input_type': shopify_sgqlc_schema.OrderInput},
    'pageCreate': {'payload_type': shopify_sgqlc_schema.PageCreatePayload, 'input_type': None},
    'pageDelete': {'payload_type': shopify_sgqlc_schema.PageDeletePayload, 'input_type': None},
    'pageUpdate': {'payload_type': shopify_sgqlc_schema.PageUpdatePayload, 'input_type': None},
    'paymentCustomizationActivation': {'payload_type': shopify_sgqlc_schema.PaymentCustomizationActivationPayload, 'input_type': None},
    'paymentCustomizationCreate': {'payload_type': shopify_sgqlc_schema.PaymentCustomizationCreatePayload, 'input_type': None},
    'paymentCustomizationDelete': {'payload_type': shopify_sgqlc_schema.PaymentCustomizationDeletePayload, 'input_type': None},
    'paymentCustomizationUpdate': {'payload_type': shopify_sgqlc_schema.PaymentCustomizationUpdatePayload, 'input_type': None},
    'paymentReminderSend': {'payload_type': shopify_sgqlc_schema.PaymentReminderSendPayload, 'input_type': None},
    'paymentTermsCreate': {'payload_type': shopify_sgqlc_schema.PaymentTermsCreatePayload, 'input_type': None},
    'paymentTermsDelete': {'payload_type': shopify_sgqlc_schema.PaymentTermsDeletePayload, 'input_type': shopify_sgqlc_schema.PaymentTermsDeleteInput},
    'paymentTermsUpdate': {'payload_type': shopify_sgqlc_schema.PaymentTermsUpdatePayload, 'input_type': shopify_sgqlc_schema.PaymentTermsUpdateInput},
    'priceListCreate': {'payload_type': shopify_sgqlc_schema.PriceListCreatePayload, 'input_type': shopify_sgqlc_schema.PriceListCreateInput},
    'priceListDelete': {'payload_type': shopify_sgqlc_schema.PriceListDeletePayload, 'input_type': None},
    'priceListFixedPricesAdd': {'payload_type': shopify_sgqlc_schema.PriceListFixedPricesAddPayload, 'input_type': None},
    'priceListFixedPricesByProductUpdate': {'payload_type': shopify_sgqlc_schema.PriceListFixedPricesByProductUpdatePayload, 'input_type': None},
    'priceListFixedPricesDelete': {'payload_type': shopify_sgqlc_schema.PriceListFixedPricesDeletePayload, 'input_type': None},
    'priceListFixedPricesUpdate': {'payload_type': shopify_sgqlc_schema.PriceListFixedPricesUpdatePayload, 'input_type': None},
    'priceListUpdate': {'payload_type': shopify_sgqlc_schema.PriceListUpdatePayload, 'input_type': shopify_sgqlc_schema.PriceListUpdateInput},
    'privateMetafieldDelete': {'payload_type': shopify_sgqlc_schema.PrivateMetafieldDeletePayload, 'input_type': shopify_sgqlc_schema.PrivateMetafieldDeleteInput},
    'privateMetafieldUpsert': {'payload_type': shopify_sgqlc_schema.PrivateMetafieldUpsertPayload, 'input_type': shopify_sgqlc_schema.PrivateMetafieldInput},
    'productBundleCreate': {'payload_type': shopify_sgqlc_schema.ProductBundleCreatePayload, 'input_type': shopify_sgqlc_schema.ProductBundleCreateInput},
    'productBundleUpdate': {'payload_type': shopify_sgqlc_schema.ProductBundleUpdatePayload, 'input_type': shopify_sgqlc_schema.ProductBundleUpdateInput},
    'productChangeStatus': {'payload_type': shopify_sgqlc_schema.ProductChangeStatusPayload, 'input_type': None},
    'productCreate': {'payload_type': shopify_sgqlc_schema.ProductCreatePayload, 'input_type': None},
    'productCreateMedia': {'payload_type': shopify_sgqlc_schema.ProductCreateMediaPayload, 'input_type': None},
    'productDelete': {'payload_type': shopify_sgqlc_schema.ProductDeletePayload, 'input_type': shopify_sgqlc_schema.ProductDeleteInput},
    'productDeleteMedia': {'payload_type': shopify_sgqlc_schema.ProductDeleteMediaPayload, 'input_type': None},
    'productDuplicate': {'payload_type': shopify_sgqlc_schema.ProductDuplicatePayload, 'input_type': None},
    'productFeedCreate': {'payload_type': shopify_sgqlc_schema.ProductFeedCreatePayload, 'input_type': shopify_sgqlc_schema.ProductFeedInput},
    'productFeedDelete': {'payload_type': shopify_sgqlc_schema.ProductFeedDeletePayload, 'input_type': None},
    'productFullSync': {'payload_type': shopify_sgqlc_schema.ProductFullSyncPayload, 'input_type': None},
    'productJoinSellingPlanGroups': {'payload_type': shopify_sgqlc_schema.ProductJoinSellingPlanGroupsPayload, 'input_type': None},
    'productLeaveSellingPlanGroups': {'payload_type': shopify_sgqlc_schema.ProductLeaveSellingPlanGroupsPayload, 'input_type': None},
    'productOptionUpdate': {'payload_type': shopify_sgqlc_schema.ProductOptionUpdatePayload, 'input_type': None},
    'productOptionsCreate': {'payload_type': shopify_sgqlc_schema.ProductOptionsCreatePayload, 'input_type': None},
    'productOptionsDelete': {'payload_type': shopify_sgqlc_schema.ProductOptionsDeletePayload, 'input_type': None},
    'productOptionsReorder': {'payload_type': shopify_sgqlc_schema.ProductOptionsReorderPayload, 'input_type': None},
    'productPublish': {'payload_type': shopify_sgqlc_schema.ProductPublishPayload, 'input_type': shopify_sgqlc_schema.ProductPublishInput},
    'productReorderMedia': {'payload_type': shopify_sgqlc_schema.ProductReorderMediaPayload, 'input_type': None},
    'productSet': {'payload_type': shopify_sgqlc_schema.ProductSetPayload, 'input_type': shopify_sgqlc_schema.ProductSetInput},
    'productUnpublish': {'payload_type': shopify_sgqlc_schema.ProductUnpublishPayload, 'input_type': shopify_sgqlc_schema.ProductUnpublishInput},
    'productUpdate': {'payload_type': shopify_sgqlc_schema.ProductUpdatePayload, 'input_type': None},
    'productUpdateMedia': {'payload_type': shopify_sgqlc_schema.ProductUpdateMediaPayload, 'input_type': None},
    'productVariantAppendMedia': {'payload_type': shopify_sgqlc_schema.ProductVariantAppendMediaPayload, 'input_type': None},
    'productVariantDetachMedia': {'payload_type': shopify_sgqlc_schema.ProductVariantDetachMediaPayload, 'input_type': None},
    'productVariantJoinSellingPlanGroups': {'payload_type': shopify_sgqlc_schema.ProductVariantJoinSellingPlanGroupsPayload, 'input_type': None},
    'productVariantLeaveSellingPlanGroups': {'payload_type': shopify_sgqlc_schema.ProductVariantLeaveSellingPlanGroupsPayload, 'input_type': None},
    'productVariantRelationshipBulkUpdate': {'payload_type': shopify_sgqlc_schema.ProductVariantRelationshipBulkUpdatePayload, 'input_type': shopify_sgqlc_schema.ProductVariantRelationshipUpdateInput},
    'productVariantsBulkCreate': {'payload_type': shopify_sgqlc_schema.ProductVariantsBulkCreatePayload, 'input_type': None},
    'productVariantsBulkDelete': {'payload_type': shopify_sgqlc_schema.ProductVariantsBulkDeletePayload, 'input_type': None},
    'productVariantsBulkReorder': {'payload_type': shopify_sgqlc_schema.ProductVariantsBulkReorderPayload, 'input_type': None},
    'productVariantsBulkUpdate': {'payload_type': shopify_sgqlc_schema.ProductVariantsBulkUpdatePayload, 'input_type': None},
    'pubSubServerPixelUpdate': {'payload_type': shopify_sgqlc_schema.PubSubServerPixelUpdatePayload, 'input_type': None},
    'pubSubWebhookSubscriptionCreate': {'payload_type': shopify_sgqlc_schema.PubSubWebhookSubscriptionCreatePayload, 'input_type': None},
    'pubSubWebhookSubscriptionUpdate': {'payload_type': shopify_sgqlc_schema.PubSubWebhookSubscriptionUpdatePayload, 'input_type': None},
    'publicationCreate': {'payload_type': shopify_sgqlc_schema.PublicationCreatePayload, 'input_type': shopify_sgqlc_schema.PublicationCreateInput},
    'publicationDelete': {'payload_type': shopify_sgqlc_schema.PublicationDeletePayload, 'input_type': None},
    'publicationUpdate': {'payload_type': shopify_sgqlc_schema.PublicationUpdatePayload, 'input_type': shopify_sgqlc_schema.PublicationUpdateInput},
    'publishablePublish': {'payload_type': shopify_sgqlc_schema.PublishablePublishPayload, 'input_type': shopify_sgqlc_schema.PublicationInput},
    'publishablePublishToCurrentChannel': {'payload_type': shopify_sgqlc_schema.PublishablePublishToCurrentChannelPayload, 'input_type': None},
    'publishableUnpublish': {'payload_type': shopify_sgqlc_schema.PublishableUnpublishPayload, 'input_type': shopify_sgqlc_schema.PublicationInput},
    'publishableUnpublishToCurrentChannel': {'payload_type': shopify_sgqlc_schema.PublishableUnpublishToCurrentChannelPayload, 'input_type': None},
    'quantityPricingByVariantUpdate': {'payload_type': shopify_sgqlc_schema.QuantityPricingByVariantUpdatePayload, 'input_type': shopify_sgqlc_schema.QuantityPricingByVariantUpdateInput},
    'quantityRulesAdd': {'payload_type': shopify_sgqlc_schema.QuantityRulesAddPayload, 'input_type': None},
    'quantityRulesDelete': {'payload_type': shopify_sgqlc_schema.QuantityRulesDeletePayload, 'input_type': None},
    'refundCreate': {'payload_type': shopify_sgqlc_schema.RefundCreatePayload, 'input_type': shopify_sgqlc_schema.RefundInput},
    'returnApproveRequest': {'payload_type': shopify_sgqlc_schema.ReturnApproveRequestPayload, 'input_type': shopify_sgqlc_schema.ReturnApproveRequestInput},
    'returnCancel': {'payload_type': shopify_sgqlc_schema.ReturnCancelPayload, 'input_type': None},
    'returnClose': {'payload_type': shopify_sgqlc_schema.ReturnClosePayload, 'input_type': None},
    'returnCreate': {'payload_type': shopify_sgqlc_schema.ReturnCreatePayload, 'input_type': None},
    'returnDeclineRequest': {'payload_type': shopify_sgqlc_schema.ReturnDeclineRequestPayload, 'input_type': shopify_sgqlc_schema.ReturnDeclineRequestInput},
    'returnLineItemRemoveFromReturn': {'payload_type': shopify_sgqlc_schema.ReturnLineItemRemoveFromReturnPayload, 'input_type': None},
    'returnRefund': {'payload_type': shopify_sgqlc_schema.ReturnRefundPayload, 'input_type': None},
    'returnReopen': {'payload_type': shopify_sgqlc_schema.ReturnReopenPayload, 'input_type': None},
    'returnRequest': {'payload_type': shopify_sgqlc_schema.ReturnRequestPayload, 'input_type': shopify_sgqlc_schema.ReturnRequestInput},
    'reverseDeliveryCreateWithShipping': {'payload_type': shopify_sgqlc_schema.ReverseDeliveryCreateWithShippingPayload, 'input_type': None},
    'reverseDeliveryShippingUpdate': {'payload_type': shopify_sgqlc_schema.ReverseDeliveryShippingUpdatePayload, 'input_type': None},
    'reverseFulfillmentOrderDispose': {'payload_type': shopify_sgqlc_schema.ReverseFulfillmentOrderDisposePayload, 'input_type': None},
    'savedSearchCreate': {'payload_type': shopify_sgqlc_schema.SavedSearchCreatePayload, 'input_type': shopify_sgqlc_schema.SavedSearchCreateInput},
    'savedSearchDelete': {'payload_type': shopify_sgqlc_schema.SavedSearchDeletePayload, 'input_type': shopify_sgqlc_schema.SavedSearchDeleteInput},
    'savedSearchUpdate': {'payload_type': shopify_sgqlc_schema.SavedSearchUpdatePayload, 'input_type': shopify_sgqlc_schema.SavedSearchUpdateInput},
    'scriptTagCreate': {'payload_type': shopify_sgqlc_schema.ScriptTagCreatePayload, 'input_type': shopify_sgqlc_schema.ScriptTagInput},
    'scriptTagDelete': {'payload_type': shopify_sgqlc_schema.ScriptTagDeletePayload, 'input_type': None},
    'scriptTagUpdate': {'payload_type': shopify_sgqlc_schema.ScriptTagUpdatePayload, 'input_type': shopify_sgqlc_schema.ScriptTagInput},
    'segmentCreate': {'payload_type': shopify_sgqlc_schema.SegmentCreatePayload, 'input_type': None},
    'segmentDelete': {'payload_type': shopify_sgqlc_schema.SegmentDeletePayload, 'input_type': None},
    'segmentUpdate': {'payload_type': shopify_sgqlc_schema.SegmentUpdatePayload, 'input_type': None},
    'sellingPlanGroupAddProductVariants': {'payload_type': shopify_sgqlc_schema.SellingPlanGroupAddProductVariantsPayload, 'input_type': None},
    'sellingPlanGroupAddProducts': {'payload_type': shopify_sgqlc_schema.SellingPlanGroupAddProductsPayload, 'input_type': None},
    'sellingPlanGroupCreate': {'payload_type': shopify_sgqlc_schema.SellingPlanGroupCreatePayload, 'input_type': shopify_sgqlc_schema.SellingPlanGroupInput},
    'sellingPlanGroupDelete': {'payload_type': shopify_sgqlc_schema.SellingPlanGroupDeletePayload, 'input_type': None},
    'sellingPlanGroupRemoveProductVariants': {'payload_type': shopify_sgqlc_schema.SellingPlanGroupRemoveProductVariantsPayload, 'input_type': None},
    'sellingPlanGroupRemoveProducts': {'payload_type': shopify_sgqlc_schema.SellingPlanGroupRemoveProductsPayload, 'input_type': None},
    'sellingPlanGroupUpdate': {'payload_type': shopify_sgqlc_schema.SellingPlanGroupUpdatePayload, 'input_type': shopify_sgqlc_schema.SellingPlanGroupInput},
    'serverPixelCreate': {'payload_type': shopify_sgqlc_schema.ServerPixelCreatePayload, 'input_type': None},
    'serverPixelDelete': {'payload_type': shopify_sgqlc_schema.ServerPixelDeletePayload, 'input_type': None},
    'shippingPackageDelete': {'payload_type': shopify_sgqlc_schema.ShippingPackageDeletePayload, 'input_type': None},
    'shippingPackageMakeDefault': {'payload_type': shopify_sgqlc_schema.ShippingPackageMakeDefaultPayload, 'input_type': None},
    'shippingPackageUpdate': {'payload_type': shopify_sgqlc_schema.ShippingPackageUpdatePayload, 'input_type': None},
    'shopLocaleDisable': {'payload_type': shopify_sgqlc_schema.ShopLocaleDisablePayload, 'input_type': None},
    'shopLocaleEnable': {'payload_type': shopify_sgqlc_schema.ShopLocaleEnablePayload, 'input_type': None},
    'shopLocaleUpdate': {'payload_type': shopify_sgqlc_schema.ShopLocaleUpdatePayload, 'input_type': None},
    'shopPolicyUpdate': {'payload_type': shopify_sgqlc_schema.ShopPolicyUpdatePayload, 'input_type': None},
    'shopResourceFeedbackCreate': {'payload_type': shopify_sgqlc_schema.ShopResourceFeedbackCreatePayload, 'input_type': shopify_sgqlc_schema.ResourceFeedbackCreateInput},
    'shopifyPaymentsPayoutAlternateCurrencyCreate': {'payload_type': shopify_sgqlc_schema.ShopifyPaymentsPayoutAlternateCurrencyCreatePayload, 'input_type': None},
    'stagedUploadTargetGenerate': {'payload_type': shopify_sgqlc_schema.StagedUploadTargetGeneratePayload, 'input_type': shopify_sgqlc_schema.StagedUploadTargetGenerateInput},
    'stagedUploadTargetsGenerate': {'payload_type': shopify_sgqlc_schema.StagedUploadTargetsGeneratePayload, 'input_type': shopify_sgqlc_schema.StageImageInput},
    'stagedUploadsCreate': {'payload_type': shopify_sgqlc_schema.StagedUploadsCreatePayload, 'input_type': shopify_sgqlc_schema.StagedUploadInput},
    'standardMetafieldDefinitionEnable': {'payload_type': shopify_sgqlc_schema.StandardMetafieldDefinitionEnablePayload, 'input_type': None},
    'standardMetaobjectDefinitionEnable': {'payload_type': shopify_sgqlc_schema.StandardMetaobjectDefinitionEnablePayload, 'input_type': None},
    'storeCreditAccountCredit': {'payload_type': shopify_sgqlc_schema.StoreCreditAccountCreditPayload, 'input_type': None},
    'storeCreditAccountDebit': {'payload_type': shopify_sgqlc_schema.StoreCreditAccountDebitPayload, 'input_type': None},
    'storefrontAccessTokenCreate': {'payload_type': shopify_sgqlc_schema.StorefrontAccessTokenCreatePayload, 'input_type': shopify_sgqlc_schema.StorefrontAccessTokenInput},
    'storefrontAccessTokenDelete': {'payload_type': shopify_sgqlc_schema.StorefrontAccessTokenDeletePayload, 'input_type': shopify_sgqlc_schema.StorefrontAccessTokenDeleteInput},
    'subscriptionBillingAttemptCreate': {'payload_type': shopify_sgqlc_schema.SubscriptionBillingAttemptCreatePayload, 'input_type': None},
    'subscriptionBillingCycleBulkCharge': {'payload_type': shopify_sgqlc_schema.SubscriptionBillingCycleBulkChargePayload, 'input_type': None},
    'subscriptionBillingCycleBulkSearch': {'payload_type': shopify_sgqlc_schema.SubscriptionBillingCycleBulkSearchPayload, 'input_type': None},
    'subscriptionBillingCycleCharge': {'payload_type': shopify_sgqlc_schema.SubscriptionBillingCycleChargePayload, 'input_type': None},
    'subscriptionBillingCycleContractDraftCommit': {'payload_type': shopify_sgqlc_schema.SubscriptionBillingCycleContractDraftCommitPayload, 'input_type': None},
    'subscriptionBillingCycleContractDraftConcatenate': {'payload_type': shopify_sgqlc_schema.SubscriptionBillingCycleContractDraftConcatenatePayload, 'input_type': None},
    'subscriptionBillingCycleContractEdit': {'payload_type': shopify_sgqlc_schema.SubscriptionBillingCycleContractEditPayload, 'input_type': None},
    'subscriptionBillingCycleEditDelete': {'payload_type': shopify_sgqlc_schema.SubscriptionBillingCycleEditDeletePayload, 'input_type': None},
    'subscriptionBillingCycleEditsDelete': {'payload_type': shopify_sgqlc_schema.SubscriptionBillingCycleEditsDeletePayload, 'input_type': None},
    'subscriptionBillingCycleScheduleEdit': {'payload_type': shopify_sgqlc_schema.SubscriptionBillingCycleScheduleEditPayload, 'input_type': shopify_sgqlc_schema.SubscriptionBillingCycleScheduleEditInput},
    'subscriptionBillingCycleSkip': {'payload_type': shopify_sgqlc_schema.SubscriptionBillingCycleSkipPayload, 'input_type': None},
    'subscriptionBillingCycleUnskip': {'payload_type': shopify_sgqlc_schema.SubscriptionBillingCycleUnskipPayload, 'input_type': None},
    'subscriptionContractActivate': {'payload_type': shopify_sgqlc_schema.SubscriptionContractActivatePayload, 'input_type': None},
    'subscriptionContractAtomicCreate': {'payload_type': shopify_sgqlc_schema.SubscriptionContractAtomicCreatePayload, 'input_type': shopify_sgqlc_schema.SubscriptionContractAtomicCreateInput},
    'subscriptionContractCancel': {'payload_type': shopify_sgqlc_schema.SubscriptionContractCancelPayload, 'input_type': None},
    'subscriptionContractCreate': {'payload_type': shopify_sgqlc_schema.SubscriptionContractCreatePayload, 'input_type': shopify_sgqlc_schema.SubscriptionContractCreateInput},
    'subscriptionContractExpire': {'payload_type': shopify_sgqlc_schema.SubscriptionContractExpirePayload, 'input_type': None},
    'subscriptionContractFail': {'payload_type': shopify_sgqlc_schema.SubscriptionContractFailPayload, 'input_type': None},
    'subscriptionContractPause': {'payload_type': shopify_sgqlc_schema.SubscriptionContractPausePayload, 'input_type': None},
    'subscriptionContractProductChange': {'payload_type': shopify_sgqlc_schema.SubscriptionContractProductChangePayload, 'input_type': shopify_sgqlc_schema.SubscriptionContractProductChangeInput},
    'subscriptionContractSetNextBillingDate': {'payload_type': shopify_sgqlc_schema.SubscriptionContractSetNextBillingDatePayload, 'input_type': None},
    'subscriptionContractUpdate': {'payload_type': shopify_sgqlc_schema.SubscriptionContractUpdatePayload, 'input_type': None},
    'subscriptionDraftCommit': {'payload_type': shopify_sgqlc_schema.SubscriptionDraftCommitPayload, 'input_type': None},
    'subscriptionDraftDiscountAdd': {'payload_type': shopify_sgqlc_schema.SubscriptionDraftDiscountAddPayload, 'input_type': shopify_sgqlc_schema.SubscriptionManualDiscountInput},
    'subscriptionDraftDiscountCodeApply': {'payload_type': shopify_sgqlc_schema.SubscriptionDraftDiscountCodeApplyPayload, 'input_type': None},
    'subscriptionDraftDiscountRemove': {'payload_type': shopify_sgqlc_schema.SubscriptionDraftDiscountRemovePayload, 'input_type': None},
    'subscriptionDraftDiscountUpdate': {'payload_type': shopify_sgqlc_schema.SubscriptionDraftDiscountUpdatePayload, 'input_type': shopify_sgqlc_schema.SubscriptionManualDiscountInput},
    'subscriptionDraftFreeShippingDiscountAdd': {'payload_type': shopify_sgqlc_schema.SubscriptionDraftFreeShippingDiscountAddPayload, 'input_type': shopify_sgqlc_schema.SubscriptionFreeShippingDiscountInput},
    'subscriptionDraftFreeShippingDiscountUpdate': {'payload_type': shopify_sgqlc_schema.SubscriptionDraftFreeShippingDiscountUpdatePayload, 'input_type': shopify_sgqlc_schema.SubscriptionFreeShippingDiscountInput},
    'subscriptionDraftLineAdd': {'payload_type': shopify_sgqlc_schema.SubscriptionDraftLineAddPayload, 'input_type': shopify_sgqlc_schema.SubscriptionLineInput},
    'subscriptionDraftLineRemove': {'payload_type': shopify_sgqlc_schema.SubscriptionDraftLineRemovePayload, 'input_type': None},
    'subscriptionDraftLineUpdate': {'payload_type': shopify_sgqlc_schema.SubscriptionDraftLineUpdatePayload, 'input_type': shopify_sgqlc_schema.SubscriptionLineUpdateInput},
    'subscriptionDraftUpdate': {'payload_type': shopify_sgqlc_schema.SubscriptionDraftUpdatePayload, 'input_type': shopify_sgqlc_schema.SubscriptionDraftInput},
    'tagsAdd': {'payload_type': shopify_sgqlc_schema.TagsAddPayload, 'input_type': None},
    'tagsRemove': {'payload_type': shopify_sgqlc_schema.TagsRemovePayload, 'input_type': None},
    'taxAppConfigure': {'payload_type': shopify_sgqlc_schema.TaxAppConfigurePayload, 'input_type': None},
    'themeCreate': {'payload_type': shopify_sgqlc_schema.ThemeCreatePayload, 'input_type': None},
    'themeDelete': {'payload_type': shopify_sgqlc_schema.ThemeDeletePayload, 'input_type': None},
    'themeFilesCopy': {'payload_type': shopify_sgqlc_schema.ThemeFilesCopyPayload, 'input_type': None},
    'themeFilesDelete': {'payload_type': shopify_sgqlc_schema.ThemeFilesDeletePayload, 'input_type': None},
    'themeFilesUpsert': {'payload_type': shopify_sgqlc_schema.ThemeFilesUpsertPayload, 'input_type': None},
    'themePublish': {'payload_type': shopify_sgqlc_schema.ThemePublishPayload, 'input_type': None},
    'themeUpdate': {'payload_type': shopify_sgqlc_schema.ThemeUpdatePayload, 'input_type': shopify_sgqlc_schema.OnlineStoreThemeInput},
    'transactionVoid': {'payload_type': shopify_sgqlc_schema.TransactionVoidPayload, 'input_type': None},
    'translationsRegister': {'payload_type': shopify_sgqlc_schema.TranslationsRegisterPayload, 'input_type': None},
    'translationsRemove': {'payload_type': shopify_sgqlc_schema.TranslationsRemovePayload, 'input_type': None},
    'urlRedirectBulkDeleteAll': {'payload_type': shopify_sgqlc_schema.UrlRedirectBulkDeleteAllPayload, 'input_type': None},
    'urlRedirectBulkDeleteByIds': {'payload_type': shopify_sgqlc_schema.UrlRedirectBulkDeleteByIdsPayload, 'input_type': None},
    'urlRedirectBulkDeleteBySavedSearch': {'payload_type': shopify_sgqlc_schema.UrlRedirectBulkDeleteBySavedSearchPayload, 'input_type': None},
    'urlRedirectBulkDeleteBySearch': {'payload_type': shopify_sgqlc_schema.UrlRedirectBulkDeleteBySearchPayload, 'input_type': None},
    'urlRedirectCreate': {'payload_type': shopify_sgqlc_schema.UrlRedirectCreatePayload, 'input_type': None},
    'urlRedirectDelete': {'payload_type': shopify_sgqlc_schema.UrlRedirectDeletePayload, 'input_type': None},
    'urlRedirectImportCreate': {'payload_type': shopify_sgqlc_schema.UrlRedirectImportCreatePayload, 'input_type': None},
    'urlRedirectImportSubmit': {'payload_type': shopify_sgqlc_schema.UrlRedirectImportSubmitPayload, 'input_type': None},
    'urlRedirectUpdate': {'payload_type': shopify_sgqlc_schema.UrlRedirectUpdatePayload, 'input_type': None},
    'validationCreate': {'payload_type': shopify_sgqlc_schema.ValidationCreatePayload, 'input_type': None},
    'validationDelete': {'payload_type': shopify_sgqlc_schema.ValidationDeletePayload, 'input_type': None},
    'validationUpdate': {'payload_type': shopify_sgqlc_schema.ValidationUpdatePayload, 'input_type': None},
    'webPixelCreate': {'payload_type': shopify_sgqlc_schema.WebPixelCreatePayload, 'input_type': None},
    'webPixelDelete': {'payload_type': shopify_sgqlc_schema.WebPixelDeletePayload, 'input_type': None},
    'webPixelUpdate': {'payload_type': shopify_sgqlc_schema.WebPixelUpdatePayload, 'input_type': None},
    'webhookSubscriptionCreate': {'payload_type': shopify_sgqlc_schema.WebhookSubscriptionCreatePayload, 'input_type': None},
    'webhookSubscriptionDelete': {'payload_type': shopify_sgqlc_schema.WebhookSubscriptionDeletePayload, 'input_type': None},
    'webhookSubscriptionUpdate': {'payload_type': shopify_sgqlc_schema.WebhookSubscriptionUpdatePayload, 'input_type': None},
}

QUERY_PAYLOAD_MAP: Dict[str, Dict[str, Any]] = {
    'abandonedCheckouts': {'return_type': shopify_sgqlc_schema.AbandonedCheckoutConnection, 'node_type': shopify_sgqlc_schema.AbandonedCheckout},
    'abandonedCheckoutsCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'abandonment': {'return_type': shopify_sgqlc_schema.Abandonment, 'node_type': None},
    'abandonmentByAbandonedCheckoutId': {'return_type': shopify_sgqlc_schema.Abandonment, 'node_type': None},
    'app': {'return_type': shopify_sgqlc_schema.App, 'node_type': None},
    'appByHandle': {'return_type': shopify_sgqlc_schema.App, 'node_type': None},
    'appByKey': {'return_type': shopify_sgqlc_schema.App, 'node_type': None},
    'appDiscountType': {'return_type': shopify_sgqlc_schema.AppDiscountType, 'node_type': None},
    'appDiscountTypes': {'return_type': shopify_sgqlc_schema.AppDiscountType, 'node_type': None},
    'appInstallation': {'return_type': shopify_sgqlc_schema.AppInstallation, 'node_type': None},
    'appInstallations': {'return_type': shopify_sgqlc_schema.AppInstallationConnection, 'node_type': shopify_sgqlc_schema.AppInstallation},
    'article': {'return_type': shopify_sgqlc_schema.Article, 'node_type': None},
    'articleTags': {'return_type': shopify_sgqlc_schema.String, 'node_type': None},
    'articles': {'return_type': shopify_sgqlc_schema.ArticleConnection, 'node_type': shopify_sgqlc_schema.Article},
    'assignedFulfillmentOrders': {'return_type': shopify_sgqlc_schema.FulfillmentOrderConnection, 'node_type': shopify_sgqlc_schema.FulfillmentOrder},
    'automaticDiscount': {'return_type': shopify_sgqlc_schema.DiscountAutomatic, 'node_type': None},
    'automaticDiscountNode': {'return_type': shopify_sgqlc_schema.DiscountAutomaticNode, 'node_type': None},
    'automaticDiscountNodes': {'return_type': shopify_sgqlc_schema.DiscountAutomaticNodeConnection, 'node_type': shopify_sgqlc_schema.DiscountAutomaticNode},
    'automaticDiscountSavedSearches': {'return_type': shopify_sgqlc_schema.SavedSearchConnection, 'node_type': shopify_sgqlc_schema.SavedSearch},
    'automaticDiscounts': {'return_type': shopify_sgqlc_schema.DiscountAutomaticConnection, 'node_type': shopify_sgqlc_schema.DiscountAutomatic},
    'availableCarrierServices': {'return_type': shopify_sgqlc_schema.DeliveryCarrierServiceAndLocations, 'node_type': None},
    'availableLocales': {'return_type': shopify_sgqlc_schema.Locale, 'node_type': None},
    'blog': {'return_type': shopify_sgqlc_schema.Blog, 'node_type': None},
    'blogs': {'return_type': shopify_sgqlc_schema.BlogConnection, 'node_type': shopify_sgqlc_schema.Blog},
    'blogsCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'businessEntities': {'return_type': shopify_sgqlc_schema.BusinessEntity, 'node_type': None},
    'businessEntity': {'return_type': shopify_sgqlc_schema.BusinessEntity, 'node_type': None},
    'carrierService': {'return_type': shopify_sgqlc_schema.DeliveryCarrierService, 'node_type': None},
    'carrierServices': {'return_type': shopify_sgqlc_schema.DeliveryCarrierServiceConnection, 'node_type': shopify_sgqlc_schema.DeliveryCarrierService},
    'cartTransforms': {'return_type': shopify_sgqlc_schema.CartTransformConnection, 'node_type': shopify_sgqlc_schema.CartTransform},
    'cashTrackingSession': {'return_type': shopify_sgqlc_schema.CashTrackingSession, 'node_type': None},
    'cashTrackingSessions': {'return_type': shopify_sgqlc_schema.CashTrackingSessionConnection, 'node_type': shopify_sgqlc_schema.CashTrackingSession},
    'catalog': {'return_type': shopify_sgqlc_schema.Catalog, 'node_type': None},
    'catalogOperations': {'return_type': shopify_sgqlc_schema.ResourceOperation, 'node_type': None},
    'catalogs': {'return_type': shopify_sgqlc_schema.CatalogConnection, 'node_type': shopify_sgqlc_schema.Catalog},
    'catalogsCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'channel': {'return_type': shopify_sgqlc_schema.Channel, 'node_type': None},
    'channels': {'return_type': shopify_sgqlc_schema.ChannelConnection, 'node_type': shopify_sgqlc_schema.Channel},
    'checkoutBranding': {'return_type': shopify_sgqlc_schema.CheckoutBranding, 'node_type': None},
    'checkoutProfile': {'return_type': shopify_sgqlc_schema.CheckoutProfile, 'node_type': None},
    'checkoutProfiles': {'return_type': shopify_sgqlc_schema.CheckoutProfileConnection, 'node_type': shopify_sgqlc_schema.CheckoutProfile},
    'codeDiscountNode': {'return_type': shopify_sgqlc_schema.DiscountCodeNode, 'node_type': None},
    'codeDiscountNodeByCode': {'return_type': shopify_sgqlc_schema.DiscountCodeNode, 'node_type': None},
    'codeDiscountNodes': {'return_type': shopify_sgqlc_schema.DiscountCodeNodeConnection, 'node_type': shopify_sgqlc_schema.DiscountCodeNode},
    'codeDiscountSavedSearches': {'return_type': shopify_sgqlc_schema.SavedSearchConnection, 'node_type': shopify_sgqlc_schema.SavedSearch},
    'collection': {'return_type': shopify_sgqlc_schema.Collection, 'node_type': None},
    'collectionByHandle': {'return_type': shopify_sgqlc_schema.Collection, 'node_type': None},
    'collectionRulesConditions': {'return_type': shopify_sgqlc_schema.CollectionRuleConditions, 'node_type': None},
    'collectionSavedSearches': {'return_type': shopify_sgqlc_schema.SavedSearchConnection, 'node_type': shopify_sgqlc_schema.SavedSearch},
    'collections': {'return_type': shopify_sgqlc_schema.CollectionConnection, 'node_type': shopify_sgqlc_schema.Collection},
    'collectionsCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'comment': {'return_type': shopify_sgqlc_schema.Comment, 'node_type': None},
    'comments': {'return_type': shopify_sgqlc_schema.CommentConnection, 'node_type': shopify_sgqlc_schema.Comment},
    'companies': {'return_type': shopify_sgqlc_schema.CompanyConnection, 'node_type': shopify_sgqlc_schema.Company},
    'companiesCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'company': {'return_type': shopify_sgqlc_schema.Company, 'node_type': None},
    'companyContact': {'return_type': shopify_sgqlc_schema.CompanyContact, 'node_type': None},
    'companyContactRole': {'return_type': shopify_sgqlc_schema.CompanyContactRole, 'node_type': None},
    'companyLocation': {'return_type': shopify_sgqlc_schema.CompanyLocation, 'node_type': None},
    'companyLocations': {'return_type': shopify_sgqlc_schema.CompanyLocationConnection, 'node_type': shopify_sgqlc_schema.CompanyLocation},
    'currentAppInstallation': {'return_type': shopify_sgqlc_schema.AppInstallation, 'node_type': None},
    'currentBulkOperation': {'return_type': shopify_sgqlc_schema.BulkOperation, 'node_type': None},
    'currentStaffMember': {'return_type': shopify_sgqlc_schema.StaffMember, 'node_type': None},
    'customer': {'return_type': shopify_sgqlc_schema.Customer, 'node_type': None},
    'customerAccountPage': {'return_type': shopify_sgqlc_schema.CustomerAccountPage, 'node_type': None},
    'customerAccountPages': {'return_type': shopify_sgqlc_schema.CustomerAccountPageConnection, 'node_type': shopify_sgqlc_schema.CustomerAccountPage},
    'customerMergeJobStatus': {'return_type': shopify_sgqlc_schema.CustomerMergeRequest, 'node_type': None},
    'customerMergePreview': {'return_type': shopify_sgqlc_schema.CustomerMergePreview, 'node_type': None},
    'customerPaymentMethod': {'return_type': shopify_sgqlc_schema.CustomerPaymentMethod, 'node_type': None},
    'customerSegmentMembers': {'return_type': shopify_sgqlc_schema.CustomerSegmentMemberConnection, 'node_type': shopify_sgqlc_schema.CustomerSegmentMember},
    'customerSegmentMembersQuery': {'return_type': shopify_sgqlc_schema.CustomerSegmentMembersQuery, 'node_type': None},
    'customerSegmentMembership': {'return_type': shopify_sgqlc_schema.SegmentMembershipResponse, 'node_type': None},
    'customers': {'return_type': shopify_sgqlc_schema.CustomerConnection, 'node_type': shopify_sgqlc_schema.Customer},
    'customersCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'deletionEvents': {'return_type': shopify_sgqlc_schema.DeletionEventConnection, 'node_type': shopify_sgqlc_schema.DeletionEvent},
    'deliveryCustomization': {'return_type': shopify_sgqlc_schema.DeliveryCustomization, 'node_type': None},
    'deliveryCustomizations': {'return_type': shopify_sgqlc_schema.DeliveryCustomizationConnection, 'node_type': shopify_sgqlc_schema.DeliveryCustomization},
    'deliveryProfile': {'return_type': shopify_sgqlc_schema.DeliveryProfile, 'node_type': None},
    'deliveryProfiles': {'return_type': shopify_sgqlc_schema.DeliveryProfileConnection, 'node_type': shopify_sgqlc_schema.DeliveryProfile},
    'deliveryPromiseProvider': {'return_type': shopify_sgqlc_schema.DeliveryPromiseProvider, 'node_type': None},
    'deliverySettings': {'return_type': shopify_sgqlc_schema.DeliverySetting, 'node_type': None},
    'discountCodesCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'discountNode': {'return_type': shopify_sgqlc_schema.DiscountNode, 'node_type': None},
    'discountNodes': {'return_type': shopify_sgqlc_schema.DiscountNodeConnection, 'node_type': shopify_sgqlc_schema.DiscountNode},
    'discountNodesCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'discountRedeemCodeBulkCreation': {'return_type': shopify_sgqlc_schema.DiscountRedeemCodeBulkCreation, 'node_type': None},
    'discountRedeemCodeSavedSearches': {'return_type': shopify_sgqlc_schema.SavedSearchConnection, 'node_type': shopify_sgqlc_schema.SavedSearch},
    'dispute': {'return_type': shopify_sgqlc_schema.ShopifyPaymentsDispute, 'node_type': None},
    'disputeEvidence': {'return_type': shopify_sgqlc_schema.ShopifyPaymentsDisputeEvidence, 'node_type': None},
    'disputes': {'return_type': shopify_sgqlc_schema.ShopifyPaymentsDisputeConnection, 'node_type': shopify_sgqlc_schema.ShopifyPaymentsDispute},
    'domain': {'return_type': shopify_sgqlc_schema.Domain, 'node_type': None},
    'draftOrder': {'return_type': shopify_sgqlc_schema.DraftOrder, 'node_type': None},
    'draftOrderSavedSearches': {'return_type': shopify_sgqlc_schema.SavedSearchConnection, 'node_type': shopify_sgqlc_schema.SavedSearch},
    'draftOrderTag': {'return_type': shopify_sgqlc_schema.DraftOrderTag, 'node_type': None},
    'draftOrders': {'return_type': shopify_sgqlc_schema.DraftOrderConnection, 'node_type': shopify_sgqlc_schema.DraftOrder},
    'event': {'return_type': shopify_sgqlc_schema.Event, 'node_type': None},
    'events': {'return_type': shopify_sgqlc_schema.EventConnection, 'node_type': shopify_sgqlc_schema.Event},
    'eventsCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'fileSavedSearches': {'return_type': shopify_sgqlc_schema.SavedSearchConnection, 'node_type': shopify_sgqlc_schema.SavedSearch},
    'files': {'return_type': shopify_sgqlc_schema.FileConnection, 'node_type': shopify_sgqlc_schema.File},
    'fulfillment': {'return_type': shopify_sgqlc_schema.Fulfillment, 'node_type': None},
    'fulfillmentConstraintRules': {'return_type': shopify_sgqlc_schema.FulfillmentConstraintRule, 'node_type': None},
    'fulfillmentOrder': {'return_type': shopify_sgqlc_schema.FulfillmentOrder, 'node_type': None},
    'fulfillmentOrders': {'return_type': shopify_sgqlc_schema.FulfillmentOrderConnection, 'node_type': shopify_sgqlc_schema.FulfillmentOrder},
    'fulfillmentService': {'return_type': shopify_sgqlc_schema.FulfillmentService, 'node_type': None},
    'giftCard': {'return_type': shopify_sgqlc_schema.GiftCard, 'node_type': None},
    'giftCards': {'return_type': shopify_sgqlc_schema.GiftCardConnection, 'node_type': shopify_sgqlc_schema.GiftCard},
    'giftCardsCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'inventoryItem': {'return_type': shopify_sgqlc_schema.InventoryItem, 'node_type': None},
    'inventoryItems': {'return_type': shopify_sgqlc_schema.InventoryItemConnection, 'node_type': shopify_sgqlc_schema.InventoryItem},
    'inventoryLevel': {'return_type': shopify_sgqlc_schema.InventoryLevel, 'node_type': None},
    'inventoryProperties': {'return_type': shopify_sgqlc_schema.InventoryProperties, 'node_type': None},
    'job': {'return_type': shopify_sgqlc_schema.Job, 'node_type': None},
    'location': {'return_type': shopify_sgqlc_schema.Location, 'node_type': None},
    'locations': {'return_type': shopify_sgqlc_schema.LocationConnection, 'node_type': shopify_sgqlc_schema.Location},
    'locationsAvailableForDeliveryProfiles': {'return_type': shopify_sgqlc_schema.Location, 'node_type': None},
    'locationsAvailableForDeliveryProfilesConnection': {'return_type': shopify_sgqlc_schema.LocationConnection, 'node_type': shopify_sgqlc_schema.Location},
    'locationsCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'manualHoldsFulfillmentOrders': {'return_type': shopify_sgqlc_schema.FulfillmentOrderConnection, 'node_type': shopify_sgqlc_schema.FulfillmentOrder},
    'market': {'return_type': shopify_sgqlc_schema.Market, 'node_type': None},
    'marketByGeography': {'return_type': shopify_sgqlc_schema.Market, 'node_type': None},
    'marketLocalizableResource': {'return_type': shopify_sgqlc_schema.MarketLocalizableResource, 'node_type': None},
    'marketLocalizableResources': {'return_type': shopify_sgqlc_schema.MarketLocalizableResourceConnection, 'node_type': shopify_sgqlc_schema.MarketLocalizableResource},
    'marketLocalizableResourcesByIds': {'return_type': shopify_sgqlc_schema.MarketLocalizableResourceConnection, 'node_type': shopify_sgqlc_schema.MarketLocalizableResource},
    'marketingActivities': {'return_type': shopify_sgqlc_schema.MarketingActivityConnection, 'node_type': shopify_sgqlc_schema.MarketingActivity},
    'marketingActivity': {'return_type': shopify_sgqlc_schema.MarketingActivity, 'node_type': None},
    'marketingEvent': {'return_type': shopify_sgqlc_schema.MarketingEvent, 'node_type': None},
    'marketingEvents': {'return_type': shopify_sgqlc_schema.MarketingEventConnection, 'node_type': shopify_sgqlc_schema.MarketingEvent},
    'markets': {'return_type': shopify_sgqlc_schema.MarketConnection, 'node_type': shopify_sgqlc_schema.Market},
    'menu': {'return_type': shopify_sgqlc_schema.Menu, 'node_type': None},
    'menus': {'return_type': shopify_sgqlc_schema.MenuConnection, 'node_type': shopify_sgqlc_schema.Menu},
    'metafieldDefinition': {'return_type': shopify_sgqlc_schema.MetafieldDefinition, 'node_type': None},
    'metafieldDefinitionTypes': {'return_type': shopify_sgqlc_schema.MetafieldDefinitionType, 'node_type': None},
    'metafieldDefinitions': {'return_type': shopify_sgqlc_schema.MetafieldDefinitionConnection, 'node_type': shopify_sgqlc_schema.MetafieldDefinition},
    'metafieldStorefrontVisibilities': {'return_type': shopify_sgqlc_schema.MetafieldStorefrontVisibilityConnection, 'node_type': shopify_sgqlc_schema.MetafieldStorefrontVisibility},
    'metafieldStorefrontVisibility': {'return_type': shopify_sgqlc_schema.MetafieldStorefrontVisibility, 'node_type': None},
    'metaobject': {'return_type': shopify_sgqlc_schema.Metaobject, 'node_type': None},
    'metaobjectByHandle': {'return_type': shopify_sgqlc_schema.Metaobject, 'node_type': None},
    'metaobjectDefinition': {'return_type': shopify_sgqlc_schema.MetaobjectDefinition, 'node_type': None},
    'metaobjectDefinitionByType': {'return_type': shopify_sgqlc_schema.MetaobjectDefinition, 'node_type': None},
    'metaobjectDefinitions': {'return_type': shopify_sgqlc_schema.MetaobjectDefinitionConnection, 'node_type': shopify_sgqlc_schema.MetaobjectDefinition},
    'metaobjects': {'return_type': shopify_sgqlc_schema.MetaobjectConnection, 'node_type': shopify_sgqlc_schema.Metaobject},
    'mobilePlatformApplication': {'return_type': shopify_sgqlc_schema.MobilePlatformApplication, 'node_type': None},
    'mobilePlatformApplications': {'return_type': shopify_sgqlc_schema.MobilePlatformApplicationConnection, 'node_type': shopify_sgqlc_schema.MobilePlatformApplication},
    'node': {'return_type': shopify_sgqlc_schema.Node, 'node_type': None},
    'nodes': {'return_type': shopify_sgqlc_schema.Node, 'node_type': None},
    'onlineStore': {'return_type': shopify_sgqlc_schema.OnlineStore, 'node_type': None},
    'order': {'return_type': shopify_sgqlc_schema.Order, 'node_type': None},
    'orderPaymentStatus': {'return_type': shopify_sgqlc_schema.OrderPaymentStatus, 'node_type': None},
    'orderSavedSearches': {'return_type': shopify_sgqlc_schema.SavedSearchConnection, 'node_type': shopify_sgqlc_schema.SavedSearch},
    'orders': {'return_type': shopify_sgqlc_schema.OrderConnection, 'node_type': shopify_sgqlc_schema.Order},
    'ordersCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'page': {'return_type': shopify_sgqlc_schema.Page, 'node_type': None},
    'pages': {'return_type': shopify_sgqlc_schema.PageConnection, 'node_type': shopify_sgqlc_schema.Page},
    'pagesCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'paymentCustomization': {'return_type': shopify_sgqlc_schema.PaymentCustomization, 'node_type': None},
    'paymentCustomizations': {'return_type': shopify_sgqlc_schema.PaymentCustomizationConnection, 'node_type': shopify_sgqlc_schema.PaymentCustomization},
    'paymentTermsTemplates': {'return_type': shopify_sgqlc_schema.PaymentTermsTemplate, 'node_type': None},
    'pendingOrdersCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'priceList': {'return_type': shopify_sgqlc_schema.PriceList, 'node_type': None},
    'priceLists': {'return_type': shopify_sgqlc_schema.PriceListConnection, 'node_type': shopify_sgqlc_schema.PriceList},
    'primaryMarket': {'return_type': shopify_sgqlc_schema.Market, 'node_type': None},
    'privateMetafield': {'return_type': shopify_sgqlc_schema.PrivateMetafield, 'node_type': None},
    'privateMetafields': {'return_type': shopify_sgqlc_schema.PrivateMetafieldConnection, 'node_type': shopify_sgqlc_schema.PrivateMetafield},
    'product': {'return_type': shopify_sgqlc_schema.Product, 'node_type': None},
    'productByHandle': {'return_type': shopify_sgqlc_schema.Product, 'node_type': None},
    'productDuplicateJob': {'return_type': shopify_sgqlc_schema.ProductDuplicateJob, 'node_type': None},
    'productFeed': {'return_type': shopify_sgqlc_schema.ProductFeed, 'node_type': None},
    'productFeeds': {'return_type': shopify_sgqlc_schema.ProductFeedConnection, 'node_type': shopify_sgqlc_schema.ProductFeed},
    'productOperation': {'return_type': shopify_sgqlc_schema.ProductOperation, 'node_type': None},
    'productResourceFeedback': {'return_type': shopify_sgqlc_schema.ProductResourceFeedback, 'node_type': None},
    'productSavedSearches': {'return_type': shopify_sgqlc_schema.SavedSearchConnection, 'node_type': shopify_sgqlc_schema.SavedSearch},
    'productVariant': {'return_type': shopify_sgqlc_schema.ProductVariant, 'node_type': None},
    'productVariants': {'return_type': shopify_sgqlc_schema.ProductVariantConnection, 'node_type': shopify_sgqlc_schema.ProductVariant},
    'productVariantsCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'products': {'return_type': shopify_sgqlc_schema.ProductConnection, 'node_type': shopify_sgqlc_schema.Product},
    'productsCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'publicApiVersions': {'return_type': shopify_sgqlc_schema.ApiVersion, 'node_type': None},
    'publication': {'return_type': shopify_sgqlc_schema.Publication, 'node_type': None},
    'publications': {'return_type': shopify_sgqlc_schema.PublicationConnection, 'node_type': shopify_sgqlc_schema.Publication},
    'publicationsCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'publishedProductsCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'refund': {'return_type': shopify_sgqlc_schema.Refund, 'node_type': None},
    'return': {'return_type': shopify_sgqlc_schema.Return, 'node_type': None},
    'returnCalculate': {'return_type': shopify_sgqlc_schema.CalculatedReturn, 'node_type': None},
    'returnableFulfillment': {'return_type': shopify_sgqlc_schema.ReturnableFulfillment, 'node_type': None},
    'returnableFulfillments': {'return_type': shopify_sgqlc_schema.ReturnableFulfillmentConnection, 'node_type': shopify_sgqlc_schema.ReturnableFulfillment},
    'reverseDelivery': {'return_type': shopify_sgqlc_schema.ReverseDelivery, 'node_type': None},
    'reverseFulfillmentOrder': {'return_type': shopify_sgqlc_schema.ReverseFulfillmentOrder, 'node_type': None},
    'scriptTag': {'return_type': shopify_sgqlc_schema.ScriptTag, 'node_type': None},
    'scriptTags': {'return_type': shopify_sgqlc_schema.ScriptTagConnection, 'node_type': shopify_sgqlc_schema.ScriptTag},
    'segment': {'return_type': shopify_sgqlc_schema.Segment, 'node_type': None},
    'segmentFilterSuggestions': {'return_type': shopify_sgqlc_schema.SegmentFilterConnection, 'node_type': shopify_sgqlc_schema.SegmentFilter},
    'segmentFilters': {'return_type': shopify_sgqlc_schema.SegmentFilterConnection, 'node_type': shopify_sgqlc_schema.SegmentFilter},
    'segmentMigrations': {'return_type': shopify_sgqlc_schema.SegmentMigrationConnection, 'node_type': shopify_sgqlc_schema.SegmentMigration},
    'segmentValueSuggestions': {'return_type': shopify_sgqlc_schema.SegmentValueConnection, 'node_type': shopify_sgqlc_schema.SegmentValue},
    'segments': {'return_type': shopify_sgqlc_schema.SegmentConnection, 'node_type': shopify_sgqlc_schema.Segment},
    'segmentsCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'sellingPlanGroup': {'return_type': shopify_sgqlc_schema.SellingPlanGroup, 'node_type': None},
    'sellingPlanGroups': {'return_type': shopify_sgqlc_schema.SellingPlanGroupConnection, 'node_type': shopify_sgqlc_schema.SellingPlanGroup},
    'serverPixel': {'return_type': shopify_sgqlc_schema.ServerPixel, 'node_type': None},
    'shop': {'return_type': shopify_sgqlc_schema.Shop, 'node_type': None},
    'shopBillingPreferences': {'return_type': shopify_sgqlc_schema.ShopBillingPreferences, 'node_type': None},
    'shopLocales': {'return_type': shopify_sgqlc_schema.ShopLocale, 'node_type': None},
    'shopifyFunction': {'return_type': shopify_sgqlc_schema.ShopifyFunction, 'node_type': None},
    'shopifyFunctions': {'return_type': shopify_sgqlc_schema.ShopifyFunctionConnection, 'node_type': shopify_sgqlc_schema.ShopifyFunction},
    'shopifyPaymentsAccount': {'return_type': shopify_sgqlc_schema.ShopifyPaymentsAccount, 'node_type': None},
    'staffMember': {'return_type': shopify_sgqlc_schema.StaffMember, 'node_type': None},
    'staffMembers': {'return_type': shopify_sgqlc_schema.StaffMemberConnection, 'node_type': shopify_sgqlc_schema.StaffMember},
    'standardMetafieldDefinitionTemplates': {'return_type': shopify_sgqlc_schema.StandardMetafieldDefinitionTemplateConnection, 'node_type': shopify_sgqlc_schema.StandardMetafieldDefinitionTemplate},
    'storeCreditAccount': {'return_type': shopify_sgqlc_schema.StoreCreditAccount, 'node_type': None},
    'subscriptionBillingAttempt': {'return_type': shopify_sgqlc_schema.SubscriptionBillingAttempt, 'node_type': None},
    'subscriptionBillingAttempts': {'return_type': shopify_sgqlc_schema.SubscriptionBillingAttemptConnection, 'node_type': shopify_sgqlc_schema.SubscriptionBillingAttempt},
    'subscriptionBillingCycle': {'return_type': shopify_sgqlc_schema.SubscriptionBillingCycle, 'node_type': None},
    'subscriptionBillingCycleBulkResults': {'return_type': shopify_sgqlc_schema.SubscriptionBillingCycleConnection, 'node_type': shopify_sgqlc_schema.SubscriptionBillingCycle},
    'subscriptionBillingCycles': {'return_type': shopify_sgqlc_schema.SubscriptionBillingCycleConnection, 'node_type': shopify_sgqlc_schema.SubscriptionBillingCycle},
    'subscriptionContract': {'return_type': shopify_sgqlc_schema.SubscriptionContract, 'node_type': None},
    'subscriptionContracts': {'return_type': shopify_sgqlc_schema.SubscriptionContractConnection, 'node_type': shopify_sgqlc_schema.SubscriptionContract},
    'subscriptionDraft': {'return_type': shopify_sgqlc_schema.SubscriptionDraft, 'node_type': None},
    'taxonomy': {'return_type': shopify_sgqlc_schema.Taxonomy, 'node_type': None},
    'tenderTransactions': {'return_type': shopify_sgqlc_schema.TenderTransactionConnection, 'node_type': shopify_sgqlc_schema.TenderTransaction},
    'theme': {'return_type': shopify_sgqlc_schema.OnlineStoreTheme, 'node_type': None},
    'themes': {'return_type': shopify_sgqlc_schema.OnlineStoreThemeConnection, 'node_type': shopify_sgqlc_schema.OnlineStoreTheme},
    'translatableResource': {'return_type': shopify_sgqlc_schema.TranslatableResource, 'node_type': None},
    'translatableResources': {'return_type': shopify_sgqlc_schema.TranslatableResourceConnection, 'node_type': shopify_sgqlc_schema.TranslatableResource},
    'translatableResourcesByIds': {'return_type': shopify_sgqlc_schema.TranslatableResourceConnection, 'node_type': shopify_sgqlc_schema.TranslatableResource},
    'urlRedirect': {'return_type': shopify_sgqlc_schema.UrlRedirect, 'node_type': None},
    'urlRedirectImport': {'return_type': shopify_sgqlc_schema.UrlRedirectImport, 'node_type': None},
    'urlRedirectSavedSearches': {'return_type': shopify_sgqlc_schema.SavedSearchConnection, 'node_type': shopify_sgqlc_schema.SavedSearch},
    'urlRedirects': {'return_type': shopify_sgqlc_schema.UrlRedirectConnection, 'node_type': shopify_sgqlc_schema.UrlRedirect},
    'urlRedirectsCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
    'validation': {'return_type': shopify_sgqlc_schema.Validation, 'node_type': None},
    'validations': {'return_type': shopify_sgqlc_schema.ValidationConnection, 'node_type': shopify_sgqlc_schema.Validation},
    'webPixel': {'return_type': shopify_sgqlc_schema.WebPixel, 'node_type': None},
    'webhookSubscription': {'return_type': shopify_sgqlc_schema.WebhookSubscription, 'node_type': None},
    'webhookSubscriptions': {'return_type': shopify_sgqlc_schema.WebhookSubscriptionConnection, 'node_type': shopify_sgqlc_schema.WebhookSubscription},
    'webhookSubscriptionsCount': {'return_type': shopify_sgqlc_schema.Count, 'node_type': None},
}

GRAPHQL_TO_MODEL_MAP: Dict[Type[Any], Type[Any]] = {
    shopify_sgqlc_schema.AbandonedCheckout: shopify_models.ShopifyAbandonedCheckout,
    shopify_sgqlc_schema.AbandonedCheckoutLineItem: shopify_models.ShopifyAbandonedCheckoutLineItem,
    shopify_sgqlc_schema.Abandonment: shopify_models.ShopifyAbandonment,
    shopify_sgqlc_schema.AccessScope: shopify_models.ShopifyAccessScope,
    shopify_sgqlc_schema.AddAllProductsOperation: shopify_models.ShopifyAddAllProductsOperation,
    shopify_sgqlc_schema.AdditionalFee: shopify_models.ShopifyAdditionalFee,
    shopify_sgqlc_schema.AdditionalFeeSale: shopify_models.ShopifyAdditionalFeeSale,
    shopify_sgqlc_schema.AdjustmentSale: shopify_models.ShopifyAdjustmentSale,
    shopify_sgqlc_schema.AllDiscountItems: shopify_models.ShopifyAllDiscountItems,
    shopify_sgqlc_schema.AndroidApplication: shopify_models.ShopifyAndroidApplication,
    shopify_sgqlc_schema.ApiVersion: shopify_models.ShopifyApiVersion,
    shopify_sgqlc_schema.AppCatalog: shopify_models.ShopifyAppCatalog,
    shopify_sgqlc_schema.AppFeedback: shopify_models.ShopifyAppFeedback,
    shopify_sgqlc_schema.AppRecurringPricing: shopify_models.ShopifyAppRecurringPricing,
    shopify_sgqlc_schema.AppRevokeAccessScopesAppRevokeScopeError: shopify_models.ShopifyAppRevokeAccessScopesAppRevokeScopeError,
    shopify_sgqlc_schema.AppSubscriptionDiscount: shopify_models.ShopifyAppSubscriptionDiscount,
    shopify_sgqlc_schema.AppSubscriptionDiscountAmount: shopify_models.ShopifyAppSubscriptionDiscountAmount,
    shopify_sgqlc_schema.AppSubscriptionDiscountPercentage: shopify_models.ShopifyAppSubscriptionDiscountPercentage,
    shopify_sgqlc_schema.AppUsagePricing: shopify_models.ShopifyAppUsagePricing,
    shopify_sgqlc_schema.AppUsageRecord: shopify_models.ShopifyAppUsageRecord,
    shopify_sgqlc_schema.AppleApplication: shopify_models.ShopifyAppleApplication,
    shopify_sgqlc_schema.Article: shopify_models.ShopifyArticle,
    shopify_sgqlc_schema.ArticleAuthor: shopify_models.ShopifyArticleAuthor,
    shopify_sgqlc_schema.AutomaticDiscountApplication: shopify_models.ShopifyAutomaticDiscountApplication,
    shopify_sgqlc_schema.AvailableChannelDefinitionsByChannel: shopify_models.ShopifyAvailableChannelDefinitionsByChannel,
    shopify_sgqlc_schema.BasicEvent: shopify_models.ShopifyBasicEvent,
    shopify_sgqlc_schema.Blog: shopify_models.ShopifyBlog,
    shopify_sgqlc_schema.BlogFeed: shopify_models.ShopifyBlogFeed,
    shopify_sgqlc_schema.BulkOperation: shopify_models.ShopifyBulkOperation,
    shopify_sgqlc_schema.BundlesFeature: shopify_models.ShopifyBundlesFeature,
    shopify_sgqlc_schema.BusinessEntity: shopify_models.ShopifyBusinessEntity,
    shopify_sgqlc_schema.BusinessEntityAddress: shopify_models.ShopifyBusinessEntityAddress,
    shopify_sgqlc_schema.BuyerExperienceConfiguration: shopify_models.ShopifyBuyerExperienceConfiguration,
    shopify_sgqlc_schema.CalculatedAutomaticDiscountApplication: shopify_models.ShopifyCalculatedAutomaticDiscountApplication,
    shopify_sgqlc_schema.CalculatedDiscountAllocation: shopify_models.ShopifyCalculatedDiscountAllocation,
    shopify_sgqlc_schema.CalculatedDiscountCodeApplication: shopify_models.ShopifyCalculatedDiscountCodeApplication,
    shopify_sgqlc_schema.CalculatedDraftOrder: shopify_models.ShopifyCalculatedDraftOrder,
    shopify_sgqlc_schema.CalculatedDraftOrderLineItem: shopify_models.ShopifyCalculatedDraftOrderLineItem,
    shopify_sgqlc_schema.CalculatedExchangeLineItem: shopify_models.ShopifyCalculatedExchangeLineItem,
    shopify_sgqlc_schema.CalculatedLineItem: shopify_models.ShopifyCalculatedLineItem,
    shopify_sgqlc_schema.CalculatedManualDiscountApplication: shopify_models.ShopifyCalculatedManualDiscountApplication,
    shopify_sgqlc_schema.CalculatedOrder: shopify_models.ShopifyCalculatedOrder,
    shopify_sgqlc_schema.CalculatedRestockingFee: shopify_models.ShopifyCalculatedRestockingFee,
    shopify_sgqlc_schema.CalculatedReturn: shopify_models.ShopifyCalculatedReturn,
    shopify_sgqlc_schema.CalculatedReturnLineItem: shopify_models.ShopifyCalculatedReturnLineItem,
    shopify_sgqlc_schema.CalculatedReturnShippingFee: shopify_models.ShopifyCalculatedReturnShippingFee,
    shopify_sgqlc_schema.CalculatedScriptDiscountApplication: shopify_models.ShopifyCalculatedScriptDiscountApplication,
    shopify_sgqlc_schema.CalculatedShippingLine: shopify_models.ShopifyCalculatedShippingLine,
    shopify_sgqlc_schema.CardPaymentDetails: shopify_models.ShopifyCardPaymentDetails,
    shopify_sgqlc_schema.CartTransform: shopify_models.ShopifyCartTransform,
    shopify_sgqlc_schema.CartTransformEligibleOperations: shopify_models.ShopifyCartTransformEligibleOperations,
    shopify_sgqlc_schema.CartTransformFeature: shopify_models.ShopifyCartTransformFeature,
    shopify_sgqlc_schema.CashRoundingAdjustment: shopify_models.ShopifyCashRoundingAdjustment,
    shopify_sgqlc_schema.CashTrackingAdjustment: shopify_models.ShopifyCashTrackingAdjustment,
    shopify_sgqlc_schema.CashTrackingSession: shopify_models.ShopifyCashTrackingSession,
    shopify_sgqlc_schema.CatalogCsvOperation: shopify_models.ShopifyCatalogCsvOperation,
    shopify_sgqlc_schema.Channel: shopify_models.ShopifyChannel,
    shopify_sgqlc_schema.ChannelDefinition: shopify_models.ShopifyChannelDefinition,
    shopify_sgqlc_schema.ChannelInformation: shopify_models.ShopifyChannelInformation,
    shopify_sgqlc_schema.CheckoutBranding: shopify_models.ShopifyCheckoutBranding,
    shopify_sgqlc_schema.CheckoutBrandingButton: shopify_models.ShopifyCheckoutBrandingButton,
    shopify_sgqlc_schema.CheckoutBrandingButtonColorRoles: shopify_models.ShopifyCheckoutBrandingButtonColorRoles,
    shopify_sgqlc_schema.CheckoutBrandingBuyerJourney: shopify_models.ShopifyCheckoutBrandingBuyerJourney,
    shopify_sgqlc_schema.CheckoutBrandingCartLink: shopify_models.ShopifyCheckoutBrandingCartLink,
    shopify_sgqlc_schema.CheckoutBrandingCheckbox: shopify_models.ShopifyCheckoutBrandingCheckbox,
    shopify_sgqlc_schema.CheckoutBrandingChoiceList: shopify_models.ShopifyCheckoutBrandingChoiceList,
    shopify_sgqlc_schema.CheckoutBrandingChoiceListGroup: shopify_models.ShopifyCheckoutBrandingChoiceListGroup,
    shopify_sgqlc_schema.CheckoutBrandingColorGlobal: shopify_models.ShopifyCheckoutBrandingColorGlobal,
    shopify_sgqlc_schema.CheckoutBrandingColorRoles: shopify_models.ShopifyCheckoutBrandingColorRoles,
    shopify_sgqlc_schema.CheckoutBrandingColorScheme: shopify_models.ShopifyCheckoutBrandingColorScheme,
    shopify_sgqlc_schema.CheckoutBrandingColorSchemes: shopify_models.ShopifyCheckoutBrandingColorSchemes,
    shopify_sgqlc_schema.CheckoutBrandingColors: shopify_models.ShopifyCheckoutBrandingColors,
    shopify_sgqlc_schema.CheckoutBrandingContainerDivider: shopify_models.ShopifyCheckoutBrandingContainerDivider,
    shopify_sgqlc_schema.CheckoutBrandingContent: shopify_models.ShopifyCheckoutBrandingContent,
    shopify_sgqlc_schema.CheckoutBrandingControl: shopify_models.ShopifyCheckoutBrandingControl,
    shopify_sgqlc_schema.CheckoutBrandingControlColorRoles: shopify_models.ShopifyCheckoutBrandingControlColorRoles,
    shopify_sgqlc_schema.CheckoutBrandingCornerRadiusVariables: shopify_models.ShopifyCheckoutBrandingCornerRadiusVariables,
    shopify_sgqlc_schema.CheckoutBrandingCustomFont: shopify_models.ShopifyCheckoutBrandingCustomFont,
    shopify_sgqlc_schema.CheckoutBrandingCustomizations: shopify_models.ShopifyCheckoutBrandingCustomizations,
    shopify_sgqlc_schema.CheckoutBrandingDesignSystem: shopify_models.ShopifyCheckoutBrandingDesignSystem,
    shopify_sgqlc_schema.CheckoutBrandingDividerStyle: shopify_models.ShopifyCheckoutBrandingDividerStyle,
    shopify_sgqlc_schema.CheckoutBrandingExpressCheckout: shopify_models.ShopifyCheckoutBrandingExpressCheckout,
    shopify_sgqlc_schema.CheckoutBrandingExpressCheckoutButton: shopify_models.ShopifyCheckoutBrandingExpressCheckoutButton,
    shopify_sgqlc_schema.CheckoutBrandingFontGroup: shopify_models.ShopifyCheckoutBrandingFontGroup,
    shopify_sgqlc_schema.CheckoutBrandingFontSize: shopify_models.ShopifyCheckoutBrandingFontSize,
    shopify_sgqlc_schema.CheckoutBrandingFooter: shopify_models.ShopifyCheckoutBrandingFooter,
    shopify_sgqlc_schema.CheckoutBrandingFooterContent: shopify_models.ShopifyCheckoutBrandingFooterContent,
    shopify_sgqlc_schema.CheckoutBrandingGlobal: shopify_models.ShopifyCheckoutBrandingGlobal,
    shopify_sgqlc_schema.CheckoutBrandingHeader: shopify_models.ShopifyCheckoutBrandingHeader,
    shopify_sgqlc_schema.CheckoutBrandingHeaderCartLink: shopify_models.ShopifyCheckoutBrandingHeaderCartLink,
    shopify_sgqlc_schema.CheckoutBrandingHeadingLevel: shopify_models.ShopifyCheckoutBrandingHeadingLevel,
    shopify_sgqlc_schema.CheckoutBrandingImage: shopify_models.ShopifyCheckoutBrandingImage,
    shopify_sgqlc_schema.CheckoutBrandingLogo: shopify_models.ShopifyCheckoutBrandingLogo,
    shopify_sgqlc_schema.CheckoutBrandingMain: shopify_models.ShopifyCheckoutBrandingMain,
    shopify_sgqlc_schema.CheckoutBrandingMainSection: shopify_models.ShopifyCheckoutBrandingMainSection,
    shopify_sgqlc_schema.CheckoutBrandingMerchandiseThumbnail: shopify_models.ShopifyCheckoutBrandingMerchandiseThumbnail,
    shopify_sgqlc_schema.CheckoutBrandingOrderSummary: shopify_models.ShopifyCheckoutBrandingOrderSummary,
    shopify_sgqlc_schema.CheckoutBrandingOrderSummarySection: shopify_models.ShopifyCheckoutBrandingOrderSummarySection,
    shopify_sgqlc_schema.CheckoutBrandingSelect: shopify_models.ShopifyCheckoutBrandingSelect,
    shopify_sgqlc_schema.CheckoutBrandingShopifyFont: shopify_models.ShopifyCheckoutBrandingShopifyFont,
    shopify_sgqlc_schema.CheckoutBrandingTextField: shopify_models.ShopifyCheckoutBrandingTextField,
    shopify_sgqlc_schema.CheckoutBrandingTypography: shopify_models.ShopifyCheckoutBrandingTypography,
    shopify_sgqlc_schema.CheckoutBrandingTypographyStyle: shopify_models.ShopifyCheckoutBrandingTypographyStyle,
    shopify_sgqlc_schema.CheckoutBrandingTypographyStyleGlobal: shopify_models.ShopifyCheckoutBrandingTypographyStyleGlobal,
    shopify_sgqlc_schema.CheckoutProfile: shopify_models.ShopifyCheckoutProfile,
    shopify_sgqlc_schema.Collection: shopify_models.ShopifyCollection,
    shopify_sgqlc_schema.CollectionPublication: shopify_models.ShopifyCollectionPublication,
    shopify_sgqlc_schema.CollectionRule: shopify_models.ShopifyCollectionRule,
    shopify_sgqlc_schema.CollectionRuleCategoryCondition: shopify_models.ShopifyCollectionRuleCategoryCondition,
    shopify_sgqlc_schema.CollectionRuleConditions: shopify_models.ShopifyCollectionRuleConditions,
    shopify_sgqlc_schema.CollectionRuleMetafieldCondition: shopify_models.ShopifyCollectionRuleMetafieldCondition,
    shopify_sgqlc_schema.CollectionRuleProductCategoryCondition: shopify_models.ShopifyCollectionRuleProductCategoryCondition,
    shopify_sgqlc_schema.CollectionRuleSet: shopify_models.ShopifyCollectionRuleSet,
    shopify_sgqlc_schema.CollectionRuleTextCondition: shopify_models.ShopifyCollectionRuleTextCondition,
    shopify_sgqlc_schema.CombinedListing: shopify_models.ShopifyCombinedListing,
    shopify_sgqlc_schema.CombinedListingChild: shopify_models.ShopifyCombinedListingChild,
    shopify_sgqlc_schema.Comment: shopify_models.ShopifyComment,
    shopify_sgqlc_schema.CommentAuthor: shopify_models.ShopifyCommentAuthor,
    shopify_sgqlc_schema.CommentEvent: shopify_models.ShopifyCommentEvent,
    shopify_sgqlc_schema.CommentEventAttachment: shopify_models.ShopifyCommentEventAttachment,
    shopify_sgqlc_schema.Company: shopify_models.ShopifyCompany,
    shopify_sgqlc_schema.CompanyAddress: shopify_models.ShopifyCompanyAddress,
    shopify_sgqlc_schema.CompanyContact: shopify_models.ShopifyCompanyContact,
    shopify_sgqlc_schema.CompanyContactRole: shopify_models.ShopifyCompanyContactRole,
    shopify_sgqlc_schema.CompanyContactRoleAssignment: shopify_models.ShopifyCompanyContactRoleAssignment,
    shopify_sgqlc_schema.CompanyLocation: shopify_models.ShopifyCompanyLocation,
    shopify_sgqlc_schema.CompanyLocationCatalog: shopify_models.ShopifyCompanyLocationCatalog,
    shopify_sgqlc_schema.CompanyLocationStaffMemberAssignment: shopify_models.ShopifyCompanyLocationStaffMemberAssignment,
    shopify_sgqlc_schema.Count: shopify_models.ShopifyCount,
    shopify_sgqlc_schema.CountriesInShippingZones: shopify_models.ShopifyCountriesInShippingZones,
    shopify_sgqlc_schema.CountryHarmonizedSystemCode: shopify_models.ShopifyCountryHarmonizedSystemCode,
    shopify_sgqlc_schema.CurrencyFormats: shopify_models.ShopifyCurrencyFormats,
    shopify_sgqlc_schema.CurrencySetting: shopify_models.ShopifyCurrencySetting,
    shopify_sgqlc_schema.Customer: shopify_models.ShopifyCustomer,
    shopify_sgqlc_schema.CustomerAccountAppExtensionPage: shopify_models.ShopifyCustomerAccountAppExtensionPage,
    shopify_sgqlc_schema.CustomerAccountNativePage: shopify_models.ShopifyCustomerAccountNativePage,
    shopify_sgqlc_schema.CustomerAccountsV2: shopify_models.ShopifyCustomerAccountsV2,
    shopify_sgqlc_schema.CustomerCreditCard: shopify_models.ShopifyCustomerCreditCard,
    shopify_sgqlc_schema.CustomerCreditCardBillingAddress: shopify_models.ShopifyCustomerCreditCardBillingAddress,
    shopify_sgqlc_schema.CustomerEmailAddress: shopify_models.ShopifyCustomerEmailAddress,
    shopify_sgqlc_schema.CustomerEmailMarketingConsentState: shopify_models.ShopifyCustomerEmailMarketingConsentState,
    shopify_sgqlc_schema.CustomerJourney: shopify_models.ShopifyCustomerJourney,
    shopify_sgqlc_schema.CustomerJourneySummary: shopify_models.ShopifyCustomerJourneySummary,
    shopify_sgqlc_schema.CustomerMergeError: shopify_models.ShopifyCustomerMergeError,
    shopify_sgqlc_schema.CustomerMergePreview: shopify_models.ShopifyCustomerMergePreview,
    shopify_sgqlc_schema.CustomerMergePreviewAlternateFields: shopify_models.ShopifyCustomerMergePreviewAlternateFields,
    shopify_sgqlc_schema.CustomerMergePreviewBlockingFields: shopify_models.ShopifyCustomerMergePreviewBlockingFields,
    shopify_sgqlc_schema.CustomerMergePreviewDefaultFields: shopify_models.ShopifyCustomerMergePreviewDefaultFields,
    shopify_sgqlc_schema.CustomerMergeRequest: shopify_models.ShopifyCustomerMergeRequest,
    shopify_sgqlc_schema.CustomerMergeable: shopify_models.ShopifyCustomerMergeable,
    shopify_sgqlc_schema.CustomerPaymentInstrumentBillingAddress: shopify_models.ShopifyCustomerPaymentInstrumentBillingAddress,
    shopify_sgqlc_schema.CustomerPaymentMethod: shopify_models.ShopifyCustomerPaymentMethod,
    shopify_sgqlc_schema.CustomerPaypalBillingAgreement: shopify_models.ShopifyCustomerPaypalBillingAgreement,
    shopify_sgqlc_schema.CustomerPhoneNumber: shopify_models.ShopifyCustomerPhoneNumber,
    shopify_sgqlc_schema.CustomerSegmentMember: shopify_models.ShopifyCustomerSegmentMember,
    shopify_sgqlc_schema.CustomerSegmentMembersQuery: shopify_models.ShopifyCustomerSegmentMembersQuery,
    shopify_sgqlc_schema.CustomerShopPayAgreement: shopify_models.ShopifyCustomerShopPayAgreement,
    shopify_sgqlc_schema.CustomerSmsMarketingConsentError: shopify_models.ShopifyCustomerSmsMarketingConsentError,
    shopify_sgqlc_schema.CustomerSmsMarketingConsentState: shopify_models.ShopifyCustomerSmsMarketingConsentState,
    shopify_sgqlc_schema.CustomerStatistics: shopify_models.ShopifyCustomerStatistics,
    shopify_sgqlc_schema.CustomerVisit: shopify_models.ShopifyCustomerVisit,
    shopify_sgqlc_schema.DelegateAccessToken: shopify_models.ShopifyDelegateAccessToken,
    shopify_sgqlc_schema.DeletionEvent: shopify_models.ShopifyDeletionEvent,
    shopify_sgqlc_schema.DeliveryAvailableService: shopify_models.ShopifyDeliveryAvailableService,
    shopify_sgqlc_schema.DeliveryBrandedPromise: shopify_models.ShopifyDeliveryBrandedPromise,
    shopify_sgqlc_schema.DeliveryCarrierService: shopify_models.ShopifyDeliveryCarrierService,
    shopify_sgqlc_schema.DeliveryCarrierServiceAndLocations: shopify_models.ShopifyDeliveryCarrierServiceAndLocations,
    shopify_sgqlc_schema.DeliveryCondition: shopify_models.ShopifyDeliveryCondition,
    shopify_sgqlc_schema.DeliveryCountry: shopify_models.ShopifyDeliveryCountry,
    shopify_sgqlc_schema.DeliveryCountryAndZone: shopify_models.ShopifyDeliveryCountryAndZone,
    shopify_sgqlc_schema.DeliveryCountryCodeOrRestOfWorld: shopify_models.ShopifyDeliveryCountryCodeOrRestOfWorld,
    shopify_sgqlc_schema.DeliveryCountryCodesOrRestOfWorld: shopify_models.ShopifyDeliveryCountryCodesOrRestOfWorld,
    shopify_sgqlc_schema.DeliveryCustomization: shopify_models.ShopifyDeliveryCustomization,
    shopify_sgqlc_schema.DeliveryCustomizationError: shopify_models.ShopifyDeliveryCustomizationError,
    shopify_sgqlc_schema.DeliveryLegacyModeBlocked: shopify_models.ShopifyDeliveryLegacyModeBlocked,
    shopify_sgqlc_schema.DeliveryLocalPickupSettings: shopify_models.ShopifyDeliveryLocalPickupSettings,
    shopify_sgqlc_schema.DeliveryLocationGroup: shopify_models.ShopifyDeliveryLocationGroup,
    shopify_sgqlc_schema.DeliveryLocationGroupZone: shopify_models.ShopifyDeliveryLocationGroupZone,
    shopify_sgqlc_schema.DeliveryLocationLocalPickupSettingsError: shopify_models.ShopifyDeliveryLocationLocalPickupSettingsError,
    shopify_sgqlc_schema.DeliveryMethod: shopify_models.ShopifyDeliveryMethod,
    shopify_sgqlc_schema.DeliveryMethodAdditionalInformation: shopify_models.ShopifyDeliveryMethodAdditionalInformation,
    shopify_sgqlc_schema.DeliveryMethodDefinition: shopify_models.ShopifyDeliveryMethodDefinition,
    shopify_sgqlc_schema.DeliveryMethodDefinitionCounts: shopify_models.ShopifyDeliveryMethodDefinitionCounts,
    shopify_sgqlc_schema.DeliveryParticipant: shopify_models.ShopifyDeliveryParticipant,
    shopify_sgqlc_schema.DeliveryParticipantService: shopify_models.ShopifyDeliveryParticipantService,
    shopify_sgqlc_schema.DeliveryProductVariantsCount: shopify_models.ShopifyDeliveryProductVariantsCount,
    shopify_sgqlc_schema.DeliveryProfile: shopify_models.ShopifyDeliveryProfile,
    shopify_sgqlc_schema.DeliveryProfileItem: shopify_models.ShopifyDeliveryProfileItem,
    shopify_sgqlc_schema.DeliveryProfileLocationGroup: shopify_models.ShopifyDeliveryProfileLocationGroup,
    shopify_sgqlc_schema.DeliveryPromiseProvider: shopify_models.ShopifyDeliveryPromiseProvider,
    shopify_sgqlc_schema.DeliveryProvince: shopify_models.ShopifyDeliveryProvince,
    shopify_sgqlc_schema.DeliveryRateDefinition: shopify_models.ShopifyDeliveryRateDefinition,
    shopify_sgqlc_schema.DeliverySetting: shopify_models.ShopifyDeliverySetting,
    shopify_sgqlc_schema.DeliveryZone: shopify_models.ShopifyDeliveryZone,
    shopify_sgqlc_schema.DepositPercentage: shopify_models.ShopifyDepositPercentage,
    shopify_sgqlc_schema.DiscountAmount: shopify_models.ShopifyDiscountAmount,
    shopify_sgqlc_schema.DiscountAutomaticApp: shopify_models.ShopifyDiscountAutomaticApp,
    shopify_sgqlc_schema.DiscountAutomaticBasic: shopify_models.ShopifyDiscountAutomaticBasic,
    shopify_sgqlc_schema.DiscountAutomaticBxgy: shopify_models.ShopifyDiscountAutomaticBxgy,
    shopify_sgqlc_schema.DiscountAutomaticFreeShipping: shopify_models.ShopifyDiscountAutomaticFreeShipping,
    shopify_sgqlc_schema.DiscountAutomaticNode: shopify_models.ShopifyDiscountAutomaticNode,
    shopify_sgqlc_schema.DiscountCodeApp: shopify_models.ShopifyDiscountCodeApp,
    shopify_sgqlc_schema.DiscountCodeApplication: shopify_models.ShopifyDiscountCodeApplication,
    shopify_sgqlc_schema.DiscountCodeBasic: shopify_models.ShopifyDiscountCodeBasic,
    shopify_sgqlc_schema.DiscountCodeBxgy: shopify_models.ShopifyDiscountCodeBxgy,
    shopify_sgqlc_schema.DiscountCodeFreeShipping: shopify_models.ShopifyDiscountCodeFreeShipping,
    shopify_sgqlc_schema.DiscountCodeNode: shopify_models.ShopifyDiscountCodeNode,
    shopify_sgqlc_schema.DiscountCollections: shopify_models.ShopifyDiscountCollections,
    shopify_sgqlc_schema.DiscountCombinesWith: shopify_models.ShopifyDiscountCombinesWith,
    shopify_sgqlc_schema.DiscountCountries: shopify_models.ShopifyDiscountCountries,
    shopify_sgqlc_schema.DiscountCountryAll: shopify_models.ShopifyDiscountCountryAll,
    shopify_sgqlc_schema.DiscountCustomerAll: shopify_models.ShopifyDiscountCustomerAll,
    shopify_sgqlc_schema.DiscountCustomerBuys: shopify_models.ShopifyDiscountCustomerBuys,
    shopify_sgqlc_schema.DiscountCustomerGets: shopify_models.ShopifyDiscountCustomerGets,
    shopify_sgqlc_schema.DiscountCustomerSegments: shopify_models.ShopifyDiscountCustomerSegments,
    shopify_sgqlc_schema.DiscountCustomers: shopify_models.ShopifyDiscountCustomers,
    shopify_sgqlc_schema.DiscountMinimumQuantity: shopify_models.ShopifyDiscountMinimumQuantity,
    shopify_sgqlc_schema.DiscountMinimumSubtotal: shopify_models.ShopifyDiscountMinimumSubtotal,
    shopify_sgqlc_schema.DiscountNode: shopify_models.ShopifyDiscountNode,
    shopify_sgqlc_schema.DiscountOnQuantity: shopify_models.ShopifyDiscountOnQuantity,
    shopify_sgqlc_schema.DiscountPercentage: shopify_models.ShopifyDiscountPercentage,
    shopify_sgqlc_schema.DiscountProducts: shopify_models.ShopifyDiscountProducts,
    shopify_sgqlc_schema.DiscountPurchaseAmount: shopify_models.ShopifyDiscountPurchaseAmount,
    shopify_sgqlc_schema.DiscountQuantity: shopify_models.ShopifyDiscountQuantity,
    shopify_sgqlc_schema.DiscountRedeemCode: shopify_models.ShopifyDiscountRedeemCode,
    shopify_sgqlc_schema.DiscountRedeemCodeBulkCreation: shopify_models.ShopifyDiscountRedeemCodeBulkCreation,
    shopify_sgqlc_schema.DiscountRedeemCodeBulkCreationCode: shopify_models.ShopifyDiscountRedeemCodeBulkCreationCode,
    shopify_sgqlc_schema.DiscountShareableUrl: shopify_models.ShopifyDiscountShareableUrl,
    shopify_sgqlc_schema.Domain: shopify_models.ShopifyDomain,
    shopify_sgqlc_schema.DomainLocalization: shopify_models.ShopifyDomainLocalization,
    shopify_sgqlc_schema.DraftOrder: shopify_models.ShopifyDraftOrder,
    shopify_sgqlc_schema.DraftOrderAppliedDiscount: shopify_models.ShopifyDraftOrderAppliedDiscount,
    shopify_sgqlc_schema.DraftOrderBundleAddedWarning: shopify_models.ShopifyDraftOrderBundleAddedWarning,
    shopify_sgqlc_schema.DraftOrderDiscountNotAppliedWarning: shopify_models.ShopifyDraftOrderDiscountNotAppliedWarning,
    shopify_sgqlc_schema.DraftOrderLineItem: shopify_models.ShopifyDraftOrderLineItem,
    shopify_sgqlc_schema.DraftOrderPlatformDiscount: shopify_models.ShopifyDraftOrderPlatformDiscount,
    shopify_sgqlc_schema.DraftOrderPlatformDiscountAllocation: shopify_models.ShopifyDraftOrderPlatformDiscountAllocation,
    shopify_sgqlc_schema.DraftOrderTag: shopify_models.ShopifyDraftOrderTag,
    shopify_sgqlc_schema.Duty: shopify_models.ShopifyDuty,
    shopify_sgqlc_schema.DutySale: shopify_models.ShopifyDutySale,
    shopify_sgqlc_schema.EditableProperty: shopify_models.ShopifyEditableProperty,
    shopify_sgqlc_schema.ExchangeLineItem: shopify_models.ShopifyExchangeLineItem,
    shopify_sgqlc_schema.ExchangeV2: shopify_models.ShopifyExchangeV2,
    shopify_sgqlc_schema.ExchangeV2Additions: shopify_models.ShopifyExchangeV2Additions,
    shopify_sgqlc_schema.ExchangeV2LineItem: shopify_models.ShopifyExchangeV2LineItem,
    shopify_sgqlc_schema.ExchangeV2Returns: shopify_models.ShopifyExchangeV2Returns,
    shopify_sgqlc_schema.ExternalVideo: shopify_models.ShopifyExternalVideo,
    shopify_sgqlc_schema.FailedRequirement: shopify_models.ShopifyFailedRequirement,
    shopify_sgqlc_schema.FeeSale: shopify_models.ShopifyFeeSale,
    shopify_sgqlc_schema.FileError: shopify_models.ShopifyFileError,
    shopify_sgqlc_schema.FilterOption: shopify_models.ShopifyFilterOption,
    shopify_sgqlc_schema.FinancialSummaryDiscountAllocation: shopify_models.ShopifyFinancialSummaryDiscountAllocation,
    shopify_sgqlc_schema.FinancialSummaryDiscountApplication: shopify_models.ShopifyFinancialSummaryDiscountApplication,
    shopify_sgqlc_schema.Fulfillment: shopify_models.ShopifyFulfillment,
    shopify_sgqlc_schema.FulfillmentConstraintRule: shopify_models.ShopifyFulfillmentConstraintRule,
    shopify_sgqlc_schema.FulfillmentEvent: shopify_models.ShopifyFulfillmentEvent,
    shopify_sgqlc_schema.FulfillmentHold: shopify_models.ShopifyFulfillmentHold,
    shopify_sgqlc_schema.FulfillmentLineItem: shopify_models.ShopifyFulfillmentLineItem,
    shopify_sgqlc_schema.FulfillmentOrder: shopify_models.ShopifyFulfillmentOrder,
    shopify_sgqlc_schema.FulfillmentOrderAssignedLocation: shopify_models.ShopifyFulfillmentOrderAssignedLocation,
    shopify_sgqlc_schema.FulfillmentOrderDestination: shopify_models.ShopifyFulfillmentOrderDestination,
    shopify_sgqlc_schema.FulfillmentOrderInternationalDuties: shopify_models.ShopifyFulfillmentOrderInternationalDuties,
    shopify_sgqlc_schema.FulfillmentOrderLineItem: shopify_models.ShopifyFulfillmentOrderLineItem,
    shopify_sgqlc_schema.FulfillmentOrderLineItemFinancialSummary: shopify_models.ShopifyFulfillmentOrderLineItemFinancialSummary,
    shopify_sgqlc_schema.FulfillmentOrderLineItemWarning: shopify_models.ShopifyFulfillmentOrderLineItemWarning,
    shopify_sgqlc_schema.FulfillmentOrderLocationForMove: shopify_models.ShopifyFulfillmentOrderLocationForMove,
    shopify_sgqlc_schema.FulfillmentOrderMerchantRequest: shopify_models.ShopifyFulfillmentOrderMerchantRequest,
    shopify_sgqlc_schema.FulfillmentOrderMergeResult: shopify_models.ShopifyFulfillmentOrderMergeResult,
    shopify_sgqlc_schema.FulfillmentOrderSplitResult: shopify_models.ShopifyFulfillmentOrderSplitResult,
    shopify_sgqlc_schema.FulfillmentOrderSupportedAction: shopify_models.ShopifyFulfillmentOrderSupportedAction,
    shopify_sgqlc_schema.FulfillmentOriginAddress: shopify_models.ShopifyFulfillmentOriginAddress,
    shopify_sgqlc_schema.FulfillmentService: shopify_models.ShopifyFulfillmentService,
    shopify_sgqlc_schema.FulfillmentTrackingInfo: shopify_models.ShopifyFulfillmentTrackingInfo,
    shopify_sgqlc_schema.FunctionsAppBridge: shopify_models.ShopifyFunctionsAppBridge,
    shopify_sgqlc_schema.FunctionsErrorHistory: shopify_models.ShopifyFunctionsErrorHistory,
    shopify_sgqlc_schema.GenericFile: shopify_models.ShopifyGenericFile,
    shopify_sgqlc_schema.GiftCard: shopify_models.ShopifyGiftCard,
    shopify_sgqlc_schema.GiftCardCreditTransaction: shopify_models.ShopifyGiftCardCreditTransaction,
    shopify_sgqlc_schema.GiftCardDebitTransaction: shopify_models.ShopifyGiftCardDebitTransaction,
    shopify_sgqlc_schema.GiftCardRecipient: shopify_models.ShopifyGiftCardRecipient,
    shopify_sgqlc_schema.GiftCardSale: shopify_models.ShopifyGiftCardSale,
    shopify_sgqlc_schema.ImageUploadParameter: shopify_models.ShopifyImageUploadParameter,
    shopify_sgqlc_schema.InventoryAdjustmentGroup: shopify_models.ShopifyInventoryAdjustmentGroup,
    shopify_sgqlc_schema.InventoryChange: shopify_models.ShopifyInventoryChange,
    shopify_sgqlc_schema.InventoryItem: shopify_models.ShopifyInventoryItem,
    shopify_sgqlc_schema.InventoryItemMeasurement: shopify_models.ShopifyInventoryItemMeasurement,
    shopify_sgqlc_schema.InventoryLevel: shopify_models.ShopifyInventoryLevel,
    shopify_sgqlc_schema.InventoryProperties: shopify_models.ShopifyInventoryProperties,
    shopify_sgqlc_schema.InventoryQuantity: shopify_models.ShopifyInventoryQuantity,
    shopify_sgqlc_schema.InventoryQuantityName: shopify_models.ShopifyInventoryQuantityName,
    shopify_sgqlc_schema.InventoryScheduledChange: shopify_models.ShopifyInventoryScheduledChange,
    shopify_sgqlc_schema.Job: shopify_models.ShopifyJob,
    shopify_sgqlc_schema.LimitedPendingOrderCount: shopify_models.ShopifyLimitedPendingOrderCount,
    shopify_sgqlc_schema.LineItem: shopify_models.ShopifyLineItem,
    shopify_sgqlc_schema.LineItemGroup: shopify_models.ShopifyLineItemGroup,
    shopify_sgqlc_schema.LineItemSellingPlan: shopify_models.ShopifyLineItemSellingPlan,
    shopify_sgqlc_schema.LinkedMetafield: shopify_models.ShopifyLinkedMetafield,
    shopify_sgqlc_schema.LocalPaymentMethodsPaymentDetails: shopify_models.ShopifyLocalPaymentMethodsPaymentDetails,
    shopify_sgqlc_schema.Locale: shopify_models.ShopifyLocale,
    shopify_sgqlc_schema.LocalizationExtension: shopify_models.ShopifyLocalizationExtension,
    shopify_sgqlc_schema.Location: shopify_models.ShopifyLocation,
    shopify_sgqlc_schema.LocationAddress: shopify_models.ShopifyLocationAddress,
    shopify_sgqlc_schema.LocationSuggestedAddress: shopify_models.ShopifyLocationSuggestedAddress,
    shopify_sgqlc_schema.ManualDiscountApplication: shopify_models.ShopifyManualDiscountApplication,
    shopify_sgqlc_schema.Market: shopify_models.ShopifyMarket,
    shopify_sgqlc_schema.MarketCatalog: shopify_models.ShopifyMarketCatalog,
    shopify_sgqlc_schema.MarketCurrencySettings: shopify_models.ShopifyMarketCurrencySettings,
    shopify_sgqlc_schema.MarketLocalizableContent: shopify_models.ShopifyMarketLocalizableContent,
    shopify_sgqlc_schema.MarketLocalizableResource: shopify_models.ShopifyMarketLocalizableResource,
    shopify_sgqlc_schema.MarketLocalization: shopify_models.ShopifyMarketLocalization,
    shopify_sgqlc_schema.MarketRegionCountry: shopify_models.ShopifyMarketRegionCountry,
    shopify_sgqlc_schema.MarketWebPresence: shopify_models.ShopifyMarketWebPresence,
    shopify_sgqlc_schema.MarketWebPresenceRootUrl: shopify_models.ShopifyMarketWebPresenceRootUrl,
    shopify_sgqlc_schema.MarketingActivity: shopify_models.ShopifyMarketingActivity,
    shopify_sgqlc_schema.MarketingActivityExtensionAppErrors: shopify_models.ShopifyMarketingActivityExtensionAppErrors,
    shopify_sgqlc_schema.MarketingBudget: shopify_models.ShopifyMarketingBudget,
    shopify_sgqlc_schema.MarketingEngagement: shopify_models.ShopifyMarketingEngagement,
    shopify_sgqlc_schema.MarketingEvent: shopify_models.ShopifyMarketingEvent,
    shopify_sgqlc_schema.MediaError: shopify_models.ShopifyMediaError,
    shopify_sgqlc_schema.MediaImage: shopify_models.ShopifyMediaImage,
    shopify_sgqlc_schema.MediaImageOriginalSource: shopify_models.ShopifyMediaImageOriginalSource,
    shopify_sgqlc_schema.MediaPreviewImage: shopify_models.ShopifyMediaPreviewImage,
    shopify_sgqlc_schema.MediaWarning: shopify_models.ShopifyMediaWarning,
    shopify_sgqlc_schema.Menu: shopify_models.ShopifyMenu,
    shopify_sgqlc_schema.MenuItem: shopify_models.ShopifyMenuItem,
    shopify_sgqlc_schema.MerchantApprovalSignals: shopify_models.ShopifyMerchantApprovalSignals,
    shopify_sgqlc_schema.Metafield: shopify_models.ShopifyMetafield,
    shopify_sgqlc_schema.MetafieldAccess: shopify_models.ShopifyMetafieldAccess,
    shopify_sgqlc_schema.MetafieldAccessGrant: shopify_models.ShopifyMetafieldAccessGrant,
    shopify_sgqlc_schema.MetafieldCapabilities: shopify_models.ShopifyMetafieldCapabilities,
    shopify_sgqlc_schema.MetafieldCapabilityAdminFilterable: shopify_models.ShopifyMetafieldCapabilityAdminFilterable,
    shopify_sgqlc_schema.MetafieldCapabilitySmartCollectionCondition: shopify_models.ShopifyMetafieldCapabilitySmartCollectionCondition,
    shopify_sgqlc_schema.MetafieldDefinition: shopify_models.ShopifyMetafieldDefinition,
    shopify_sgqlc_schema.MetafieldDefinitionConstraintValue: shopify_models.ShopifyMetafieldDefinitionConstraintValue,
    shopify_sgqlc_schema.MetafieldDefinitionConstraints: shopify_models.ShopifyMetafieldDefinitionConstraints,
    shopify_sgqlc_schema.MetafieldDefinitionSupportedValidation: shopify_models.ShopifyMetafieldDefinitionSupportedValidation,
    shopify_sgqlc_schema.MetafieldDefinitionType: shopify_models.ShopifyMetafieldDefinitionType,
    shopify_sgqlc_schema.MetafieldDefinitionValidation: shopify_models.ShopifyMetafieldDefinitionValidation,
    shopify_sgqlc_schema.MetafieldIdentifier: shopify_models.ShopifyMetafieldIdentifier,
    shopify_sgqlc_schema.MetafieldRelation: shopify_models.ShopifyMetafieldRelation,
    shopify_sgqlc_schema.MetafieldStorefrontVisibility: shopify_models.ShopifyMetafieldStorefrontVisibility,
    shopify_sgqlc_schema.Metaobject: shopify_models.ShopifyMetaobject,
    shopify_sgqlc_schema.MetaobjectAccess: shopify_models.ShopifyMetaobjectAccess,
    shopify_sgqlc_schema.MetaobjectCapabilities: shopify_models.ShopifyMetaobjectCapabilities,
    shopify_sgqlc_schema.MetaobjectCapabilitiesOnlineStore: shopify_models.ShopifyMetaobjectCapabilitiesOnlineStore,
    shopify_sgqlc_schema.MetaobjectCapabilitiesPublishable: shopify_models.ShopifyMetaobjectCapabilitiesPublishable,
    shopify_sgqlc_schema.MetaobjectCapabilitiesRenderable: shopify_models.ShopifyMetaobjectCapabilitiesRenderable,
    shopify_sgqlc_schema.MetaobjectCapabilitiesTranslatable: shopify_models.ShopifyMetaobjectCapabilitiesTranslatable,
    shopify_sgqlc_schema.MetaobjectCapabilityData: shopify_models.ShopifyMetaobjectCapabilityData,
    shopify_sgqlc_schema.MetaobjectCapabilityDataOnlineStore: shopify_models.ShopifyMetaobjectCapabilityDataOnlineStore,
    shopify_sgqlc_schema.MetaobjectCapabilityDataPublishable: shopify_models.ShopifyMetaobjectCapabilityDataPublishable,
    shopify_sgqlc_schema.MetaobjectCapabilityDefinitionDataOnlineStore: shopify_models.ShopifyMetaobjectCapabilityDefinitionDataOnlineStore,
    shopify_sgqlc_schema.MetaobjectCapabilityDefinitionDataRenderable: shopify_models.ShopifyMetaobjectCapabilityDefinitionDataRenderable,
    shopify_sgqlc_schema.MetaobjectDefinition: shopify_models.ShopifyMetaobjectDefinition,
    shopify_sgqlc_schema.MetaobjectField: shopify_models.ShopifyMetaobjectField,
    shopify_sgqlc_schema.MetaobjectFieldDefinition: shopify_models.ShopifyMetaobjectFieldDefinition,
    shopify_sgqlc_schema.MetaobjectThumbnail: shopify_models.ShopifyMetaobjectThumbnail,
    shopify_sgqlc_schema.Model3d: shopify_models.ShopifyModel3d,
    shopify_sgqlc_schema.Model3dBoundingBox: shopify_models.ShopifyModel3dBoundingBox,
    shopify_sgqlc_schema.Model3dSource: shopify_models.ShopifyModel3dSource,
    shopify_sgqlc_schema.MutationsStagedUploadTargetGenerateUploadParameter: shopify_models.ShopifyMutationsStagedUploadTargetGenerateUploadParameter,
    shopify_sgqlc_schema.NavigationItem: shopify_models.ShopifyNavigationItem,
    shopify_sgqlc_schema.OnlineStore: shopify_models.ShopifyOnlineStore,
    shopify_sgqlc_schema.OnlineStorePasswordProtection: shopify_models.ShopifyOnlineStorePasswordProtection,
    shopify_sgqlc_schema.OnlineStoreTheme: shopify_models.ShopifyOnlineStoreTheme,
    shopify_sgqlc_schema.OnlineStoreThemeFile: shopify_models.ShopifyOnlineStoreThemeFile,
    shopify_sgqlc_schema.OnlineStoreThemeFileBodyBase64: shopify_models.ShopifyOnlineStoreThemeFileBodyBase64,
    shopify_sgqlc_schema.OnlineStoreThemeFileBodyText: shopify_models.ShopifyOnlineStoreThemeFileBodyText,
    shopify_sgqlc_schema.OnlineStoreThemeFileBodyUrl: shopify_models.ShopifyOnlineStoreThemeFileBodyUrl,
    shopify_sgqlc_schema.OnlineStoreThemeFileOperationResult: shopify_models.ShopifyOnlineStoreThemeFileOperationResult,
    shopify_sgqlc_schema.OnlineStoreThemeFileReadResult: shopify_models.ShopifyOnlineStoreThemeFileReadResult,
    shopify_sgqlc_schema.OnlineStoreThemeFilesUserErrors: shopify_models.ShopifyOnlineStoreThemeFilesUserErrors,
    shopify_sgqlc_schema.Order: shopify_models.ShopifyOrder,
    shopify_sgqlc_schema.OrderAdjustment: shopify_models.ShopifyOrderAdjustment,
    shopify_sgqlc_schema.OrderAgreement: shopify_models.ShopifyOrderAgreement,
    shopify_sgqlc_schema.OrderApp: shopify_models.ShopifyOrderApp,
    shopify_sgqlc_schema.OrderCancellation: shopify_models.ShopifyOrderCancellation,
    shopify_sgqlc_schema.OrderDisputeSummary: shopify_models.ShopifyOrderDisputeSummary,
    shopify_sgqlc_schema.OrderEditAgreement: shopify_models.ShopifyOrderEditAgreement,
    shopify_sgqlc_schema.OrderPaymentCollectionDetails: shopify_models.ShopifyOrderPaymentCollectionDetails,
    shopify_sgqlc_schema.OrderPaymentStatus: shopify_models.ShopifyOrderPaymentStatus,
    shopify_sgqlc_schema.OrderRisk: shopify_models.ShopifyOrderRisk,
    shopify_sgqlc_schema.OrderRiskAssessment: shopify_models.ShopifyOrderRiskAssessment,
    shopify_sgqlc_schema.OrderRiskSummary: shopify_models.ShopifyOrderRiskSummary,
    shopify_sgqlc_schema.OrderStagedChangeAddCustomItem: shopify_models.ShopifyOrderStagedChangeAddCustomItem,
    shopify_sgqlc_schema.OrderStagedChangeAddLineItemDiscount: shopify_models.ShopifyOrderStagedChangeAddLineItemDiscount,
    shopify_sgqlc_schema.OrderStagedChangeAddShippingLine: shopify_models.ShopifyOrderStagedChangeAddShippingLine,
    shopify_sgqlc_schema.OrderStagedChangeAddVariant: shopify_models.ShopifyOrderStagedChangeAddVariant,
    shopify_sgqlc_schema.OrderStagedChangeDecrementItem: shopify_models.ShopifyOrderStagedChangeDecrementItem,
    shopify_sgqlc_schema.OrderStagedChangeIncrementItem: shopify_models.ShopifyOrderStagedChangeIncrementItem,
    shopify_sgqlc_schema.OrderStagedChangeRemoveShippingLine: shopify_models.ShopifyOrderStagedChangeRemoveShippingLine,
    shopify_sgqlc_schema.OrderTransaction: shopify_models.ShopifyOrderTransaction,
    shopify_sgqlc_schema.Page: shopify_models.ShopifyPage,
    shopify_sgqlc_schema.PaymentCustomization: shopify_models.ShopifyPaymentCustomization,
    shopify_sgqlc_schema.PaymentCustomizationError: shopify_models.ShopifyPaymentCustomizationError,
    shopify_sgqlc_schema.PaymentMandate: shopify_models.ShopifyPaymentMandate,
    shopify_sgqlc_schema.PaymentSchedule: shopify_models.ShopifyPaymentSchedule,
    shopify_sgqlc_schema.PaymentSettings: shopify_models.ShopifyPaymentSettings,
    shopify_sgqlc_schema.PaymentTerms: shopify_models.ShopifyPaymentTerms,
    shopify_sgqlc_schema.PaymentTermsTemplate: shopify_models.ShopifyPaymentTermsTemplate,
    shopify_sgqlc_schema.PriceList: shopify_models.ShopifyPriceList,
    shopify_sgqlc_schema.PriceListAdjustment: shopify_models.ShopifyPriceListAdjustment,
    shopify_sgqlc_schema.PriceListAdjustmentSettings: shopify_models.ShopifyPriceListAdjustmentSettings,
    shopify_sgqlc_schema.PriceListParent: shopify_models.ShopifyPriceListParent,
    shopify_sgqlc_schema.PriceListPrice: shopify_models.ShopifyPriceListPrice,
    shopify_sgqlc_schema.PriceRule: shopify_models.ShopifyPriceRule,
    shopify_sgqlc_schema.PriceRuleCustomerSelection: shopify_models.ShopifyPriceRuleCustomerSelection,
    shopify_sgqlc_schema.PriceRuleDiscountCode: shopify_models.ShopifyPriceRuleDiscountCode,
    shopify_sgqlc_schema.PriceRuleEntitlementToPrerequisiteQuantityRatio: shopify_models.ShopifyPriceRuleEntitlementToPrerequisiteQuantityRatio,
    shopify_sgqlc_schema.PriceRuleFixedAmountValue: shopify_models.ShopifyPriceRuleFixedAmountValue,
    shopify_sgqlc_schema.PriceRuleItemEntitlements: shopify_models.ShopifyPriceRuleItemEntitlements,
    shopify_sgqlc_schema.PriceRuleLineItemPrerequisites: shopify_models.ShopifyPriceRuleLineItemPrerequisites,
    shopify_sgqlc_schema.PriceRuleMoneyRange: shopify_models.ShopifyPriceRuleMoneyRange,
    shopify_sgqlc_schema.PriceRulePercentValue: shopify_models.ShopifyPriceRulePercentValue,
    shopify_sgqlc_schema.PriceRulePrerequisiteToEntitlementQuantityRatio: shopify_models.ShopifyPriceRulePrerequisiteToEntitlementQuantityRatio,
    shopify_sgqlc_schema.PriceRuleQuantityRange: shopify_models.ShopifyPriceRuleQuantityRange,
    shopify_sgqlc_schema.PriceRuleShareableUrl: shopify_models.ShopifyPriceRuleShareableUrl,
    shopify_sgqlc_schema.PriceRuleShippingLineEntitlements: shopify_models.ShopifyPriceRuleShippingLineEntitlements,
    shopify_sgqlc_schema.PriceRuleValidityPeriod: shopify_models.ShopifyPriceRuleValidityPeriod,
    shopify_sgqlc_schema.PricingPercentageValue: shopify_models.ShopifyPricingPercentageValue,
    shopify_sgqlc_schema.PrivateMetafield: shopify_models.ShopifyPrivateMetafield,
    shopify_sgqlc_schema.Product: shopify_models.ShopifyProduct,
    shopify_sgqlc_schema.ProductBundleComponent: shopify_models.ShopifyProductBundleComponent,
    shopify_sgqlc_schema.ProductBundleComponentOptionSelection: shopify_models.ShopifyProductBundleComponentOptionSelection,
    shopify_sgqlc_schema.ProductBundleComponentOptionSelectionValue: shopify_models.ShopifyProductBundleComponentOptionSelectionValue,
    shopify_sgqlc_schema.ProductBundleComponentQuantityOption: shopify_models.ShopifyProductBundleComponentQuantityOption,
    shopify_sgqlc_schema.ProductBundleComponentQuantityOptionValue: shopify_models.ShopifyProductBundleComponentQuantityOptionValue,
    shopify_sgqlc_schema.ProductBundleOperation: shopify_models.ShopifyProductBundleOperation,
    shopify_sgqlc_schema.ProductCategory: shopify_models.ShopifyProductCategory,
    shopify_sgqlc_schema.ProductCompareAtPriceRange: shopify_models.ShopifyProductCompareAtPriceRange,
    shopify_sgqlc_schema.ProductContextualPricing: shopify_models.ShopifyProductContextualPricing,
    shopify_sgqlc_schema.ProductDeleteOperation: shopify_models.ShopifyProductDeleteOperation,
    shopify_sgqlc_schema.ProductDuplicateJob: shopify_models.ShopifyProductDuplicateJob,
    shopify_sgqlc_schema.ProductDuplicateOperation: shopify_models.ShopifyProductDuplicateOperation,
    shopify_sgqlc_schema.ProductFeed: shopify_models.ShopifyProductFeed,
    shopify_sgqlc_schema.ProductOption: shopify_models.ShopifyProductOption,
    shopify_sgqlc_schema.ProductOptionValue: shopify_models.ShopifyProductOptionValue,
    shopify_sgqlc_schema.ProductOptionValueSwatch: shopify_models.ShopifyProductOptionValueSwatch,
    shopify_sgqlc_schema.ProductPriceRange: shopify_models.ShopifyProductPriceRange,
    shopify_sgqlc_schema.ProductPriceRangeV2: shopify_models.ShopifyProductPriceRangeV2,
    shopify_sgqlc_schema.ProductPublication: shopify_models.ShopifyProductPublication,
    shopify_sgqlc_schema.ProductResourceFeedback: shopify_models.ShopifyProductResourceFeedback,
    shopify_sgqlc_schema.ProductSale: shopify_models.ShopifyProductSale,
    shopify_sgqlc_schema.ProductSetOperation: shopify_models.ShopifyProductSetOperation,
    shopify_sgqlc_schema.ProductTaxonomyNode: shopify_models.ShopifyProductTaxonomyNode,
    shopify_sgqlc_schema.ProductVariant: shopify_models.ShopifyProductVariant,
    shopify_sgqlc_schema.ProductVariantComponent: shopify_models.ShopifyProductVariantComponent,
    shopify_sgqlc_schema.ProductVariantContextualPricing: shopify_models.ShopifyProductVariantContextualPricing,
    shopify_sgqlc_schema.ProductVariantPricePair: shopify_models.ShopifyProductVariantPricePair,
    shopify_sgqlc_schema.Publication: shopify_models.ShopifyPublication,
    shopify_sgqlc_schema.PublicationResourceOperation: shopify_models.ShopifyPublicationResourceOperation,
    shopify_sgqlc_schema.PurchasingCompany: shopify_models.ShopifyPurchasingCompany,
    shopify_sgqlc_schema.QuantityPriceBreak: shopify_models.ShopifyQuantityPriceBreak,
    shopify_sgqlc_schema.QuantityRule: shopify_models.ShopifyQuantityRule,
    shopify_sgqlc_schema.Refund: shopify_models.ShopifyRefund,
    shopify_sgqlc_schema.RefundAgreement: shopify_models.ShopifyRefundAgreement,
    shopify_sgqlc_schema.RefundDuty: shopify_models.ShopifyRefundDuty,
    shopify_sgqlc_schema.RefundLineItem: shopify_models.ShopifyRefundLineItem,
    shopify_sgqlc_schema.RefundShippingLine: shopify_models.ShopifyRefundShippingLine,
    shopify_sgqlc_schema.ResourceAlert: shopify_models.ShopifyResourceAlert,
    shopify_sgqlc_schema.ResourceAlertAction: shopify_models.ShopifyResourceAlertAction,
    shopify_sgqlc_schema.ResourceFeedback: shopify_models.ShopifyResourceFeedback,
    shopify_sgqlc_schema.ResourcePublication: shopify_models.ShopifyResourcePublication,
    shopify_sgqlc_schema.ResourcePublicationV2: shopify_models.ShopifyResourcePublicationV2,
    shopify_sgqlc_schema.RestockingFee: shopify_models.ShopifyRestockingFee,
    shopify_sgqlc_schema.RestrictedForResource: shopify_models.ShopifyRestrictedForResource,
    shopify_sgqlc_schema.Return: shopify_models.ShopifyReturn,
    shopify_sgqlc_schema.ReturnAgreement: shopify_models.ShopifyReturnAgreement,
    shopify_sgqlc_schema.ReturnDecline: shopify_models.ShopifyReturnDecline,
    shopify_sgqlc_schema.ReturnLineItem: shopify_models.ShopifyReturnLineItem,
    shopify_sgqlc_schema.ReturnShippingFee: shopify_models.ShopifyReturnShippingFee,
    shopify_sgqlc_schema.ReturnableFulfillment: shopify_models.ShopifyReturnableFulfillment,
    shopify_sgqlc_schema.ReturnableFulfillmentLineItem: shopify_models.ShopifyReturnableFulfillmentLineItem,
    shopify_sgqlc_schema.ReverseDelivery: shopify_models.ShopifyReverseDelivery,
    shopify_sgqlc_schema.ReverseDeliveryLabelV2: shopify_models.ShopifyReverseDeliveryLabelV2,
    shopify_sgqlc_schema.ReverseDeliveryLineItem: shopify_models.ShopifyReverseDeliveryLineItem,
    shopify_sgqlc_schema.ReverseDeliveryShippingDeliverable: shopify_models.ShopifyReverseDeliveryShippingDeliverable,
    shopify_sgqlc_schema.ReverseDeliveryTrackingV2: shopify_models.ShopifyReverseDeliveryTrackingV2,
    shopify_sgqlc_schema.ReverseFulfillmentOrder: shopify_models.ShopifyReverseFulfillmentOrder,
    shopify_sgqlc_schema.ReverseFulfillmentOrderDisposition: shopify_models.ShopifyReverseFulfillmentOrderDisposition,
    shopify_sgqlc_schema.ReverseFulfillmentOrderLineItem: shopify_models.ShopifyReverseFulfillmentOrderLineItem,
    shopify_sgqlc_schema.ReverseFulfillmentOrderThirdPartyConfirmation: shopify_models.ShopifyReverseFulfillmentOrderThirdPartyConfirmation,
    shopify_sgqlc_schema.RiskFact: shopify_models.ShopifyRiskFact,
    shopify_sgqlc_schema.SEO: shopify_models.ShopifySEO,
    shopify_sgqlc_schema.SaleAdditionalFee: shopify_models.ShopifySaleAdditionalFee,
    shopify_sgqlc_schema.SaleTax: shopify_models.ShopifySaleTax,
    shopify_sgqlc_schema.SavedSearch: shopify_models.ShopifySavedSearch,
    shopify_sgqlc_schema.ScriptDiscountApplication: shopify_models.ShopifyScriptDiscountApplication,
    shopify_sgqlc_schema.ScriptTag: shopify_models.ShopifyScriptTag,
    shopify_sgqlc_schema.SearchFilterOptions: shopify_models.ShopifySearchFilterOptions,
    shopify_sgqlc_schema.SearchResult: shopify_models.ShopifySearchResult,
    shopify_sgqlc_schema.Segment: shopify_models.ShopifySegment,
    shopify_sgqlc_schema.SegmentAttributeStatistics: shopify_models.ShopifySegmentAttributeStatistics,
    shopify_sgqlc_schema.SegmentEventFilterParameter: shopify_models.ShopifySegmentEventFilterParameter,
    shopify_sgqlc_schema.SegmentMembership: shopify_models.ShopifySegmentMembership,
    shopify_sgqlc_schema.SegmentMembershipResponse: shopify_models.ShopifySegmentMembershipResponse,
    shopify_sgqlc_schema.SegmentMigration: shopify_models.ShopifySegmentMigration,
    shopify_sgqlc_schema.SegmentStatistics: shopify_models.ShopifySegmentStatistics,
    shopify_sgqlc_schema.SegmentValue: shopify_models.ShopifySegmentValue,
    shopify_sgqlc_schema.SelectedOption: shopify_models.ShopifySelectedOption,
    shopify_sgqlc_schema.SellingPlan: shopify_models.ShopifySellingPlan,
    shopify_sgqlc_schema.SellingPlanAnchor: shopify_models.ShopifySellingPlanAnchor,
    shopify_sgqlc_schema.SellingPlanCheckoutCharge: shopify_models.ShopifySellingPlanCheckoutCharge,
    shopify_sgqlc_schema.SellingPlanCheckoutChargePercentageValue: shopify_models.ShopifySellingPlanCheckoutChargePercentageValue,
    shopify_sgqlc_schema.SellingPlanFixedBillingPolicy: shopify_models.ShopifySellingPlanFixedBillingPolicy,
    shopify_sgqlc_schema.SellingPlanFixedDeliveryPolicy: shopify_models.ShopifySellingPlanFixedDeliveryPolicy,
    shopify_sgqlc_schema.SellingPlanFixedPricingPolicy: shopify_models.ShopifySellingPlanFixedPricingPolicy,
    shopify_sgqlc_schema.SellingPlanGroup: shopify_models.ShopifySellingPlanGroup,
    shopify_sgqlc_schema.SellingPlanInventoryPolicy: shopify_models.ShopifySellingPlanInventoryPolicy,
    shopify_sgqlc_schema.SellingPlanPricingPolicyPercentageValue: shopify_models.ShopifySellingPlanPricingPolicyPercentageValue,
    shopify_sgqlc_schema.SellingPlanRecurringBillingPolicy: shopify_models.ShopifySellingPlanRecurringBillingPolicy,
    shopify_sgqlc_schema.SellingPlanRecurringDeliveryPolicy: shopify_models.ShopifySellingPlanRecurringDeliveryPolicy,
    shopify_sgqlc_schema.SellingPlanRecurringPricingPolicy: shopify_models.ShopifySellingPlanRecurringPricingPolicy,
    shopify_sgqlc_schema.ServerPixel: shopify_models.ShopifyServerPixel,
    shopify_sgqlc_schema.ShippingLine: shopify_models.ShopifyShippingLine,
    shopify_sgqlc_schema.ShippingLineSale: shopify_models.ShopifyShippingLineSale,
    shopify_sgqlc_schema.ShippingRate: shopify_models.ShopifyShippingRate,
    shopify_sgqlc_schema.ShippingRefund: shopify_models.ShopifyShippingRefund,
    shopify_sgqlc_schema.Shop: shopify_models.ShopifyShop,
    shopify_sgqlc_schema.ShopAddress: shopify_models.ShopifyShopAddress,
    shopify_sgqlc_schema.ShopAlert: shopify_models.ShopifyShopAlert,
    shopify_sgqlc_schema.ShopAlertAction: shopify_models.ShopifyShopAlertAction,
    shopify_sgqlc_schema.ShopBillingPreferences: shopify_models.ShopifyShopBillingPreferences,
    shopify_sgqlc_schema.ShopFeatures: shopify_models.ShopifyShopFeatures,
    shopify_sgqlc_schema.ShopLocale: shopify_models.ShopifyShopLocale,
    shopify_sgqlc_schema.ShopPayInstallmentsPaymentDetails: shopify_models.ShopifyShopPayInstallmentsPaymentDetails,
    shopify_sgqlc_schema.ShopPolicy: shopify_models.ShopifyShopPolicy,
    shopify_sgqlc_schema.ShopResourceLimits: shopify_models.ShopifyShopResourceLimits,
    shopify_sgqlc_schema.ShopifyFunction: shopify_models.ShopifyShopifyFunction,
    shopify_sgqlc_schema.ShopifyPaymentsAccount: shopify_models.ShopifyShopifyPaymentsAccount,
    shopify_sgqlc_schema.ShopifyPaymentsAdjustmentOrder: shopify_models.ShopifyShopifyPaymentsAdjustmentOrder,
    shopify_sgqlc_schema.ShopifyPaymentsAssociatedOrder: shopify_models.ShopifyShopifyPaymentsAssociatedOrder,
    shopify_sgqlc_schema.ShopifyPaymentsBalanceTransaction: shopify_models.ShopifyShopifyPaymentsBalanceTransaction,
    shopify_sgqlc_schema.ShopifyPaymentsBalanceTransactionAssociatedPayout: shopify_models.ShopifyShopifyPaymentsBalanceTransactionAssociatedPayout,
    shopify_sgqlc_schema.ShopifyPaymentsBankAccount: shopify_models.ShopifyShopifyPaymentsBankAccount,
    shopify_sgqlc_schema.ShopifyPaymentsDefaultChargeStatementDescriptor: shopify_models.ShopifyShopifyPaymentsDefaultChargeStatementDescriptor,
    shopify_sgqlc_schema.ShopifyPaymentsDispute: shopify_models.ShopifyShopifyPaymentsDispute,
    shopify_sgqlc_schema.ShopifyPaymentsDisputeEvidence: shopify_models.ShopifyShopifyPaymentsDisputeEvidence,
    shopify_sgqlc_schema.ShopifyPaymentsDisputeFileUpload: shopify_models.ShopifyShopifyPaymentsDisputeFileUpload,
    shopify_sgqlc_schema.ShopifyPaymentsDisputeFulfillment: shopify_models.ShopifyShopifyPaymentsDisputeFulfillment,
    shopify_sgqlc_schema.ShopifyPaymentsDisputeReasonDetails: shopify_models.ShopifyShopifyPaymentsDisputeReasonDetails,
    shopify_sgqlc_schema.ShopifyPaymentsExtendedAuthorization: shopify_models.ShopifyShopifyPaymentsExtendedAuthorization,
    shopify_sgqlc_schema.ShopifyPaymentsJpChargeStatementDescriptor: shopify_models.ShopifyShopifyPaymentsJpChargeStatementDescriptor,
    shopify_sgqlc_schema.ShopifyPaymentsPayout: shopify_models.ShopifyShopifyPaymentsPayout,
    shopify_sgqlc_schema.ShopifyPaymentsPayoutSchedule: shopify_models.ShopifyShopifyPaymentsPayoutSchedule,
    shopify_sgqlc_schema.ShopifyPaymentsPayoutSummary: shopify_models.ShopifyShopifyPaymentsPayoutSummary,
    shopify_sgqlc_schema.ShopifyPaymentsRefundSet: shopify_models.ShopifyShopifyPaymentsRefundSet,
    shopify_sgqlc_schema.ShopifyPaymentsToolingProviderPayout: shopify_models.ShopifyShopifyPaymentsToolingProviderPayout,
    shopify_sgqlc_schema.ShopifyPaymentsTransactionSet: shopify_models.ShopifyShopifyPaymentsTransactionSet,
    shopify_sgqlc_schema.ShopifyProtectOrderEligibility: shopify_models.ShopifyShopifyProtectOrderEligibility,
    shopify_sgqlc_schema.ShopifyProtectOrderSummary: shopify_models.ShopifyShopifyProtectOrderSummary,
    shopify_sgqlc_schema.StaffMember: shopify_models.ShopifyStaffMember,
    shopify_sgqlc_schema.StaffMemberPrivateData: shopify_models.ShopifyStaffMemberPrivateData,
    shopify_sgqlc_schema.StagedMediaUploadTarget: shopify_models.ShopifyStagedMediaUploadTarget,
    shopify_sgqlc_schema.StagedUploadParameter: shopify_models.ShopifyStagedUploadParameter,
    shopify_sgqlc_schema.StagedUploadTarget: shopify_models.ShopifyStagedUploadTarget,
    shopify_sgqlc_schema.StandardMetafieldDefinitionTemplate: shopify_models.ShopifyStandardMetafieldDefinitionTemplate,
    shopify_sgqlc_schema.StandardizedProductType: shopify_models.ShopifyStandardizedProductType,
    shopify_sgqlc_schema.StoreCreditAccount: shopify_models.ShopifyStoreCreditAccount,
    shopify_sgqlc_schema.StoreCreditAccountCreditTransaction: shopify_models.ShopifyStoreCreditAccountCreditTransaction,
    shopify_sgqlc_schema.StoreCreditAccountDebitRevertTransaction: shopify_models.ShopifyStoreCreditAccountDebitRevertTransaction,
    shopify_sgqlc_schema.StoreCreditAccountDebitTransaction: shopify_models.ShopifyStoreCreditAccountDebitTransaction,
    shopify_sgqlc_schema.StoreCreditAccountExpirationTransaction: shopify_models.ShopifyStoreCreditAccountExpirationTransaction,
    shopify_sgqlc_schema.StorefrontAccessToken: shopify_models.ShopifyStorefrontAccessToken,
    shopify_sgqlc_schema.SubscriptionAppliedCodeDiscount: shopify_models.ShopifySubscriptionAppliedCodeDiscount,
    shopify_sgqlc_schema.SubscriptionBillingAttempt: shopify_models.ShopifySubscriptionBillingAttempt,
    shopify_sgqlc_schema.SubscriptionBillingCycle: shopify_models.ShopifySubscriptionBillingCycle,
    shopify_sgqlc_schema.SubscriptionBillingCycleEditedContract: shopify_models.ShopifySubscriptionBillingCycleEditedContract,
    shopify_sgqlc_schema.SubscriptionBillingPolicy: shopify_models.ShopifySubscriptionBillingPolicy,
    shopify_sgqlc_schema.SubscriptionContract: shopify_models.ShopifySubscriptionContract,
    shopify_sgqlc_schema.SubscriptionCyclePriceAdjustment: shopify_models.ShopifySubscriptionCyclePriceAdjustment,
    shopify_sgqlc_schema.SubscriptionDeliveryMethodLocalDelivery: shopify_models.ShopifySubscriptionDeliveryMethodLocalDelivery,
    shopify_sgqlc_schema.SubscriptionDeliveryMethodLocalDeliveryOption: shopify_models.ShopifySubscriptionDeliveryMethodLocalDeliveryOption,
    shopify_sgqlc_schema.SubscriptionDeliveryMethodPickup: shopify_models.ShopifySubscriptionDeliveryMethodPickup,
    shopify_sgqlc_schema.SubscriptionDeliveryMethodPickupOption: shopify_models.ShopifySubscriptionDeliveryMethodPickupOption,
    shopify_sgqlc_schema.SubscriptionDeliveryMethodShipping: shopify_models.ShopifySubscriptionDeliveryMethodShipping,
    shopify_sgqlc_schema.SubscriptionDeliveryMethodShippingOption: shopify_models.ShopifySubscriptionDeliveryMethodShippingOption,
    shopify_sgqlc_schema.SubscriptionDeliveryOptionResultFailure: shopify_models.ShopifySubscriptionDeliveryOptionResultFailure,
    shopify_sgqlc_schema.SubscriptionDeliveryOptionResultSuccess: shopify_models.ShopifySubscriptionDeliveryOptionResultSuccess,
    shopify_sgqlc_schema.SubscriptionDeliveryPolicy: shopify_models.ShopifySubscriptionDeliveryPolicy,
    shopify_sgqlc_schema.SubscriptionDiscountAllocation: shopify_models.ShopifySubscriptionDiscountAllocation,
    shopify_sgqlc_schema.SubscriptionDiscountEntitledLines: shopify_models.ShopifySubscriptionDiscountEntitledLines,
    shopify_sgqlc_schema.SubscriptionDiscountFixedAmountValue: shopify_models.ShopifySubscriptionDiscountFixedAmountValue,
    shopify_sgqlc_schema.SubscriptionDiscountPercentageValue: shopify_models.ShopifySubscriptionDiscountPercentageValue,
    shopify_sgqlc_schema.SubscriptionDraft: shopify_models.ShopifySubscriptionDraft,
    shopify_sgqlc_schema.SubscriptionLine: shopify_models.ShopifySubscriptionLine,
    shopify_sgqlc_schema.SubscriptionLocalDeliveryOption: shopify_models.ShopifySubscriptionLocalDeliveryOption,
    shopify_sgqlc_schema.SubscriptionMailingAddress: shopify_models.ShopifySubscriptionMailingAddress,
    shopify_sgqlc_schema.SubscriptionManualDiscount: shopify_models.ShopifySubscriptionManualDiscount,
    shopify_sgqlc_schema.SubscriptionPickupOption: shopify_models.ShopifySubscriptionPickupOption,
    shopify_sgqlc_schema.SubscriptionPricingPolicy: shopify_models.ShopifySubscriptionPricingPolicy,
    shopify_sgqlc_schema.SubscriptionShippingOption: shopify_models.ShopifySubscriptionShippingOption,
    shopify_sgqlc_schema.SubscriptionShippingOptionResultFailure: shopify_models.ShopifySubscriptionShippingOptionResultFailure,
    shopify_sgqlc_schema.SubscriptionShippingOptionResultSuccess: shopify_models.ShopifySubscriptionShippingOptionResultSuccess,
    shopify_sgqlc_schema.SuggestedOrderTransaction: shopify_models.ShopifySuggestedOrderTransaction,
    shopify_sgqlc_schema.SuggestedRefund: shopify_models.ShopifySuggestedRefund,
    shopify_sgqlc_schema.SuggestedReturnRefund: shopify_models.ShopifySuggestedReturnRefund,
    shopify_sgqlc_schema.TaxAppConfiguration: shopify_models.ShopifyTaxAppConfiguration,
    shopify_sgqlc_schema.Taxonomy: shopify_models.ShopifyTaxonomy,
    shopify_sgqlc_schema.TaxonomyAttribute: shopify_models.ShopifyTaxonomyAttribute,
    shopify_sgqlc_schema.TaxonomyCategory: shopify_models.ShopifyTaxonomyCategory,
    shopify_sgqlc_schema.TaxonomyChoiceListAttribute: shopify_models.ShopifyTaxonomyChoiceListAttribute,
    shopify_sgqlc_schema.TaxonomyMeasurementAttribute: shopify_models.ShopifyTaxonomyMeasurementAttribute,
    shopify_sgqlc_schema.TaxonomyValue: shopify_models.ShopifyTaxonomyValue,
    shopify_sgqlc_schema.TenderTransaction: shopify_models.ShopifyTenderTransaction,
    shopify_sgqlc_schema.TenderTransactionCreditCardDetails: shopify_models.ShopifyTenderTransactionCreditCardDetails,
    shopify_sgqlc_schema.TipSale: shopify_models.ShopifyTipSale,
    shopify_sgqlc_schema.TransactionFee: shopify_models.ShopifyTransactionFee,
    shopify_sgqlc_schema.TranslatableContent: shopify_models.ShopifyTranslatableContent,
    shopify_sgqlc_schema.TranslatableResource: shopify_models.ShopifyTranslatableResource,
    shopify_sgqlc_schema.Translation: shopify_models.ShopifyTranslation,
    shopify_sgqlc_schema.TypedAttribute: shopify_models.ShopifyTypedAttribute,
    shopify_sgqlc_schema.UTMParameters: shopify_models.ShopifyUTMParameters,
    shopify_sgqlc_schema.UnitPriceMeasurement: shopify_models.ShopifyUnitPriceMeasurement,
    shopify_sgqlc_schema.UnknownSale: shopify_models.ShopifyUnknownSale,
    shopify_sgqlc_schema.UnverifiedReturnLineItem: shopify_models.ShopifyUnverifiedReturnLineItem,
    shopify_sgqlc_schema.UrlRedirect: shopify_models.ShopifyUrlRedirect,
    shopify_sgqlc_schema.UrlRedirectImport: shopify_models.ShopifyUrlRedirectImport,
    shopify_sgqlc_schema.UrlRedirectImportPreview: shopify_models.ShopifyUrlRedirectImportPreview,
    shopify_sgqlc_schema.Validation: shopify_models.ShopifyValidation,
    shopify_sgqlc_schema.VaultCreditCard: shopify_models.ShopifyVaultCreditCard,
    shopify_sgqlc_schema.VaultPaypalBillingAgreement: shopify_models.ShopifyVaultPaypalBillingAgreement,
    shopify_sgqlc_schema.Vector3: shopify_models.ShopifyVector3,
    shopify_sgqlc_schema.Video: shopify_models.ShopifyVideo,
    shopify_sgqlc_schema.VideoSource: shopify_models.ShopifyVideoSource,
    shopify_sgqlc_schema.WebPixel: shopify_models.ShopifyWebPixel,
    shopify_sgqlc_schema.WebhookEventBridgeEndpoint: shopify_models.ShopifyWebhookEventBridgeEndpoint,
    shopify_sgqlc_schema.WebhookHttpEndpoint: shopify_models.ShopifyWebhookHttpEndpoint,
    shopify_sgqlc_schema.WebhookPubSubEndpoint: shopify_models.ShopifyWebhookPubSubEndpoint,
    shopify_sgqlc_schema.WebhookSubscription: shopify_models.ShopifyWebhookSubscription,
    shopify_sgqlc_schema.Weight: shopify_models.ShopifyWeight,
}
