from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Column, Date, DateTime, Float, Foreign<PERSON>ey, Integer, String, Text
from sqlalchemy.orm import Mapped, relationship, mapped_column
from sqlalchemy.sql import func

from datetime import datetime, date, UTC
from typing import Optional

from database import Base


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    stores = relationship("Store", back_populates="owner")


class Store(Base):
    __tablename__ = "stores"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    platform = Column(String, nullable=False, default="shopify")
    api_key = Column(String)
    api_secret_key = Column(String)
    admin_access_token = Column(String)
    storefront_access_token = Column(String)
    shop_domain = Column(String, nullable=True)
    shop_id = Column(String, nullable=True)
    shop_name = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    last_sync = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    owner_id = Column(Integer, ForeignKey("users.id"))
    owner = relationship("User", back_populates="stores")
    


class Forecast(Base):
    __tablename__ = "forecasts"

    id = Column(Integer, primary_key=True, index=True)
    store_id = Column(Integer, ForeignKey("stores.id"))
    forecast_date = Column(DateTime(timezone=True), nullable=False)
    predicted_sales = Column(Float, nullable=False)
    confidence_lower = Column(Float, nullable=True)
    confidence_upper = Column(Float, nullable=True)
    forecast_period = Column(String, nullable=True)  # Added for caching
    raw_forecast_data = Column(Text, nullable=True)  # Added for caching
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    store = relationship("Store")

    store = relationship("Store")


class StoreSale(Base):
    __tablename__ = "store_sales"

    id = Column(Integer, primary_key=True, index=True)
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=False)
    product_id = Column(
        String, ForeignKey("shopify_products.external_id"), nullable=True
    )  # Can be null for overall store sales
    variant_id = Column(
        String, ForeignKey("shopify_product_variants.external_id"), nullable=True
    )  # Can be null for overall store sales
    sale_date = Column(Date, nullable=False)
    quantity_sold = Column(Integer, nullable=False)
    revenue = Column(Float, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    store = relationship("Store")
    product = relationship(
        "ShopifyProduct", primaryjoin="StoreSale.product_id == ShopifyProduct.external_id", foreign_keys=[product_id]
    )
    variant = relationship(
        "ShopifyProductVariant",
        primaryjoin="StoreSale.variant_id == ShopifyProductVariant.external_id",
        foreign_keys=[variant_id],
    )


class Holiday(Base):
    __tablename__ = "holidays"

    id = Column(Integer, primary_key=True, index=True)
    holiday_date = Column(DateTime(timezone=True), nullable=False, unique=True)
    name = Column(String, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class ProductPerformance(Base):
    __tablename__ = "product_performance"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(String, ForeignKey("shopify_products.external_id"), nullable=False, unique=True)
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=False)
    total_sold = Column(Integer, default=0)
    total_revenue = Column(Float, default=0.0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    store = relationship("Store")
    product = relationship(
        "ShopifyProduct",
        primaryjoin="ProductPerformance.product_id == ShopifyProduct.external_id",
        foreign_keys=[product_id],
    )


class StoreAnalyticsSnapshot(Base):
    __tablename__ = "store_analytics_snapshots"

    id = Column(Integer, primary_key=True, index=True)
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=False)
    date = Column(Date, nullable=False, unique=True)
    total_sales = Column(Float, default=0.0)
    total_orders = Column(Integer, default=0)
    average_order_value = Column(Float, default=0.0)
    new_customers = Column(Integer, default=0)
    conversion_rate = Column(Float, default=0.0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    store = relationship("Store")
