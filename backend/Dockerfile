# Use a slim, modern Python base image
FROM python:3.13-slim

# Set the working directory
WORKDIR /app

# Install system dependencies including C++ compiler for forecasting libraries
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    build-essential \
    gfortran \
    libopenblas-dev \
    liblapack-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

COPY pyproject.toml ./

RUN pip install --no-cache-dir --upgrade pip &&     pip install --no-cache-dir . pytest pytest-asyncio

# Copy application code


COPY . .

# Create non-root user
# RUN useradd --create-home --shell /bin/bash app \
#     && chown -R app:app /app
# USER app

# Expose port
EXPOSE 8000

# Run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
