#!/usr/bin/env python3
"""
Test script to verify all agent APIs are working correctly.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the src directory to the Python path
backend_dir = Path(__file__).parent
src_dir = backend_dir / "src"
sys.path.insert(0, str(src_dir))


async def test_imports():
    """Test that all modules can be imported successfully."""
    print("Testing imports...")

    try:
        # Test core imports
        from modules.agents import crud, schemas, service

        print("✅ Core agent modules imported successfully")

        # Test router import
        from modules.agents.router import router

        print("✅ Agent router imported successfully")

        # Test websocket manager
        from modules.agents.websocket_manager import websocket_manager

        print("✅ WebSocket manager imported successfully")

        # Test database imports
        from core.db.database import get_db, get_db_session_factory

        print("✅ Database functions imported successfully")

        return True

    except Exception as e:
        print(f"❌ Import error: {e}")
        return False


async def test_schemas():
    """Test that schemas can be instantiated."""
    print("\nTesting schemas...")

    try:
        from modules.agents import schemas

        # Test basic agent creation schema
        agent_create = schemas.AgentCreate(
            name="Test Agent",
            description="A test agent",
            category="test",
            is_active=True,
        )
        print("✅ AgentCreate schema works")

        # Test workflow schema
        workflow = schemas.WorkflowData(
            id="test-workflow",
            name="Test Workflow",
            version="1.0.0",
            active=True,
            nodes=[],
            connections={},
            start_node_id="start",
        )
        print("✅ WorkflowData schema works")

        # Test node config schema
        node_config = schemas.NodeConfig(
            id="test-node", name="Test Node", type=schemas.NodeType.START
        )
        print("✅ NodeConfig schema works")

        return True

    except Exception as e:
        print(f"❌ Schema error: {e}")
        return False


async def test_service_functions():
    """Test that service functions can be called."""
    print("\nTesting service functions...")

    try:
        from modules.agents import service

        # Test node types
        node_types = await service.get_node_types()
        print(f"✅ get_node_types returned {len(node_types)} node types")

        # Test node categories
        node_categories = await service.get_node_categories()
        print(f"✅ get_node_categories returned {len(node_categories)} categories")

        # Test UI config
        ui_config = await service.get_ui_config()
        print("✅ get_ui_config works")

        # Test workflow validation
        from modules.agents.schemas import WorkflowData

        test_workflow = WorkflowData(
            id="test",
            name="Test",
            version="1.0.0",
            active=True,
            nodes=[],
            connections={},
            start_node_id="start",
        )
        validation_result = await service.validate_workflow(test_workflow)
        print(f"✅ validate_workflow works (valid: {validation_result.is_valid})")

        return True

    except Exception as e:
        print(f"❌ Service error: {e}")
        return False


async def test_router_endpoints():
    """Test that router endpoints are properly defined."""
    print("\nTesting router endpoints...")

    try:
        from modules.agents.router import router

        # Check that router has the expected routes
        routes = [route.path for route in router.routes]

        expected_routes = [
            "/agents/",
            "/agents/{agent_id}",
            "/agents/workflows/validate",
            "/agents/nodes/types",
            "/agents/nodes/categories",
            "/agents/templates",
            "/agents/config/ui",
        ]

        found_routes = []
        for expected in expected_routes:
            matching_routes = [
                r
                for r in routes
                if expected.replace("{agent_id}", "{path}") in r or expected in r
            ]
            if matching_routes:
                found_routes.append(expected)

        print(f"✅ Found {len(found_routes)}/{len(expected_routes)} expected routes")

        # Check WebSocket routes
        websocket_routes = [
            route
            for route in router.routes
            if hasattr(route, "path") and "/ws/" in route.path
        ]
        print(f"✅ Found {len(websocket_routes)} WebSocket routes")

        return True

    except Exception as e:
        print(f"❌ Router error: {e}")
        return False


async def main():
    """Run all tests."""
    print("🧪 Testing Agent Builder API")
    print("=" * 50)

    tests = [test_imports, test_schemas, test_service_functions, test_router_endpoints]

    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)

    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"✅ Passed: {sum(results)}/{len(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}/{len(results)}")

    if all(results):
        print("\n🎉 All tests passed! The Agent Builder API is ready.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
