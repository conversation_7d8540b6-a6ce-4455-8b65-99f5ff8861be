from contextlib import asynccontextmanager
import json
import logging
import time

from dotenv import load_dotenv
from fastapi import FastAP<PERSON>, Request, status, HTTPException
from fastapi.exceptions import ResponseValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from logging_config import setup_logging
from config import settings
from database import engine
from models.models import Base
from routers import auth, shopify, stores, users, webhooks, sync

load_dotenv()

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Create database tables only if not in testing mode
if not settings.testing:
    Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="E-commerce Integration API",
    description="API for integrating with Shopify and WooCommerce stores",
    version="1.0.0",
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info(f"E-commerce Integration API is running at: http://localhost:{settings.port}")
    yield


# Request Logging Middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    if request.url.path == "/health":
        return await call_next(request)

    start_time = time.time()

    response = await call_next(request)

    process_time = time.time() - start_time

    response_body = b""
    async for chunk in response.body_iterator:
        response_body += chunk

    log_dict = {
        "request": {
            "method": request.method,
            "url": str(request.url),
            "headers": dict(request.headers),
        },
        "response": {
            "status_code": response.status_code,
            "process_time_seconds": f"{process_time:.4f}",
            "body": response_body.decode(),
        },
    }

    logger.info(json.dumps(log_dict))

    # After reading the body, we need to create a new iterator for the response
    async def new_body_iterator():
        yield response_body

    response.body_iterator = new_body_iterator()

    return response


# Add exception handler for ResponseValidationError
@app.exception_handler(ResponseValidationError)
async def validation_exception_handler(request, exc):
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": "The server encountered an error while processing the response. Please contact support."},
    )

# Add exception handler for HTTPException to debug recursion error
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc: HTTPException):
    print(f"Caught HTTPException: Status Code: {exc.status_code}, Detail: {exc.detail}")
    # You can add more debugging prints here if needed, e.g., exc.headers
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail}
    )


# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
app.include_router(stores.router, prefix="/api/stores", tags=["stores"])
app.include_router(users.router, prefix="/api/users", tags=["users"])
app.include_router(sync.router, prefix="/api/sync", tags=["sync"])

app.include_router(webhooks.router, prefix="/api", tags=["webhooks"])
app.include_router(shopify.router, prefix="/api", tags=["shopify"])


@app.get("/")
async def root():
    return {"message": "E-commerce Integration API", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=settings.port)
