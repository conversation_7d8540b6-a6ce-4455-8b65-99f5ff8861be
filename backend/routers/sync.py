from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Type

from database import get_db
from models import models
from services.abstract_sync_service import AbstractSyncService, SourceType
from services.shopify_sync_service import ShopifySyncService

router = APIRouter(prefix="/sync", tags=["sync"])

# Mapping of SourceType to concrete AbstractSyncService implementations
SYNC_SERVICE_MAP: Dict[SourceType, Type[AbstractSyncService]] = {
    SourceType.SHOPIFY: ShopifySyncService,
    # Add other mappings here (e.g., SourceType.WOOCOMMERCE: WoocommerceSyncService)
}


async def get_sync_service(store_id: int, db: Session = Depends(get_db)) -> AbstractSyncService:
    """Dependency to get the correct sync service based on store_id and determine source type."""
    store = db.query(models.Store).filter(models.Store.id == store_id).first()
    if not store:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Store with ID {store_id} not found.")

    source_type: SourceType
    if "myshopify.com" in store.shop_domain:
        source_type = SourceType.SHOPIFY
    # Add more conditions for other source types (e.g., WooCommerce)
    # elif "woocommerce.com" in store.shop_domain:
    #     source_type = SourceType.WOOCOMMERCE
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"Unsupported store domain: {store.shop_domain}"
        )

    service_class = SYNC_SERVICE_MAP.get(source_type)
    if not service_class:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=f"Sync service for source type {source_type} not found."
        )

    return service_class(store=store)


@router.get("/{store_id}/test_connection")
async def test_connection(store_id: int, sync_service: AbstractSyncService = Depends(get_sync_service)):
    """
    Test endpoint to verify connection and basic service functionality for a given source type.
    """
    try:
        # Assuming test_connection is implemented in concrete services
        return await sync_service.test_connection()
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post("/{store_id}/sync_all")
async def sync_all(
    store_id: int,
    db: Session = Depends(get_db),
    sync_service: AbstractSyncService = Depends(lambda db=Depends(get_db): get_sync_service(store_id, db)),
):
    """
    Trigger sync for all entities for a given source type and stream status for each.
    """
    results = []
    async for status_update in sync_service.fetch_all_entities(db):
        results.append(status_update)
    return results


@router.post("/{store_id}/query")
async def execute_graphql_query(
    store_id: int,
    query_name: str,
    variables: Optional[Dict] = None,
    sync_service: AbstractSyncService = Depends(get_sync_service),
):
    """
    Executes a dynamic GraphQL query against the external service.
    """
    try:
        result = await sync_service.execute_query(query_name, variables)
        return result
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to execute query '{query_name}': {e}",
        )


@router.post("/{store_id}/mutation")
async def execute_graphql_mutation(
    store_id: int,
    mutation_name: str,
    input_data: Dict,
    sync_service: AbstractSyncService = Depends(get_sync_service),
):
    """
    Executes a dynamic GraphQL mutation against the external service.\n    """
    try:
        result = await sync_service.execute_mutation(mutation_name, input_data)
        return result
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to execute mutation '{mutation_name}': {e}",
        )