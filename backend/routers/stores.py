"""
API Router for Store Management and Bi-directional Sync
"""

import logging
from typing import List

from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session

from models import models
from schemas import schemas, shopify_mappings
from database import get_db
from routers import auth

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/mappings/{store_id}", response_model=schemas.StoreMappingResponse)
async def get_store_mappings(
    store_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user),
):
    """
    Retrieve API mappings (queries and mutations) for a given store.
    """
    store = db.query(models.Store).filter(models.Store.id == store_id, models.Store.owner_id == current_user.id).first()
    if not store:
        raise HTTPException(status_code=404, detail="Store not found or not owned by user.")

    if store.platform.lower() != "shopify":
        raise HTTPException(status_code=404, detail="Store type not supported for mappings.")

    mutations = []
    for name, details in shopify_mappings.MUTATION_PAYLOAD_MAP.items():
        payload_type = details["payload_type"]
        input_type = details["input_type"]

        payload_fields = []
        if payload_type:
            for field_name, field_type in payload_type.__annotations__.items():
                payload_fields.append(schemas.FieldDetail(name=field_name, type=str(field_type)))

        input_fields = []
        if input_type:
            for field_name, field_type in input_type.__annotations__.items():
                input_fields.append(schemas.FieldDetail(name=field_name, type=str(field_type)))

        mutations.append(
            schemas.ActionDetail(
                name=name,
                payload_type=str(payload_type.__name__) if payload_type else "None",
                input_type=str(input_type.__name__) if input_type else "None",
                payload_fields=payload_fields,
                input_fields=input_fields,
            )
        )

    queries = []
    for name, details in shopify_mappings.QUERY_PAYLOAD_MAP.items():
        return_type = details["return_type"]
        node_type = details[
            "node_type"
        ]  # This is not directly used for fields, but kept for completeness if needed later

        payload_fields = []
        if return_type:
            for field_name, field_type in return_type.__annotations__.items():
                payload_fields.append(schemas.FieldDetail(name=field_name, type=str(field_type)))

        queries.append(
            schemas.ActionDetail(
                name=name,
                payload_type=str(return_type.__name__) if return_type else "None",
                input_type="None",  # Queries typically don't have a separate 'input_type' in the same way mutations do
                payload_fields=payload_fields,
                input_fields=[],
            )
        )

    return schemas.StoreMappingResponse(store_type=store.platform, queries=queries, mutations=mutations)


@router.post("/", response_model=schemas.Store)
async def create_store(
    store_data: schemas.StoreCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user),
):
    """
    Create a new store for the current user.
    """
    db_store = models.Store(**store_data.model_dump(exclude_unset=True), owner_id=current_user.id)
    db.add(db_store)
    db.commit()
    db.refresh(db_store)
    return db_store


@router.get("/", response_model=List[schemas.Store])
async def get_stores(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user),
):
    """
    Retrieve all stores owned by the current user.
    """
    return db.query(models.Store).filter(models.Store.owner_id == current_user.id).all()


@router.get("/{store_id}", response_model=schemas.Store)
async def get_store_by_id(
    store_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user),
):
    """
    Retrieve a specific store by its ID, ensuring it belongs to the current user.
    """
    store = db.query(models.Store).filter(models.Store.id == store_id, models.Store.owner_id == current_user.id).first()
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")
    return store


@router.delete("/{store_id}", status_code=204)
async def delete_store(
    store_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user),
):
    """
    Delete a store by its ID, ensuring it belongs to the current user.
    """
    store = db.query(models.Store).filter(models.Store.id == store_id, models.Store.owner_id == current_user.id).first()
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")
    db.delete(store)
    db.commit()
    return


@router.post("/{store_id}/toggle-activation")
async def toggle_store_activation(
    store_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(auth.get_current_user),
):
    """
    Toggle the activation status of a store.
    """
    logger.info(f"Attempting to toggle activation for store_id: {store_id} by user_id: {current_user.id}")
    store = db.query(models.Store).filter(models.Store.id == store_id, models.Store.owner_id == current_user.id).first()
    if not store:
        logger.warning(f"Store {store_id} not found or not owned by user {current_user.id}")
        raise HTTPException(status_code=404, detail="Store not found or not owned by user")

    store.is_active = not store.is_active
    db.commit()
    db.refresh(store)
    return {"message": f"Store {store_id} activation toggled to {store.is_active}"}
