import logging

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session

from database import get_db


logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/shopify/{store_id}")
async def handle_shopify_webhook(store_id: int, request: Request, db: Session = Depends(get_db)):
    """Handle incoming Shopify webhooks."""

    return {"status": "ok"}
