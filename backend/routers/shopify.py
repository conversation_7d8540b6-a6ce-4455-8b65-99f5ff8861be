from fastapi import APIRouter, Depends, HTTPException, status, Request
import httpx
from typing import Dict, Any, Type, List, Optional, Union
from pydantic import BaseModel, create_model
from sqlalchemy.orm import Session
import logging

from schemas.shopify_mappings import MUTATION_PAYLOAD_MAP, QUERY_PAYLOAD_MAP, GRAPHQL_TO_MODEL_MAP
from services.shopify_sync_service import ShopifySyncService
from database import get_db
from models import models as db_models
from config import settings

# Import the generated sgqlc schema to access the types for dynamic model creation
from schemas import shopify_sgqlc_schema, shopify_graphql_models
from services.shopify_graphql_builder import GQLGenerator, load_introspection_data

if settings.testing:
    from unittest.mock import MagicMock

logger = logging.getLogger(__name__)

router = APIRouter()

# Load introspection data and create GQLGenerator for dynamic route creation
introspection_data_for_routes = load_introspection_data()
if not introspection_data_for_routes:
    logger.error("Failed to load Shopify GraphQL introspection data for router. Dynamic query routes might not be correctly generated.")
router_gql_generator = GQLGenerator(introspection_data_for_routes) if introspection_data_for_routes else None


async def get_shopify_sync_service(
    store_id: int, db: Session = Depends(get_db)
) -> tuple[Optional[ShopifySyncService], Optional[str]]:
    store = db.query(db_models.Store).filter(db_models.Store.id == store_id).first()
    if not store:
        return None, "Store not found."
    if store.platform != "shopify":
        db.expunge(store)
        return None, "Store is not a Shopify store."
    # The abstract methods 'delete_entity' and 'sync_all_entities' are implemented in ShopifySyncService
    return ShopifySyncService(store, client=httpx.AsyncClient()), None


# Dynamically create mutation routes
for mutation_name, details in MUTATION_PAYLOAD_MAP.items():
    payload_type = details["payload_type"]
    input_type = details["input_type"]

    def create_mutation_route(current_mutation_name: str, current_payload_type: Any, current_input_type: Any):
        request_model = None
        if current_input_type:
            # Assuming the Pydantic model name is the same as the sgqlc input type name
            model_name = current_input_type.__name__
            request_model = getattr(shopify_graphql_models, model_name, None)
            if not request_model:
                logger.warning(f"Pydantic model '{model_name}' not found in shopify_graphql_models. Using generic BaseModel.")
                request_model = BaseModel # Fallback to a generic BaseModel if not found

        @router.post(f"/shopify/{{store_id}}/mutations/{current_mutation_name}", summary=f"Execute Shopify {current_mutation_name} mutation")
        async def _dynamic_mutation_route(
            store_id: int,
            input_data: Optional[request_model] = None,
            service_info: tuple[Optional[ShopifySyncService], Optional[str]] = Depends(get_shopify_sync_service),
        ):
            shopify_service, error_detail = service_info
            if error_detail:
                status_code = status.HTTP_404_NOT_FOUND if "not found" in error_detail else status.HTTP_400_BAD_REQUEST
                raise HTTPException(
                    status_code=status_code, detail=error_detail
                )

            if input_data:
                response_data = await shopify_service.execute_mutation(current_mutation_name, input_data.model_dump(by_alias=True, exclude_unset=True))
            else:
                response_data = await shopify_service.execute_mutation(current_mutation_name, {})
            return response_data
        return _dynamic_mutation_route

    globals()[f"_dynamic_mutation_route_{mutation_name}"] = create_mutation_route(mutation_name, payload_type, input_type)


def create_query_request_model(query_name: str, gql_generator: GQLGenerator) -> Type[BaseModel]:
    fields = {}
    query_args = gql_generator._get_query_field_args(query_name)

    for arg_name, arg_meta in query_args.items():
        graphql_type_name = arg_meta["type"]
        is_non_null = arg_meta["non_null"]
        is_list = arg_meta.get("is_list", False) # Assuming _get_query_field_args provides this

        python_type = None

        # 1. Try to get the type from GRAPHQL_TO_MODEL_MAP first
        if graphql_type_name in GRAPHQL_TO_MODEL_MAP:
            python_type = GRAPHQL_TO_MODEL_MAP[graphql_type_name]
        else:
            # 2. Fallback for common scalar types not explicitly in GRAPHQL_TO_MODEL_MAP
            if graphql_type_name == "ID":
                python_type = str
            elif graphql_type_name == "String":
                python_type = str
            elif graphql_type_name == "Int":
                python_type = int
            elif graphql_type_name == "Boolean":
                python_type = bool
            elif graphql_type_name == "Float":
                python_type = float
            elif graphql_type_name == "DateTime":
                python_type = str # Or datetime.datetime if we parse it
            elif graphql_type_name.endswith("SortKeys"): # For sort keys enums
                enum_type = getattr(shopify_graphql_models, graphql_type_name, None)
                if enum_type:
                    python_type = enum_type
                else:
                    logger.warning(f"Enum type '{graphql_type_name}' not found in shopify_graphql_models. Using str.")
                    python_type = str
            elif graphql_type_name.endswith("Input"): # For complex input objects
                input_model = getattr(shopify_graphql_models, graphql_type_name, None)
                if input_model:
                    python_type = input_model
                else:
                    logger.warning(f"Input model '{graphql_type_name}' not found in shopify_graphql_models. Using Dict[str, Any].")
                    python_type = Dict[str, Any]
            else:
                # For other custom scalar types or unhandled types, use Dict[str, Any] or str
                logger.debug(f"Unhandled GraphQL type '{graphql_type_name}' for query argument '{arg_name}'. Using Dict[str, Any].")
                python_type = Dict[str, Any] # More robust fallback for complex types

        if python_type is None:
            logger.error(f"Could not determine Python type for GraphQL type '{graphql_type_name}'. Defaulting to Any.")
            python_type = Any

        # Handle List types
        if is_list:
            python_type = List[python_type]

        if is_non_null:
            fields[arg_name] = (python_type, ...) # Required field
        else:
            fields[arg_name] = (Optional[python_type], None) # Optional field

    model_name = f"{query_name.capitalize()}QueryRequest"
    return create_model(model_name, **fields)


# Dynamically create query routes
for query_name, query_info in QUERY_PAYLOAD_MAP.items():
    # Skip if router_gql_generator is not initialized
    if not router_gql_generator:
        logger.warning(f"router_gql_generator not initialized. Skipping query route for {query_name}.")
        continue

    request_model = create_query_request_model(query_name, router_gql_generator)

    def create_query_route(current_query_name: str, current_request_model: Type[BaseModel]):
        @router.post(f"/shopify/{{store_id}}/queries/{current_query_name}", summary=f"Execute Shopify {current_query_name} query")
        async def _dynamic_query_route(
            store_id: int,
            request_data: Optional[current_request_model] = None,
            service_info: tuple[Optional[ShopifySyncService], Optional[str]] = Depends(get_shopify_sync_service),
        ):
            shopify_service, error_detail = service_info
            if error_detail:
                status_code = status.HTTP_404_NOT_FOUND if "not found" in error_detail else status.HTTP_400_BAD_REQUEST
                raise HTTPException(
                    status_code=status_code, detail=error_detail
                )

            # Pass the request_data as variables to execute_query
            response_data = await shopify_service.execute_query(current_query_name, request_data.model_dump(by_alias=True, exclude_unset=True))
            return response_data
        return _dynamic_query_route

    globals()[f"_dynamic_query_route_{query_name}"] = create_query_route(query_name, request_model)
   