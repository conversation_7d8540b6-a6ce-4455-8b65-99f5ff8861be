from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, AsyncGenerator, Dict, List, Optional

from sqlalchemy.orm import Session
from schemas import schemas


class SourceType(str, Enum):
    SHOPIFY = "shopify"
    WOOCOMMERCE = "woocommerce"
    # Add other external service types here


class AbstractSyncService(ABC):
    """Abstract base class for all external service synchronization services."""

    def __init__(self, store: schemas.Store):
        self.store = store

    @abstractmethod
    async def fetch_all_entities(self, db: Session) -> AsyncGenerator[Dict, None]:
        """Fetches all entities for the given store and yields status updates."""
        pass

    @abstractmethod
    async def test_connection(self) -> schemas.StoreConnectionTest:
        """Tests the connection to the external service."""
        pass

    @abstractmethod
    async def execute_query(self, query_name: str, variables: Optional[Dict] = None) -> Dict[str, Any]:
        """Executes a generic GraphQL query against the external service."""
        pass

    @abstractmethod
    async def execute_mutation(self, mutation_name: str, input_data: Dict) -> Dict[str, Any]:
        """Executes a generic GraphQL mutation against the external service."""
        pass

    @abstractmethod
    def verify_webhook(self, data: bytes, hmac_header: str) -> bool:
        """Verifies a webhook payload from the external service."""
        pass

    @abstractmethod
    def get_queries(self) -> List[Dict[str, Any]]:
        """Returns a list of available GraphQL query operations and their details."""
        pass

    @abstractmethod
    def get_mutations(self) -> List[Dict[str, Any]]:
        """Returns a list of available GraphQL mutation operations and their details."""
        pass
