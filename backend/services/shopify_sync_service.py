from __future__ import annotations

import base64
import binascii
import hashlib
import hmac
import json
import keyword
import logging
from datetime import datetime
import json
from pathlib import Path
import re
from typing import Any, AsyncGenerator, Dict, List, Optional

import httpx
from fastapi import HTTPException
from sqlalchemy.orm import Session
from sqlalchemy.inspection import inspect


# Import the generated Shopify SQLAlchemy models and mappings
from models.shopify_models import *
from schemas import schemas
from schemas.schemas import StoreConnectionTest
from schemas.shopify_mappings import GRAPHQL_TO_MODEL_MAP, MUTATION_PAYLOAD_MAP, QUERY_PAYLOAD_MAP

from services.abstract_sync_service import AbstractSyncService, SourceType
from services.shopify_graphql_builder import GQLGenerator, load_introspection_data, to_camel_case


# Import the generated Shopify SQLAlchemy models and mappings
from models.shopify_models import *
from schemas import schemas
from schemas.schemas import StoreConnectionTest
from schemas.shopify_mappings import GRAPHQL_TO_MODEL_MAP, MUTATION_PAYLOAD_MAP, QUERY_PAYLOAD_MAP

from services.abstract_sync_service import AbstractSyncService, SourceType

logger = logging.getLogger(__name__)


RESERVED_KEYWORDS = set(keyword.kwlist)


def to_snake_case(name):
    s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)
    snake_case_name = re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1).lower()
    if snake_case_name in RESERVED_KEYWORDS:
        return f"{snake_case_name}_obj"  # Append _obj to avoid keyword conflicts
    return snake_case_name


class ShopifySyncService(AbstractSyncService):
    """Service for interacting with all aspects of the Shopify API"""

    def __init__(self, store: schemas.Store, client: httpx.AsyncClient = None):
        super().__init__(store)
        self.api_version = "2025-07"
        self.graphql_endpoint = f"https://{self.store.shop_domain}/admin/api/{self.api_version}/graphql.json"
        self.storefront_graphql_endpoint = f"https://{self.store.shop_domain}/api/{self.api_version}/graphql.json"
        if not self.store.admin_access_token:
            raise HTTPException(status_code=400, detail="Shopify admin access token is not configured for this store.")
        self.admin_headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": self.store.admin_access_token,
        }
        self.introspection_data = load_introspection_data()
        if not self.introspection_data:
            raise HTTPException(status_code=500, detail="Failed to load Shopify GraphQL introspection data.")
        self.gql_generator = GQLGenerator(self.introspection_data)
        self.client = client or httpx.AsyncClient()
        logger.info(f"Initialized Shopify service for {self.store.shop_domain}")

    async def _execute_graphql_query(self, query_string: str, variables: Optional[Dict] = None) -> Dict[str, Any]:
        """Executes a raw GraphQL query string against the Shopify Admin API."""
        payload = {"query": str(query_string)}
        if variables:
            payload["variables"] = variables
        try:
            response = await self.client.post(self.graphql_endpoint, json=payload, headers=self.admin_headers)
            response.raise_for_status()
            result = response.json()
            if "errors" in result:
                logger.exception(f'GraphQL errors: {json.dumps(result["errors"])}', exc_info=True)
                raise HTTPException(status_code=400, detail=f'GraphQL error: {result["errors"][0]["message"]}')
            return result.get("data", {})
        except httpx.HTTPStatusError as e:
            logger.exception(f"HTTP error: {e.response.status_code} - {e.response.text}")
            raise HTTPException(status_code=e.response.status_code, detail=f"Shopify API error: {e.response.text}")
        except httpx.RequestError as e:
            logger.exception(f"Request error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error connecting to Shopify: {str(e)}")

    async def _execute_storefront_graphql_query(self, query_string: str, variables: Optional[Dict] = None) -> Dict[str, Any]:
        if not self.store.storefront_access_token:
            raise HTTPException(status_code=400, detail="Shopify storefront access token is required for this operation but is not configured.")
        self.storefront_headers = {
            "Content-Type": "application/json",
            "X-Shopify-Storefront-Access-Token": self.store.storefront_access_token,
        }
        payload = {"query": str(query_string)}
        if variables:
            payload["variables"] = variables
        try:
            response = await self.client.post(self.storefront_graphql_endpoint, json=payload, headers=self.storefront_headers)
            response.raise_for_status()
            result = response.json()
            if "errors" in result:
                logger.exception(f'GraphQL errors: {json.dumps(result["errors"])}', exc_info=True)
                raise HTTPException(status_code=400, detail=f'GraphQL error: {result["errors"][0]["message"]}')
            return result.get("data", {})
        except httpx.HTTPStatusError as e:
            logger.exception(f"HTTP error: {e.response.status_code} - {e.response.text}")
            raise HTTPException(status_code=e.response.status_code, detail=f"Shopify API error: {e.response.text}")
        except httpx.RequestError as e:
            logger.exception(f"Request error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error connecting to Shopify: {str(e)}")

    def verify_webhook(self, data: bytes, hmac_header: str) -> bool:
        if not self.store.api_secret_key:
            logger.warning("Shopify API secret key not configured. Webhook verification skipped.")
            return False
        digest = hmac.new(self.store.api_secret_key.encode("utf-8"), data, hashlib.sha256).digest()
        try:
            decoded_hmac_header = base64.b64decode(hmac_header)
        except binascii.Error:
            logger.error("Invalid base64 string for HMAC header.")
            return False
        return hmac.compare_digest(digest, decoded_hmac_header)

    def get_queries(self) -> List[str]:
        """Returns a list of available GraphQL query operation names."""
        return list(QUERY_PAYLOAD_MAP.keys())

    def get_mutations(self) -> List[str]:
        """Returns a list of available GraphQL mutation operation names."""
        return list(MUTATION_PAYLOAD_MAP.keys())

    async def execute_query(self, query_name: str, variables: Optional[Dict] = None) -> Dict[str, Any]:
        """Executes a generic GraphQL query against the Shopify API using dynamically generated GQL strings."""
        query_info = QUERY_PAYLOAD_MAP.get(query_name)
        if not query_info:
            raise HTTPException(status_code=404, detail=f"Query '{query_name}' not found in QUERY_PAYLOAD_MAP.")
        
        return_type_name = query_info["return_type"].__name__ if hasattr(query_info["return_type"], '__name__') else query_info["return_type"]
        node_type_name = query_info["node_type"].__name__ if hasattr(query_info["node_type"], '__name__') else query_info["node_type"]

        query_string, stats = self.gql_generator.generate_query_gql(query_name, return_type_name, node_type_name, variables)
        logger.debug(f"DEBUG: Executing query '{query_name}' with variables: {variables}, stats: {stats}, query_string: {query_string}")
        result = await self._execute_graphql_query(query_string, variables)
        return result.get(query_name, {})

    async def execute_mutation(self, mutation_name: str, input_data: Dict) -> Dict[str, Any]:
        """Executes a generic GraphQL mutation against the Shopify API using dynamically generated GQL strings."""
        mutation_info = MUTATION_PAYLOAD_MAP.get(mutation_name)
        if not mutation_info:
            raise HTTPException(status_code=404, detail=f"Mutation '{mutation_name}' not found in MUTATION_PAYLOAD_MAP.")

        payload_type_name = mutation_info["payload_type"].__name__ if hasattr(mutation_info["payload_type"], '__name__') else mutation_info["payload_type"]
        input_type_name = mutation_info["input_type"].__name__ if hasattr(mutation_info["input_type"], '__name__') else mutation_info["input_type"]

        mutation_string, stats = self.gql_generator.generate_mutation_gql(mutation_name, payload_type_name, input_type_name, input_data)
        logger.debug(f"DEBUG: Executing mutation '{mutation_name}' with input_data: {input_data}, stats: {stats}, mutation_string: {mutation_string}")
        
        result = await self._execute_graphql_query(mutation_string, variables={"input": input_data})

        shopify_response = result.get(mutation_name, {})
        if shopify_response.get("userErrors"):
            error_messages = [err["message"] for err in shopify_response["userErrors"]]
            raise HTTPException(status_code=400, detail=f"Shopify API Error: {', '.join(error_messages)}")

        return shopify_response

    def _get_or_create(self, db: Session, model, external_id: str, **kwargs):
        instance = db.query(model).filter_by(external_id=external_id).first()
        if not instance:
            instance = model(external_id=external_id, **kwargs)
            db.add(instance)
            db.flush()
        return instance

    async def _fetch_paginated_graphql(
        self, query_name: str, resource_path: List[str], initial_variables: Optional[Dict] = None
    ) -> AsyncGenerator[Dict, None]:
        # Convert initial_variables to camelCase once at the beginning
        camel_case_initial_variables = {to_camel_case(k): v for k, v in (initial_variables or {}).items()}
        
        # Handle 'updatedAtMin' specifically for 'products' query
        query_arg_value = None
        if query_name == "products" and "updatedAtMin" in camel_case_initial_variables:
            query_arg_value = f"updated_at:>='{camel_case_initial_variables['updatedAtMin']}'"
            del camel_case_initial_variables["updatedAtMin"] # Remove it from direct variables

        variables = {"first": 50, **camel_case_initial_variables}
        if query_arg_value:
            variables["query"] = query_arg_value # Add it to the 'query' variable

        has_next_page = True
        while has_next_page:
            query_info = QUERY_PAYLOAD_MAP.get(query_name)
            if not query_info:
                raise HTTPException(status_code=404, detail=f"Query '{query_name}' not found in QUERY_PAYLOAD_MAP.")
            
            return_type_name = query_info["return_type"].__name__ if hasattr(query_info["return_type"], '__name__') else query_info["return_type"]
            node_type_name = query_info["node_type"].__name__ if hasattr(query_info["node_type"], '__name__') else query_info["node_type"]

            query_string, stats = self.gql_generator.generate_query_gql(query_name, return_type_name, node_type_name, variables)

            result = await self._execute_graphql_query(query_string, variables.copy())
            data = result # Directly use result, as _execute_graphql_query already extracts 'data'
            for key in resource_path[:-1]:
                data = data.get(key, {})
            edges = data.get(resource_path[-1], {}).get("edges", [])
            for edge in edges:
                yield edge["node"]
            page_info = data.get(resource_path[-1], {}).get("pageInfo", {})
            has_next_page = page_info.get("hasNextPage", False)
            if has_next_page:
                variables["after"] = page_info.get("endCursor")
            else:
                variables.pop("after", None)

    async def test_connection(self) -> StoreConnectionTest:
        query_name = "shop"
        query_info = QUERY_PAYLOAD_MAP.get(query_name)
        if not query_info:
            return StoreConnectionTest(success=False, message="Shop query info not found in QUERY_PAYLOAD_MAP.")
        
        return_type_name = query_info["return_type"].__name__ if hasattr(query_info["return_type"], '__name__') else query_info["return_type"]
        node_type_name = query_info["node_type"].__name__ if hasattr(query_info["node_type"], '__name__') else query_info["node_type"]

        query_string = self.gql_generator.generate_query_gql(query_name, return_type_name, node_type_name)
        result = await self._execute_graphql_query(query_string)
        if "errors" in result:
            return StoreConnectionTest(success=False, message=result["errors"][0]["message"])
        shop_data = result.get("shop", {})
        if not shop_data:
            return StoreConnectionTest(success=False, message="Could not retrieve shop details.")
        return StoreConnectionTest(
            success=True, message="Successfully connected to Shopify store.", store_info=shop_data
        )

    async def fetch_all_entities(self, db: Session) -> AsyncGenerator[Dict, None]:
        """Fetches all entities from Shopify and persists them to the local database, respecting dependencies."""
        import traceback
        from datetime import datetime
        from collections import defaultdict

        # 1. Build Dependency Graph
        # A -> B means A depends on B (B must be synced before A)
        dependency_graph = defaultdict(set)
        # Map lowercase GraphQL type names to their SQLAlchemy models and original GraphQL type names
        graphql_type_to_model_info = {
            graphql_class.lower(): {"model": db_model, "graphql_name": graphql_class}
            for graphql_class, db_model in GRAPHQL_TO_MODEL_MAP.items()
        }
        logger.debug(f"graphql_type_to_model_info: {graphql_type_to_model_info}")

        # Identify all top-level query names that correspond to syncable entities
        syncable_query_names = set()
        for query_name, query_info in QUERY_PAYLOAD_MAP.items():
            return_type_name = query_info.get("return_type")
            if return_type_name and (isinstance(return_type_name, str) and return_type_name.endswith("Connection") or (hasattr(return_type_name, '__name__') and return_type_name.__name__.endswith("Connection"))):
                # Extract singular entity name from connection type (e.g., Product from ProductConnection)
                entity_name_singular = return_type_name.__name__.replace("Connection", "")
                if entity_name_singular.lower() in graphql_type_to_model_info:
                    syncable_query_names.add(query_name)
        logger.debug(f"syncable_query_names: {syncable_query_names}")

        # Build graph based on SQLAlchemy relationships
        for graphql_type_lower, info in graphql_type_to_model_info.items():
            model_class = info["model"]
            logger.debug(f"DEBUG: Processing model_class: {model_class.name} (GraphQL type: {graphql_type_lower})")
            try:
                # Get relationships defined on the SQLAlchemy model
                relationships = inspect(model_class).relationships
                logger.debug(f"DEBUG: Relationships for {model_class.name}: {relationships}")
                for rel in relationships.values():
                    logger.debug(f"DEBUG: Processing relationship: {rel.key} (direction: {rel.direction.name})")
                    # Get the related SQLAlchemy model class
                    related_model_class = rel.mapper.class_
                    logger.debug(f"DEBUG: Related model class: {related_model_class.name}")

                    # Find the GraphQL type name corresponding to the related model
                    related_graphql_type_name = None
                    for gql_cls, db_mod in GRAPHQL_TO_MODEL_MAP.items():
                        logger.debug(f"DEBUG: Comparing db_mod.__name__: {db_mod.__name__} with related_model_class.__name__: {related_model_class.__name__}")
                        if db_mod.__name__ == related_model_class.__name__:
                            related_graphql_type_name = gql_cls
                            break
                    logger.debug(f"DEBUG: Related GraphQL type name: {related_graphql_type_name}")

                    if related_graphql_type_name:
                        # If the current model (graphql_type_lower) has a FK to related_graphql_type_name,
                        # then current model depends on related model.
                        # We need to find the query name for the related model.
                        related_query_name = None
                        for q_name, q_info in QUERY_PAYLOAD_MAP.items():
                            if (
                                q_info.get("return_type").__name__.replace("Connection", "").lower()
                                == related_graphql_type_name.lower()
                            ):
                                related_query_name = q_name
                                break
                        logger.debug(f"DEBUG: Related query name: {related_query_name}")

                        current_query_name = None
                        for q_name, q_info in QUERY_PAYLOAD_MAP.items():
                            q_return_type_name = q_info["return_type"].__name__ if hasattr(q_info["return_type"], '__name__') else q_info["return_type"]
                            logger.debug(f"DEBUG: Comparing q_return_type_name: {q_return_type_name.replace("Connection", "").lower()} with graphql_type_lower: {graphql_type_lower}")
                            if q_return_type_name.replace("Connection", "").lower() == graphql_type_lower:
                                current_query_name = q_name
                                break
                        logger.debug(f"DEBUG: Determined current_query_name: {current_query_name}")
                        logger.debug(f"DEBUG: Current query name: {current_query_name}")

                        if current_query_name and related_query_name and current_query_name != related_query_name:
                            dependency_graph[current_query_name].add(related_query_name)
                            logger.debug(f"DEBUG: Added dependency: {current_query_name} -> {related_query_name}")
            except Exception as e:
                logger.warning(f"Could not inspect relationships for {model_class.name}: {e}")
                logger.debug(f"DEBUG: Exception during relationship inspection: {traceback.format_exc()}")
                continue
        logger.debug(f"dependency_graph: {dependency_graph}")

        # 2. Topological Sort (Kahn's algorithm)
        in_degree = defaultdict(int)
        # Initialize in-degrees for all syncable entities to 0
        for entity_name in syncable_query_names:
            in_degree[entity_name] = 0

        # Populate in-degrees based on the dependency graph
        # If A depends on B (A -> B in our graph), then B is a prerequisite for A.
        # So, there's an implicit edge from B to A.
        # We need to count incoming edges for each node.
        for dependent_entity, prerequisites in dependency_graph.items():
            for prerequisite_entity in prerequisites:
                in_degree[dependent_entity] += 1 # Increment in-degree of the dependent entity
        logger.debug(f"in_degree: {in_degree}")

        queue = [node for node in syncable_query_names if in_degree[node] == 0]
        logger.debug(f"Final queue before check: {queue}")
        if not queue:
            # Handle circular dependencies or no starting nodes
            logger.error("Circular dependency detected or no independent entities to start sync.")
            yield {
                "entity": "system",
                "status": "error",
                "error": "Circular dependency detected or no independent entities to start sync.",
                "finished_at": datetime.utcnow().isoformat(),
            }
            return

        sorted_entities = []
        while queue:
            node = queue.pop(0)
            sorted_entities.append(node)

            # Find nodes that depend on the current node
            for dependent_node in list(dependency_graph.keys()):  # Iterate over a copy
                if node in dependency_graph[dependent_node]:
                    dependency_graph[dependent_node].remove(node)
                    in_degree[dependent_node] -= 1
                    if in_degree[dependent_node] == 0:
                        queue.append(dependent_node)
        logger.info(f"Sync order: {sorted_entities}")
        if len(sorted_entities) != len(syncable_query_names):
            logger.error("Topological sort failed: not all entities were included. Possible circular dependency.")
            yield {
                "entity": "system",
                "status": "error",
                "error": "Topological sort failed. Possible circular dependency.",
                "finished_at": datetime.utcnow().isoformat(),
            }
            return


        # 3. Iterate and Sync
        for query_name in sorted_entities:
            status = {
                "entity": query_name,
                "started_at": datetime.utcnow().isoformat(),
                "status": "started",
                "error": None,
                "result": None,
            }
            yield status.copy()

            total_synced = 0
            next_cursor = None
            has_next_page = True
            limit = 50  # Default page size

            try:
                # Get the singular entity name from the query name (e.g., 'products' -> 'product')
                entity_name_singular = query_name[:-1] if query_name.endswith("s") else query_name
                model_info = graphql_type_to_model_info.get(entity_name_singular.lower())

                if not model_info:
                    logger.warning(
                        f"No model info found for entity '{entity_name_singular}'. Skipping sync for '{query_name}'."
                    )
                    status["status"] = "skipped"
                    status["result"] = f"Skipped: No model info found for entity '{entity_name_singular}'."
                    status["finished_at"] = datetime.utcnow().isoformat()
                    yield status
                    continue

                model = model_info["model"]

                while has_next_page:
                    variables = {"first": limit}
                    if next_cursor:
                        variables["after"] = next_cursor

                    # Execute the query using the generic execute_query method
                    query_result = await self.execute_query(query_name, variables=variables)

                    # Navigate the result to find edges and pageInfo
                    # query_result already contains the data for the specific entity (e.g., {'edges': [...], 'pageInfo': {...}})
                    data_path = query_result
                    edges = data_path.get("edges", [])
                    page_info = data_path.get("pageInfo", {})

                    for edge in edges:
                        node_data = edge.get("node", {})
                        external_id = node_data.get("id")
                        if external_id:
                            # Use _get_or_create to save/update in local DB
                            # Need to convert GraphQL camelCase to snake_case for SQLAlchemy model
                            snake_case_data = {to_snake_case(k): v for k, v in node_data.items()}

                            # Filter out keys that are not columns in the model
                            # This is crucial because our simplified field selection might still bring in
                            # fields that are not directly mapped to columns (e.g., nested IDs that are not FKs)
                            model_columns = {c.name for c in inspect(model).columns if hasattr(c, 'name')}
                            filtered_snake_case_data = {k: v for k, v in snake_case_data.items() if k in model_columns}

                            # Handle relationships: For direct object relationships (like customer_id on Order),
                            # the GraphQL response will have 'customer': {'id': 'gid://shopify/Customer/123'}.
                            # We need to extract the ID and map it to the FK column.
                            for rel in inspect(model).relationships.values():
                                if rel.direction.name == "MANYTOONE":  # This is a foreign key relationship
                                    related_graphql_type_name = rel.mapper.class_.__name__.replace(
                                        "Shopify", ""
                                    ).lower()
                                    fk_column_name = rel.local_remote_pairs[0][0].name  # Get the local FK column name

                                    # Check if the related object data is present in the fetched node_data
                                    if (
                                        related_graphql_type_name in node_data
                                        and node_data[related_graphql_type_name]
                                        and "id" in node_data[related_graphql_type_name]
                                    ):
                                        filtered_snake_case_data[fk_column_name] = node_data[related_graphql_type_name][
                                            "id"
                                        ]

                                    # Remove the object key from filtered_snake_case_data to avoid passing non-column data
                                    if related_graphql_type_name in filtered_snake_case_data:
                                        del filtered_snake_case_data[related_graphql_type_name]

                            self._get_or_create(db, model, external_id=external_id, **filtered_snake_case_data)
                            total_synced += 1

                    db.commit()

                    has_next_page = page_info.get("hasNextPage", False)
                    next_cursor = page_info.get("endCursor")

                status["status"] = "success"
                status["result"] = f"Fetched and synced {total_synced} {query_name}."

            except Exception as e:
                db.rollback()  # Rollback any partial transactions on error
                status["status"] = "error"
                status["error"] = str(e)
                status["traceback"] = traceback.format_exc()
            status["finished_at"] = datetime.utcnow().isoformat()
            yield status