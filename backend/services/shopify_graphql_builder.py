import json
import logging
import logging.handlers
import re
import keyword
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Type

from sgqlc.operation import Operation
import sgqlc.types

# Import the generated Shopify sgqlc schema and mappings
from schemas.shopify_sgqlc_schema import QueryRoot as Query, Mutation
from schemas.shopify_mappings import MUTATION_PAYLOAD_MAP, QUERY_PAYLOAD_MAP
from schemas import shopify_sgqlc_schema

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG) # Set to DEBUG for more verbosity

# Define paths (relative to this file for introspection data)
BASE_DIR = Path(__file__).resolve().parent.parent
INTROSPECTION_FILE = BASE_DIR / "shopify" / "shopify_introspection.json"

RESERVED_KEYWORDS = set(keyword.kwlist)

KNOWN_SCALAR_TYPES = {"ID", "String", "Int", "Boolean", "Float", "DateTime", "Decimal", "Url", "Date", "JSON", "HTML"}

def to_snake_case(name):
    name = re.sub(r'(?<!^)(?=[A-Z])', '_', name).lower()
    if name in RESERVED_KEYWORDS:
        return f"{name}_obj"
    return name

def to_camel_case(snake_str):
    components = snake_str.split('_')
    return components[0] + ''.join(x.title() for x in components[1:])

def load_introspection_data():
    try:
        with open(INTROSPECTION_FILE, "r") as f:
            introspection_data = json.load(f)
        return introspection_data
    except FileNotFoundError:
        logger.error(f"Error: Introspection file not found at {INTROSPECTION_FILE}")
        return None
    except json.JSONDecodeError:
        logger.error(f"Error: Could not decode JSON from {INTROSPECTION_FILE}")
        return None

class GQLGenerator:
    def __init__(self, introspection_data):
        self.types_map = self._get_graphql_types(introspection_data)
        self.types_with_dedicated_mutations = self._precompute_types_with_dedicated_mutations()
        self.types_with_dedicated_queries = self._precompute_types_with_dedicated_queries()
        self.query_stats = {}
        self.mutation_stats = {}

    def _get_graphql_types(self, introspection_data):
        types = {t["name"]: t for t in introspection_data["data"]["__schema"]["types"]}
        return types

    def _get_base_graphql_type_name(self, graphql_type_ref: Dict[str, Any]) -> str:
        current_type = graphql_type_ref
        while current_type and current_type.get("ofType"):
            current_type = current_type["ofType"]
        return current_type["name"] if current_type else "Unknown" # Return "Unknown" or handle as appropriate

    def _get_type_from_mutation_name(self, mutation_name: str, payload_type_name: str) -> Optional[str]:
        # Derive the entity type directly from the payload type name
        # e.g., CustomerCreatePayload -> Customer
        if payload_type_name.endswith("Payload"):
            entity_name = payload_type_name[:-7] # Remove "Payload"
            if entity_name:
                return entity_name
        return None # If it doesn't fit the Payload pattern, we can't reliably derive it this way.

    def _precompute_types_with_dedicated_mutations(self) -> Set[str]:
        types_with_mutations = set()
        for mutation_name, details in MUTATION_PAYLOAD_MAP.items():
            payload_type_name = details["payload_type"].__name__ if hasattr(details["payload_type"], '__name__') else details["payload_type"]
            
            entity_type = self._get_type_from_mutation_name(mutation_name, payload_type_name)
            if entity_type:
                types_with_mutations.add(entity_type)
            
        return types_with_mutations

    def _precompute_types_with_dedicated_queries(self) -> Set[str]:
        types_with_queries = set()
        for query_name, details in QUERY_PAYLOAD_MAP.items():
            return_type_name = details["return_type"].__name__ if hasattr(details["return_type"], '__name__') else details["return_type"]
            node_type_name = details["node_type"].__name__ if hasattr(details["node_type"], '__name__') else details["node_type"]

            if return_type_name:
                types_with_queries.add(return_type_name)
            if node_type_name:
                types_with_queries.add(node_type_name)
        return types_with_queries

    def _get_query_field_args(self, query_name: str) -> Dict[str, Any]:
        logger.debug(f"_get_query_field_args: Processing query_name: {query_name}")
        query_root_type = self.types_map.get("QueryRoot")
        if not query_root_type:
            logger.debug("QueryRoot type not found in introspection data.")
            return {}
        
        for field in query_root_type.get("fields", []):
            if field["name"] == query_name:
                args = {}
                for arg in field.get("args", []):
                    arg_name = arg["name"]
                    arg_type_ref = arg["type"]
                    is_non_null = False
                    is_list = False
                    current_arg_type = arg_type_ref
                    while current_arg_type.get("ofType"):
                        if current_arg_type["kind"] == "NON_NULL":
                            is_non_null = True
                        elif current_arg_type["kind"] == "LIST":
                            is_list = True
                        current_arg_type = current_arg_type["ofType"]
                    base_arg_type_name = current_arg_type["name"]
                    args[arg_name] = {"type": base_arg_type_name, "non_null": is_non_null, "is_list": is_list}
                logger.debug(f"_get_query_field_args: Found args for '{query_name}': {args}")
                return args
        logger.debug(f"_get_query_field_args: No args found for '{query_name}'.")
        return {}

    def _is_list_of_non_scalars(self, graphql_type_ref: Dict[str, Any]) -> bool:
        current_type = graphql_type_ref
        is_list = False
        while current_type.get("ofType"):
            if current_type["kind"] == "LIST":
                is_list = True
            current_type = current_type["ofType"]

        if is_list:
            base_type_name = current_type["name"]
            base_type_def = self.types_map.get(base_type_name)
            if base_type_def and base_type_def["kind"] not in ["SCALAR", "ENUM"]:
                return True
        return False

    def _add_dynamic_scalar_fields(self, sgqlc_object_instance: Any, graphql_type_name: str, visited_types: Optional[Set[str]] = None, parent_field_name: str = "Root", current_mutation_entity_type: Optional[str] = None, stats_collector: Dict[str, Any] = None):
        logger.debug(f"_add_dynamic_scalar_fields: Processing type: {graphql_type_name}, Kind: {self.types_map.get(graphql_type_name, {}).get('kind')}, Instance type: {type(sgqlc_object_instance)}, Parent Field: {parent_field_name}")
        if visited_types is None:
            visited_types = set()

        if stats_collector is None:
            stats_collector = {"included": 0, "skipped": 0, "attribute_errors": 0, "sgqlc_class_not_found": 0, "skipped_inline_fragment_no_on_method": 0}

        if graphql_type_name in visited_types:
            logger.debug(f"Detected recursion for type '{graphql_type_name}' (parent: {parent_field_name}). Stopping traversal.")
            return

        visited_types.add(graphql_type_name)
        
        graphql_type_def = self.types_map.get(graphql_type_name)

        if not graphql_type_def:
            logger.debug(f"GraphQL type '{graphql_type_name}' not found in introspection data. Skipping field selection.")
            return

        kind = graphql_type_def["kind"]
        logger.debug(f"Processing type: {graphql_type_name}, Kind: {kind}, Instance type: {type(sgqlc_object_instance)}, Parent Field: {parent_field_name}")
        
        if kind in ["SCALAR", "ENUM"]:
            # Base case: select scalar or enum fields
            if hasattr(sgqlc_object_instance, '__call__'):
                stats_collector["included"] += 1
                sgqlc_object_instance()
            return

        if kind == "OBJECT" or kind == "INTERFACE":
            if kind == "INTERFACE":
                if hasattr(sgqlc_object_instance, '__typename__'):
                    stats_collector["included"] += 1
                    sgqlc_object_instance.__typename__()

            for field in graphql_type_def.get("fields", []):
                field_name = field["name"]
                field_type_ref = field["type"]
                base_type_name = self._get_base_graphql_type_name(field_type_ref)
                field_name_snake_case = to_snake_case(field_name)

                logger.debug(f"  Processing field: {field_name_snake_case} (GraphQL: {field_name}), Base Type: {base_type_name}, Parent Type: {graphql_type_name}")

                if not hasattr(sgqlc_object_instance, field_name_snake_case):
                    logger.debug(f"    Instance does not have field: {field_name_snake_case}. Skipping.")
                    stats_collector["attribute_errors"] += 1
                    continue
                field_sgqlc_instance = getattr(sgqlc_object_instance, field_name_snake_case)
                logger.debug(f"    Field sgqlc instance type: {type(field_sgqlc_instance)}")
                
                field_type_def = self.types_map.get(base_type_name)

                if base_type_name in KNOWN_SCALAR_TYPES:
                    logger.debug(f"      Selecting known scalar field: {field_name_snake_case} (Type: {base_type_name})")
                    stats_collector["included"] += 1
                    field_sgqlc_instance()
                    continue

                if not field_type_def:
                    logger.debug(f"Field type '{base_type_name}' not found for field '{field_name}'. Skipping.")
                    continue

                field_kind = field_type_def["kind"]
                logger.debug(f"    Field kind: {field_kind}")

                if field_name_snake_case == "metafield":
                    logger.debug(f"      Skipping 'metafield' field as requested.")
                    stats_collector["skipped"] += 1
                    continue
                if field_kind in ["SCALAR", "ENUM"]:
                    logger.debug(f"      Selecting scalar/enum field: {field_name_snake_case}")
                    stats_collector["included"] += 1
                    field_sgqlc_instance()
                elif base_type_name.endswith(("Set", "V2")) and "amount" in field_type_def.get("fields", []):
                    logger.debug(f"      Handling special type (Set/V2) with amount: {field_name_snake_case}")
                    if hasattr(field_sgqlc_instance, "amount"):
                        stats_collector["included"] += 1
                        field_sgqlc_instance.amount()
                    if hasattr(field_sgqlc_instance, "currency_code"):
                        stats_collector["included"] += 1
                        field_sgqlc_instance.currency_code()
                elif base_type_name == "ShopPlan":
                    logger.debug(f"      Handling special type (ShopPlan): {field_name_snake_case}")
                    if hasattr(field_sgqlc_instance, "display_name"):
                        stats_collector["included"] += 1
                        field_sgqlc_instance.display_name()
                elif base_type_name == "Image":
                    logger.debug(f"      Handling special type (Image): {field_name_snake_case}")
                    if field_type_ref["kind"] != "LIST" and hasattr(field_sgqlc_instance, "src"):
                        stats_collector["included"] += 1
                        field_sgqlc_instance.src()
                elif field_kind in ["OBJECT", "INTERFACE", "UNION"]:
                    logger.debug(f"      DEBUG: Checking field '{field_name_snake_case}' (Base Type: {base_type_name}). In types_with_dedicated_queries: {base_type_name in self.types_with_dedicated_queries}")
                    if (base_type_name != current_mutation_entity_type and base_type_name in self.types_with_dedicated_mutations) or \
                       (base_type_name in self.types_with_dedicated_queries):
                        logger.debug(f"      Skipping field '{field_name_snake_case}' (GraphQL: {field_name}, Base Type: {base_type_name}) because it has dedicated mutations or queries.")
                        stats_collector["skipped"] += 1
                        continue

                    if self._is_list_of_non_scalars(field_type_ref) or "Connection" in base_type_name or "Edge" in base_type_name:
                        logger.debug(f"      Skipping connection/list of non-scalar field: {field_name_snake_case} (GraphQL: {field_name}, Base Type: {base_type_name}).")
                        stats_collector["skipped"] += 1
                        continue
                    else:
                        logger.debug(f"      Recursing into object/interface/union field: {field_name_snake_case}")
                        if field_kind == "UNION" or field_kind == "INTERFACE": # Handle both UNION and INTERFACE with possibleTypes
                            if hasattr(field_sgqlc_instance, '__typename__'):
                                stats_collector["included"] += 1 # For __typename__
                                field_sgqlc_instance.__typename__()
                            for possible_type in field_type_def.get("possibleTypes", []):
                                        possible_type_name = possible_type["name"]
                                        sgqlc_possible_type_class = getattr(shopify_sgqlc_schema, possible_type_name, None)
                                        if sgqlc_possible_type_class:
                                            if isinstance(field_sgqlc_instance, sgqlc.operation.Selection) and \
                                               self.types_map.get(base_type_name, {}).get("kind") in ["UNION", "INTERFACE"] and \
                                               hasattr(field_sgqlc_instance, 'on'):
                                                try:
                                                    self._add_dynamic_scalar_fields(field_sgqlc_instance.on(sgqlc_possible_type_class), possible_type_name, visited_types, parent_field_name=field_name_snake_case, current_mutation_entity_type=current_mutation_entity_type, stats_collector=stats_collector)
                                                except AttributeError:
                                                    logger.debug(f"Skipping inline fragment for field '{field_name_snake_case}' (GraphQL: {field_name}, Base Type: {base_type_name}) because it does not have 'on' method for possible type '{possible_type_name}'.")
                                                    stats_collector["skipped_inline_fragment_no_on_method"] += 1
                                            else:
                                                logger.debug(f"Skipping inline fragment for field '{field_name_snake_case}' (GraphQL: {field_name}, Base Type: {base_type_name}) because it's not a valid selector for possible type '{possible_type_name}'.")
                                                stats_collector["skipped_inline_fragment_no_on_method"] += 1
                                        else:
                                            logger.debug(f"sgqlc class for possible type '{possible_type_name}' not found. Skipping inline fragment.")
                                            stats_collector["sgqlc_class_not_found"] += 1
                        else:
                            self._add_dynamic_scalar_fields(field_sgqlc_instance, base_type_name, visited_types, parent_field_name=field_name_snake_case, current_mutation_entity_type=current_mutation_entity_type, stats_collector=stats_collector)
                else:
                    logger.debug(f"    Field kind '{field_kind}' not explicitly handled. Selecting field '{field_name_snake_case}'.")
                    stats_collector["included"] += 1
                    field_sgqlc_instance()
        elif kind == "UNION":
            # New logic: Skip if it's a type with its own dedicated mutations AND not the primary entity of the current mutation
            if graphql_type_name != current_mutation_entity_type and graphql_type_name in self.types_with_dedicated_mutations:
                logger.debug(f"Handling top-level UNION: {graphql_type_name}, Instance type: {type(sgqlc_object_instance)}, Parent Field: {parent_field_name}. Skipping because it has dedicated mutations.")
                stats_collector["skipped"] += 1
                return
            
            logger.debug(f"Handling top-level UNION: {graphql_type_name}, Instance type: {type(sgqlc_object_instance)}, Parent Field: {parent_field_name}")
            if hasattr(sgqlc_object_instance, '__typename__'):
                stats_collector["included"] += 1
                sgqlc_object_instance.__typename__()
            
            for possible_type in graphql_type_def.get("possibleTypes", []):
                possible_type_name = possible_type["name"]
                logger.debug(f"  Possible type for top-level union: {possible_type_name}")
                sgqlc_possible_type_class = getattr(shopify_sgqlc_schema, possible_type_name, None)
                if sgqlc_possible_type_class:
                    if isinstance(sgqlc_object_instance, sgqlc.operation.Selection) and self.types_map.get(graphql_type_name, {}).get("kind") == "UNION" and hasattr(sgqlc_object_instance, 'on'):
                        logger.debug(f"  sgqlc_possible_type_class: {sgqlc_possible_type_class}")
                        self._add_dynamic_scalar_fields(sgqlc_object_instance.on(sgqlc_possible_type_class), possible_type_name, visited_types, parent_field_name=graphql_type_name, current_mutation_entity_type=current_mutation_entity_type, stats_collector=stats_collector)
                    else:
                        logger.debug(f"Skipping inline fragment for top-level union field '{graphql_type_name}' because it's not a union selector or does not have 'on' method. Possible type: '{possible_type_name}'.")
                        stats_collector["skipped_inline_fragment_no_on_method"] += 1
                else:
                    logger.debug(f"sgqlc class for possible type '{possible_type_name}' not found. Skipping top-level inline fragment.")
                    stats_collector["sgqlc_class_not_found"] += 1
        else:
            logger.debug(f"GraphQL type '{graphql_type_name}' is of kind '{kind}'. Selecting field '{graphql_type_name}'.")
            stats_collector["included"] += 1
            sgqlc_object_instance()

    def generate_query_gql(self, query_name: str, return_type_name: str, node_type_name: Optional[str], variables: Dict[str, Any] = None) -> str:
        stats = {"included": 0, "skipped": 0, "attribute_errors": 0, "sgqlc_class_not_found": 0, "skipped_inline_fragment_no_on_method": 0}
        logger.debug(f"DEBUG: Generating query GQL for {query_name}, return_type_name: {return_type_name}, node_type_name: {node_type_name}")
        
        query_args_meta = self._get_query_field_args(query_name)
        
        defined_variables = {}
        if variables:
            for arg_name, arg_meta in query_args_meta.items():
                if arg_name in variables:
                    sgqlc_type_name = arg_meta["type"]
                    sgqlc_type = getattr(shopify_sgqlc_schema, sgqlc_type_name, None)
                    
                    if sgqlc_type is None:
                        if sgqlc_type_name == "String":
                            sgqlc_type = sgqlc.types.String
                        elif sgqlc_type_name == "Int":
                            sgqlc_type = sgqlc.types.Int
                        elif sgqlc_type_name == "ID":
                            sgqlc_type = sgqlc.types.ID
                        elif sgqlc_type_name == "Boolean":
                            sgqlc_type = sgqlc.types.Boolean
                        elif sgqlc_type_name == "Float":
                            sgqlc_type = sgqlc.types.Float
                        else:
                            logger.warning(f"Could not find sgqlc type for '{sgqlc_type_name}'. Defaulting to String.")
                            sgqlc_type = sgqlc.types.String
                    
                    if arg_meta["non_null"]:
                        sgqlc_type = sgqlc.types.non_null(sgqlc_type)
                    
                    defined_variables[arg_name] = sgqlc.types.Arg(sgqlc_type)

        op = Operation(Query, variables=defined_variables)
        
        call_args = {}
        if variables:
            for arg_name, arg_meta in query_args_meta.items():
                if arg_name in variables:
                    call_args[arg_name] = sgqlc.types.Variable(arg_name)
                else:
                    logger.debug(f"Variable '{arg_name}' defined in schema but not provided in input variables. Skipping variable definition for this argument.")

        try:
            if query_name == "return":
                query_func = getattr(op, "return_")
            else:
                query_func = getattr(op, to_snake_case(query_name))
            
            query_field = query_func(**call_args)

            if node_type_name:
                self._add_dynamic_scalar_fields(query_field.edges.node, node_type_name, visited_types=set(), current_mutation_entity_type=None, stats_collector=stats)
                stats["included"] += 2
                query_field.page_info.has_next_page()
                query_field.page_info.end_cursor()
            else: # This indicates a singular query (e.g., 'customer', 'shop')
                self._add_dynamic_scalar_fields(query_field, return_type_name, visited_types=set(), current_mutation_entity_type=None, stats_collector=stats)
            
            self.query_stats[query_name] = stats
            return str(op), stats
        except AttributeError as e:
            logger.error(f"AttributeError for query '{query_name}': {e}. This query might not be directly accessible as a field on the Query object or requires specific arguments.")
            stats["attribute_errors"] += 1
            self.query_stats[query_name] = stats
            raise
        except Exception as e:
            logger.error(f"An unexpected error occurred while generating GQL for query '{query_name}': {e}")
            self.query_stats[query_name] = stats
            raise

    def generate_mutation_gql(self, mutation_name: str, payload_type_name: str, input_type_name: Optional[str], input_data: Dict[str, Any] = None) -> str:
        stats = {"included": 0, "skipped": 0, "attribute_errors": 0, "sgqlc_class_not_found": 0, "skipped_inline_fragment_no_on_method": 0}
        op = Operation(Mutation)
        try:
            mutation_func = getattr(op, to_snake_case(mutation_name))
            
            if 'input' in mutation_func.__field__.args:
                mutation_field = mutation_func(input=input_data if input_data is not None else {})
            else:
                mutation_field = mutation_func()
            
            logger.debug(f"  Mutation field instance: {mutation_field}, type: {type(mutation_field)}")


            current_mutation_entity_type = self._get_type_from_mutation_name(mutation_name, payload_type_name)
            logger.debug(f"  Current mutation primary entity type: {current_mutation_entity_type}")


            payload_type_def = self.types_map.get(payload_type_name)
            if payload_type_def and "fields" in payload_type_def:
                user_errors_field_def = next((f for f in payload_type_def["fields"] if f["name"] == "userErrors"), None)
                if user_errors_field_def:
                    user_error_base_type_name = self._get_base_graphql_type_name(user_errors_field_def["type"])
                    if hasattr(mutation_field, "user_errors"):
                        self._add_dynamic_scalar_fields(getattr(mutation_field, "user_errors"), user_error_base_type_name, visited_types=set(), parent_field_name="userErrors", current_mutation_entity_type=current_mutation_entity_type, stats_collector=stats)
                    else:
                        logger.debug(f"Mutation payload '{payload_type_name}' has userErrors field in schema, but sgqlc object does not have 'user_errors' attribute.")
                        stats["attribute_errors"] += 1
            
            self.mutation_stats[mutation_name] = stats
            return str(op), stats
        except AttributeError as e:
            logger.error(f"AttributeError for mutation '{mutation_name}': {e}. This mutation might not be directly accessible as a field on the Mutation object or requires specific arguments.")
            stats["attribute_errors"] += 1
            self.mutation_stats[mutation_name] = stats
            raise
        except Exception as e:
            logger.error(f"An unexpected error occurred while generating GQL for mutation '{mutation_name}': {e}")
            self.mutation_stats[mutation_name] = stats
            raise
