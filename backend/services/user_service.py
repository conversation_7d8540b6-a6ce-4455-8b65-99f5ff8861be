import uuid

from passlib.context import <PERSON><PERSON><PERSON>ontext
from sqlalchemy.orm import Session

from models.models import User

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def get_password_hash(password):
    return pwd_context.hash(password)


def sync_shopify_customers(db: Session, customers: list):
    added = 0
    updated = 0
    unchanged = 0
    for customer_data in customers:
        customer_node = customer_data.get("node", {})
        if not customer_node.get("email"):
            continue

        user = db.query(User).filter(User.email == customer_node["email"]).first()
        full_name = f"{customer_node.get('firstName', '')} {customer_node.get('lastName', '')}".strip()

        if not user:
            # Create a new user
            raw_password = str(uuid.uuid4())
            hashed_password = get_password_hash(raw_password)

            new_user = User(
                email=customer_node["email"],
                hashed_password=hashed_password,
                full_name=full_name,
                is_active=True,
            )
            db.add(new_user)
            added += 1
        else:
            # Check for updates
            if user.full_name != full_name:
                user.full_name = full_name
                updated += 1
            else:
                unchanged += 1

    db.commit()
    return added, updated, unchanged
