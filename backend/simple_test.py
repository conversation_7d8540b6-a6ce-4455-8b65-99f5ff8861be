#!/usr/bin/env python3
"""
Simple test to verify agent API components are working.
"""

import os
import sys
from pathlib import Path

# Add the src directory to the Python path
backend_dir = Path(__file__).parent
src_dir = backend_dir / "src"
sys.path.insert(0, str(src_dir))


def test_basic_imports():
    """Test basic imports."""
    print("Testing basic imports...")

    try:
        # Test schemas
        from modules.agents.schemas import Agent, AgentCreate, NodeType, WorkflowData

        print("✅ Schemas imported successfully")

        # Test basic schema creation
        agent_create = AgentCreate(
            name="Test Agent", description="Test description", is_active=True
        )
        print("✅ AgentCreate schema works")

        # Test enum
        node_type = NodeType.START
        print("✅ NodeType enum works")

        return True

    except Exception as e:
        print(f"❌ Import error: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_router_structure():
    """Test router structure."""
    print("\nTesting router structure...")

    try:
        from modules.agents.router import router

        print("✅ Router imported successfully")

        # Check routes
        routes = [route.path for route in router.routes if hasattr(route, "path")]
        print(f"✅ Found {len(routes)} routes")

        # Check for key routes
        key_routes = ["/agents/", "/agents/{agent_id}"]
        found_routes = []
        for key_route in key_routes:
            if any(key_route in route for route in routes):
                found_routes.append(key_route)

        print(f"✅ Found {len(found_routes)}/{len(key_routes)} key routes")

        return True

    except Exception as e:
        print(f"❌ Router error: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """Run tests."""
    print("🧪 Simple Agent API Test")
    print("=" * 40)

    tests = [test_basic_imports, test_router_structure]

    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed: {e}")
            results.append(False)

    print("\n" + "=" * 40)
    print(f"Results: {sum(results)}/{len(results)} tests passed")

    if all(results):
        print("🎉 All basic tests passed!")
        return 0
    else:
        print("⚠️  Some tests failed.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
