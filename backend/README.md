# E-commerce Integration API

**Last Updated:** July 31, 2025

## 1. Overview

This project is a FastAPI-based backend designed to integrate with various e-commerce platforms like Shopify and WooCommerce. It provides a unified API to manage stores, products, orders, and customers, and also includes a sales forecasting feature.

## 2. Features

*   **Multi-platform Support**: Easily connect to and manage multiple e-commerce stores from different platforms.
*   **Unified API**: A single, consistent API for all supported platforms.
*   **Data Synchronization**: Sync products, orders, and customers from your stores to a local database.
*   **Sales Forecasting**: Predict future sales using historical data.
*   **Authentication**: Secure API with JWT-based authentication.
*   **Webhook Support**: Real-time updates from e-commerce platforms.

## 3. Technologies Used

*   **Backend**: Python, FastAPI
*   **Database**: PostgreSQL, SQLAlchemy
*   **Authentication**: JWT (JSON Web Tokens)
*   **Forecasting**: Prophet, Darts
*   **Testing**: Pytest
*   **Dependency Management**: `uv`

## 4. Prerequisites

*   Python 3.11+
*   PostgreSQL
*   `uv` (for dependency management)

## 5. Installation and Setup

1.  **Clone the repository**:
    ```bash
    git clone <repository-url>
    cd <repository-directory>
    ```

2.  **Create a virtual environment**:
    ```bash
    python -m venv .venv
    source .venv/bin/activate
    ```

3.  **Install dependencies**:
    ```bash
    uv pip install -e ".[all]"
    ```

4.  **Set up the database**:
    *   Create a PostgreSQL database.
    *   Copy the `.env.example` file to `.env` and update the `DATABASE_URL` with your database connection string.

5.  **Run database migrations**:
    ```bash
    alembic upgrade head
    ```

## 6. Running the Application

To start the development server, run the following command:

```bash
uvicorn main:app --reload
```

The API will be available at `http://localhost:8000`.

## 7. Running Tests

To run the test suite, use the following command:

```bash
TESTING=True .venv/bin/python -m pytest
```

## 8. API Endpoints

Here is a brief overview of the available API endpoints:

*   `/api/auth`: User registration and login.
*   `/api/stores`: Manage your connected stores.
*   `/api/products`: View and manage products.
*   `/api/orders`: View and manage orders.
*   `/api/users`: Manage users.
*   `/api/consumers`: View store customers.
*   `/api/shopify`: Shopify-specific endpoints.
*   `/api/webhooks`: Webhook endpoints for real-time updates.

For more details, you can access the interactive API documentation at `http://localhost:8000/docs`.

## 9. Project Structure

```
.
├── alembic/              # Database migration scripts
├── database/             # Database-related files
├── migrations/           # Alembic migration versions
├── routers/              # API endpoint definitions
├── services/             # Business logic and external API integrations
├── tests/                # Test suite
├── .env                  # Environment variables
├── alembic.ini           # Alembic configuration
├── config.py             # Application configuration
├── database.py           # Database session management
├── main.py               # FastAPI application entry point
├── models.py             # SQLAlchemy database models
├── pyproject.toml        # Project metadata and dependencies
└── schemas.py            # Pydantic data validation schemas
```
