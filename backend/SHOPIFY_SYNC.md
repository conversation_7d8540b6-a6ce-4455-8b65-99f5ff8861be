# Shopify Data Synchronization Process

This document outlines the end-to-end process for synchronizing data from a Shopify store into the local backend database. It covers fetching the Shopify GraphQL schema, generating Python code from it, and integrating this generated code into the backend services and API routers to handle data fetching, updates, and synchronization logic.

## 1. Prerequisites

Before proceeding with the synchronization setup, ensure you have the following tools and access:

*   **Node.js & npm**: Required for executing the JavaScript-based schema fetching and conversion scripts.
*   **Python 3.11+**: The primary language for the backend application.
*   **`uv`**: A fast Python package installer and resolver, used for managing Python dependencies.
*   **Shopify Store Credentials**:
    *   Shopify Store Domain (e.g., `your-shop.myshopify.com`)
    *   Shopify Admin API Access Token (Private App or Custom App token with necessary permissions)
    *   Shopify Admin API Secret Key (for webhook verification)
    *   Shopify Storefront API Access Token (if storefront operations are needed)
*   **Installed Python Libraries**:
    *   `datamodel-code-generator[graphql]`
    *   `sgqlc[requests]`
    *   `httpx`
    *   `SQLAlchemy`
    *   `FastAPI`

## 2. Shopify GraphQL Schema Management (JavaScript/Node.js)

The Shopify GraphQL schema is dynamic and can change with API version updates or app installations. It's crucial to keep your local schema definition up-to-date. This project uses Node.js scripts located in the `shopify/` directory for this purpose.

### 2.1. Fetching Introspection JSON

The `shopify/get_introspection.js` script performs a GraphQL introspection query against your Shopify Admin API endpoint. This query asks the Shopify server to describe its entire schema, and the response is saved as a large JSON file.

**Purpose**: To obtain the complete, machine-readable definition of the Shopify GraphQL API. This JSON is the canonical source for code generation.

**Execution Steps**:

1.  **Navigate to the `shopify` directory**:
    ```bash
    cd /home/<USER>/Projects/leanchain/e-commerce/backend/shopify
    ```
2.  **Install Node.js dependencies**:
    ```bash
    npm install
    ```
3.  **Update `get_introspection.js`**: Open `shopify/get_introspection.js` and ensure the `endpoint` and `accessToken` variables are correctly set for your Shopify store and Admin API access token.
    ```javascript
    const endpoint = 'https://YOUR_SHOP_DOMAIN/admin/api/YOUR_API_VERSION/graphql.json';
    const accessToken = 'YOUR_ADMIN_ACCESS_TOKEN';
    ```
4.  **Run the script**:
    ```bash
    node get_introspection.js
    ```

**Result**: A file named `shopify/shopify_introspection.json` will be created or updated in the `shopify/` directory. This file contains the full GraphQL schema in JSON format.

### 2.2. Converting Introspection JSON to GraphQL SDL

The `shopify/convert_schema.js` script takes the `shopify_introspection.json` file and converts it into a human-readable GraphQL Schema Definition Language (SDL) file.

**Purpose**: To provide a human-readable version of the schema (`.graphql` file) which can be useful for manual inspection and is also a common input format for some code generation tools.

**Execution Steps**:

1.  **Ensure you are in the `shopify` directory**:
    ```bash
    cd /home/<USER>/Projects/leanchain/e-commerce/backend/shopify
    ```
2.  **Run the script**:
    ```bash
    node convert_schema.js
    ```

**Result**: A file named `shopify/shopify_schema.graphql` will be created or updated in the `shopify/` directory.

### 2.3. Combined Schema Fetching and Conversion

For convenience, the `package.json` in the `shopify/` directory includes a script to run both steps sequentially:

**Execution Steps**:

1.  **Navigate to the `shopify` directory**:
    ```bash
    cd /home/<USER>/Projects/leanchain/e-commerce/backend/shopify
    ```
2.  **Run the combined script**:
    ```bash
    npm run get-schema
    ```

**Result**: Both `shopify_introspection.json` and `shopify_schema.graphql` will be updated.

## 3. Python Code Generation

Once you have the up-to-date Shopify GraphQL schema files, you can generate Python code that provides type-safe interfaces for interacting with the API.

### 3.1. Generating Pydantic Models with `datamodel-code-generator`

`datamodel-code-generator` creates Pydantic models from your GraphQL SDL. These models are used for data validation (e.g., when receiving webhooks or constructing data to send to Shopify) and provide strong typing throughout your Python application.

**Purpose**: To ensure data consistency, enable static analysis, and improve developer experience by providing auto-completion and type hints for Shopify data structures.

**Execution Steps**:

1.  **Ensure `datamodel-code-generator` is listed in `pyproject.toml`**:
    It should be in the `[project.optional-dependencies.dev]` section:
    ```toml
    [project.optional-dependencies]
    dev = [
        "datamodel-code-generator[graphql]",
        # ... other dev dependencies
    ]
    ```
2.  **Install/Sync Python dependencies**:
    ```bash
    uv sync
    ```
3.  **Generate models**:
    ```bash
    uv run datamodel-codegen --input /home/<USER>/Projects/leanchain/e-commerce/backend/shopify/shopify_schema.graphql --output /home/<USER>/Projects/leanchain/e-commerce/backend/schemas/shopify_graphql_models.py --input-file-type graphql
    ```

**Result**: A file named `schemas/shopify_graphql_models.py` will be created or updated. This file contains Pydantic classes representing all types defined in your Shopify GraphQL schema.

### 3.2. Generating `sgqlc` Schema Classes

`sgqlc` provides its own code generation utility that creates Python classes specifically designed for programmatic GraphQL query and mutation construction. This allows you to build complex GraphQL operations using Python objects and methods, rather than raw strings.

**Purpose**: To enable a more Pythonic, readable, and less error-prone way to construct GraphQL queries and mutations dynamically.

**Execution Steps**:

1.  **Ensure `sgqlc` is listed in `pyproject.toml`**:
    It should be in the `[project.optional-dependencies.dev]` section:
    ```toml
    [project.optional-dependencies]
    dev = [
        "sgqlc[requests]",
        # ... other dev dependencies
    ]
    ```
2.  **Install/Sync Python dependencies**:
    ```bash
    uv sync
    ```
3.  **Generate `sgqlc` classes**: Note that `sgqlc.codegen` expects the **introspection JSON** file as input.
    ```bash
    uv run python -m sgqlc.codegen schema /home/<USER>/Projects/leanchain/e-commerce/backend/shopify/shopify_introspection.json /home/<USER>/Projects/leanchain/e-commerce/backend/schemas/shopify_sgqlc_schema.py
    ```

**Result**: A file named `schemas/shopify_sgqlc_schema.py` will be created or updated. This file contains `sgqlc`-specific Python classes, including `Query` and `Mutation` root types, which are used to build GraphQL operations.

## 4. Backend Integration

The generated Python code is integrated into the backend services and routers to facilitate data synchronization and API interactions.

### 4.1. `services/shopify_service.py`

This service is the primary interface for interacting with the Shopify API.

*   **`sgqlc` Integration**:
    *   **Imports**: `sgqlc.endpoint.HTTPEndpoint`, `sgqlc.operation.Operation`, `Query`, and `Mutation` are imported.
    *   **Endpoint Initialization**: `HTTPEndpoint` instances (`self.admin_endpoint`, `self.storefront_endpoint`) are initialized in `__init__`, replacing direct `httpx.AsyncClient` usage for `sgqlc` operations.
    *   **`_execute_graphql_query` & `_execute_storefront_graphql_query`**: These methods now accept an `sgqlc.operation.Operation` object directly. `sgqlc` handles the serialization of this object into the GraphQL request body.
    *   **`_fetch_paginated_graphql`**: This generic helper is enhanced to work with `sgqlc` operation builders. It accepts a function (`operation_builder`) that dynamically constructs an `sgqlc.Operation` for each page, and a `resource_path` to navigate the nested `sgqlc` response object to find the `edges` and `pageInfo`.
    *   **`get_*_for_sync` Methods**: All data fetching methods (e.g., `get_products_for_sync`, `get_orders_for_sync`, `get_customers_for_sync`, `get_collections_for_sync`, `get_abandoned_checkouts_for_sync`, `get_marketing_activities_for_sync`, `get_discount_codes_for_sync`, `get_store_properties_for_sync`) are refactored to:
        *   Define an inner `*_query_builder` function that uses `sgqlc.operation.Operation(Query)` to programmatically select the required fields and apply filters (like `updated_at_min`).
        *   Utilize `_fetch_paginated_graphql` to iterate over all pages of data.
    *   **`create_*`, `update_*`, `delete_*` Methods**: All mutation methods (e.g., `create_product`, `update_product`, `delete_product`, `create_customer`, `update_customer`, `delete_customer`, `create_collection`, `update_collection`, `delete_collection`, `create_discount_code`, `update_discount_code`, `delete_discount_code`, `create_cart`, `add_to_cart`, `get_cart`) are refactored to:
        *   Construct `sgqlc.operation.Operation(Mutation)` objects.
        *   Set input variables directly on the mutation fields (e.g., `op.product_create(input=product_data)`).
        *   Specify the desired return fields (e.g., `product_create.product.id()`, `product_create.user_errors.message()`).
*   **Data Synchronization Helpers (`_sync_*_data`)**: These methods remain largely the same in their purpose (mapping Shopify data to SQLAlchemy models) but now receive data directly from `sgqlc`'s parsed dictionary-like objects.

### 4.2. `services/sync_service.py`

The `SyncService` orchestrates the overall data synchronization process.

*   **Enhanced Progress Reporting**:
    *   The `sync_store` method now tracks detailed metrics for each data category: `fetched`, `added`, `updated`, `unchanged`, and `failed`.
    *   A new `update_progress` helper function is introduced to centralize sending granular progress updates via WebSocket, including the current category, message, and detailed counts.
*   **Deduplication and Update Logic**:
    *   For each data category, before inserting a new record, the service checks if a record with the same `external_id` already exists in the local database.
    *   If a record exists, it performs a comparison (e.g., based on `updated_at` timestamps or a deeper diff) to determine if the local record needs to be updated.
    *   Counters for `added`, `updated`, and `unchanged` items are incremented accordingly.
*   **Atomic Commits per Data Type**:
    *   Changes for each data type (e.g., all products, all orders) are now committed to the database in separate transactions. This ensures that if an error occurs during the sync of one data type, it does not affect the successful synchronization of previous data types.
    *   `self.db.commit()` is called after processing each category, and `self.db.rollback()` is called in case of an exception within a category's sync block.

### 4.3. `routers/stores.py` and `routers/storefront.py`

The API routers expose endpoints for managing stores and interacting with the storefront.

*   **Handling `userErrors`**:
    *   For all API endpoints that trigger Shopify mutations (e.g., `create_product`, `update_customer`, `create_cart`), the response from `ShopifyService` is now checked for `userErrors` within the specific mutation's result field (e.g., `shopify_response.get("productCreate", {}).get("userErrors")`).
    *   If `userErrors` are present, an `HTTPException` is raised with the error message from Shopify.
*   **Accessing Response Data**: The data returned from `ShopifyService` (which originates from `sgqlc`'s parsed response) is accessed using nested dictionary lookups (e.g., `shopify_response["productCreate"]["product"]["id"]`).

## 5. Running the Synchronization

To trigger a data synchronization for a specific store:

*   **Via HTTP Endpoint**: Send a POST request to `/stores/{store_id}/sync`.
    ```bash
    curl -X POST "http://localhost:8000/stores/1/sync" \
      -H "Authorization: Bearer YOUR_AUTH_TOKEN"
    ```
*   **Via WebSocket**: Connect to the WebSocket endpoint `/stores/{store_id}/ws-sync` to receive real-time progress updates.
    ```javascript
    // Example using JavaScript in a browser or Node.js WebSocket client
    const socket = new WebSocket("ws://localhost:8000/stores/1/ws-sync");

    socket.onopen = (event) => {
        console.log("WebSocket opened:", event);
    };

    socket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        console.log("Sync update:", data);
    };

    socket.onclose = (event) => {
        console.log("WebSocket closed:", event);
    };

    socket.onerror = (error) => {
        console.error("WebSocket error:", error);
    };
    ```

## 6. Key Concepts Implemented

*   **Pagination**: The `_fetch_paginated_graphql` helper in `ShopifyService` ensures that all available data is fetched from Shopify by iteratively requesting pages using `first` and `after` (cursor-based pagination).
*   **Error Handling**: Comprehensive `try...except` blocks are implemented in `ShopifyService` to catch `httpx` errors (network issues, HTTP status errors) and GraphQL errors returned in the response payload. These are translated into `HTTPException` for API consumers.
*   **Deduplication and Updates**: The `sync_store` method in `SyncService` and the `_get_or_create` helper in `ShopifyService` work together to prevent duplicate records and update existing ones based on `external_id` and `updated_at` timestamps.
*   **Atomic Commits**: Changes for each data category are committed independently, improving data integrity and allowing for more resilient synchronization processes.
*   **Type Safety**: The use of `datamodel-code-generator` and `sgqlc` ensures that data structures are well-defined and interactions with the GraphQL API are type-checked, reducing runtime errors and improving code maintainability.

---
