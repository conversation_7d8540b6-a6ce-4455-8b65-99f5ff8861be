#!/usr/bin/env python3
"""
Test script to verify frontend authentication flow works correctly.
"""

import sys

import requests


def test_frontend_auth_flow():
    """Test the complete frontend authentication flow."""
    print("🔐 Testing Frontend Authentication Flow")
    print("=" * 50)

    base_url = "http://localhost:8000"
    session = requests.Session()

    # Step 1: Test login endpoint returns user data
    print("1. Testing login endpoint returns user data...")
    login_data = {"username": "<EMAIL>", "password": "testpassword123"}

    try:
        response = session.post(
            f"{base_url}/api/auth/token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )

        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            user_data = response.json()
            print(f"   ✅ Login successful")
            print(f"   User data: {user_data}")

            # Check if it has the expected user fields
            if "email" in user_data and "full_name" in user_data:
                print("   ✅ User data has correct format")
            else:
                print("   ❌ User data missing expected fields")
                return False

            # Check if cookie was set
            if "access_token" in session.cookies:
                print("   ✅ Authentication cookie set")
            else:
                print("   ❌ No authentication cookie found")
                return False

        else:
            print(f"   ❌ Login failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False

    except Exception as e:
        print(f"   ❌ Login error: {e}")
        return False

    # Step 2: Test protected agents endpoint
    print("\n2. Testing agents endpoint with cookie...")
    try:
        response = session.get(f"{base_url}/api/agents/?skip=0&limit=100")

        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            print("   ✅ Agents endpoint accessible with cookie")
            agents = response.json()
            print(f"   Response type: {type(agents)}")
            if isinstance(agents, list):
                print(f"   Number of agents: {len(agents)}")
            return True
        elif response.status_code == 401:
            print("   ❌ Still getting 401 - cookie auth not working")
            print(f"   Response: {response.text}")
            return False
        else:
            print(f"   ❌ Unexpected status: {response.status_code}")
            print(f"   Response: {response.text}")
            return False

    except Exception as e:
        print(f"   ❌ Agents endpoint error: {e}")
        return False


def test_cors_and_cookies():
    """Test CORS and cookie settings."""
    print("\n3. Testing CORS and cookie configuration...")

    base_url = "http://localhost:8000"

    try:
        # Test CORS preflight for agents endpoint
        response = requests.options(
            f"{base_url}/api/agents/",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "GET",
                "Access-Control-Request-Headers": "Content-Type",
            },
        )

        print(f"   CORS preflight status: {response.status_code}")

        cors_headers = {
            "Access-Control-Allow-Origin": response.headers.get(
                "Access-Control-Allow-Origin"
            ),
            "Access-Control-Allow-Credentials": response.headers.get(
                "Access-Control-Allow-Credentials"
            ),
            "Access-Control-Allow-Methods": response.headers.get(
                "Access-Control-Allow-Methods"
            ),
        }

        print(f"   CORS headers: {cors_headers}")

        if cors_headers["Access-Control-Allow-Credentials"] == "true":
            print("   ✅ CORS allows credentials")
        else:
            print("   ❌ CORS doesn't allow credentials")
            return False

        return True

    except Exception as e:
        print(f"   ❌ CORS test error: {e}")
        return False


def test_auth_me_endpoint():
    """Test the /auth/me endpoint."""
    print("\n4. Testing /auth/me endpoint...")

    base_url = "http://localhost:8000"
    session = requests.Session()

    # Login first
    login_data = {"username": "<EMAIL>", "password": "testpassword123"}

    try:
        # Login
        login_response = session.post(
            f"{base_url}/api/auth/token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )

        if login_response.status_code != 200:
            print("   ❌ Login failed, can't test /auth/me")
            return False

        # Test /auth/me
        me_response = session.get(f"{base_url}/api/auth/me")

        print(f"   Status: {me_response.status_code}")

        if me_response.status_code == 200:
            user_data = me_response.json()
            print(f"   ✅ /auth/me works")
            print(f"   User data: {user_data}")
            return True
        else:
            print(f"   ❌ /auth/me failed: {me_response.text}")
            return False

    except Exception as e:
        print(f"   ❌ /auth/me test error: {e}")
        return False


def main():
    """Run all frontend authentication tests."""
    print("Make sure backend server is running on http://localhost:8000\n")

    tests = [test_frontend_auth_flow, test_cors_and_cookies, test_auth_me_endpoint]

    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)

    print("\n" + "=" * 50)
    print("📊 Frontend Authentication Test Results:")
    print(f"✅ Passed: {sum(results)}/{len(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}/{len(results)}")

    if all(results):
        print("\n🎉 Frontend authentication should work!")
        print("\n✅ What this means:")
        print("   • Login returns user data (not just message)")
        print("   • HTTP-only cookies are set and sent")
        print("   • CORS allows credentials")
        print("   • Protected endpoints are accessible")
        print("   • Frontend should work without 401 errors")

        print("\n🔧 Next steps:")
        print("   1. Restart your backend server")
        print("   2. Clear browser cookies/localStorage")
        print("   3. Login via frontend")
        print("   4. Test agents page - should work now!")
        return 0
    else:
        print("\n❌ Some frontend authentication tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
