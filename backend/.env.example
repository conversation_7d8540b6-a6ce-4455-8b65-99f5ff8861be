FRONTEND_URLS="http://localhost:5173,http://localhost:3000"
# IMPORTANT: Replace with your actual ElevenLabs API Key, Agent ID, and Phone Number ID
# For production, use a proper secret management system.
ELEVENLABS_API_KEY="sk_xx" # Replace with your actual key
# ELEVENLABS_AGENT_ID: "agent_6101k1qjx66veq4sgkr6z28fkasg" # hs-general
ELEVENLABS_AGENT_ID="agent_xx" # hs-dutch
ELEVENLABS_PHONE_NUMBER_ID="phnum_xx" # Replace with your actual phone number ID

TWILIO_ACCOUNT_SID="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
TWILIO_AUTH_TOKEN="your_auth_token"
TWILIO_PHONE_NUMBER="+***********"
ASSEMBLYAI_API_KEY="your_assemblyai_api_key"
BASE_URL="skilled-lemur-yearly.ngrok-free.app"

LLM_PROVIDER="gemini"
OPENAI_API_KEY="sk-xx"
GEMINI_API_KEY="AIxx"
SECRET_KEY="36xx"
DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/homeservice"

# Set to 'prod' in production environments to enable secure cookies
ENVIRONMENT=dev

# Access token expiration time in minutes
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Dify Configuration (NEW - Required for agent orchestration)
DIFY_API_KEY="your-dify-api-key"
DIFY_BASE_URL="https://api.dify.ai/v1"
