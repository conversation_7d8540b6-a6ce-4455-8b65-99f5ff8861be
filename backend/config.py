from typing import Optional

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Database
    database_url: str = "postgresql://app_user:dev_password@localhost:5432/ecommerce_db"

    # JWT
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 43200  # 30 days

    # GCP
    gcp_project_id: Optional[str] = None
    gcp_region: str = "us-central1"

    # Shopify
    shopify_api_key: Optional[str] = None
    shopify_api_secret: Optional[str] = None
    shopify_access_token: Optional[str] = None
    shopify_store_url: Optional[str] = None

    # WooCommerce
    woocommerce_url: Optional[str] = None
    woocommerce_consumer_key: Optional[str] = None
    woocommerce_consumer_secret: Optional[str] = None

    # Environment
    environment: str = "development"
    testing: bool = False
    port: int = 8000 # Port for the FastAPI application
    cors_origins: list[str] = ["http://localhost:3000", "http://localhost:5173"]

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


settings = Settings()
