#!/usr/bin/env python3
"""
Test script to demonstrate proper curl authentication usage.
"""

import json
import subprocess
import sys

import requests


def test_curl_authentication():
    """Test authentication with curl commands."""
    print("🔐 Testing Curl Authentication")
    print("=" * 50)

    base_url = "http://localhost:8000"

    # Step 1: Get access token via login
    print("1. Getting access token via login...")

    login_data = {"username": "<EMAIL>", "password": "testpassword123"}

    try:
        response = requests.post(
            f"{base_url}/api/auth/token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )

        if response.status_code == 200:
            print("   ✅ Login successful")

            # Check if we can extract token from response or cookie
            response_data = response.json()
            print(f"   Response: {response_data}")

            # Check cookies
            cookies = response.cookies
            if "access_token" in cookies:
                token = cookies["access_token"]
                print(f"   ✅ Token found in cookie: {token[:20]}...")

                # Test with Authorization header
                print("\n2. Testing with Authorization header...")
                auth_response = requests.get(
                    f"{base_url}/api/agents/?skip=0&limit=100",
                    headers={"Authorization": f"Bearer {token}"},
                )

                print(f"   Status: {auth_response.status_code}")
                if auth_response.status_code == 200:
                    print("   ✅ Authorization header works")

                    # Show curl command
                    print(f"\n🔧 Working curl command:")
                    print(f"curl -X 'GET' \\")
                    print(f"  '{base_url}/api/agents/?skip=0&limit=100' \\")
                    print(f"  -H 'accept: application/json' \\")
                    print(f"  -H 'Authorization: Bearer {token}'")

                    return True
                else:
                    print(f"   ❌ Authorization header failed: {auth_response.text}")
                    return False
            else:
                print("   ❌ No access_token cookie found")
                print(f"   Available cookies: {list(cookies.keys())}")
                return False

        else:
            print(f"   ❌ Login failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False

    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


def test_cookie_authentication():
    """Test authentication with cookies (like browser)."""
    print("\n3. Testing cookie-based authentication...")

    base_url = "http://localhost:8000"
    session = requests.Session()

    # Login to set cookie
    login_data = {"username": "<EMAIL>", "password": "testpassword123"}

    try:
        response = session.post(
            f"{base_url}/api/auth/token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )

        if response.status_code == 200:
            print("   ✅ Login successful, cookie set")

            # Test with cookie
            cookie_response = session.get(f"{base_url}/api/agents/?skip=0&limit=100")

            print(f"   Status: {cookie_response.status_code}")
            if cookie_response.status_code == 200:
                print("   ✅ Cookie authentication works")

                # Show curl command with cookie
                cookie_value = session.cookies.get("access_token")
                print(f"\n🔧 Working curl command with cookie:")
                print(f"curl -X 'GET' \\")
                print(f"  '{base_url}/api/agents/?skip=0&limit=100' \\")
                print(f"  -H 'accept: application/json' \\")
                print(f"  -H 'Cookie: access_token={cookie_value}'")

                return True
            else:
                print(f"   ❌ Cookie authentication failed: {cookie_response.text}")
                return False
        else:
            print(f"   ❌ Login failed: {response.status_code}")
            return False

    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


def check_swagger_ui():
    """Check how Swagger UI handles authentication."""
    print("\n4. Checking Swagger UI authentication...")

    base_url = "http://localhost:8000"

    try:
        # Check if /docs is accessible
        response = requests.get(f"{base_url}/docs")
        if response.status_code == 200:
            print("   ✅ Swagger UI accessible at /docs")
            print("   💡 Swagger UI has built-in authentication:")
            print("      1. Go to http://localhost:8000/docs")
            print("      2. Click 'Authorize' button")
            print("      3. Enter credentials or token")
            print("      4. Test endpoints directly")
        else:
            print(f"   ❌ Swagger UI not accessible: {response.status_code}")

    except Exception as e:
        print(f"   ❌ Error checking Swagger UI: {e}")


def main():
    """Run all authentication tests."""
    print("Testing different authentication methods...\n")

    # Test token-based auth
    token_test = test_curl_authentication()

    # Test cookie-based auth
    cookie_test = test_cookie_authentication()

    # Check Swagger UI
    check_swagger_ui()

    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"✅ Token Authentication: {'PASS' if token_test else 'FAIL'}")
    print(f"✅ Cookie Authentication: {'PASS' if cookie_test else 'FAIL'}")

    if token_test or cookie_test:
        print("\n🎉 Authentication is working!")
        print("\n📝 Summary:")
        print("   • For curl: Use Authorization header with Bearer token")
        print("   • For browsers: Cookies are automatically handled")
        print("   • For /docs: Use built-in Authorize button")

        print("\n🔧 Common curl usage:")
        print(
            "   1. Login: curl -X POST .../api/auth/token -d 'username=...&password=...'"
        )
        print("   2. Get token from response cookie")
        print("   3. Use token: curl -H 'Authorization: Bearer <token>' ...")

        return 0
    else:
        print("\n❌ Authentication tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
