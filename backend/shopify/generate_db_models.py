import json
from pathlib import Path
from datetime import datetime
import re
import hashlib

# Define a base for declarative models
from sqlalchemy import Column, Integer, String, DateTime, Float, Boolean, Text, ForeignKey, BigInteger

from database import Base
from models.models import Store # Added this import as it was in the original generate_db_models.py


import keyword

RESERVED_KEYWORDS = set(keyword.kwlist)


def to_snake_case(name):
    s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)
    snake_case_name = re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1).lower()
    if snake_case_name in RESERVED_KEYWORDS:
        return f"{snake_case_name}_obj"  # Append _obj to avoid keyword conflicts
    return snake_case_name


def get_base_graphql_type_name(graphql_type_ref):
    current_type = graphql_type_ref
    while current_type.get("ofType"):
        current_type = current_type["ofType"]
    return current_type["name"]

def is_graphql_type_list(graphql_type_ref):
    current_type = graphql_type_ref
    while current_type:
        if current_type.get("kind") == "LIST":
            return True
        current_type = current_type.get("ofType")
    return False


def generate_db_models(introspection_data: dict, types: dict, syncable_entity_names: set):
    output_file = Path(__file__).parent.parent / "models" / "shopify_models.py"

    model_definitions = []
    imports = [
        "from sqlalchemy import Column, Integer, String, DateTime, Float, Boolean, Text, ForeignKey, BigInteger",
        "",
        "from datetime import datetime",
        "from database import Base",
        "from models.models import Store", # Ensure this import is present
        "",
    ]

    # Mapping GraphQL types to SQLAlchemy types
    graphql_to_sqlalchemy_type = {
        "ID": "String",
        "String": "String",
        "Int": "Integer",
        "Float": "Float",
        "Boolean": "Boolean",
        "DateTime": "DateTime",
        "URL": "String",
        "Decimal": "Float",
        "BigInt": "BigInteger",
    }

    # First pass: Prepare a mapping for generated model names and table names
    # And store the full type definition for later use
    generated_model_map = {}
    for name in sorted(list(syncable_entity_names)):
        model_name = f"Shopify{name}"
        table_name_base = f"shopify_{to_snake_case(name)}"
        if len(table_name_base) > 50:  # 63 - len(shopify_)(8) - 5 max table name size.
            hash_suffix = hashlib.sha1(table_name_base.encode()).hexdigest()[:5]
            table_name = f"{table_name_base[:50]}_{hash_suffix}s"
        else:
            table_name = f"{table_name_base}s"

        generated_model_map[name] = {
            "model_name": model_name,
            "table_name": table_name,
            "graphql_type_def": types[name],
            "columns_list": [], # Initialize columns_list
        }

    # ShopifyIntegration model definition
    shopify_integration_model_template = f"""
class ShopifyIntegration(Base):
    __tablename__ = 'shopify_integrations'

    id = Column(Integer, primary_key=True, index=True)
    store_id = Column(Integer, ForeignKey('stores.id'), nullable=False)
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, nullable=False)

    def __repr__(self):
        return f"<ShopifyIntegration(id={{self.id}}, store_id={{self.store_id}})>"
"""
    model_definitions.append(shopify_integration_model_template)


    # Second pass: Process each syncable entity to build its SQLAlchemy definition
    # Now that generated_model_map is fully populated, we can infer relationships correctly.
    for graphql_type_name in sorted(list(syncable_entity_names)):
        type_def = generated_model_map[graphql_type_name]["graphql_type_def"]
        model_info = generated_model_map[graphql_type_name]
        model_name = model_info["model_name"]
        table_name = model_info["table_name"]

        # Initialize base columns for all models
        base_columns = [
            "    id = Column(Integer, primary_key=True, index=True)",
            "    shopify_integration_id = Column(Integer, ForeignKey('shopify_integrations.id'), nullable=False)",
            "    external_id = Column(String, unique=True, index=True, nullable=False)",
            "    created_at = Column(DateTime, default=datetime.now, nullable=False)",
            "    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, nullable=False)",
        ]
        model_info["columns_list"].extend(base_columns)

        # Process fields to create columns and identify relationships
        fields_to_process = type_def.get("fields", [])

        for field in fields_to_process:
            field_name = field["name"]
            graphql_field_type_ref = field["type"]

            if field_name in ["id", "createdAt", "updatedAt", "pageInfo"]:
                continue

            base_type_name = get_base_graphql_type_name(graphql_field_type_ref)
            column_name = to_snake_case(field_name)
            is_nullable = graphql_field_type_ref["kind"] != "NON_NULL"
            nullable_str = "nullable=True" if is_nullable else "nullable=False"

            if base_type_name in generated_model_map:  # This is a relationship to another syncable entity
                related_model_info = generated_model_map[base_type_name]
                related_model_name = related_model_info["model_name"]
                is_list_relationship = is_graphql_type_list(graphql_field_type_ref)

                if is_list_relationship:  # One-to-many relationship (e.g., Product has many Variants)
                    # Add foreign key to the target model (the "many" side)
                    fk_column_on_target = to_snake_case(graphql_type_name) + "_id"
                    if f"    {fk_column_on_target} = Column(String, ForeignKey('{model_info['table_name']}.external_id'), {nullable_str})" not in related_model_info["columns_list"]:
                        related_model_info["columns_list"].append(f"    {fk_column_on_target} = Column(String, ForeignKey('{model_info['table_name']}.external_id'), {nullable_str})")

                else:  # Many-to-one relationship (e.g., Order has one Customer)
                    fk_column = f"{column_name}_id"
                    fk_table = related_model_info["table_name"]
                    model_info["columns_list"].append(f"    {fk_column} = Column(String, ForeignKey('{fk_table}.external_id'), {nullable_str})")

            elif base_type_name in graphql_to_sqlalchemy_type:  # Direct scalar field
                sqlalchemy_type = graphql_to_sqlalchemy_type[base_type_name]
                model_info["columns_list"].append(f"    {column_name} = Column({sqlalchemy_type}, {nullable_str})")
            elif base_type_name.endswith(("Set", "V2")) and "amount" in types.get(base_type_name, {}).get("fields", []):
                model_info["columns_list"].append(f"    {column_name}_amount = Column(Float, {nullable_str})")
                if "currencyCode" in types.get(base_type_name, {}).get("fields", []):
                    model_info["columns_list"].append(f"    {column_name}_currency_code = Column(String, {nullable_str})")
            elif base_type_name == "ShopPlan":
                model_info["columns_list"].append(f"    {column_name}_display_name = Column(String, {nullable_str})")
            elif base_type_name == "Image":
                model_info["columns_list"].append(f"    {column_name}_src = Column(String, {nullable_str})")
            elif types.get(base_type_name, {}).get("kind") == "ENUM":
                model_info["columns_list"].append(f"    {column_name} = Column(String, {nullable_str})\n") # Added newline for consistency
            else:
                # Fallback for unhandled complex types: store as Text (JSON string) or skip
                pass

    # Final rendering of model definitions
    for graphql_type_name in sorted(list(syncable_entity_names)):
        model_info = generated_model_map[graphql_type_name]
        model_name = model_info["model_name"]
        table_name = model_info["table_name"]
        columns_str = "\n".join([col for col in model_info["columns_list"] if col]) # Ensure columns_list is populated

        model_template = f"""
class {model_name}(Base):
    __tablename__ = '{table_name}'

{columns_str}
    def __repr__(self):
        return f"<{model_name}(id={{self.id}}, external_id='{{self.external_id}}')>"
"""
        model_definitions.append(model_template)

    output_content = "\n".join(imports) + "\n".join(model_definitions)

    output_file.parent.mkdir(parents=True, exist_ok=True)
    with open(output_file, "w") as f:
        f.write(output_content)

    print(f"Generated SQLAlchemy models to {output_file}")
    return generated_model_map # Return the map for use in mappings file


if __name__ == "__main__":
    # Ensure the script is run from the backend root
    project_root = Path(__file__).parent
    introspection_json_path = project_root / "shopify_introspection.json"
    output_models_path = project_root.parent / "models" / "shopify_models.py" # Corrected path

    # Load introspection data
    with open(introspection_json_path, "r") as f:
        schema_data = json.load(f)

    types = {t["name"]: t for t in schema_data["data"]["__schema"]["types"]}

    # Heuristics to identify "syncable" top-level entities
    syncable_entity_names = set()
    for type_name, type_def in types.items():
        if (
            type_def["kind"] == "OBJECT"
            and not type_name.endswith(
                ("Connection", "Edge", "Payload", "Input", "UserError", "SortKeys", "Filter", "Enum")
            )
            and not type_name.startswith("__")
            and type_name
            not in [
                "QueryRoot",
                "Mutation",
                "Subscription",
                "Node",
                "PageInfo",
                "MoneyV2",
                "MoneyBag",
                "ARN",
                "URL",
                "Decimal",
                "BigInt",
                "App",
                "AppDiscountType",
                "AppInstallation",
                "AppCredit",
                "AppPurchaseOneTime",
                "AppRevenueAttributionRecord",
                "AppSubscription",
                "AppSubscriptionLineItem",
                "AppPlanV2",
                "Link",
                "RowCount",
                "CustomerVisitProductInfo",
                "Attribute",
                "TaxLine",
                "DiscountAllocation",
                "MailingAddress",
                "Image",
                "ShopPlan",
            ]
        ):
            syncable_entity_names.add(type_name)

    if "Shop" in types:
        syncable_entity_names.add("Shop")
    if "DiscountCodeBasic" in types:
        syncable_entity_names.add("DiscountCodeBasic")

    generate_db_models(schema_data, types, syncable_entity_names)
