
const { buildClientSchema, printSchema } = require('graphql');
const fs = require('fs');

async function convertSchema() {
  try {
    const introspectionJson = JSON.parse(fs.readFileSync('shopify_introspection.json', 'utf8'));
    const schema = buildClientSchema(introspectionJson.data);
    let schemaSDL = printSchema(schema);
    schemaSDL = schemaSDL.replace(/implements Node & (\w+)/g, 'implements $1');
    fs.writeFileSync('shopify_schema.graphql', schemaSDL);
    console.log('Shopify schema converted to SDL and saved to shopify_schema.graphql');
  } catch (error) {
    console.error('Error converting schema:', error);
  }
}

convertSchema();
