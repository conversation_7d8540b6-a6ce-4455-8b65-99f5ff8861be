import json
from pathlib import Path
from typing import Any, Dict, Optional

import keyword
import hashlib
import re

from shopify.generate_db_models import generate_db_models


RESERVED_KEYWORDS = set(keyword.kwlist)


def to_snake_case(name):
    s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)
    snake_case_name = re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1).lower()
    if snake_case_name in RESERVED_KEYWORDS:
        return f"{snake_case_name}_obj"  # Append _obj to avoid keyword conflicts
    return snake_case_name


# Define paths
BASE_DIR = Path(__file__).resolve().parent.parent
SCHEMAS_DIR = BASE_DIR / "schemas"

INTROSPECTION_FILE = BASE_DIR / "shopify" / "shopify_introspection.json"
MODELS_FILE = BASE_DIR / "models" / "shopify_models.py"


def load_introspection_data():
    """Loads and parses the Shopify GraphQL introspection data."""
    try:
        with open(INTROSPECTION_FILE, "r") as f:
            introspection_data = json.load(f)
        return introspection_data
    except FileNotFoundError:
        print(f"Error: Introspection file not found at {INTROSPECTION_FILE}")
        return None
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from {INTROSPECTION_FILE}")
        return None


def get_graphql_types(introspection_data):
    """Extracts and organizes GraphQL types from introspection data."""
    types = {t["name"]: t for t in introspection_data["data"]["__schema"]["types"]}
    return types


def get_syncable_entity_names(types):
    s = set()
    for type_name, type_def in types.items():
        if (
            type_def["kind"] == "OBJECT"
            and not type_name.endswith(
                ("Connection", "Edge", "Payload", "Input", "UserError", "SortKeys", "Filter", "Enum")
            )
            and not type_name.startswith("__")
            and type_name
            not in [
                "QueryRoot",
                "Mutation",
                "Subscription",
                "Node",
                "PageInfo",
                "MoneyV2",
                "MoneyBag",
                "ARN",
                "URL",
                "Decimal",
                "BigInt",
                "App",
                "AppDiscountType",
                "AppInstallation",
                "AppCredit",
                "AppPurchaseOneTime",
                "AppRevenueAttributionRecord",
                "AppSubscription",
                "AppSubscriptionLineItem",
                "AppPlanV2",
                "Link",
                "RowCount",
                "CustomerVisitProductInfo",
                "Attribute",
                "TaxLine",
                "DiscountAllocation",
                "MailingAddress",
                "Image",
                "ShopPlan",
            ]
        ):
            s.add(type_name)

    if "Shop" in types:
        s.add("Shop")
    if "DiscountCodeBasic" in types:
        s.add("DiscountCodeBasic")
    return sorted(list(s))


def get_base_graphql_type_name(graphql_type_ref):
    current_type = graphql_type_ref
    while current_type.get("ofType"):
        current_type = current_type["ofType"]
    return current_type["name"]


def get_mutation_fields(introspection_data: Dict[str, Any], types: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
    """Extracts mutation fields, their input and payload types from introspection data."""
    mutation_fields_map = {}
    for t in introspection_data["data"]["__schema"]["types"]:
        if t["name"] == "Mutation":
            for field in t["fields"]:
                mutation_name = field["name"]
                payload_type_name = get_base_graphql_type_name(field["type"])

                input_type_name = None
                for arg in field.get("args", []):
                    if arg["name"] == "input":
                        input_type_name = get_base_graphql_type_name(arg["type"])
                        break

                mutation_fields_map[mutation_name] = {
                    "payload_type": payload_type_name,
                    "input_type": input_type_name,  # Will be None if no input arg
                }
            break
    return mutation_fields_map


def get_query_fields(introspection_data: Dict[str, Any], types: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
    """Extracts query fields and their return types from introspection data."""
    query_fields_map = {}
    for t in introspection_data["data"]["__schema"]["types"]:
        if t["name"] == "QueryRoot":
            for field in t["fields"]:
                query_name = field["name"]
                return_type_ref = field["type"]
                return_type_name = get_base_graphql_type_name(return_type_ref)

                query_info = {"return_type": return_type_name}

                # If it's a connection type, find the node type
                if return_type_name.endswith("Connection"):
                    for conn_field in types.get(return_type_name, {}).get("fields", []):
                        if conn_field["name"] == "edges":
                            edge_type_ref = get_base_graphql_type_name(conn_field["type"])
                            if edge_type_ref and types.get(edge_type_ref, {}).get("kind") == "OBJECT":
                                for edge_field in types[edge_type_ref].get("fields", []):
                                    if edge_field["name"] == "node":
                                        node_type_name = get_base_graphql_type_name(edge_field["type"])
                                        query_info["node_type"] = node_type_name
                                        break
                                break
                query_fields_map[query_name] = query_info
            break
    return query_fields_map


def generate_sgqlc_schema(introspection_file: Path, output_file: Path):
    """Generates sgqlc schema classes from introspection data."""
    try:
        # Assuming sgqlc-codegen is installed and available in the environment
        command = f"uv run python -m sgqlc.codegen schema {introspection_file} {output_file}"
        print(f"Running: {command}")
        import subprocess

        subprocess.run(command, shell=True, check=True)
        print(f"Successfully generated sgqlc schema to {output_file}")
    except subprocess.CalledProcessError as e:
        print(f"Error generating sgqlc schema: {e}")
        print(f"Stderr: {e.stderr}")
        raise
    except FileNotFoundError:
        print("Error: sgqlc-codegen not found. Please ensure it is installed and in your PATH.")
        raise


def generate_pydantic_models(graphql_schema_file: Path, output_file: Path):
    """Generates Pydantic models from GraphQL SDL using datamodel-code-generator."""
    try:
        # Assuming datamodel-code-generator is installed and available in the environment
        command = f"uv run python -m datamodel_code_generator --input {graphql_schema_file} --output {output_file} --input-file-type graphql"
        print(f"Running: {command}")
        import subprocess

        subprocess.run(command, shell=True, check=True)
        print(f"Successfully generated Pydantic models to {output_file}")
    except subprocess.CalledProcessError as e:
        print(f"Error generating Pydantic models: {e}")
        print(f"Stderr: {e.stderr}")
        raise
    except FileNotFoundError:
        print("Error: datamodel-code-generator not found. Please ensure it is installed and in your PATH.")
        raise


def _normalize_name(name: str) -> str:
    """Normalizes a name for comparison by removing non-alphanumeric characters and converting to lowercase."""
    return "".join(filter(str.isalnum, name)).lower()


def _find_best_mutation_match(entity_name: str, mutation_fields: dict, action: str) -> Optional[str]:
    """Finds the best matching mutation name for a given entity and action."""
    normalized_entity_name = _normalize_name(entity_name)
    best_match = None
    best_score = -1

    # Prioritized patterns (more specific to less specific)
    action_lower = action.lower()
    patterns = [
        f"{normalized_entity_name}{action_lower}",  # e.g., productcreate
        f"{normalized_entity_name}s{action_lower}",  # e.g., productscreate (less common but possible)
        f"{action_lower}{normalized_entity_name}",  # e.g., createproduct
    ]

    for mutation_field_name in mutation_fields.keys():
        normalized_mutation_name = _normalize_name(mutation_field_name)

        for i, pattern in enumerate(patterns):
            if normalized_mutation_name == pattern:
                # Exact match for a prioritized pattern, highest score
                if i == 0:  # productCreate
                    return mutation_field_name
                score = 100 - i  # Higher score for earlier patterns
                if score > best_score:
                    best_score = score
                    best_match = mutation_field_name
            elif pattern in normalized_mutation_name:
                # Contains the pattern
                score = 50 - i  # Lower score for contains
                if score > best_score:
                    best_score = score
                    best_match = mutation_field_name
    return best_match


def write_shopify_mappings_file(
    mutation_fields_map: dict, query_fields_map: dict, generated_model_map: dict, types: dict
):
    """Writes the mutation_fields, query_fields, and graphql_to_model_map to a Python file."""
    output_path = SCHEMAS_DIR / "shopify_mappings.py"

    model_imports = []
    graphql_to_model_map_str = []
    graphql_type_imports = []  # New list for GraphQL type imports

    for graphql_name, model_info in generated_model_map.items():  # Use generated_model_map here
        model_name = model_info["model_name"]
        model_imports.append(f"    {model_name}")
        graphql_type_imports.append(f"    {graphql_name}")  # Add GraphQL type to imports
        graphql_to_model_map_str.append(
            f"    shopify_sgqlc_schema.{graphql_name}: {model_name},"
        )  # Use class directly as key

    model_imports_str = ", \
".join(
        sorted(model_imports)
    )
    graphql_to_model_map_str = []
    for graphql_name, model_info in generated_model_map.items():
        model_name = model_info["model_name"]
        graphql_to_model_map_str.append(f"    shopify_sgqlc_schema.{graphql_name}: shopify_models.{model_name},")

    graphql_to_model_map_content = "{\n" + "\n".join(graphql_to_model_map_str) + "\n}"

    # Collect unique mutation payload class names for imports and generate MUTATION_PAYLOAD_MAP
    mutation_payload_imports = []
    mutation_payload_map_str = []
    for mutation_name, details in mutation_fields_map.items():
        payload_name = details["payload_type"]
        input_type_name = details["input_type"]
        mutation_payload_imports.append(f"    {payload_name}")
        if input_type_name:  # Only import if an input type exists
            mutation_payload_imports.append(f"    {input_type_name}")

        # Corrected f-string for input_type
        input_type_expr = f"shopify_sgqlc_schema.{input_type_name}" if input_type_name else "None"
        mutation_payload_map_str.append(
            f"    '{mutation_name}': {{'payload_type': shopify_sgqlc_schema.{payload_name}, 'input_type': {input_type_expr}}},"
        )

    mutation_payload_map_content = "{\n" + "\n".join(mutation_payload_map_str) + "\n}"

    # Collect unique query payload class names for imports and generate QUERY_PAYLOAD_MAP
    query_payload_imports = []
    QUERY_PAYLOAD_MAP_str = []
    for query_name, details in query_fields_map.items():
        return_type_name = details["return_type"]
        node_type_name = details.get("node_type")
        query_payload_imports.append(f"    {return_type_name}")
        if node_type_name:  # Only import if a node type exists
            query_payload_imports.append(f"    {node_type_name}")

        # Corrected f-string for node_type
        node_type_expr = f"shopify_sgqlc_schema.{node_type_name}" if node_type_name else "None"
        QUERY_PAYLOAD_MAP_str.append(
            f"    '{query_name}': {{'return_type': shopify_sgqlc_schema.{return_type_name}, 'node_type': {node_type_expr}}},"
        )

    QUERY_PAYLOAD_MAP_content = "{\n" + "\n".join(QUERY_PAYLOAD_MAP_str) + "\n}"

    content = f"""# This file is auto-generated by generate_shopify_api.py\n# Do not modify it directly.\n\nfrom typing import Dict, Any, Type, List\nfrom models import shopify_models\nfrom schemas import shopify_sgqlc_schema\n\nMUTATION_PAYLOAD_MAP: Dict[str, Dict[str, Any]] = {mutation_payload_map_content}\n\nQUERY_PAYLOAD_MAP: Dict[str, Dict[str, Any]] = {QUERY_PAYLOAD_MAP_content}\n\nGRAPHQL_TO_MODEL_MAP: Dict[Type[Any], Type[Any]] = {graphql_to_model_map_content}\n"""
    try:
        SCHEMAS_DIR.mkdir(parents=True, exist_ok=True)
        with open(output_path, "w") as f:
            f.write(content)
        print(f"Successfully wrote Shopify mappings to {output_path}")
    except IOError as e:
        print(f"Error writing Shopify mappings to {output_path}: {e}")


if __name__ == "__main__":
    # Define output paths for generated schema files
    OUTPUT_SGQLC_SCHEMA_FILE = SCHEMAS_DIR / "shopify_sgqlc_schema.py"
    OUTPUT_GRAPHQL_MODELS_FILE = SCHEMAS_DIR / "shopify_graphql_models.py"
    SHOPIFY_SCHEMA_GRAPHQL = BASE_DIR / "shopify" / "shopify_schema.graphql"

    introspection_data = load_introspection_data()
    if introspection_data:
        # Generate Pydantic models first (from .graphql schema)
        generate_pydantic_models(SHOPIFY_SCHEMA_GRAPHQL, OUTPUT_GRAPHQL_MODELS_FILE)

        # Generate sgqlc schema (from .json introspection)
        generate_sgqlc_schema(INTROSPECTION_FILE, OUTPUT_SGQLC_SCHEMA_FILE)

        types = get_graphql_types(introspection_data)
        syncable_entity_names = set(get_syncable_entity_names(types))

        # Generate query_fields_map first, as it's a dependency for mutation_fields_map
        query_fields_map = get_query_fields(introspection_data, types)
        mutation_fields_map = get_mutation_fields(introspection_data, types)

        generated_model_map = generate_db_models(introspection_data, types, syncable_entity_names)

        write_shopify_mappings_file(mutation_fields_map, query_fields_map, generated_model_map, types)
