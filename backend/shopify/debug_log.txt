DEBUG: Initializing generated_model_map. ProductImage in types: False
DEBUG: syncable_entity_names after ProductImage handling: {'DiscountQuantity', 'ProductResourceFeedback', 'CustomerShopPayAgreement', 'MarketRegionCountry', 'SubscriptionDiscountAllocation', 'DraftOrder', 'CheckoutBrandingColorGlobal', 'PaymentSchedule', 'PriceRuleDiscountCode', 'Channel', 'CompanyLocationStaffMemberAssignment', 'DepositPercentage', 'OnlineStoreThemeFileBodyUrl', 'CurrencySetting', 'ManualDiscountApplication', 'RefundShippingLine', 'DeliveryProfileItem', 'TaxonomyCategory', 'RefundAgreement', 'WebhookSubscription', 'CheckoutBrandingHeaderCartLink', 'CheckoutBrandingOrderSummary', 'DomainLocalization', 'CommentEventAttachment', 'CheckoutBrandingControlColorRoles', 'ShopAlert', 'CustomerAccountAppExtensionPage', 'DiscountCodeBasic', 'DeliverySetting', 'OnlineStoreTheme', 'Model3d', 'CommentEvent', 'DeliveryProvince', 'WebhookEventBridgeEndpoint', 'InventoryAdjustmentGroup', 'DraftOrderBundleAddedWarning', 'CheckoutBrandingCustomizations', 'SubscriptionDeliveryPolicy', 'DiscountAmount', 'DiscountCodeNode', 'DeliveryProfileLocationGroup', 'CheckoutBrandingButtonColorRoles', 'AllDiscountItems', 'DiscountOnQuantity', 'DiscountRedeemCodeBulkCreationCode', 'FulfillmentService', 'AppFeedback', 'DeliveryCountryCodesOrRestOfWorld', 'CheckoutBrandingFooterContent', 'ShopifyPaymentsToolingProviderPayout', 'CashTrackingAdjustment', 'ShopifyFunction', 'ProductPriceRangeV2', 'CheckoutBrandingCornerRadiusVariables', 'CustomerMergeError', 'CartTransform', 'SuggestedRefund', 'ProductBundleComponentOptionSelectionValue', 'PrivateMetafield', 'CalculatedDraftOrder', 'FulfillmentOriginAddress', 'CheckoutBrandingTypography', 'OrderStagedChangeIncrementItem', 'CollectionRuleConditions', 'Metaobject', 'CombinedListing', 'PriceRuleShareableUrl', 'SubscriptionManualDiscount', 'DiscountCountryAll', 'CheckoutBrandingGlobal', 'ProductBundleComponentQuantityOption', 'Return', 'OnlineStoreThemeFileReadResult', 'DeliveryCustomizationError', 'CheckoutBrandingTypographyStyle', 'CustomerCreditCardBillingAddress', 'SegmentMigration', 'SubscriptionPickupOption', 'SellingPlanRecurringDeliveryPolicy', 'Model3dBoundingBox', 'StorefrontAccessToken', 'SubscriptionShippingOptionResultFailure', 'FulfillmentOrderSplitResult', 'CheckoutBrandingMerchandiseThumbnail', 'CustomerPhoneNumber', 'CheckoutBrandingCheckbox', 'AdditionalFee', 'CheckoutBrandingContent', 'CompanyContactRole', 'Company', 'SearchFilterOptions', 'PriceListParent', 'CheckoutBrandingTypographyStyleGlobal', 'FulfillmentOrderLineItemFinancialSummary', 'FulfillmentHold', 'ProductPriceRange', 'CheckoutProfile', 'SubscriptionBillingAttempt', 'ShopifyPaymentsDisputeFileUpload', 'CheckoutBrandingCustomFont', 'MarketLocalizableResource', 'SubscriptionShippingOption', 'AdditionalFeeSale', 'CustomerMergeable', 'DiscountCodeApplication', 'Shop', 'PriceListAdjustment', 'CheckoutBrandingControl', 'MarketWebPresence', 'SegmentMembership', 'MetaobjectCapabilityDefinitionDataRenderable', 'SellingPlanCheckoutChargePercentageValue', 'WebhookHttpEndpoint', 'PriceListPrice', 'SavedSearch', 'CustomerSegmentMember', 'DeliveryLocationGroupZone', 'CollectionRuleCategoryCondition', 'DeliveryMethodDefinition', 'DiscountAutomaticFreeShipping', 'ShippingLineSale', 'ScriptDiscountApplication', 'SubscriptionBillingCycleEditedContract', 'ReturnableFulfillment', 'ProductVariantPricePair', 'SegmentStatistics', 'SegmentValue', 'Comment', 'CustomerMergePreviewAlternateFields', 'CustomerPaymentInstrumentBillingAddress', 'MetaobjectField', 'SubscriptionDeliveryMethodLocalDelivery', 'TenderTransaction', 'MetafieldAccess', 'FulfillmentOrderMergeResult', 'OnlineStorePasswordProtection', 'SubscriptionPricingPolicy', 'CollectionRuleTextCondition', 'CollectionRuleSet', 'ReturnableFulfillmentLineItem', 'SegmentEventFilterParameter', 'BundlesFeature', 'SubscriptionDeliveryOptionResultSuccess', 'ShippingRefund', 'MarketingActivity', 'OnlineStoreThemeFilesUserErrors', 'CashTrackingSession', 'MetaobjectCapabilitiesTranslatable', 'OrderStagedChangeRemoveShippingLine', 'LineItem', 'VaultPaypalBillingAgreement', 'ProductBundleComponentOptionSelection', 'MetafieldDefinitionConstraintValue', 'ShippingLine', 'DeliveryLocalPickupSettings', 'MetafieldCapabilities', 'CollectionRule', 'PricingPercentageValue', 'Locale', 'ReverseDeliveryShippingDeliverable', 'ShopifyPaymentsDisputeFulfillment', 'LineItemGroup', 'ShopifyPaymentsAssociatedOrder', 'TranslatableContent', 'DiscountCustomerGets', 'CalculatedRestockingFee', 'DeliveryAvailableService', 'DiscountAutomaticBxgy', 'CartTransformFeature', 'DeletionEvent', 'ExternalVideo', 'SaleAdditionalFee', 'ShopifyPaymentsBalanceTransaction', 'CheckoutBrandingMainSection', 'DelegateAccessToken', 'BusinessEntity', 'OnlineStoreThemeFileBodyBase64', 'ProductVariant', 'MarketingEvent', 'LinkedMetafield', 'PriceRuleCustomerSelection', 'SEO', 'UrlRedirectImportPreview', 'ReverseDeliveryTrackingV2', 'StandardizedProductType', 'DeliveryCustomization', 'AppleApplication', 'SegmentAttributeStatistics', 'CompanyLocationCatalog', 'ResourceAlertAction', 'ShopifyPaymentsDisputeReasonDetails', 'ProductOptionValue', 'OrderRisk', 'CustomerMergePreviewDefaultFields', 'TaxonomyValue', 'ShopifyPaymentsPayoutSchedule', 'DiscountCodeBxgy', 'DeliveryRateDefinition', 'DeliveryParticipant', 'Market', 'ApiVersion', 'MarketingBudget', 'SubscriptionLine', 'CartTransformEligibleOperations', 'SubscriptionAppliedCodeDiscount', 'StaffMember', 'CheckoutBrandingTextField', 'Taxonomy', 'InventoryQuantity', 'CalculatedManualDiscountApplication', 'ReturnAgreement', 'MediaPreviewImage', 'ReverseFulfillmentOrderThirdPartyConfirmation', 'CalculatedExchangeLineItem', 'CommentAuthor', 'CustomerVisit', 'GenericFile', 'SellingPlanRecurringBillingPolicy', 'ProductPublication', 'DiscountCombinesWith', 'CardPaymentDetails', 'MetaobjectCapabilityData', 'DeliveryCarrierServiceAndLocations', 'CustomerPaymentMethod', 'Abandonment', 'Translation', 'ExchangeV2Additions', 'InventoryQuantityName', 'UrlRedirectImport', 'CheckoutBrandingColors', 'PaymentTermsTemplate', 'CustomerAccountsV2', 'CalculatedReturnLineItem', 'MetafieldDefinitionSupportedValidation', 'SellingPlanInventoryPolicy', 'SegmentMembershipResponse', 'ExchangeV2Returns', 'ShopifyProtectOrderSummary', 'ReverseDeliveryLabelV2', 'CheckoutBrandingFooter', 'InventoryScheduledChange', 'MarketCatalog', 'Segment', 'CheckoutBrandingExpressCheckout', 'DiscountShareableUrl', 'MediaWarning', 'StoreCreditAccount', 'MediaImage', 'SearchResult', 'Customer', 'ShopifyPaymentsAccount', 'CalculatedDiscountCodeApplication', 'DiscountCustomerBuys', 'InventoryProperties', 'CollectionRuleProductCategoryCondition', 'Publication', 'UTMParameters', 'ProductSetOperation', 'ShopifyPaymentsBalanceTransactionAssociatedPayout', 'CheckoutBrandingHeadingLevel', 'ShopifyPaymentsDispute', 'MetaobjectCapabilityDefinitionDataOnlineStore', 'CalculatedShippingLine', 'TaxonomyMeasurementAttribute', 'MediaError', 'CalculatedOrder', 'RiskFact', 'ServerPixel', 'BuyerExperienceConfiguration', 'VaultCreditCard', 'CheckoutBrandingColorScheme', 'OnlineStoreThemeFile', 'MetaobjectCapabilitiesOnlineStore', 'SubscriptionDraft', 'OnlineStoreThemeFileBodyText', 'Weight', 'ProductBundleOperation', 'CheckoutBrandingButton', 'DiscountCustomerAll', 'MenuItem', 'PriceRuleEntitlementToPrerequisiteQuantityRatio', 'ShopifyPaymentsDefaultChargeStatementDescriptor', 'FailedRequirement', 'DeliveryProfile', 'GiftCard', 'CheckoutBrandingColorSchemes', 'ShopBillingPreferences', 'CalculatedLineItem', 'CurrencyFormats', 'MetaobjectThumbnail', 'ShopAlertAction', 'DraftOrderAppliedDiscount', 'ShopifyProtectOrderEligibility', 'ShopifyPaymentsExtendedAuthorization', 'ResourceFeedback', 'DraftOrderPlatformDiscountAllocation', 'Model3dSource', 'AppCatalog', 'DiscountAutomaticNode', 'FulfillmentOrderInternationalDuties', 'InventoryItemMeasurement', 'ShopifyPaymentsDisputeEvidence', 'GiftCardCreditTransaction', 'SubscriptionContract', 'TransactionFee', 'DraftOrderDiscountNotAppliedWarning', 'DutySale', 'SellingPlanRecurringPricingPolicy', 'CheckoutBrandingCartLink', 'AppUsageRecord', 'Collection', 'QuantityPriceBreak', 'CheckoutBrandingChoiceListGroup', 'SubscriptionDeliveryMethodPickupOption', 'DiscountProducts', 'LimitedPendingOrderCount', 'CustomerEmailAddress', 'OrderApp', 'OrderPaymentStatus', 'SellingPlanCheckoutCharge', 'FilterOption', 'AppSubscriptionDiscount', 'CheckoutBrandingHeader', 'SubscriptionBillingCycle', 'CollectionPublication', 'TypedAttribute', 'ChannelDefinition', 'OrderStagedChangeAddVariant', 'ShopPolicy', 'MetafieldRelation', 'Video', 'MarketWebPresenceRootUrl', 'FulfillmentOrderLineItemWarning', 'MetaobjectCapabilities', 'RefundLineItem', 'MarketingActivityExtensionAppErrors', 'Metafield', 'OrderStagedChangeAddShippingLine', 'DeliveryCountryAndZone', 'CheckoutBrandingMain', 'UnitPriceMeasurement', 'OrderStagedChangeAddCustomItem', 'CheckoutBrandingSelect', 'CustomerAccountNativePage', 'DeliveryLocationGroup', 'ExchangeV2LineItem', 'SubscriptionDeliveryMethodShipping', 'SelectedOption', 'CatalogCsvOperation', 'SuggestedReturnRefund', 'DiscountMinimumSubtotal', 'RestrictedForResource', 'CheckoutBrandingFontGroup', 'ProductCompareAtPriceRange', 'CombinedListingChild', 'CustomerMergePreview', 'DiscountMinimumQuantity', 'MerchantApprovalSignals', 'ProductDeleteOperation', 'Blog', 'CalculatedDraftOrderLineItem', 'CashRoundingAdjustment', 'PublicationResourceOperation', 'AbandonedCheckout', 'CheckoutBrandingShopifyFont', 'FunctionsErrorHistory', 'ScriptTag', 'PriceList', 'MarketingEngagement', 'SubscriptionLocalDeliveryOption', 'InventoryLevel', 'ProductOption', 'SubscriptionDiscountEntitledLines', 'ProductBundleComponentQuantityOptionValue', 'PriceRulePercentValue', 'MetaobjectDefinition', 'StoreCreditAccountDebitTransaction', 'Product', 'GiftCardRecipient', 'ProductVariantContextualPricing', 'FinancialSummaryDiscountApplication', 'ResourcePublication', 'ShopifyPaymentsJpChargeStatementDescriptor', 'SellingPlanFixedDeliveryPolicy', 'BusinessEntityAddress', 'Fulfillment', 'MetaobjectCapabilitiesPublishable', 'StagedUploadTarget', 'FulfillmentEvent', 'ProductVariantComponent', 'ReturnDecline', 'FileError', 'CompanyContactRoleAssignment', 'GiftCardDebitTransaction', 'AddAllProductsOperation', 'OrderEditAgreement', 'OrderRiskSummary', 'ExchangeV2', 'DeliveryParticipantService', 'FunctionsAppBridge', 'DiscountRedeemCodeBulkCreation', 'PriceRuleQuantityRange', 'LocalizationExtension', 'MetaobjectCapabilityDataPublishable', 'MetafieldDefinitionConstraints', 'TenderTransactionCreditCardDetails', 'ShopResourceLimits', 'CheckoutBrandingDesignSystem', 'DeliveryCarrierService', 'FulfillmentOrderLineItem', 'LocalPaymentMethodsPaymentDetails', 'ReturnShippingFee', 'SubscriptionMailingAddress', 'ProductTaxonomyNode', 'Domain', 'MediaImageOriginalSource', 'CheckoutBrandingChoiceList', 'DiscountCountries', 'ResourcePublicationV2', 'SellingPlan', 'PaymentCustomization', 'AvailableChannelDefinitionsByChannel', 'CompanyAddress', 'FinancialSummaryDiscountAllocation', 'CustomerMergeRequest', 'SellingPlanPricingPolicyPercentageValue', 'ShopifyPaymentsPayout', 'ProductCategory', 'ProductDuplicateJob', 'DeliveryZone', 'FulfillmentOrderAssignedLocation', 'BlogFeed', 'MetafieldCapabilitySmartCollectionCondition', 'Article', 'DiscountCodeFreeShipping', 'OrderTransaction', 'DiscountCollections', 'FulfillmentOrder', 'CalculatedScriptDiscountApplication', 'DeliveryLocationLocalPickupSettingsError', 'DeliveryMethodDefinitionCounts', 'ResourceAlert', 'AppRecurringPricing', 'AppUsagePricing', 'SaleTax', 'WebPixel', 'DeliveryCondition', 'InventoryChange', 'PaymentSettings', 'DeliveryCountryCodeOrRestOfWorld', 'FulfillmentOrderSupportedAction', 'ShopFeatures', 'WebhookPubSubEndpoint', 'EditableProperty', 'MetafieldAccessGrant', 'LineItemSellingPlan', 'SubscriptionDeliveryMethodPickup', 'FulfillmentLineItem', 'TaxAppConfiguration', 'SubscriptionCyclePriceAdjustment', 'FulfillmentOrderMerchantRequest', 'PriceRulePrerequisiteToEntitlementQuantityRatio', 'ProductFeed', 'OrderDisputeSummary', 'MetafieldDefinitionType', 'Order', 'ReverseDeliveryLineItem', 'MetaobjectCapabilityDataOnlineStore', 'DeliveryMethodAdditionalInformation', 'OrderPaymentCollectionDetails', 'ShopifyPaymentsBankAccount', 'PriceRuleItemEntitlements', 'BasicEvent', 'CustomerSmsMarketingConsentState', 'MetafieldIdentifier', 'PaymentTerms', 'ProductBundleComponent', 'PriceRuleMoneyRange', 'CheckoutBrandingImage', 'MetafieldCapabilityAdminFilterable', 'DiscountCustomerSegments', 'SellingPlanFixedBillingPolicy', 'SellingPlanAnchor', 'StagedMediaUploadTarget', 'AppSubscriptionDiscountAmount', 'CalculatedAutomaticDiscountApplication', 'SubscriptionDeliveryMethodLocalDeliveryOption', 'QuantityRule', 'ShopifyPaymentsRefundSet', 'PaymentCustomizationError', 'OrderStagedChangeAddLineItemDiscount', 'ShopPayInstallmentsPaymentDetails', 'Duty', 'CheckoutBrandingContainerDivider', 'SellingPlanFixedPricingPolicy', 'SubscriptionDeliveryMethodShippingOption', 'CalculatedReturnShippingFee', 'CompanyContact', 'LocationAddress', 'StoreCreditAccountExpirationTransaction', 'CountriesInShippingZones', 'AppSubscriptionDiscountPercentage', 'ProductDuplicateOperation', 'SellingPlanGroup', 'StagedUploadParameter', 'FulfillmentOrderLocationForMove', 'OrderAgreement', 'DiscountRedeemCode', 'CompanyLocation', 'StoreCreditAccountDebitRevertTransaction', 'ExchangeLineItem', 'FulfillmentTrackingInfo', 'NavigationItem', 'AccessScope', 'CustomerJourney', 'AbandonedCheckoutLineItem', 'SuggestedOrderTransaction', 'OrderStagedChangeDecrementItem', 'CustomerJourneySummary', 'ArticleAuthor', 'SubscriptionDiscountFixedAmountValue', 'CalculatedReturn', 'ProductContextualPricing', 'CustomerStatistics', 'Page', 'Count', 'StaffMemberPrivateData', 'DeliveryPromiseProvider', 'AndroidApplication', 'FeeSale', 'CustomerSmsMarketingConsentError', 'DraftOrderPlatformDiscount', 'ShopLocale', 'Menu', 'CustomerCreditCard', 'CheckoutBrandingLogo', 'RefundDuty', 'PriceRuleFixedAmountValue', 'ReverseFulfillmentOrderLineItem', 'Vector3', 'DeliveryBrandedPromise', 'UnknownSale', 'AppRevokeAccessScopesAppRevokeScopeError', 'CustomerPaypalBillingAgreement', 'PriceListAdjustmentSettings', 'StandardMetafieldDefinitionTemplate', 'MarketLocalizableContent', 'MetaobjectCapabilitiesRenderable', 'DeliveryMethod', 'MetafieldStorefrontVisibility', 'TaxonomyAttribute', 'ShopifyPaymentsTransactionSet', 'CheckoutBrandingFontSize', 'CustomerEmailMarketingConsentState', 'BulkOperation', 'MetaobjectFieldDefinition', 'DeliveryLegacyModeBlocked', 'ShopAddress', 'ImageUploadParameter', 'InventoryItem', 'ReturnLineItem', 'DiscountAutomaticApp', 'PriceRuleValidityPeriod', 'ReverseFulfillmentOrderDisposition', 'PurchasingCompany', 'AdjustmentSale', 'OrderAdjustment', 'DeliveryCountry', 'VideoSource', 'CheckoutBranding', 'CustomerSegmentMembersQuery', 'DiscountCodeApp', 'MetafieldDefinition', 'LocationSuggestedAddress', 'TipSale', 'PriceRuleShippingLineEntitlements', 'SubscriptionDiscountPercentageValue', 'ShopifyPaymentsAdjustmentOrder', 'TaxonomyChoiceListAttribute', 'DiscountAutomaticBasic', 'MutationsStagedUploadTargetGenerateUploadParameter', 'Job', 'Validation', 'DiscountPurchaseAmount', 'OrderCancellation', 'MarketCurrencySettings', 'PriceRule', 'ShippingRate', 'DiscountPercentage', 'DiscountNode', 'CountryHarmonizedSystemCode', 'CollectionRuleMetafieldCondition', 'CheckoutBrandingOrderSummarySection', 'ReverseFulfillmentOrder', 'SubscriptionShippingOptionResultSuccess', 'CheckoutBrandingDividerStyle', 'CustomerMergePreviewBlockingFields', 'DraftOrderLineItem', 'UnverifiedReturnLineItem', 'AutomaticDiscountApplication', 'ProductSale', 'CalculatedDiscountAllocation', 'MarketLocalization', 'OrderRiskAssessment', 'PriceRuleLineItemPrerequisites', 'Location', 'ChannelInformation', 'OnlineStore', 'SubscriptionBillingPolicy', 'DiscountCustomers', 'RestockingFee', 'OnlineStoreThemeFileOperationResult', 'GiftCardSale', 'CheckoutBrandingBuyerJourney', 'Refund', 'MetafieldDefinitionValidation', 'PaymentMandate', 'DeliveryProductVariantsCount', 'UrlRedirect', 'SubscriptionDeliveryOptionResultFailure', 'FulfillmentConstraintRule', 'CheckoutBrandingColorRoles', 'DraftOrderTag', 'ShopifyPaymentsPayoutSummary', 'FulfillmentOrderDestination', 'TranslatableResource', 'ProductOptionValueSwatch', 'MetaobjectAccess', 'ReverseDelivery', 'StoreCreditAccountCreditTransaction', 'CheckoutBrandingExpressCheckoutButton'}
DEBUG: Starting second pass. generated_model_map keys: dict_keys(['AbandonedCheckout', 'AbandonedCheckoutLineItem', 'Abandonment', 'AccessScope', 'AddAllProductsOperation', 'AdditionalFee', 'AdditionalFeeSale', 'AdjustmentSale', 'AllDiscountItems', 'AndroidApplication', 'ApiVersion', 'AppCatalog', 'AppFeedback', 'AppRecurringPricing', 'AppRevokeAccessScopesAppRevokeScopeError', 'AppSubscriptionDiscount', 'AppSubscriptionDiscountAmount', 'AppSubscriptionDiscountPercentage', 'AppUsagePricing', 'AppUsageRecord', 'AppleApplication', 'Article', 'ArticleAuthor', 'AutomaticDiscountApplication', 'AvailableChannelDefinitionsByChannel', 'BasicEvent', 'Blog', 'BlogFeed', 'BulkOperation', 'BundlesFeature', 'BusinessEntity', 'BusinessEntityAddress', 'BuyerExperienceConfiguration', 'CalculatedAutomaticDiscountApplication', 'CalculatedDiscountAllocation', 'CalculatedDiscountCodeApplication', 'CalculatedDraftOrder', 'CalculatedDraftOrderLineItem', 'CalculatedExchangeLineItem', 'CalculatedLineItem', 'CalculatedManualDiscountApplication', 'CalculatedOrder', 'CalculatedRestockingFee', 'CalculatedReturn', 'CalculatedReturnLineItem', 'CalculatedReturnShippingFee', 'CalculatedScriptDiscountApplication', 'CalculatedShippingLine', 'CardPaymentDetails', 'CartTransform', 'CartTransformEligibleOperations', 'CartTransformFeature', 'CashRoundingAdjustment', 'CashTrackingAdjustment', 'CashTrackingSession', 'CatalogCsvOperation', 'Channel', 'ChannelDefinition', 'ChannelInformation', 'CheckoutBranding', 'CheckoutBrandingButton', 'CheckoutBrandingButtonColorRoles', 'CheckoutBrandingBuyerJourney', 'CheckoutBrandingCartLink', 'CheckoutBrandingCheckbox', 'CheckoutBrandingChoiceList', 'CheckoutBrandingChoiceListGroup', 'CheckoutBrandingColorGlobal', 'CheckoutBrandingColorRoles', 'CheckoutBrandingColorScheme', 'CheckoutBrandingColorSchemes', 'CheckoutBrandingColors', 'CheckoutBrandingContainerDivider', 'CheckoutBrandingContent', 'CheckoutBrandingControl', 'CheckoutBrandingControlColorRoles', 'CheckoutBrandingCornerRadiusVariables', 'CheckoutBrandingCustomFont', 'CheckoutBrandingCustomizations', 'CheckoutBrandingDesignSystem', 'CheckoutBrandingDividerStyle', 'CheckoutBrandingExpressCheckout', 'CheckoutBrandingExpressCheckoutButton', 'CheckoutBrandingFontGroup', 'CheckoutBrandingFontSize', 'CheckoutBrandingFooter', 'CheckoutBrandingFooterContent', 'CheckoutBrandingGlobal', 'CheckoutBrandingHeader', 'CheckoutBrandingHeaderCartLink', 'CheckoutBrandingHeadingLevel', 'CheckoutBrandingImage', 'CheckoutBrandingLogo', 'CheckoutBrandingMain', 'CheckoutBrandingMainSection', 'CheckoutBrandingMerchandiseThumbnail', 'CheckoutBrandingOrderSummary', 'CheckoutBrandingOrderSummarySection', 'CheckoutBrandingSelect', 'CheckoutBrandingShopifyFont', 'CheckoutBrandingTextField', 'CheckoutBrandingTypography', 'CheckoutBrandingTypographyStyle', 'CheckoutBrandingTypographyStyleGlobal', 'CheckoutProfile', 'Collection', 'CollectionPublication', 'CollectionRule', 'CollectionRuleCategoryCondition', 'CollectionRuleConditions', 'CollectionRuleMetafieldCondition', 'CollectionRuleProductCategoryCondition', 'CollectionRuleSet', 'CollectionRuleTextCondition', 'CombinedListing', 'CombinedListingChild', 'Comment', 'CommentAuthor', 'CommentEvent', 'CommentEventAttachment', 'Company', 'CompanyAddress', 'CompanyContact', 'CompanyContactRole', 'CompanyContactRoleAssignment', 'CompanyLocation', 'CompanyLocationCatalog', 'CompanyLocationStaffMemberAssignment', 'Count', 'CountriesInShippingZones', 'CountryHarmonizedSystemCode', 'CurrencyFormats', 'CurrencySetting', 'Customer', 'CustomerAccountAppExtensionPage', 'CustomerAccountNativePage', 'CustomerAccountsV2', 'CustomerCreditCard', 'CustomerCreditCardBillingAddress', 'CustomerEmailAddress', 'CustomerEmailMarketingConsentState', 'CustomerJourney', 'CustomerJourneySummary', 'CustomerMergeError', 'CustomerMergePreview', 'CustomerMergePreviewAlternateFields', 'CustomerMergePreviewBlockingFields', 'CustomerMergePreviewDefaultFields', 'CustomerMergeRequest', 'CustomerMergeable', 'CustomerPaymentInstrumentBillingAddress', 'CustomerPaymentMethod', 'CustomerPaypalBillingAgreement', 'CustomerPhoneNumber', 'CustomerSegmentMember', 'CustomerSegmentMembersQuery', 'CustomerShopPayAgreement', 'CustomerSmsMarketingConsentError', 'CustomerSmsMarketingConsentState', 'CustomerStatistics', 'CustomerVisit', 'DelegateAccessToken', 'DeletionEvent', 'DeliveryAvailableService', 'DeliveryBrandedPromise', 'DeliveryCarrierService', 'DeliveryCarrierServiceAndLocations', 'DeliveryCondition', 'DeliveryCountry', 'DeliveryCountryAndZone', 'DeliveryCountryCodeOrRestOfWorld', 'DeliveryCountryCodesOrRestOfWorld', 'DeliveryCustomization', 'DeliveryCustomizationError', 'DeliveryLegacyModeBlocked', 'DeliveryLocalPickupSettings', 'DeliveryLocationGroup', 'DeliveryLocationGroupZone', 'DeliveryLocationLocalPickupSettingsError', 'DeliveryMethod', 'DeliveryMethodAdditionalInformation', 'DeliveryMethodDefinition', 'DeliveryMethodDefinitionCounts', 'DeliveryParticipant', 'DeliveryParticipantService', 'DeliveryProductVariantsCount', 'DeliveryProfile', 'DeliveryProfileItem', 'DeliveryProfileLocationGroup', 'DeliveryPromiseProvider', 'DeliveryProvince', 'DeliveryRateDefinition', 'DeliverySetting', 'DeliveryZone', 'DepositPercentage', 'DiscountAmount', 'DiscountAutomaticApp', 'DiscountAutomaticBasic', 'DiscountAutomaticBxgy', 'DiscountAutomaticFreeShipping', 'DiscountAutomaticNode', 'DiscountCodeApp', 'DiscountCodeApplication', 'DiscountCodeBasic', 'DiscountCodeBxgy', 'DiscountCodeFreeShipping', 'DiscountCodeNode', 'DiscountCollections', 'DiscountCombinesWith', 'DiscountCountries', 'DiscountCountryAll', 'DiscountCustomerAll', 'DiscountCustomerBuys', 'DiscountCustomerGets', 'DiscountCustomerSegments', 'DiscountCustomers', 'DiscountMinimumQuantity', 'DiscountMinimumSubtotal', 'DiscountNode', 'DiscountOnQuantity', 'DiscountPercentage', 'DiscountProducts', 'DiscountPurchaseAmount', 'DiscountQuantity', 'DiscountRedeemCode', 'DiscountRedeemCodeBulkCreation', 'DiscountRedeemCodeBulkCreationCode', 'DiscountShareableUrl', 'Domain', 'DomainLocalization', 'DraftOrder', 'DraftOrderAppliedDiscount', 'DraftOrderBundleAddedWarning', 'DraftOrderDiscountNotAppliedWarning', 'DraftOrderLineItem', 'DraftOrderPlatformDiscount', 'DraftOrderPlatformDiscountAllocation', 'DraftOrderTag', 'Duty', 'DutySale', 'EditableProperty', 'ExchangeLineItem', 'ExchangeV2', 'ExchangeV2Additions', 'ExchangeV2LineItem', 'ExchangeV2Returns', 'ExternalVideo', 'FailedRequirement', 'FeeSale', 'FileError', 'FilterOption', 'FinancialSummaryDiscountAllocation', 'FinancialSummaryDiscountApplication', 'Fulfillment', 'FulfillmentConstraintRule', 'FulfillmentEvent', 'FulfillmentHold', 'FulfillmentLineItem', 'FulfillmentOrder', 'FulfillmentOrderAssignedLocation', 'FulfillmentOrderDestination', 'FulfillmentOrderInternationalDuties', 'FulfillmentOrderLineItem', 'FulfillmentOrderLineItemFinancialSummary', 'FulfillmentOrderLineItemWarning', 'FulfillmentOrderLocationForMove', 'FulfillmentOrderMerchantRequest', 'FulfillmentOrderMergeResult', 'FulfillmentOrderSplitResult', 'FulfillmentOrderSupportedAction', 'FulfillmentOriginAddress', 'FulfillmentService', 'FulfillmentTrackingInfo', 'FunctionsAppBridge', 'FunctionsErrorHistory', 'GenericFile', 'GiftCard', 'GiftCardCreditTransaction', 'GiftCardDebitTransaction', 'GiftCardRecipient', 'GiftCardSale', 'ImageUploadParameter', 'InventoryAdjustmentGroup', 'InventoryChange', 'InventoryItem', 'InventoryItemMeasurement', 'InventoryLevel', 'InventoryProperties', 'InventoryQuantity', 'InventoryQuantityName', 'InventoryScheduledChange', 'Job', 'LimitedPendingOrderCount', 'LineItem', 'LineItemGroup', 'LineItemSellingPlan', 'LinkedMetafield', 'LocalPaymentMethodsPaymentDetails', 'Locale', 'LocalizationExtension', 'Location', 'LocationAddress', 'LocationSuggestedAddress', 'ManualDiscountApplication', 'Market', 'MarketCatalog', 'MarketCurrencySettings', 'MarketLocalizableContent', 'MarketLocalizableResource', 'MarketLocalization', 'MarketRegionCountry', 'MarketWebPresence', 'MarketWebPresenceRootUrl', 'MarketingActivity', 'MarketingActivityExtensionAppErrors', 'MarketingBudget', 'MarketingEngagement', 'MarketingEvent', 'MediaError', 'MediaImage', 'MediaImageOriginalSource', 'MediaPreviewImage', 'MediaWarning', 'Menu', 'MenuItem', 'MerchantApprovalSignals', 'Metafield', 'MetafieldAccess', 'MetafieldAccessGrant', 'MetafieldCapabilities', 'MetafieldCapabilityAdminFilterable', 'MetafieldCapabilitySmartCollectionCondition', 'MetafieldDefinition', 'MetafieldDefinitionConstraintValue', 'MetafieldDefinitionConstraints', 'MetafieldDefinitionSupportedValidation', 'MetafieldDefinitionType', 'MetafieldDefinitionValidation', 'MetafieldIdentifier', 'MetafieldRelation', 'MetafieldStorefrontVisibility', 'Metaobject', 'MetaobjectAccess', 'MetaobjectCapabilities', 'MetaobjectCapabilitiesOnlineStore', 'MetaobjectCapabilitiesPublishable', 'MetaobjectCapabilitiesRenderable', 'MetaobjectCapabilitiesTranslatable', 'MetaobjectCapabilityData', 'MetaobjectCapabilityDataOnlineStore', 'MetaobjectCapabilityDataPublishable', 'MetaobjectCapabilityDefinitionDataOnlineStore', 'MetaobjectCapabilityDefinitionDataRenderable', 'MetaobjectDefinition', 'MetaobjectField', 'MetaobjectFieldDefinition', 'MetaobjectThumbnail', 'Model3d', 'Model3dBoundingBox', 'Model3dSource', 'MutationsStagedUploadTargetGenerateUploadParameter', 'NavigationItem', 'OnlineStore', 'OnlineStorePasswordProtection', 'OnlineStoreTheme', 'OnlineStoreThemeFile', 'OnlineStoreThemeFileBodyBase64', 'OnlineStoreThemeFileBodyText', 'OnlineStoreThemeFileBodyUrl', 'OnlineStoreThemeFileOperationResult', 'OnlineStoreThemeFileReadResult', 'OnlineStoreThemeFilesUserErrors', 'Order', 'OrderAdjustment', 'OrderAgreement', 'OrderApp', 'OrderCancellation', 'OrderDisputeSummary', 'OrderEditAgreement', 'OrderPaymentCollectionDetails', 'OrderPaymentStatus', 'OrderRisk', 'OrderRiskAssessment', 'OrderRiskSummary', 'OrderStagedChangeAddCustomItem', 'OrderStagedChangeAddLineItemDiscount', 'OrderStagedChangeAddShippingLine', 'OrderStagedChangeAddVariant', 'OrderStagedChangeDecrementItem', 'OrderStagedChangeIncrementItem', 'OrderStagedChangeRemoveShippingLine', 'OrderTransaction', 'Page', 'PaymentCustomization', 'PaymentCustomizationError', 'PaymentMandate', 'PaymentSchedule', 'PaymentSettings', 'PaymentTerms', 'PaymentTermsTemplate', 'PriceList', 'PriceListAdjustment', 'PriceListAdjustmentSettings', 'PriceListParent', 'PriceListPrice', 'PriceRule', 'PriceRuleCustomerSelection', 'PriceRuleDiscountCode', 'PriceRuleEntitlementToPrerequisiteQuantityRatio', 'PriceRuleFixedAmountValue', 'PriceRuleItemEntitlements', 'PriceRuleLineItemPrerequisites', 'PriceRuleMoneyRange', 'PriceRulePercentValue', 'PriceRulePrerequisiteToEntitlementQuantityRatio', 'PriceRuleQuantityRange', 'PriceRuleShareableUrl', 'PriceRuleShippingLineEntitlements', 'PriceRuleValidityPeriod', 'PricingPercentageValue', 'PrivateMetafield', 'Product', 'ProductBundleComponent', 'ProductBundleComponentOptionSelection', 'ProductBundleComponentOptionSelectionValue', 'ProductBundleComponentQuantityOption', 'ProductBundleComponentQuantityOptionValue', 'ProductBundleOperation', 'ProductCategory', 'ProductCompareAtPriceRange', 'ProductContextualPricing', 'ProductDeleteOperation', 'ProductDuplicateJob', 'ProductDuplicateOperation', 'ProductFeed', 'ProductOption', 'ProductOptionValue', 'ProductOptionValueSwatch', 'ProductPriceRange', 'ProductPriceRangeV2', 'ProductPublication', 'ProductResourceFeedback', 'ProductSale', 'ProductSetOperation', 'ProductTaxonomyNode', 'ProductVariant', 'ProductVariantComponent', 'ProductVariantContextualPricing', 'ProductVariantPricePair', 'Publication', 'PublicationResourceOperation', 'PurchasingCompany', 'QuantityPriceBreak', 'QuantityRule', 'Refund', 'RefundAgreement', 'RefundDuty', 'RefundLineItem', 'RefundShippingLine', 'ResourceAlert', 'ResourceAlertAction', 'ResourceFeedback', 'ResourcePublication', 'ResourcePublicationV2', 'RestockingFee', 'RestrictedForResource', 'Return', 'ReturnAgreement', 'ReturnDecline', 'ReturnLineItem', 'ReturnShippingFee', 'ReturnableFulfillment', 'ReturnableFulfillmentLineItem', 'ReverseDelivery', 'ReverseDeliveryLabelV2', 'ReverseDeliveryLineItem', 'ReverseDeliveryShippingDeliverable', 'ReverseDeliveryTrackingV2', 'ReverseFulfillmentOrder', 'ReverseFulfillmentOrderDisposition', 'ReverseFulfillmentOrderLineItem', 'ReverseFulfillmentOrderThirdPartyConfirmation', 'RiskFact', 'SEO', 'SaleAdditionalFee', 'SaleTax', 'SavedSearch', 'ScriptDiscountApplication', 'ScriptTag', 'SearchFilterOptions', 'SearchResult', 'Segment', 'SegmentAttributeStatistics', 'SegmentEventFilterParameter', 'SegmentMembership', 'SegmentMembershipResponse', 'SegmentMigration', 'SegmentStatistics', 'SegmentValue', 'SelectedOption', 'SellingPlan', 'SellingPlanAnchor', 'SellingPlanCheckoutCharge', 'SellingPlanCheckoutChargePercentageValue', 'SellingPlanFixedBillingPolicy', 'SellingPlanFixedDeliveryPolicy', 'SellingPlanFixedPricingPolicy', 'SellingPlanGroup', 'SellingPlanInventoryPolicy', 'SellingPlanPricingPolicyPercentageValue', 'SellingPlanRecurringBillingPolicy', 'SellingPlanRecurringDeliveryPolicy', 'SellingPlanRecurringPricingPolicy', 'ServerPixel', 'ShippingLine', 'ShippingLineSale', 'ShippingRate', 'ShippingRefund', 'Shop', 'ShopAddress', 'ShopAlert', 'ShopAlertAction', 'ShopBillingPreferences', 'ShopFeatures', 'ShopLocale', 'ShopPayInstallmentsPaymentDetails', 'ShopPolicy', 'ShopResourceLimits', 'ShopifyFunction', 'ShopifyPaymentsAccount', 'ShopifyPaymentsAdjustmentOrder', 'ShopifyPaymentsAssociatedOrder', 'ShopifyPaymentsBalanceTransaction', 'ShopifyPaymentsBalanceTransactionAssociatedPayout', 'ShopifyPaymentsBankAccount', 'ShopifyPaymentsDefaultChargeStatementDescriptor', 'ShopifyPaymentsDispute', 'ShopifyPaymentsDisputeEvidence', 'ShopifyPaymentsDisputeFileUpload', 'ShopifyPaymentsDisputeFulfillment', 'ShopifyPaymentsDisputeReasonDetails', 'ShopifyPaymentsExtendedAuthorization', 'ShopifyPaymentsJpChargeStatementDescriptor', 'ShopifyPaymentsPayout', 'ShopifyPaymentsPayoutSchedule', 'ShopifyPaymentsPayoutSummary', 'ShopifyPaymentsRefundSet', 'ShopifyPaymentsToolingProviderPayout', 'ShopifyPaymentsTransactionSet', 'ShopifyProtectOrderEligibility', 'ShopifyProtectOrderSummary', 'StaffMember', 'StaffMemberPrivateData', 'StagedMediaUploadTarget', 'StagedUploadParameter', 'StagedUploadTarget', 'StandardMetafieldDefinitionTemplate', 'StandardizedProductType', 'StoreCreditAccount', 'StoreCreditAccountCreditTransaction', 'StoreCreditAccountDebitRevertTransaction', 'StoreCreditAccountDebitTransaction', 'StoreCreditAccountExpirationTransaction', 'StorefrontAccessToken', 'SubscriptionAppliedCodeDiscount', 'SubscriptionBillingAttempt', 'SubscriptionBillingCycle', 'SubscriptionBillingCycleEditedContract', 'SubscriptionBillingPolicy', 'SubscriptionContract', 'SubscriptionCyclePriceAdjustment', 'SubscriptionDeliveryMethodLocalDelivery', 'SubscriptionDeliveryMethodLocalDeliveryOption', 'SubscriptionDeliveryMethodPickup', 'SubscriptionDeliveryMethodPickupOption', 'SubscriptionDeliveryMethodShipping', 'SubscriptionDeliveryMethodShippingOption', 'SubscriptionDeliveryOptionResultFailure', 'SubscriptionDeliveryOptionResultSuccess', 'SubscriptionDeliveryPolicy', 'SubscriptionDiscountAllocation', 'SubscriptionDiscountEntitledLines', 'SubscriptionDiscountFixedAmountValue', 'SubscriptionDiscountPercentageValue', 'SubscriptionDraft', 'SubscriptionLine', 'SubscriptionLocalDeliveryOption', 'SubscriptionMailingAddress', 'SubscriptionManualDiscount', 'SubscriptionPickupOption', 'SubscriptionPricingPolicy', 'SubscriptionShippingOption', 'SubscriptionShippingOptionResultFailure', 'SubscriptionShippingOptionResultSuccess', 'SuggestedOrderTransaction', 'SuggestedRefund', 'SuggestedReturnRefund', 'TaxAppConfiguration', 'Taxonomy', 'TaxonomyAttribute', 'TaxonomyCategory', 'TaxonomyChoiceListAttribute', 'TaxonomyMeasurementAttribute', 'TaxonomyValue', 'TenderTransaction', 'TenderTransactionCreditCardDetails', 'TipSale', 'TransactionFee', 'TranslatableContent', 'TranslatableResource', 'Translation', 'TypedAttribute', 'UTMParameters', 'UnitPriceMeasurement', 'UnknownSale', 'UnverifiedReturnLineItem', 'UrlRedirect', 'UrlRedirectImport', 'UrlRedirectImportPreview', 'Validation', 'VaultCreditCard', 'VaultPaypalBillingAgreement', 'Vector3', 'Video', 'VideoSource', 'WebPixel', 'WebhookEventBridgeEndpoint', 'WebhookHttpEndpoint', 'WebhookPubSubEndpoint', 'WebhookSubscription', 'Weight'])
DEBUG: Initializing generated_model_map. ProductImage in types: False
DEBUG: syncable_entity_names after ProductImage handling: {'ReverseFulfillmentOrder', 'ShopifyPaymentsBalanceTransactionAssociatedPayout', 'DeliveryLegacyModeBlocked', 'LocalPaymentMethodsPaymentDetails', 'WebhookEventBridgeEndpoint', 'FulfillmentConstraintRule', 'SubscriptionLocalDeliveryOption', 'CustomerPaypalBillingAgreement', 'CollectionRuleTextCondition', 'SuggestedOrderTransaction', 'CustomerCreditCard', 'MarketingActivityExtensionAppErrors', 'BusinessEntityAddress', 'FinancialSummaryDiscountApplication', 'RestrictedForResource', 'CustomerSegmentMembersQuery', 'ShopLocale', 'OrderCancellation', 'PriceRuleCustomerSelection', 'MediaError', 'ShopifyPaymentsDisputeEvidence', 'FulfillmentHold', 'StaffMemberPrivateData', 'StaffMember', 'Model3d', 'ShopifyPaymentsRefundSet', 'CustomerPaymentInstrumentBillingAddress', 'InventoryScheduledChange', 'StagedUploadTarget', 'CustomerMergeRequest', 'CheckoutBrandingMain', 'CheckoutBrandingFontSize', 'Fulfillment', 'DeliveryLocationGroupZone', 'LimitedPendingOrderCount', 'BulkOperation', 'ProductBundleComponentQuantityOption', 'PriceList', 'CustomerMergeable', 'OrderApp', 'SavedSearch', 'MetaobjectCapabilityDefinitionDataOnlineStore', 'MarketLocalizableResource', 'FulfillmentOrderDestination', 'CheckoutBrandingHeaderCartLink', 'TypedAttribute', 'MarketRegionCountry', 'ProductPublication', 'CustomerJourneySummary', 'DeliveryCarrierServiceAndLocations', 'WebhookHttpEndpoint', 'CommentEvent', 'PriceRuleItemEntitlements', 'DeliveryLocationGroup', 'PaymentSchedule', 'CustomerPhoneNumber', 'CollectionRuleConditions', 'ShopBillingPreferences', 'CompanyContact', 'ShopifyPaymentsExtendedAuthorization', 'TenderTransaction', 'ReverseFulfillmentOrderThirdPartyConfirmation', 'Blog', 'LineItemSellingPlan', 'MediaWarning', 'ExchangeV2LineItem', 'CustomerShopPayAgreement', 'Article', 'CustomerAccountNativePage', 'CheckoutBrandingDesignSystem', 'MetafieldCapabilitySmartCollectionCondition', 'ProductContextualPricing', 'CheckoutBrandingChoiceList', 'SuggestedReturnRefund', 'StandardizedProductType', 'CheckoutBrandingHeadingLevel', 'MarketWebPresenceRootUrl', 'PriceRuleQuantityRange', 'StagedUploadParameter', 'DiscountRedeemCodeBulkCreation', 'ExchangeV2Returns', 'CheckoutBrandingMainSection', 'ReturnDecline', 'MarketWebPresence', 'FulfillmentOrderSplitResult', 'MarketCatalog', 'DraftOrderPlatformDiscount', 'SubscriptionDeliveryOptionResultFailure', 'ProductOptionValue', 'DeliveryPromiseProvider', 'FulfillmentOrderSupportedAction', 'DeliveryCountryAndZone', 'InventoryAdjustmentGroup', 'PublicationResourceOperation', 'OrderRisk', 'PriceRuleMoneyRange', 'CalculatedReturnShippingFee', 'TaxonomyChoiceListAttribute', 'CheckoutBrandingFontGroup', 'DiscountNode', 'OrderStagedChangeRemoveShippingLine', 'CatalogCsvOperation', 'GiftCardDebitTransaction', 'DeliveryLocalPickupSettings', 'Locale', 'CheckoutBrandingColorSchemes', 'PriceRuleShippingLineEntitlements', 'ReturnableFulfillmentLineItem', 'DeliveryCustomization', 'CheckoutBrandingBuyerJourney', 'PriceRuleShareableUrl', 'SubscriptionMailingAddress', 'MetaobjectCapabilityData', 'TaxonomyAttribute', 'LineItem', 'DraftOrder', 'Validation', 'CheckoutBrandingColorRoles', 'ProductPriceRangeV2', 'InventoryItemMeasurement', 'ApiVersion', 'Weight', 'BlogFeed', 'FulfillmentOriginAddress', 'OrderRiskSummary', 'Domain', 'SubscriptionShippingOptionResultSuccess', 'AvailableChannelDefinitionsByChannel', 'SellingPlanFixedBillingPolicy', 'UTMParameters', 'PriceRuleValidityPeriod', 'DraftOrderTag', 'PriceRulePrerequisiteToEntitlementQuantityRatio', 'Publication', 'DiscountProducts', 'DeliveryLocationLocalPickupSettingsError', 'OrderTransaction', 'CollectionRuleSet', 'SEO', 'PriceListParent', 'CheckoutBrandingColorGlobal', 'VideoSource', 'InventoryQuantity', 'MarketLocalization', 'ImageUploadParameter', 'ShopPolicy', 'AllDiscountItems', 'DeliveryAvailableService', 'ProductOption', 'CheckoutBrandingOrderSummary', 'Customer', 'SegmentValue', 'QuantityPriceBreak', 'CheckoutBrandingFooterContent', 'CollectionRuleCategoryCondition', 'MetaobjectCapabilitiesOnlineStore', 'OnlineStoreThemeFileReadResult', 'DiscountCodeFreeShipping', 'ReturnLineItem', 'StorefrontAccessToken', 'CheckoutBrandingButtonColorRoles', 'DeliveryCountry', 'FulfillmentTrackingInfo', 'CalculatedAutomaticDiscountApplication', 'CalculatedRestockingFee', 'ResourcePublicationV2', 'BasicEvent', 'CountriesInShippingZones', 'RefundDuty', 'FulfillmentOrderLocationForMove', 'ProductVariantPricePair', 'SellingPlanPricingPolicyPercentageValue', 'ServerPixel', 'CheckoutProfile', 'DeliveryProductVariantsCount', 'WebhookPubSubEndpoint', 'OrderDisputeSummary', 'ProductVariant', 'SubscriptionBillingAttempt', 'CustomerAccountsV2', 'AppUsagePricing', 'DiscountPercentage', 'OrderStagedChangeAddVariant', 'CollectionRuleProductCategoryCondition', 'FinancialSummaryDiscountAllocation', 'DiscountOnQuantity', 'ChannelInformation', 'OnlineStoreThemeFileBodyBase64', 'GenericFile', 'MetaobjectDefinition', 'SubscriptionDeliveryPolicy', 'TaxonomyValue', 'LocationSuggestedAddress', 'MetaobjectThumbnail', 'SegmentMembership', 'SegmentEventFilterParameter', 'AppSubscriptionDiscount', 'FulfillmentOrderLineItem', 'DiscountCustomers', 'ShopResourceLimits', 'MerchantApprovalSignals', 'Collection', 'CalculatedLineItem', 'DepositPercentage', 'Market', 'FulfillmentOrderLineItemFinancialSummary', 'Duty', 'CalculatedDiscountAllocation', 'DiscountCollections', 'TaxAppConfiguration', 'DeliveryProfile', 'DraftOrderDiscountNotAppliedWarning', 'MarketingEngagement', 'OrderStagedChangeIncrementItem', 'Count', 'CheckoutBrandingMerchandiseThumbnail', 'DeliveryBrandedPromise', 'AppCatalog', 'SellingPlanCheckoutCharge', 'VaultCreditCard', 'ProductDuplicateJob', 'Order', 'ProductDeleteOperation', 'CheckoutBrandingShopifyFont', 'DeliveryMethodDefinitionCounts', 'ReverseDelivery', 'AppleApplication', 'ShopifyPaymentsDisputeFulfillment', 'DiscountCustomerGets', 'FilterOption', 'InventoryChange', 'SearchResult', 'MarketingBudget', 'SubscriptionPickupOption', 'CheckoutBrandingOrderSummarySection', 'StagedMediaUploadTarget', 'MetaobjectCapabilities', 'MediaPreviewImage', 'StoreCreditAccountDebitRevertTransaction', 'DeliverySetting', 'ReverseDeliveryLineItem', 'Taxonomy', 'FulfillmentOrderMergeResult', 'OrderPaymentStatus', 'OrderStagedChangeAddLineItemDiscount', 'ProductResourceFeedback', 'SubscriptionPricingPolicy', 'CheckoutBrandingContent', 'SubscriptionDeliveryMethodLocalDeliveryOption', 'CompanyLocation', 'SubscriptionBillingPolicy', 'DiscountQuantity', 'TransactionFee', 'CustomerEmailAddress', 'FunctionsAppBridge', 'DiscountCustomerAll', 'DomainLocalization', 'CheckoutBrandingColorScheme', 'ShopifyPaymentsDisputeFileUpload', 'FailedRequirement', 'DiscountMinimumSubtotal', 'ProductOptionValueSwatch', 'SubscriptionDeliveryMethodShippingOption', 'Abandonment', 'FulfillmentService', 'SellingPlanAnchor', 'SubscriptionShippingOption', 'PaymentSettings', 'RefundAgreement', 'ManualDiscountApplication', 'Metafield', 'CashRoundingAdjustment', 'PaymentTerms', 'Shop', 'Comment', 'MutationsStagedUploadTargetGenerateUploadParameter', 'DeliveryCustomizationError', 'ExchangeV2', 'CountryHarmonizedSystemCode', 'BundlesFeature', 'ResourcePublication', 'DeliveryMethod', 'Job', 'ShopifyPaymentsDisputeReasonDetails', 'AccessScope', 'LineItemGroup', 'Model3dSource', 'Company', 'CompanyAddress', 'SuggestedRefund', 'DiscountCodeApp', 'SellingPlanGroup', 'CartTransformEligibleOperations', 'CardPaymentDetails', 'ReverseDeliveryShippingDeliverable', 'ProductBundleComponentOptionSelection', 'MetafieldDefinitionSupportedValidation', 'OrderStagedChangeDecrementItem', 'DeletionEvent', 'CurrencySetting', 'MetaobjectFieldDefinition', 'CustomerMergePreview', 'Channel', 'CustomerMergePreviewDefaultFields', 'PaymentMandate', 'ShopifyPaymentsAssociatedOrder', 'DiscountCountryAll', 'MediaImageOriginalSource', 'CompanyLocationStaffMemberAssignment', 'MetafieldAccess', 'MetaobjectCapabilitiesRenderable', 'PriceRuleDiscountCode', 'ProductSetOperation', 'ProductVariantContextualPricing', 'CustomerJourney', 'ExternalVideo', 'CustomerMergeError', 'ProductFeed', 'SegmentMigration', 'ShopPayInstallmentsPaymentDetails', 'FulfillmentLineItem', 'TipSale', 'FulfillmentEvent', 'CheckoutBrandingTypography', 'SegmentStatistics', 'ReverseFulfillmentOrderDisposition', 'TaxonomyMeasurementAttribute', 'WebPixel', 'CustomerAccountAppExtensionPage', 'RiskFact', 'SellingPlanRecurringBillingPolicy', 'TenderTransactionCreditCardDetails', 'RefundShippingLine', 'UrlRedirectImport', 'SubscriptionDiscountPercentageValue', 'CustomerSmsMarketingConsentError', 'CheckoutBrandingCheckbox', 'PaymentCustomizationError', 'CommentAuthor', 'CombinedListingChild', 'SellingPlanInventoryPolicy', 'UnverifiedReturnLineItem', 'DiscountAutomaticApp', 'ChannelDefinition', 'CompanyLocationCatalog', 'DeliveryProfileLocationGroup', 'CalculatedScriptDiscountApplication', 'MetaobjectCapabilityDefinitionDataRenderable', 'PriceRuleFixedAmountValue', 'ShopifyPaymentsBankAccount', 'QuantityRule', 'CartTransform', 'CommentEventAttachment', 'MediaImage', 'ShopifyPaymentsDefaultChargeStatementDescriptor', 'DiscountShareableUrl', 'MetafieldDefinitionConstraints', 'ShopifyFunction', 'CustomerCreditCardBillingAddress', 'ExchangeV2Additions', 'DeliveryParticipant', 'OnlineStoreThemeFilesUserErrors', 'DiscountCodeNode', 'FulfillmentOrderInternationalDuties', 'CalculatedDraftOrder', 'RefundLineItem', 'ProductSale', 'OnlineStoreThemeFileBodyText', 'SubscriptionAppliedCodeDiscount', 'DeliveryMethodAdditionalInformation', 'SellingPlanFixedDeliveryPolicy', 'CheckoutBrandingHeader', 'CheckoutBrandingChoiceListGroup', 'OnlineStoreThemeFile', 'CashTrackingAdjustment', 'Model3dBoundingBox', 'CheckoutBrandingTextField', 'ShopifyPaymentsAdjustmentOrder', 'ProductTaxonomyNode', 'MetafieldAccessGrant', 'OrderAdjustment', 'PriceRuleLineItemPrerequisites', 'ResourceAlertAction', 'Video', 'PurchasingCompany', 'OnlineStoreTheme', 'SellingPlanRecurringPricingPolicy', 'CartTransformFeature', 'DeliveryCarrierService', 'DiscountCustomerBuys', 'SellingPlanRecurringDeliveryPolicy', 'AppRecurringPricing', 'ProductBundleComponentOptionSelectionValue', 'ShopifyPaymentsPayoutSchedule', 'DeliveryCountryCodesOrRestOfWorld', 'VaultPaypalBillingAgreement', 'ShippingLine', 'DeliveryZone', 'InventoryItem', 'ShopifyPaymentsAccount', 'DiscountCodeBxgy', 'DutySale', 'ShopifyPaymentsToolingProviderPayout', 'DraftOrderPlatformDiscountAllocation', 'NavigationItem', 'DiscountAutomaticBasic', 'LinkedMetafield', 'CalculatedDiscountCodeApplication', 'Segment', 'CheckoutBrandingControlColorRoles', 'Metaobject', 'GiftCardRecipient', 'ProductPriceRange', 'CheckoutBrandingControl', 'MarketingActivity', 'FileError', 'UnitPriceMeasurement', 'SubscriptionDiscountAllocation', 'BusinessEntity', 'CheckoutBrandingCornerRadiusVariables', 'DiscountRedeemCodeBulkCreationCode', 'CheckoutBrandingCartLink', 'InventoryQuantityName', 'AppSubscriptionDiscountAmount', 'Return', 'Vector3', 'UnknownSale', 'CompanyContactRoleAssignment', 'MetafieldCapabilities', 'FulfillmentOrder', 'CustomerMergePreviewAlternateFields', 'Refund', 'StoreCreditAccount', 'EditableProperty', 'StandardMetafieldDefinitionTemplate', 'CalculatedDraftOrderLineItem', 'Product', 'SubscriptionBillingCycle', 'CustomerPaymentMethod', 'SellingPlan', 'CheckoutBrandingCustomizations', 'CheckoutBrandingExpressCheckout', 'CustomerSmsMarketingConsentState', 'DiscountAutomaticBxgy', 'OnlineStore', 'ProductCompareAtPriceRange', 'SubscriptionDiscountFixedAmountValue', 'MarketLocalizableContent', 'ReverseFulfillmentOrderLineItem', 'ShippingLineSale', 'CheckoutBrandingFooter', 'CheckoutBrandingTypographyStyleGlobal', 'LocationAddress', 'OrderPaymentCollectionDetails', 'DiscountCustomerSegments', 'CalculatedOrder', 'DeliveryMethodDefinition', 'AppRevokeAccessScopesAppRevokeScopeError', 'OrderStagedChangeAddShippingLine', 'CustomerEmailMarketingConsentState', 'Page', 'CompanyContactRole', 'SubscriptionDeliveryMethodPickup', 'AppSubscriptionDiscountPercentage', 'ProductVariantComponent', 'PrivateMetafield', 'ProductBundleComponentQuantityOptionValue', 'CombinedListing', 'RestockingFee', 'LocalizationExtension', 'ResourceFeedback', 'ProductDuplicateOperation', 'SearchFilterOptions', 'DiscountAutomaticFreeShipping', 'ProductBundleOperation', 'BuyerExperienceConfiguration', 'DiscountAmount', 'DelegateAccessToken', 'DeliveryRateDefinition', 'CollectionPublication', 'WebhookSubscription', 'SubscriptionContract', 'CheckoutBrandingExpressCheckoutButton', 'SaleTax', 'ScriptTag', 'ShopifyPaymentsBalanceTransaction', 'SaleAdditionalFee', 'PriceRule', 'CollectionRule', 'TaxonomyCategory', 'InventoryProperties', 'DiscountPurchaseAmount', 'MetaobjectCapabilitiesTranslatable', 'AbandonedCheckout', 'CalculatedReturnLineItem', 'DiscountMinimumQuantity', 'Location', 'PriceListPrice', 'MetaobjectCapabilitiesPublishable', 'FulfillmentOrderMerchantRequest', 'StoreCreditAccountCreditTransaction', 'ProductCategory', 'FeeSale', 'DraftOrderLineItem', 'ShopFeatures', 'ShopifyPaymentsPayoutSummary', 'PriceRulePercentValue', 'PricingPercentageValue', 'CheckoutBrandingButton', 'SubscriptionDeliveryOptionResultSuccess', 'MetafieldDefinition', 'FulfillmentOrderLineItemWarning', 'CollectionRuleMetafieldCondition', 'Menu', 'ReturnShippingFee', 'ArticleAuthor', 'UrlRedirectImportPreview', 'CheckoutBrandingDividerStyle', 'DeliveryProvince', 'CustomerSegmentMember', 'ShopifyPaymentsDispute', 'AppFeedback', 'CurrencyFormats', 'ShopAlertAction', 'CalculatedManualDiscountApplication', 'MetafieldDefinitionConstraintValue', 'MetafieldStorefrontVisibility', 'MenuItem', 'CashTrackingSession', 'ShopifyProtectOrderEligibility', 'PaymentCustomization', 'MetaobjectField', 'SubscriptionDiscountEntitledLines', 'OnlineStorePasswordProtection', 'ProductBundleComponent', 'CalculatedReturn', 'SubscriptionBillingCycleEditedContract', 'OrderEditAgreement', 'CalculatedExchangeLineItem', 'StoreCreditAccountExpirationTransaction', 'MetafieldDefinitionType', 'DiscountRedeemCode', 'ShopifyPaymentsJpChargeStatementDescriptor', 'PriceListAdjustmentSettings', 'PriceRuleEntitlementToPrerequisiteQuantityRatio', 'CustomerVisit', 'DiscountAutomaticNode', 'SubscriptionDraft', 'AutomaticDiscountApplication', 'StoreCreditAccountDebitTransaction', 'GiftCard', 'SubscriptionShippingOptionResultFailure', 'OnlineStoreThemeFileBodyUrl', 'ResourceAlert', 'PaymentTermsTemplate', 'DeliveryProfileItem', 'ReturnableFulfillment', 'CheckoutBrandingLogo', 'InventoryLevel', 'ShippingRate', 'FunctionsErrorHistory', 'OrderRiskAssessment', 'DeliveryCountryCodeOrRestOfWorld', 'DiscountCombinesWith', 'ShopifyPaymentsPayout', 'AndroidApplication', 'CustomerMergePreviewBlockingFields', 'MarketingEvent', 'MetafieldDefinitionValidation', 'SellingPlanFixedPricingPolicy', 'UrlRedirect', 'MetaobjectAccess', 'AdditionalFeeSale', 'CheckoutBrandingContainerDivider', 'CheckoutBrandingGlobal', 'SubscriptionDeliveryMethodLocalDelivery', 'MarketCurrencySettings', 'ShopAlert', 'MetafieldIdentifier', 'ReverseDeliveryTrackingV2', 'GiftCardSale', 'CheckoutBrandingImage', 'CheckoutBrandingSelect', 'AdditionalFee', 'CheckoutBrandingTypographyStyle', 'OrderStagedChangeAddCustomItem', 'ShopifyPaymentsTransactionSet', 'SubscriptionDeliveryMethodPickupOption', 'CustomerStatistics', 'AddAllProductsOperation', 'CheckoutBrandingColors', 'MetafieldRelation', 'PriceListAdjustment', 'FulfillmentOrderAssignedLocation', 'ShippingRefund', 'DraftOrderBundleAddedWarning', 'TranslatableResource', 'AbandonedCheckoutLineItem', 'MetaobjectCapabilityDataOnlineStore', 'DeliveryParticipantService', 'GiftCardCreditTransaction', 'OnlineStoreThemeFileOperationResult', 'CalculatedShippingLine', 'OrderAgreement', 'ExchangeLineItem', 'ShopAddress', 'ReverseDeliveryLabelV2', 'MetaobjectCapabilityDataPublishable', 'DeliveryCondition', 'CheckoutBranding', 'DiscountCodeApplication', 'SegmentAttributeStatistics', 'SelectedOption', 'ReturnAgreement', 'SubscriptionManualDiscount', 'Translation', 'CheckoutBrandingCustomFont', 'DiscountCountries', 'DraftOrderAppliedDiscount', 'SubscriptionLine', 'SegmentMembershipResponse', 'AdjustmentSale', 'ScriptDiscountApplication', 'SubscriptionDeliveryMethodShipping', 'ShopifyProtectOrderSummary', 'TranslatableContent', 'AppUsageRecord', 'DiscountCodeBasic', 'MetafieldCapabilityAdminFilterable', 'SubscriptionCyclePriceAdjustment', 'SellingPlanCheckoutChargePercentageValue'}
DEBUG: Starting second pass. generated_model_map keys: dict_keys(['AbandonedCheckout', 'AbandonedCheckoutLineItem', 'Abandonment', 'AccessScope', 'AddAllProductsOperation', 'AdditionalFee', 'AdditionalFeeSale', 'AdjustmentSale', 'AllDiscountItems', 'AndroidApplication', 'ApiVersion', 'AppCatalog', 'AppFeedback', 'AppRecurringPricing', 'AppRevokeAccessScopesAppRevokeScopeError', 'AppSubscriptionDiscount', 'AppSubscriptionDiscountAmount', 'AppSubscriptionDiscountPercentage', 'AppUsagePricing', 'AppUsageRecord', 'AppleApplication', 'Article', 'ArticleAuthor', 'AutomaticDiscountApplication', 'AvailableChannelDefinitionsByChannel', 'BasicEvent', 'Blog', 'BlogFeed', 'BulkOperation', 'BundlesFeature', 'BusinessEntity', 'BusinessEntityAddress', 'BuyerExperienceConfiguration', 'CalculatedAutomaticDiscountApplication', 'CalculatedDiscountAllocation', 'CalculatedDiscountCodeApplication', 'CalculatedDraftOrder', 'CalculatedDraftOrderLineItem', 'CalculatedExchangeLineItem', 'CalculatedLineItem', 'CalculatedManualDiscountApplication', 'CalculatedOrder', 'CalculatedRestockingFee', 'CalculatedReturn', 'CalculatedReturnLineItem', 'CalculatedReturnShippingFee', 'CalculatedScriptDiscountApplication', 'CalculatedShippingLine', 'CardPaymentDetails', 'CartTransform', 'CartTransformEligibleOperations', 'CartTransformFeature', 'CashRoundingAdjustment', 'CashTrackingAdjustment', 'CashTrackingSession', 'CatalogCsvOperation', 'Channel', 'ChannelDefinition', 'ChannelInformation', 'CheckoutBranding', 'CheckoutBrandingButton', 'CheckoutBrandingButtonColorRoles', 'CheckoutBrandingBuyerJourney', 'CheckoutBrandingCartLink', 'CheckoutBrandingCheckbox', 'CheckoutBrandingChoiceList', 'CheckoutBrandingChoiceListGroup', 'CheckoutBrandingColorGlobal', 'CheckoutBrandingColorRoles', 'CheckoutBrandingColorScheme', 'CheckoutBrandingColorSchemes', 'CheckoutBrandingColors', 'CheckoutBrandingContainerDivider', 'CheckoutBrandingContent', 'CheckoutBrandingControl', 'CheckoutBrandingControlColorRoles', 'CheckoutBrandingCornerRadiusVariables', 'CheckoutBrandingCustomFont', 'CheckoutBrandingCustomizations', 'CheckoutBrandingDesignSystem', 'CheckoutBrandingDividerStyle', 'CheckoutBrandingExpressCheckout', 'CheckoutBrandingExpressCheckoutButton', 'CheckoutBrandingFontGroup', 'CheckoutBrandingFontSize', 'CheckoutBrandingFooter', 'CheckoutBrandingFooterContent', 'CheckoutBrandingGlobal', 'CheckoutBrandingHeader', 'CheckoutBrandingHeaderCartLink', 'CheckoutBrandingHeadingLevel', 'CheckoutBrandingImage', 'CheckoutBrandingLogo', 'CheckoutBrandingMain', 'CheckoutBrandingMainSection', 'CheckoutBrandingMerchandiseThumbnail', 'CheckoutBrandingOrderSummary', 'CheckoutBrandingOrderSummarySection', 'CheckoutBrandingSelect', 'CheckoutBrandingShopifyFont', 'CheckoutBrandingTextField', 'CheckoutBrandingTypography', 'CheckoutBrandingTypographyStyle', 'CheckoutBrandingTypographyStyleGlobal', 'CheckoutProfile', 'Collection', 'CollectionPublication', 'CollectionRule', 'CollectionRuleCategoryCondition', 'CollectionRuleConditions', 'CollectionRuleMetafieldCondition', 'CollectionRuleProductCategoryCondition', 'CollectionRuleSet', 'CollectionRuleTextCondition', 'CombinedListing', 'CombinedListingChild', 'Comment', 'CommentAuthor', 'CommentEvent', 'CommentEventAttachment', 'Company', 'CompanyAddress', 'CompanyContact', 'CompanyContactRole', 'CompanyContactRoleAssignment', 'CompanyLocation', 'CompanyLocationCatalog', 'CompanyLocationStaffMemberAssignment', 'Count', 'CountriesInShippingZones', 'CountryHarmonizedSystemCode', 'CurrencyFormats', 'CurrencySetting', 'Customer', 'CustomerAccountAppExtensionPage', 'CustomerAccountNativePage', 'CustomerAccountsV2', 'CustomerCreditCard', 'CustomerCreditCardBillingAddress', 'CustomerEmailAddress', 'CustomerEmailMarketingConsentState', 'CustomerJourney', 'CustomerJourneySummary', 'CustomerMergeError', 'CustomerMergePreview', 'CustomerMergePreviewAlternateFields', 'CustomerMergePreviewBlockingFields', 'CustomerMergePreviewDefaultFields', 'CustomerMergeRequest', 'CustomerMergeable', 'CustomerPaymentInstrumentBillingAddress', 'CustomerPaymentMethod', 'CustomerPaypalBillingAgreement', 'CustomerPhoneNumber', 'CustomerSegmentMember', 'CustomerSegmentMembersQuery', 'CustomerShopPayAgreement', 'CustomerSmsMarketingConsentError', 'CustomerSmsMarketingConsentState', 'CustomerStatistics', 'CustomerVisit', 'DelegateAccessToken', 'DeletionEvent', 'DeliveryAvailableService', 'DeliveryBrandedPromise', 'DeliveryCarrierService', 'DeliveryCarrierServiceAndLocations', 'DeliveryCondition', 'DeliveryCountry', 'DeliveryCountryAndZone', 'DeliveryCountryCodeOrRestOfWorld', 'DeliveryCountryCodesOrRestOfWorld', 'DeliveryCustomization', 'DeliveryCustomizationError', 'DeliveryLegacyModeBlocked', 'DeliveryLocalPickupSettings', 'DeliveryLocationGroup', 'DeliveryLocationGroupZone', 'DeliveryLocationLocalPickupSettingsError', 'DeliveryMethod', 'DeliveryMethodAdditionalInformation', 'DeliveryMethodDefinition', 'DeliveryMethodDefinitionCounts', 'DeliveryParticipant', 'DeliveryParticipantService', 'DeliveryProductVariantsCount', 'DeliveryProfile', 'DeliveryProfileItem', 'DeliveryProfileLocationGroup', 'DeliveryPromiseProvider', 'DeliveryProvince', 'DeliveryRateDefinition', 'DeliverySetting', 'DeliveryZone', 'DepositPercentage', 'DiscountAmount', 'DiscountAutomaticApp', 'DiscountAutomaticBasic', 'DiscountAutomaticBxgy', 'DiscountAutomaticFreeShipping', 'DiscountAutomaticNode', 'DiscountCodeApp', 'DiscountCodeApplication', 'DiscountCodeBasic', 'DiscountCodeBxgy', 'DiscountCodeFreeShipping', 'DiscountCodeNode', 'DiscountCollections', 'DiscountCombinesWith', 'DiscountCountries', 'DiscountCountryAll', 'DiscountCustomerAll', 'DiscountCustomerBuys', 'DiscountCustomerGets', 'DiscountCustomerSegments', 'DiscountCustomers', 'DiscountMinimumQuantity', 'DiscountMinimumSubtotal', 'DiscountNode', 'DiscountOnQuantity', 'DiscountPercentage', 'DiscountProducts', 'DiscountPurchaseAmount', 'DiscountQuantity', 'DiscountRedeemCode', 'DiscountRedeemCodeBulkCreation', 'DiscountRedeemCodeBulkCreationCode', 'DiscountShareableUrl', 'Domain', 'DomainLocalization', 'DraftOrder', 'DraftOrderAppliedDiscount', 'DraftOrderBundleAddedWarning', 'DraftOrderDiscountNotAppliedWarning', 'DraftOrderLineItem', 'DraftOrderPlatformDiscount', 'DraftOrderPlatformDiscountAllocation', 'DraftOrderTag', 'Duty', 'DutySale', 'EditableProperty', 'ExchangeLineItem', 'ExchangeV2', 'ExchangeV2Additions', 'ExchangeV2LineItem', 'ExchangeV2Returns', 'ExternalVideo', 'FailedRequirement', 'FeeSale', 'FileError', 'FilterOption', 'FinancialSummaryDiscountAllocation', 'FinancialSummaryDiscountApplication', 'Fulfillment', 'FulfillmentConstraintRule', 'FulfillmentEvent', 'FulfillmentHold', 'FulfillmentLineItem', 'FulfillmentOrder', 'FulfillmentOrderAssignedLocation', 'FulfillmentOrderDestination', 'FulfillmentOrderInternationalDuties', 'FulfillmentOrderLineItem', 'FulfillmentOrderLineItemFinancialSummary', 'FulfillmentOrderLineItemWarning', 'FulfillmentOrderLocationForMove', 'FulfillmentOrderMerchantRequest', 'FulfillmentOrderMergeResult', 'FulfillmentOrderSplitResult', 'FulfillmentOrderSupportedAction', 'FulfillmentOriginAddress', 'FulfillmentService', 'FulfillmentTrackingInfo', 'FunctionsAppBridge', 'FunctionsErrorHistory', 'GenericFile', 'GiftCard', 'GiftCardCreditTransaction', 'GiftCardDebitTransaction', 'GiftCardRecipient', 'GiftCardSale', 'ImageUploadParameter', 'InventoryAdjustmentGroup', 'InventoryChange', 'InventoryItem', 'InventoryItemMeasurement', 'InventoryLevel', 'InventoryProperties', 'InventoryQuantity', 'InventoryQuantityName', 'InventoryScheduledChange', 'Job', 'LimitedPendingOrderCount', 'LineItem', 'LineItemGroup', 'LineItemSellingPlan', 'LinkedMetafield', 'LocalPaymentMethodsPaymentDetails', 'Locale', 'LocalizationExtension', 'Location', 'LocationAddress', 'LocationSuggestedAddress', 'ManualDiscountApplication', 'Market', 'MarketCatalog', 'MarketCurrencySettings', 'MarketLocalizableContent', 'MarketLocalizableResource', 'MarketLocalization', 'MarketRegionCountry', 'MarketWebPresence', 'MarketWebPresenceRootUrl', 'MarketingActivity', 'MarketingActivityExtensionAppErrors', 'MarketingBudget', 'MarketingEngagement', 'MarketingEvent', 'MediaError', 'MediaImage', 'MediaImageOriginalSource', 'MediaPreviewImage', 'MediaWarning', 'Menu', 'MenuItem', 'MerchantApprovalSignals', 'Metafield', 'MetafieldAccess', 'MetafieldAccessGrant', 'MetafieldCapabilities', 'MetafieldCapabilityAdminFilterable', 'MetafieldCapabilitySmartCollectionCondition', 'MetafieldDefinition', 'MetafieldDefinitionConstraintValue', 'MetafieldDefinitionConstraints', 'MetafieldDefinitionSupportedValidation', 'MetafieldDefinitionType', 'MetafieldDefinitionValidation', 'MetafieldIdentifier', 'MetafieldRelation', 'MetafieldStorefrontVisibility', 'Metaobject', 'MetaobjectAccess', 'MetaobjectCapabilities', 'MetaobjectCapabilitiesOnlineStore', 'MetaobjectCapabilitiesPublishable', 'MetaobjectCapabilitiesRenderable', 'MetaobjectCapabilitiesTranslatable', 'MetaobjectCapabilityData', 'MetaobjectCapabilityDataOnlineStore', 'MetaobjectCapabilityDataPublishable', 'MetaobjectCapabilityDefinitionDataOnlineStore', 'MetaobjectCapabilityDefinitionDataRenderable', 'MetaobjectDefinition', 'MetaobjectField', 'MetaobjectFieldDefinition', 'MetaobjectThumbnail', 'Model3d', 'Model3dBoundingBox', 'Model3dSource', 'MutationsStagedUploadTargetGenerateUploadParameter', 'NavigationItem', 'OnlineStore', 'OnlineStorePasswordProtection', 'OnlineStoreTheme', 'OnlineStoreThemeFile', 'OnlineStoreThemeFileBodyBase64', 'OnlineStoreThemeFileBodyText', 'OnlineStoreThemeFileBodyUrl', 'OnlineStoreThemeFileOperationResult', 'OnlineStoreThemeFileReadResult', 'OnlineStoreThemeFilesUserErrors', 'Order', 'OrderAdjustment', 'OrderAgreement', 'OrderApp', 'OrderCancellation', 'OrderDisputeSummary', 'OrderEditAgreement', 'OrderPaymentCollectionDetails', 'OrderPaymentStatus', 'OrderRisk', 'OrderRiskAssessment', 'OrderRiskSummary', 'OrderStagedChangeAddCustomItem', 'OrderStagedChangeAddLineItemDiscount', 'OrderStagedChangeAddShippingLine', 'OrderStagedChangeAddVariant', 'OrderStagedChangeDecrementItem', 'OrderStagedChangeIncrementItem', 'OrderStagedChangeRemoveShippingLine', 'OrderTransaction', 'Page', 'PaymentCustomization', 'PaymentCustomizationError', 'PaymentMandate', 'PaymentSchedule', 'PaymentSettings', 'PaymentTerms', 'PaymentTermsTemplate', 'PriceList', 'PriceListAdjustment', 'PriceListAdjustmentSettings', 'PriceListParent', 'PriceListPrice', 'PriceRule', 'PriceRuleCustomerSelection', 'PriceRuleDiscountCode', 'PriceRuleEntitlementToPrerequisiteQuantityRatio', 'PriceRuleFixedAmountValue', 'PriceRuleItemEntitlements', 'PriceRuleLineItemPrerequisites', 'PriceRuleMoneyRange', 'PriceRulePercentValue', 'PriceRulePrerequisiteToEntitlementQuantityRatio', 'PriceRuleQuantityRange', 'PriceRuleShareableUrl', 'PriceRuleShippingLineEntitlements', 'PriceRuleValidityPeriod', 'PricingPercentageValue', 'PrivateMetafield', 'Product', 'ProductBundleComponent', 'ProductBundleComponentOptionSelection', 'ProductBundleComponentOptionSelectionValue', 'ProductBundleComponentQuantityOption', 'ProductBundleComponentQuantityOptionValue', 'ProductBundleOperation', 'ProductCategory', 'ProductCompareAtPriceRange', 'ProductContextualPricing', 'ProductDeleteOperation', 'ProductDuplicateJob', 'ProductDuplicateOperation', 'ProductFeed', 'ProductOption', 'ProductOptionValue', 'ProductOptionValueSwatch', 'ProductPriceRange', 'ProductPriceRangeV2', 'ProductPublication', 'ProductResourceFeedback', 'ProductSale', 'ProductSetOperation', 'ProductTaxonomyNode', 'ProductVariant', 'ProductVariantComponent', 'ProductVariantContextualPricing', 'ProductVariantPricePair', 'Publication', 'PublicationResourceOperation', 'PurchasingCompany', 'QuantityPriceBreak', 'QuantityRule', 'Refund', 'RefundAgreement', 'RefundDuty', 'RefundLineItem', 'RefundShippingLine', 'ResourceAlert', 'ResourceAlertAction', 'ResourceFeedback', 'ResourcePublication', 'ResourcePublicationV2', 'RestockingFee', 'RestrictedForResource', 'Return', 'ReturnAgreement', 'ReturnDecline', 'ReturnLineItem', 'ReturnShippingFee', 'ReturnableFulfillment', 'ReturnableFulfillmentLineItem', 'ReverseDelivery', 'ReverseDeliveryLabelV2', 'ReverseDeliveryLineItem', 'ReverseDeliveryShippingDeliverable', 'ReverseDeliveryTrackingV2', 'ReverseFulfillmentOrder', 'ReverseFulfillmentOrderDisposition', 'ReverseFulfillmentOrderLineItem', 'ReverseFulfillmentOrderThirdPartyConfirmation', 'RiskFact', 'SEO', 'SaleAdditionalFee', 'SaleTax', 'SavedSearch', 'ScriptDiscountApplication', 'ScriptTag', 'SearchFilterOptions', 'SearchResult', 'Segment', 'SegmentAttributeStatistics', 'SegmentEventFilterParameter', 'SegmentMembership', 'SegmentMembershipResponse', 'SegmentMigration', 'SegmentStatistics', 'SegmentValue', 'SelectedOption', 'SellingPlan', 'SellingPlanAnchor', 'SellingPlanCheckoutCharge', 'SellingPlanCheckoutChargePercentageValue', 'SellingPlanFixedBillingPolicy', 'SellingPlanFixedDeliveryPolicy', 'SellingPlanFixedPricingPolicy', 'SellingPlanGroup', 'SellingPlanInventoryPolicy', 'SellingPlanPricingPolicyPercentageValue', 'SellingPlanRecurringBillingPolicy', 'SellingPlanRecurringDeliveryPolicy', 'SellingPlanRecurringPricingPolicy', 'ServerPixel', 'ShippingLine', 'ShippingLineSale', 'ShippingRate', 'ShippingRefund', 'Shop', 'ShopAddress', 'ShopAlert', 'ShopAlertAction', 'ShopBillingPreferences', 'ShopFeatures', 'ShopLocale', 'ShopPayInstallmentsPaymentDetails', 'ShopPolicy', 'ShopResourceLimits', 'ShopifyFunction', 'ShopifyPaymentsAccount', 'ShopifyPaymentsAdjustmentOrder', 'ShopifyPaymentsAssociatedOrder', 'ShopifyPaymentsBalanceTransaction', 'ShopifyPaymentsBalanceTransactionAssociatedPayout', 'ShopifyPaymentsBankAccount', 'ShopifyPaymentsDefaultChargeStatementDescriptor', 'ShopifyPaymentsDispute', 'ShopifyPaymentsDisputeEvidence', 'ShopifyPaymentsDisputeFileUpload', 'ShopifyPaymentsDisputeFulfillment', 'ShopifyPaymentsDisputeReasonDetails', 'ShopifyPaymentsExtendedAuthorization', 'ShopifyPaymentsJpChargeStatementDescriptor', 'ShopifyPaymentsPayout', 'ShopifyPaymentsPayoutSchedule', 'ShopifyPaymentsPayoutSummary', 'ShopifyPaymentsRefundSet', 'ShopifyPaymentsToolingProviderPayout', 'ShopifyPaymentsTransactionSet', 'ShopifyProtectOrderEligibility', 'ShopifyProtectOrderSummary', 'StaffMember', 'StaffMemberPrivateData', 'StagedMediaUploadTarget', 'StagedUploadParameter', 'StagedUploadTarget', 'StandardMetafieldDefinitionTemplate', 'StandardizedProductType', 'StoreCreditAccount', 'StoreCreditAccountCreditTransaction', 'StoreCreditAccountDebitRevertTransaction', 'StoreCreditAccountDebitTransaction', 'StoreCreditAccountExpirationTransaction', 'StorefrontAccessToken', 'SubscriptionAppliedCodeDiscount', 'SubscriptionBillingAttempt', 'SubscriptionBillingCycle', 'SubscriptionBillingCycleEditedContract', 'SubscriptionBillingPolicy', 'SubscriptionContract', 'SubscriptionCyclePriceAdjustment', 'SubscriptionDeliveryMethodLocalDelivery', 'SubscriptionDeliveryMethodLocalDeliveryOption', 'SubscriptionDeliveryMethodPickup', 'SubscriptionDeliveryMethodPickupOption', 'SubscriptionDeliveryMethodShipping', 'SubscriptionDeliveryMethodShippingOption', 'SubscriptionDeliveryOptionResultFailure', 'SubscriptionDeliveryOptionResultSuccess', 'SubscriptionDeliveryPolicy', 'SubscriptionDiscountAllocation', 'SubscriptionDiscountEntitledLines', 'SubscriptionDiscountFixedAmountValue', 'SubscriptionDiscountPercentageValue', 'SubscriptionDraft', 'SubscriptionLine', 'SubscriptionLocalDeliveryOption', 'SubscriptionMailingAddress', 'SubscriptionManualDiscount', 'SubscriptionPickupOption', 'SubscriptionPricingPolicy', 'SubscriptionShippingOption', 'SubscriptionShippingOptionResultFailure', 'SubscriptionShippingOptionResultSuccess', 'SuggestedOrderTransaction', 'SuggestedRefund', 'SuggestedReturnRefund', 'TaxAppConfiguration', 'Taxonomy', 'TaxonomyAttribute', 'TaxonomyCategory', 'TaxonomyChoiceListAttribute', 'TaxonomyMeasurementAttribute', 'TaxonomyValue', 'TenderTransaction', 'TenderTransactionCreditCardDetails', 'TipSale', 'TransactionFee', 'TranslatableContent', 'TranslatableResource', 'Translation', 'TypedAttribute', 'UTMParameters', 'UnitPriceMeasurement', 'UnknownSale', 'UnverifiedReturnLineItem', 'UrlRedirect', 'UrlRedirectImport', 'UrlRedirectImportPreview', 'Validation', 'VaultCreditCard', 'VaultPaypalBillingAgreement', 'Vector3', 'Video', 'VideoSource', 'WebPixel', 'WebhookEventBridgeEndpoint', 'WebhookHttpEndpoint', 'WebhookPubSubEndpoint', 'WebhookSubscription', 'Weight'])
Generated SQLAlchemy models to /home/<USER>/Projects/leanchain/e-commerce/backend/models/shopify_models.py
