import json
import logging
import logging.handlers
import os
import re
import keyword
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Type

from sgqlc.operation import Operation
import sgqlc.types

# Import the generated Shopify sgqlc schema and mappings
from schemas.shopify_sgqlc_schema import QueryRoot as Query, Mutation
from schemas.shopify_mappings import MUTATION_PAYLOAD_MAP, QUERY_PAYLOAD_MAP
from schemas import shopify_sgqlc_schema
from services.shopify_graphql_builder import GQLGenerator, load_introspection_data

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO) # Set to DEBUG for more verbosity

# Create a file handler
log_file = Path(__file__).resolve().parent / "logs/generate_gql_strings.log"
os.makedirs(log_file.parent, exist_ok=True)
file_handler = logging.handlers.RotatingFileHandler(
    log_file,
    maxBytes=10485760, # 10 MB
    backupCount=5
)
file_handler.setLevel(logging.INFO)

# Create a console handler
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

# Create a formatter and add it to the handlers
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)

# Add the handlers to the logger
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# Define paths
BASE_DIR = Path(__file__).resolve().parent
INTROSPECTION_FILE = BASE_DIR / "shopify_introspection.json"
OUTPUT_GQL_STRINGS_FILE = BASE_DIR  / "tmp/shopify_gql_strings.py"

RESERVED_KEYWORDS = set(keyword.kwlist)



if __name__ == "__main__":
    introspection_data = load_introspection_data()
    if not introspection_data:
        exit(1)

    gql_generator = GQLGenerator(introspection_data)

    query_gql_strings = {}
    all_query_stats = {}
    for query_name, details in QUERY_PAYLOAD_MAP.items():
        return_type = details["return_type"].__name__ if hasattr(details["return_type"], "__name__") else details["return_type"]
        node_type = details["node_type"].__name__ if hasattr(details["node_type"], "__name__") else details["node_type"]
        
        try:
            gql_string, stats = gql_generator.generate_query_gql(query_name, return_type, node_type)
            query_gql_strings[f"{query_name}"] = gql_string
            all_query_stats[query_name] = stats
        except Exception as e:
            logger.exception(f"Error generating GQL for query {query_name}: {e}")

    mutation_gql_strings = {}
    all_mutation_stats = {}
    for mutation_name, details in MUTATION_PAYLOAD_MAP.items():
        payload_type = details["payload_type"].__name__ if hasattr(details["payload_type"], "__name__") else details["payload_type"]
        input_type = details["input_type"].__name__ if hasattr(details["input_type"], "__name__") else details["input_type"]
        
        try:
            gql_string, stats = gql_generator.generate_mutation_gql(mutation_name, payload_type, input_type)
            mutation_gql_strings[mutation_name] = gql_string
            all_mutation_stats[mutation_name] = stats
        except Exception as e:
            logger.exception(f"Error generating GQL for mutation {mutation_name}: {e}")

    os.makedirs(OUTPUT_GQL_STRINGS_FILE.parent, exist_ok=True)
    with open(OUTPUT_GQL_STRINGS_FILE, "w") as f:
        f.write("# This file is auto-generated by generate_gql_strings.py\n")
        f.write("# Do not modify it directly.\n\n")
        f.write("QUERY_GQL_STRINGS = {\n")
        for name, gql_string in query_gql_strings.items():
            f.write(f"    '{name}': '''\n{gql_string}\n''',\n")
        f.write("}\n\n")

        f.write("MUTATION_GQL_STRINGS = {\n")
        for name, gql_string in mutation_gql_strings.items():
            f.write(f"    '{name}': '''\n{gql_string}\n''',\n")
        f.write("}\n")

    logger.info(f"Successfully generated GQL strings to {OUTPUT_GQL_STRINGS_FILE}")

    overall_stats = {
        "total_queries": len(all_query_stats),
        "total_mutations": len(all_mutation_stats),
        "total_included_fields": 0,
        "total_skipped_fields": 0,
        "total_attribute_errors": 0,
        "total_sgqlc_class_not_found": 0,
        "total_skipped_inline_fragment_no_on_method": 0,
        "total_query_errors": 0,
        "total_mutation_errors": 0,
    }

    for stats in all_query_stats.values():
        overall_stats["total_included_fields"] += stats["included"]
        overall_stats["total_skipped_fields"] += stats["skipped"]
        overall_stats["total_attribute_errors"] += stats["attribute_errors"]
        overall_stats["total_sgqlc_class_not_found"] += stats["sgqlc_class_not_found"]
        overall_stats["total_skipped_inline_fragment_no_on_method"] += stats["skipped_inline_fragment_no_on_method"]
        if stats["attribute_errors"] > 0 or stats["sgqlc_class_not_found"] > 0 or stats["skipped_inline_fragment_no_on_method"] > 0:
            overall_stats["total_query_errors"] += 1

    for stats in all_mutation_stats.values():
        overall_stats["total_included_fields"] += stats["included"]
        overall_stats["total_skipped_fields"] += stats["skipped"]
        overall_stats["total_attribute_errors"] += stats["attribute_errors"]
        overall_stats["total_sgqlc_class_not_found"] += stats["sgqlc_class_not_found"]
        overall_stats["total_skipped_inline_fragment_no_on_method"] += stats["skipped_inline_fragment_no_on_method"]
        if stats["attribute_errors"] > 0 or stats["sgqlc_class_not_found"] > 0 or stats["skipped_inline_fragment_no_on_method"] > 0:
            overall_stats["total_mutation_errors"] += 1

    logger.info("\n--- Overall GQL Generation Statistics ---")
    logger.info(f"Total Queries Generated: {overall_stats["total_queries"]}")
    logger.info(f"Total Mutations Generated: {overall_stats["total_mutations"]}")
    logger.info(f"Total Fields Included: {overall_stats["total_included_fields"]}")
    logger.info(f"Total Fields Skipped: {overall_stats["total_skipped_fields"]}")
    logger.info(f"Total Attribute Errors: {overall_stats["total_attribute_errors"]}")
    logger.info(f"Total SGQLC Class Not Found Errors: {overall_stats["total_sgqlc_class_not_found"]}")
    logger.info(f"Total Skipped Inline Fragments (no 'on' method): {overall_stats["total_skipped_inline_fragment_no_on_method"]}")
    logger.info(f"Total Query Generation Errors: {overall_stats["total_query_errors"]}")
    logger.info(f"Total Mutation Generation Errors: {overall_stats["total_mutation_errors"]}")
