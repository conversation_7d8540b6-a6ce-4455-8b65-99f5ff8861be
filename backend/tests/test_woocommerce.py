# This test file is temporarily commented out due to missing service dependencies.
# from datetime import datetime
# from unittest.mock import AsyncMock, MagicMock, patch

# import pytest
# from sqlalchemy.orm import Session

# from models import Store
# from services.sync_service import SyncService


# @pytest.fixture
# def db_session(mocker):
#     """Fixture for a mocked database session."""
#     return mocker.MagicMock(spec=Session)


# @pytest.fixture
# def mock_woocommerce_service(mocker):
#     """Fixture for a mocked WooCommerceService."""
#     service = mocker.MagicMock()
#     service.get_products_for_sync = AsyncMock(return_value=[])
#     service.get_orders_for_sync = AsyncMock(return_value=[])
#     return service


# @pytest.mark.asyncio
# async def test_sync_store_woocommerce(db_session, mock_woocommerce_service):
#     """Test that the SyncService correctly calls the WooCommerceService."""
#     sync_service = SyncService(db_session)

#     # Mock the store object for WooCommerce
#     mock_store = models.Store(
#         id=1,
#         name="Test WooCommerce Store",
#         platform="woocommerce",
#         is_active=True,
#         owner_id=1,
#         created_at=datetime.now(),
#         sync_config='{"products": true, "orders": true}',
#     )

#     with patch(
#         "services.sync_service.WooCommerceService",
#         return_value=mock_woocommerce_service,
#     ) as mock_get_service:
#         async for _ in sync_service.sync_store(mock_store, AsyncMock()):
#             pass

#         # Verify the correct service was called
#         mock_get_service.assert_called_once()

#         # Verify that the service's methods were called
#         mock_woocommerce_service.get_products_for_sync.assert_called_once()
#         mock_woocommerce_service.get_orders_for_sync.assert_called_once()

#         # Verify that the session was committed
#         assert db_session.commit.call_count > 0
