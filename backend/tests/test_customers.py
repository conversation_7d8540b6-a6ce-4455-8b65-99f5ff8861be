import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

pytestmark = pytest.mark.asyncio


async def test_create_and_read_customer(
    authenticated_client: AsyncClient, db: AsyncSession
):
    """
    Test creating a customer with an authenticated client.
    """
    customer_data = {"name": "Test Customer", "domain": "test.com"}

    # Create a customer
    response = await authenticated_client.post("/api/customers/", json=customer_data)
    assert response.status_code == 201
    created_customer = response.json()
    assert created_customer["name"] == customer_data["name"]
    assert "id" in created_customer

    # Read the customer back
    customer_id = created_customer["id"]
    response = await authenticated_client.get(f"/api/customers/{customer_id}")
    assert response.status_code == 200
    read_customer = response.json()
    assert read_customer["name"] == customer_data["name"]


async def test_read_customers_unauthenticated(test_client: AsyncClient):
    """
    Test that an unauthenticated user cannot access a protected endpoint.
    """
    response = await test_client.get("/api/customers/")
    assert response.status_code == 401
