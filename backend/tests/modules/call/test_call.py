import asyncio
import logging
from unittest.mock import patch

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from modules.customers.models import Customer
from src.main import app

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


def test_text_chat_websocket_conversation(db_session: AsyncSession):
    logger.info("Starting test_text_chat_websocket_conversation")
    # --- 1. Create a customer in the test DB ---
    customer = Customer(name="Test Customer", domain="test.customer")
    logger.debug(f"Created customer: {customer.name}")

    async def _create():
        logger.debug("Attempting to create customer in DB")
        async with db_session.begin():
            db_session.add(customer)
            await db_session.flush()
            await db_session.refresh(customer)
        logger.debug("Customer created and refreshed in DB")

    asyncio.get_event_loop().run_until_complete(_create())
    logger.info("Customer creation complete.")

    client = TestClient(app)
    logger.debug("FastAPI TestClient initialized.")

    # --- 2. Initiate text chat session via HTTP POST ---
    logger.info("Initiating text chat session via HTTP POST to /api/call")
    response = client.post(
        "/api/call",
        json={"type": "text", "custom_first_message": "Hello from agent!"},
    )
    logger.debug(f"HTTP POST response status code: {response.status_code}")
    assert response.status_code == 200
    data = response.json()
    call_history_id = data["call_history_id"]
    logger.info(f"Call history ID obtained: {call_history_id}")

    # --- 3. Patch ConversationManager BEFORE connecting ---
    logger.info("Patching ConversationManager.handle_conversation")
    with patch(
        "core.services.conversation_manager.ConversationManager.handle_conversation"
    ) as mock_handle_conversation:

        async def mock_flow(*args, **kwargs):
            logger.debug("Inside mock_flow for handle_conversation")
            websocket = kwargs["websocket"]
            # simulate initial message
            logger.debug("Mocking agent sending initial message")
            await websocket.send_json(
                {
                    "type": "transcript",
                    "sender": "agent",
                    "transcript": "Hello from agent!",
                    "is_final": True,
                }
            )
            # simulate follow-up
            logger.debug("Mocking agent sending follow-up message")
            await websocket.send_json(
                {
                    "type": "transcript",
                    "sender": "agent",
                    "transcript": "I can help with that.",
                    "is_final": True,
                }
            )
            # keep connection alive a bit so test can read
            await asyncio.sleep(0.1)
            logger.debug("Mock flow completed, connection kept alive.")

        mock_handle_conversation.side_effect = mock_flow
        logger.debug("Mock handle_conversation side effect set.")

        # --- 4. WebSocket conversation ---
        logger.info("Connecting to WebSocket /api/call/ws/text-chat")
        with client.websocket_connect("/api/call/ws/text-chat") as ws:
            logger.debug(f"Sending call_history_id: {call_history_id} via WebSocket")
            ws.send_json({"data": {"call_history_id": call_history_id}})

            logger.debug("Receiving first message from WebSocket")
            msg = ws.receive_json()
            logger.info(f"Received first message: {msg['transcript']}")
            assert msg["transcript"] == "Hello from agent!"

            logger.debug("Receiving second message from WebSocket")
            msg = ws.receive_json()
            logger.info(f"Received second message: {msg['transcript']}")
            assert msg["transcript"] == "I can help with that."
    logger.info("WebSocket conversation completed.")
    logger.info("Finished test_text_chat_websocket_conversation")
