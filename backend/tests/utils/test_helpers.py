"""
Test utilities and helpers for e2e testing across all conversation platforms.
"""

import asyncio
import base64
import json
import logging
import time
from typing import Any, Dict, List

from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from modules.call import crud as call_crud
from modules.call.schemas import CallH<PERSON>oryCreate, CallRequest
from modules.customers.models import Customer

logger = logging.getLogger(__name__)


class MockSTTService:
    """Mock Speech-to-Text service for testing."""

    def __init__(self, responses: List[str] = None):
        self.responses = responses or [
            "Hello, how can I help you?",
            "I need assistance",
            "Thank you",
        ]
        self.call_count = 0

    async def transcribe_audio(self, audio_data: bytes) -> str:
        """Mock transcription that returns predefined responses."""
        if self.call_count < len(self.responses):
            response = self.responses[self.call_count]
            self.call_count += 1
            return response
        return "I didn't catch that"


class MockTTSService:
    """Mock Text-to-Speech service for testing."""

    def __init__(self):
        self.synthesized_texts = []

    async def synthesize_speech(self, text: str) -> bytes:
        """Mock synthesis that returns dummy audio data."""
        self.synthesized_texts.append(text)
        # Return dummy audio data (16-bit PCM, 8kHz, mono)
        return b"\x00\x01" * 1000  # 1000 samples of dummy audio


class MockTwilioService:
    """Mock Twilio service for testing phone calls."""

    def __init__(self):
        self.calls_made = []
        self.should_fail = False
        self.call_sid_counter = 1000

    def make_outbound_call(
        self, to_number: str, call_history_id: int
    ) -> Dict[str, Any]:
        """Mock outbound call creation."""
        if self.should_fail:
            return {"success": False, "error": "Mock Twilio error"}

        call_sid = f"CA{self.call_sid_counter}"
        self.call_sid_counter += 1

        self.calls_made.append(
            {
                "to_number": to_number,
                "call_history_id": call_history_id,
                "call_sid": call_sid,
            }
        )

        return {"success": True, "call_sid": call_sid}


class WebSocketTestClient:
    """Helper class for testing WebSocket connections."""

    def __init__(self, client: TestClient, endpoint: str):
        self.client = client
        self.endpoint = endpoint
        self.messages_sent = []
        self.messages_received = []

    def __enter__(self):
        self.ws = self.client.websocket_connect(self.endpoint)
        self.connection = self.ws.__enter__()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.ws.__exit__(exc_type, exc_val, exc_tb)

    def send_json(self, data: Dict[str, Any]):
        """Send JSON message and track it."""
        self.messages_sent.append(data)
        self.connection.send_json(data)

    def receive_json(self, timeout: float = 5.0) -> Dict[str, Any]:
        """Receive JSON message with timeout."""
        try:
            message = self.connection.receive_json()
            self.messages_received.append(message)
            return message
        except Exception as e:
            logger.error(f"Error receiving WebSocket message: {e}")
            raise

    def send_text(self, text: str):
        """Send text message and track it."""
        self.messages_sent.append(text)
        self.connection.send_text(text)

    def receive_text(self, timeout: float = 5.0) -> str:
        """Receive text message with timeout."""
        try:
            message = self.connection.receive_text()
            self.messages_received.append(message)
            return message
        except Exception as e:
            logger.error(f"Error receiving WebSocket text: {e}")
            raise

    def close(self):
        """Close the WebSocket connection."""
        self.connection.close()


async def create_test_customer(
    db: AsyncSession, name: str = "Test Customer", domain: str = "test.customer"
) -> Customer:
    """Create a test customer for use in tests."""
    customer = Customer(name=name, domain=domain)
    db.add(customer)
    await db.flush()
    await db.refresh(customer)
    return customer


async def create_test_call_history(
    db: AsyncSession,
    customer_id: int,
    platform: str = "text_chat",
    custom_prompt: str = None,
    custom_first_message: str = None,
):
    """Create a test call history entry."""
    call_request = CallRequest(
        type=platform,
        custom_prompt=custom_prompt,
        custom_first_message=custom_first_message,
    )

    call_sid = f"{platform}_call_{time.time()}"
    call_history_data = CallHistoryCreate(
        customer_id=customer_id,
        call_sid=call_sid,
        call_status="initiated",
        system_prompt=custom_prompt,
        first_message=custom_first_message,
        call_metadata={"platform": platform},
    )

    return await call_crud.create_call_history(db, call_history_data)


def create_mock_audio_data(
    duration_seconds: float = 1.0, sample_rate: int = 8000
) -> bytes:
    """Create mock audio data for testing."""
    num_samples = int(duration_seconds * sample_rate)
    # Create simple sine wave as mock audio
    import math

    samples = []
    for i in range(num_samples):
        # 440Hz sine wave
        sample = int(32767 * math.sin(2 * math.pi * 440 * i / sample_rate))
        samples.append(sample.to_bytes(2, byteorder="little", signed=True))
    return b"".join(samples)


def create_twilio_media_message(audio_data: bytes) -> str:
    """Create a Twilio media stream message with audio data."""
    # Convert to μ-law and base64 encode (simplified for testing)
    payload = base64.b64encode(audio_data).decode("utf-8")

    message = {
        "event": "media",
        "sequenceNumber": "1",
        "media": {
            "track": "inbound",
            "chunk": "1",
            "timestamp": "**********",
            "payload": payload,
        },
        "streamSid": "MZ123456789",
    }
    return json.dumps(message)


def create_twilio_start_message() -> str:
    """Create a Twilio stream start message."""
    message = {
        "event": "start",
        "start": {
            "streamSid": "MZ123456789",
            "accountSid": "AC123456789",
            "callSid": "CA123456789",
            "tracks": ["inbound"],
            "mediaFormat": {
                "encoding": "audio/x-mulaw",
                "sampleRate": 8000,
                "channels": 1,
            },
        },
        "streamSid": "MZ123456789",
    }
    return json.dumps(message)


def create_twilio_stop_message() -> str:
    """Create a Twilio stream stop message."""
    message = {"event": "stop", "streamSid": "MZ123456789"}
    return json.dumps(message)


class ConversationTestHelper:
    """Helper class for testing conversation flows."""

    def __init__(self, client: TestClient, db_session: AsyncSession):
        self.client = client
        self.db_session = db_session
        self.customer = None
        self.call_history = None

    async def setup_test_data(self, platform: str = "text_chat"):
        """Set up test customer and call history."""
        self.customer = await create_test_customer(self.db_session)
        self.call_history = await create_test_call_history(
            self.db_session,
            self.customer.id,
            platform=platform,
            custom_prompt="You are a helpful test assistant.",
            custom_first_message="Hello! How can I help you today?",
        )
        return self.call_history.id

    def initiate_call(self, call_type: str, **kwargs) -> Dict[str, Any]:
        """Initiate a call of the specified type."""
        call_data = {
            "type": call_type,
            "custom_prompt": "You are a helpful test assistant.",
            "custom_first_message": "Hello! How can I help you today?",
            **kwargs,
        }

        response = self.client.post("/api/call", json=call_data)
        assert response.status_code == 200
        return response.json()

    async def simulate_conversation_flow(
        self,
        ws_client: WebSocketTestClient,
        user_messages: List[str],
        expected_responses: int = None,
    ) -> List[Dict[str, Any]]:
        """Simulate a complete conversation flow."""
        responses = []

        for i, user_message in enumerate(user_messages):
            # Send user message
            ws_client.send_json({"type": "user_message", "text": user_message})

            # Receive agent response
            response = ws_client.receive_json()
            responses.append(response)

            # Verify response structure
            assert "type" in response
            assert "transcript" in response
            assert isinstance(response["transcript"], str)
            assert len(response["transcript"]) > 0

        return responses


async def wait_for_condition(
    condition_func, timeout: float = 5.0, interval: float = 0.1
):
    """Wait for a condition to become true with timeout."""
    start_time = time.time()
    while time.time() - start_time < timeout:
        if await condition_func():
            return True
        await asyncio.sleep(interval)
    return False


def assert_valid_conversation_response(
    response: Dict[str, Any], expected_type: str = "transcript"
):
    """Assert that a conversation response has the expected structure."""
    assert "type" in response, f"Response missing 'type' field: {response}"
    assert (
        response["type"] == expected_type
    ), f"Expected type '{expected_type}', got '{response['type']}'"

    if expected_type == "transcript":
        assert (
            "transcript" in response
        ), f"Transcript response missing 'transcript' field: {response}"
        assert (
            "sender" in response
        ), f"Transcript response missing 'sender' field: {response}"
        assert (
            "is_final" in response
        ), f"Transcript response missing 'is_final' field: {response}"
        assert isinstance(response["transcript"], str), "Transcript must be a string"
        assert len(response["transcript"]) > 0, "Transcript cannot be empty"

    elif expected_type == "audio_response":
        assert "audio" in response, f"Audio response missing 'audio' field: {response}"
        assert (
            "transcript" in response
        ), f"Audio response missing 'transcript' field: {response}"
        assert isinstance(response["audio"], str), "Audio data must be base64 string"
        assert len(response["audio"]) > 0, "Audio data cannot be empty"
