import pytest
from fastapi.testclient import TestClient
from unittest.mock import MagicMock
from typing import Dict, Any, Type, List, Optional
from pydantic import BaseModel, create_model
from sgqlc.operation import Operation
from sgqlc.types import Type as SgqlcType, Field, Arg, list_of, non_null
from sqlalchemy.orm import Session
from fastapi import HTTPException # Import HTTPException

from models.models import Store
from schemas.shopify_mappings import MUTATION_PAYLOAD_MAP, QUERY_PAYLOAD_MAP
from services.shopify_sync_service import ShopifySyncService
from routers.shopify import get_shopify_sync_service # Import the dependency function


# Dynamically create tests for mutation routes
for mutation_name, details in MUTATION_PAYLOAD_MAP.items():
    test_name = f"test_dynamic_mutation_{mutation_name}"
    path = f"/api/shopify/{{store_id}}/mutations/{mutation_name}"

    input_data = {}
    if details['input_type']:
        # For simplicity, just provide a generic dictionary.
        # The actual validation will be done by FastAPI and the service.
        input_data = {"dummy_field": "dummy_value"}

    @pytest.fixture
    def _dynamic_mutation_test_fixture(client, test_store, mocker):
        # Patch get_shopify_sync_service to return a mock service
        mock_service = MagicMock(spec=ShopifySyncService)
        mock_service.execute_mutation.return_value = {"data": {mutation_name: {"id": "123", "status": "success"}}}
        mock_service._execute_graphql_query.return_value = {"data": {"mock_response": True}} # Add this line
        mocker.patch("routers.shopify.get_shopify_sync_service", return_value=(mock_service, None))

        async def _test_mutation():
            response = await client.post(
                path.format(store_id=test_store.id),
                json=input_data
            )
            assert response.status_code == 200
            assert response.json() == {"data": {mutation_name: {"id": "123", "status": "success"}}}
            mock_service.execute_mutation.assert_called_once_with(mutation_name, input_data)
        return _test_mutation
    globals()[test_name] = _dynamic_mutation_test_fixture

# Dynamically create tests for query routes
for query_name, details in QUERY_PAYLOAD_MAP.items():
    test_name = f"test_dynamic_query_{query_name}"
    path = f"/api/shopify/{{store_id}}/queries/{query_name}"

    @pytest.fixture
    def _dynamic_query_test_fixture(client, test_store, mocker):
        # Patch get_shopify_sync_service to return a mock service
        mock_service = MagicMock(spec=ShopifySyncService)
        mock_service.execute_query.return_value = {"data": {query_name: {"id": "456", "name": "Test Item"}}}
        mock_service._execute_graphql_query.return_value = {"data": {"mock_response": True}} # Add this line
        mocker.patch("routers.shopify.get_shopify_sync_service", return_value=(mock_service, None))

        async def _test_query():
            response = await client.get(
                path.format(store_id=test_store.id)
            )
            assert response.status_code == 200
            assert response.json() == {"data": {query_name: {"id": "456", "name": "Test Item"}}}
            mock_service.execute_query.assert_called_once_with(query_name, variables={})
        return _test_query
    globals()[test_name] = _dynamic_query_test_fixture

# Test cases for error handling in get_shopify_sync_service
def test_get_shopify_sync_service_store_not_found(client: TestClient, authenticated_user: dict, mocker: MagicMock):
    mocker.patch("routers.shopify.get_shopify_sync_service", side_effect=HTTPException(status_code=404, detail="Store not found."))
    response = client.post(
        "/api/shopify/999/queries/shop", # Use a dummy query path
        headers=authenticated_user,
        json={}
    )
    assert response.status_code == 404
    assert "Store not found." in response.json()["detail"]


def test_get_shopify_sync_service_not_shopify_store(client: TestClient, authenticated_user: dict, db_session: Session, authenticated_user_id: int, mocker: MagicMock):
    # Create a non-shopify store
    non_shopify_store = Store(
        name="WooCommerce Test Store",
        platform="woocommerce",
        owner_id=authenticated_user_id,
        shop_domain="woo.test.com",
        admin_access_token="woo_token",
        storefront_access_token="woo_token",
    )
    db_session.add(non_shopify_store)
    db_session.commit()
    db_session.refresh(non_shopify_store)

    mocker.patch("routers.shopify.get_shopify_sync_service", side_effect=HTTPException(status_code=400, detail="Store is not a Shopify store."))
    response = client.post(
        f"/api/shopify/{non_shopify_store.id}/queries/shop",
        headers=authenticated_user,
        json={}
    )
    assert response.status_code == 400 # Changed to 400 as per router logic for "not a Shopify store"
    assert "Store is not a Shopify store." in response.json()["detail"]


def test_dynamic_mutation_shopify_service_error(client: TestClient, test_store: Store, mocker: MagicMock):
    # Directly override the dependency to simulate an error
    client.app.dependency_overrides[get_shopify_sync_service] = lambda: (_ for _ in ()).throw(HTTPException(status_code=400, detail="Shopify service error"))

    # Use a known mutation for testing
    mutation_name = list(MUTATION_PAYLOAD_MAP.keys())[0]
    path = f"/api/shopify/{{store_id}}/mutations/{mutation_name}"

    response = client.post(
        path.format(store_id=test_store.id),
        json={}
    )
    assert response.status_code == 400 # Router returns 400 for generic bad request errors
    assert response.json()["detail"] == "Shopify service error"


def test_dynamic_query_shopify_service_error(client: TestClient, test_store: Store, mocker: MagicMock):
    # Directly override the dependency to simulate an error
    client.app.dependency_overrides[get_shopify_sync_service] = lambda: (_ for _ in ()).throw(HTTPException(status_code=400, detail="Shopify service error"))

    # Use a known query for testing
    query_name = list(QUERY_PAYLOAD_MAP.keys())[0]
    path = f"/api/shopify/{{store_id}}/queries/{query_name}"

    response = client.post(
        path.format(store_id=test_store.id),
        json={}
    )
    assert response.status_code == 400 # Router returns 400 for generic bad request errors
    assert response.json()["detail"] == "Shopify service error"