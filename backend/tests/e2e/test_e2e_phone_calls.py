"""
E2E tests for phone call functionality including Twilio integration.
"""

import asyncio
import json
import logging
from unittest.mock import patch

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.main import app
from tests.utils.test_helpers import (
    ConversationTestHelper,
    MockSTTService,
    MockTTSService,
    MockTwilioService,
    WebSocketTestClient,
    create_mock_audio_data,
    create_test_customer,
    create_twilio_media_message,
    create_twilio_start_message,
    create_twilio_stop_message,
)

logger = logging.getLogger(__name__)


@pytest.mark.asyncio
async def test_e2e_phone_call_initiation(db_session: AsyncSession):
    """Test phone call initiation with Twilio integration."""
    logger.info("Starting test_e2e_phone_call_initiation")

    client = TestClient(app)
    customer = await create_test_customer(db_session, "Phone Test Customer")

    # Mock Twilio service
    mock_twilio = MockTwilioService()

    with patch("modules.call.service.twilio_service", mock_twilio):
        # Initiate phone call
        response = client.post(
            "/api/call",
            json={
                "type": "voice",
                "phone_number": "+1234567890",
                "custom_prompt": "You are a phone support agent.",
                "custom_first_message": "Hello! Thank you for calling.",
            },
        )

        assert response.status_code == 200
        data = response.json()

        assert data["success"] is True
        assert "call_history_id" in data
        assert "call_sid" in data
        assert data["message"] == "Phone call initiated successfully."

        # Verify Twilio service was called
        assert len(mock_twilio.calls_made) == 1
        call_made = mock_twilio.calls_made[0]
        assert call_made["to_number"] == "+1234567890"
        assert call_made["call_history_id"] == data["call_history_id"]

    logger.info("Finished test_e2e_phone_call_initiation")


@pytest.mark.asyncio
async def test_e2e_phone_call_initiation_failure(db_session: AsyncSession):
    """Test phone call initiation failure handling."""
    logger.info("Starting test_e2e_phone_call_initiation_failure")

    client = TestClient(app)
    customer = await create_test_customer(db_session, "Phone Test Customer")

    # Mock Twilio service to fail
    mock_twilio = MockTwilioService()
    mock_twilio.should_fail = True

    with patch("modules.call.service.twilio_service", mock_twilio):
        # Attempt to initiate phone call
        response = client.post(
            "/api/call",
            json={
                "type": "voice",
                "phone_number": "+1234567890",
                "custom_prompt": "You are a phone support agent.",
            },
        )

        assert response.status_code == 500
        data = response.json()
        assert "Failed to initiate call" in data["detail"]

    logger.info("Finished test_e2e_phone_call_initiation_failure")


@pytest.mark.asyncio
async def test_e2e_phone_call_missing_number(db_session: AsyncSession):
    """Test phone call initiation without phone number."""
    logger.info("Starting test_e2e_phone_call_missing_number")

    client = TestClient(app)
    customer = await create_test_customer(db_session, "Phone Test Customer")

    # Attempt to initiate phone call without number
    response = client.post(
        "/api/call",
        json={"type": "voice", "custom_prompt": "You are a phone support agent."},
    )

    assert response.status_code == 400
    data = response.json()
    assert "Phone number is required" in data["detail"]

    logger.info("Finished test_e2e_phone_call_missing_number")


@pytest.mark.asyncio
async def test_e2e_phone_call_twiml_generation(db_session: AsyncSession):
    """Test TwiML generation for phone calls."""
    logger.info("Starting test_e2e_phone_call_twiml_generation")

    client = TestClient(app)
    helper = ConversationTestHelper(client, db_session)
    call_history_id = await helper.setup_test_data("phone")

    # Test TwiML endpoint
    response = client.post(f"/api/call/twiml/{call_history_id}")

    assert response.status_code == 200
    assert response.headers["content-type"] == "application/xml; charset=utf-8"

    twiml_content = response.content.decode()
    assert "<Response>" in twiml_content
    assert "<Connect>" in twiml_content
    assert "<Stream" in twiml_content
    assert f"phone-relay/{call_history_id}" in twiml_content

    logger.info("Finished test_e2e_phone_call_twiml_generation")


@pytest.mark.asyncio
async def test_e2e_phone_call_status_webhook(db_session: AsyncSession):
    """Test Twilio status webhook handling."""
    logger.info("Starting test_e2e_phone_call_status_webhook")

    client = TestClient(app)
    helper = ConversationTestHelper(client, db_session)
    call_history_id = await helper.setup_test_data("phone")

    # Simulate Twilio status webhook
    webhook_data = {
        "CallSid": "CA1234567890abcdef",
        "CallStatus": "in-progress",
        "From": "+1234567890",
        "To": "+0987654321",
    }

    response = client.post(
        f"/api/call/webhook/{call_history_id}/status", data=webhook_data
    )

    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ok"

    # Test different call statuses
    statuses = ["ringing", "answered", "completed", "failed"]
    for status in statuses:
        webhook_data["CallStatus"] = status
        response = client.post(
            f"/api/call/webhook/{call_history_id}/status", data=webhook_data
        )
        assert response.status_code == 200

    logger.info("Finished test_e2e_phone_call_status_webhook")


@pytest.mark.asyncio
async def test_e2e_phone_relay_websocket_basic(db_session: AsyncSession):
    """Test basic phone relay WebSocket connection."""
    logger.info("Starting test_e2e_phone_relay_websocket_basic")

    client = TestClient(app)
    helper = ConversationTestHelper(client, db_session)
    call_history_id = await helper.setup_test_data("phone")

    # Mock STT and TTS services
    mock_stt = MockSTTService(["Hello, I need help", "Thank you"])
    mock_tts = MockTTSService()

    with (
        patch("modules.call.service.get_stt_service", return_value=mock_stt),
        patch("modules.call.service.get_tts_service", return_value=mock_tts),
    ):
        with WebSocketTestClient(
            client, f"/api/call/ws/phone-relay/{call_history_id}"
        ) as ws:
            # Send Twilio start message
            start_msg = create_twilio_start_message()
            ws.send_text(start_msg)

            # Send audio data
            audio_data = create_mock_audio_data(1.0)  # 1 second of audio
            media_msg = create_twilio_media_message(audio_data)
            ws.send_text(media_msg)

            # Should receive TTS response back
            try:
                response = ws.receive_text()
                response_data = json.loads(response)
                assert response_data["event"] == "media"
                assert "payload" in response_data["media"]
                logger.info("Received TTS audio response")
            except Exception as e:
                logger.warning(f"No immediate response received: {e}")

            # Send stop message
            stop_msg = create_twilio_stop_message()
            ws.send_text(stop_msg)

    logger.info("Finished test_e2e_phone_relay_websocket_basic")


@pytest.mark.asyncio
async def test_e2e_phone_relay_conversation_flow(db_session: AsyncSession):
    """Test complete phone conversation flow with multiple exchanges."""
    logger.info("Starting test_e2e_phone_relay_conversation_flow")

    client = TestClient(app)
    helper = ConversationTestHelper(client, db_session)
    call_history_id = await helper.setup_test_data("phone")

    # Mock services with multiple responses
    mock_stt = MockSTTService(
        [
            "Hello, I need help with my account",
            "Can you check my balance?",
            "Thank you for your help",
        ]
    )
    mock_tts = MockTTSService()

    with (
        patch("modules.call.service.get_stt_service", return_value=mock_stt),
        patch("modules.call.service.get_tts_service", return_value=mock_tts),
    ):
        with WebSocketTestClient(
            client, f"/api/call/ws/phone-relay/{call_history_id}"
        ) as ws:
            # Start conversation
            ws.send_text(create_twilio_start_message())

            # Simulate multiple audio exchanges
            for i in range(3):
                audio_data = create_mock_audio_data(1.0)
                media_msg = create_twilio_media_message(audio_data)
                ws.send_text(media_msg)

                # Allow time for processing
                await asyncio.sleep(0.1)

            # End conversation
            ws.send_text(create_twilio_stop_message())

            # Verify TTS was called for responses
            assert len(mock_tts.synthesized_texts) > 0
            logger.info(f"TTS synthesized {len(mock_tts.synthesized_texts)} responses")

    logger.info("Finished test_e2e_phone_relay_conversation_flow")


@pytest.mark.asyncio
async def test_e2e_phone_call_updates_websocket(db_session: AsyncSession):
    """Test phone call updates WebSocket for frontend notifications."""
    logger.info("Starting test_e2e_phone_call_updates_websocket")

    client = TestClient(app)
    helper = ConversationTestHelper(client, db_session)
    call_history_id = await helper.setup_test_data("phone")

    # Test call updates WebSocket
    with WebSocketTestClient(
        client, f"/api/call/ws/call-updates/{call_history_id}"
    ) as ws:
        # Connection should be established
        logger.info("Call updates WebSocket connected")

        # Send a test message (though this endpoint typically just listens)
        try:
            ws.send_text("test")
            # This endpoint doesn't typically respond, just maintains connection
            await asyncio.sleep(0.1)
        except Exception as e:
            logger.info(f"Expected behavior - updates socket doesn't respond: {e}")

    logger.info("Finished test_e2e_phone_call_updates_websocket")


@pytest.mark.asyncio
async def test_e2e_phone_call_error_scenarios(db_session: AsyncSession):
    """Test various error scenarios in phone calls."""
    logger.info("Starting test_e2e_phone_call_error_scenarios")

    client = TestClient(app)

    # Test with invalid call history ID
    with WebSocketTestClient(client, "/api/call/ws/phone-relay/99999") as ws:
        try:
            ws.send_text(create_twilio_start_message())
            # Should handle gracefully
            await asyncio.sleep(0.1)
        except Exception as e:
            logger.info(f"Expected error for invalid call history: {e}")

    # Test TwiML with invalid call history ID
    response = client.post("/api/call/twiml/99999")
    # Should still generate TwiML even with invalid ID
    assert response.status_code == 200

    # Test status webhook with invalid call history ID
    response = client.post(
        "/api/call/webhook/99999/status",
        data={"CallSid": "CA123", "CallStatus": "completed"},
    )
    # Should handle gracefully
    assert response.status_code == 200

    logger.info("Finished test_e2e_phone_call_error_scenarios")
