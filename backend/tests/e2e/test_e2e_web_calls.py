"""
E2E tests for web call functionality including voice input/output.
"""

import base64
import logging
from unittest.mock import AsyncMock, patch

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.main import app
from tests.utils.test_helpers import (
    ConversationTestHelper,
    MockSTTService,
    MockTTSService,
    WebSocketTestClient,
    assert_valid_conversation_response,
    create_mock_audio_data,
    create_test_customer,
)

logger = logging.getLogger(__name__)


@pytest.mark.asyncio
async def test_e2e_web_call_initiation(db_session: AsyncSession):
    """Test web call initiation."""
    logger.info("Starting test_e2e_web_call_initiation")

    client = TestClient(app)
    customer = await create_test_customer(db_session, "Web Call Test Customer")

    # Initiate web call
    response = client.post(
        "/api/call",
        json={
            "type": "web",
            "custom_prompt": "You are a web-based voice assistant.",
            "custom_first_message": "Hello! I'm your voice assistant. How can I help you today?",
        },
    )

    assert response.status_code == 200
    data = response.json()

    assert data["success"] is True
    assert "call_history_id" in data
    assert data["message"] == "Web call initiated successfully."

    logger.info("Finished test_e2e_web_call_initiation")


@pytest.mark.asyncio
async def test_e2e_web_call_websocket_basic(db_session: AsyncSession):
    """Test basic web call WebSocket connection and audio handling."""
    logger.info("Starting test_e2e_web_call_websocket_basic")

    client = TestClient(app)
    helper = ConversationTestHelper(client, db_session)
    call_history_id = await helper.setup_test_data("web_call")

    # Mock STT and TTS services
    mock_stt = MockSTTService(["Hello, I need help with my account"])
    mock_tts = MockTTSService()

    with (
        patch("modules.call.service.get_stt_service", return_value=mock_stt),
        patch("modules.call.service.get_tts_service", return_value=mock_tts),
    ):
        with WebSocketTestClient(client, "/api/call/ws/web-call") as ws:
            # Initialize conversation
            ws.send_json({"data": {"call_history_id": call_history_id}})

            # Should receive first message with audio
            first_msg = ws.receive_json()
            logger.info(f"First message: {first_msg}")

            # Could be either transcript or audio_response
            if first_msg.get("type") == "audio_response":
                assert "audio" in first_msg
                assert "transcript" in first_msg
                assert isinstance(first_msg["audio"], str)  # base64 encoded
                assert len(first_msg["audio"]) > 0
            else:
                assert_valid_conversation_response(first_msg, "transcript")

            # Send audio data
            audio_data = create_mock_audio_data(1.0)
            audio_base64 = base64.b64encode(audio_data).decode("utf-8")

            ws.send_json({"type": "audio_data", "audio": audio_base64})

            # Should receive response
            response = None
            for _ in range(5):
                msg = ws.receive_json()
                logger.info(f"Response message: {msg}")

                if msg.get("type") == "audio_response":
                    assert "audio" in msg
                    assert "transcript" in msg
                    assert msg.get("is_final") is True
                    response = msg
                    break
                elif msg.get("type") == "transcript" and msg.get("is_final"):
                    response = msg
                    break

            assert response is not None, "Did not receive final response"

    logger.info("Finished test_e2e_web_call_websocket_basic")


@pytest.mark.asyncio
async def test_e2e_web_call_text_fallback(db_session: AsyncSession):
    """Test web call with text input as fallback."""
    logger.info("Starting test_e2e_web_call_text_fallback")

    client = TestClient(app)
    helper = ConversationTestHelper(client, db_session)
    call_history_id = await helper.setup_test_data("web_call")

    mock_tts = MockTTSService()

    with patch("modules.call.service.get_tts_service", return_value=mock_tts):
        with WebSocketTestClient(client, "/api/call/ws/web-call") as ws:
            # Initialize conversation
            ws.send_json({"data": {"call_history_id": call_history_id}})

            # Skip first message
            first_msg = ws.receive_json()
            logger.info(f"First message: {first_msg}")

            # Send text message instead of audio
            ws.send_json(
                {"type": "user_message", "text": "I need help with my account"}
            )

            # Should receive response with audio
            response = None
            for _ in range(5):
                msg = ws.receive_json()
                logger.info(f"Response: {msg}")

                if msg.get("is_final"):
                    response = msg
                    break

            assert response is not None
            # Should have audio response for web calls
            if response.get("type") == "audio_response":
                assert "audio" in response
                assert "transcript" in response

    logger.info("Finished test_e2e_web_call_text_fallback")


@pytest.mark.asyncio
async def test_e2e_web_call_multiple_audio_exchanges(db_session: AsyncSession):
    """Test web call with multiple audio exchanges."""
    logger.info("Starting test_e2e_web_call_multiple_audio_exchanges")

    client = TestClient(app)
    helper = ConversationTestHelper(client, db_session)
    call_history_id = await helper.setup_test_data("web_call")

    # Mock services with multiple responses
    mock_stt = MockSTTService(
        [
            "Hello, I need help",
            "Can you tell me about your services?",
            "Thank you for the information",
        ]
    )
    mock_tts = MockTTSService()

    with (
        patch("modules.call.service.get_stt_service", return_value=mock_stt),
        patch("modules.call.service.get_tts_service", return_value=mock_tts),
    ):
        with WebSocketTestClient(client, "/api/call/ws/web-call") as ws:
            # Initialize conversation
            ws.send_json({"data": {"call_history_id": call_history_id}})

            # Skip first message
            first_msg = ws.receive_json()

            # Send multiple audio messages
            for i in range(3):
                audio_data = create_mock_audio_data(1.0)
                audio_base64 = base64.b64encode(audio_data).decode("utf-8")

                ws.send_json({"type": "audio_data", "audio": audio_base64})

                # Wait for response
                response = None
                for _ in range(5):
                    msg = ws.receive_json()
                    if msg.get("is_final"):
                        response = msg
                        break

                assert response is not None
                logger.info(f"Exchange {i+1} completed")

            # Verify TTS was called for responses
            assert len(mock_tts.synthesized_texts) >= 3
            logger.info(f"TTS synthesized {len(mock_tts.synthesized_texts)} responses")

    logger.info("Finished test_e2e_web_call_multiple_audio_exchanges")


@pytest.mark.asyncio
async def test_e2e_web_call_tts_failure_fallback(db_session: AsyncSession):
    """Test web call behavior when TTS fails."""
    logger.info("Starting test_e2e_web_call_tts_failure_fallback")

    client = TestClient(app)
    helper = ConversationTestHelper(client, db_session)
    call_history_id = await helper.setup_test_data("web_call")

    mock_stt = MockSTTService(["Hello, test message"])

    # Mock TTS to fail
    mock_tts = AsyncMock()
    mock_tts.synthesize_speech.side_effect = Exception("TTS service unavailable")

    with (
        patch("modules.call.service.get_stt_service", return_value=mock_stt),
        patch("modules.call.service.get_tts_service", return_value=mock_tts),
    ):
        with WebSocketTestClient(client, "/api/call/ws/web-call") as ws:
            # Initialize conversation
            ws.send_json({"data": {"call_history_id": call_history_id}})

            # Skip first message (might also fail TTS)
            try:
                first_msg = ws.receive_json()
                logger.info(f"First message: {first_msg}")
            except Exception as e:
                logger.info(f"First message failed as expected: {e}")

            # Send audio data
            audio_data = create_mock_audio_data(1.0)
            audio_base64 = base64.b64encode(audio_data).decode("utf-8")

            ws.send_json({"type": "audio_data", "audio": audio_base64})

            # Should fallback to text-only response
            response = None
            for _ in range(5):
                msg = ws.receive_json()
                logger.info(f"Fallback response: {msg}")

                if msg.get("is_final"):
                    response = msg
                    break

            assert response is not None
            # Should be text response when TTS fails
            if response.get("type") == "transcript":
                assert_valid_conversation_response(response, "transcript")

    logger.info("Finished test_e2e_web_call_tts_failure_fallback")


@pytest.mark.asyncio
async def test_e2e_web_call_stt_failure_handling(db_session: AsyncSession):
    """Test web call behavior when STT fails."""
    logger.info("Starting test_e2e_web_call_stt_failure_handling")

    client = TestClient(app)
    helper = ConversationTestHelper(client, db_session)
    call_history_id = await helper.setup_test_data("web_call")

    # Mock STT to fail
    mock_stt = AsyncMock()
    mock_stt.transcribe_audio.side_effect = Exception("STT service unavailable")

    mock_tts = MockTTSService()

    with (
        patch("modules.call.service.get_stt_service", return_value=mock_stt),
        patch("modules.call.service.get_tts_service", return_value=mock_tts),
    ):
        with WebSocketTestClient(client, "/api/call/ws/web-call") as ws:
            # Initialize conversation
            ws.send_json({"data": {"call_history_id": call_history_id}})

            # Skip first message
            first_msg = ws.receive_json()

            # Send audio data (STT will fail)
            audio_data = create_mock_audio_data(1.0)
            audio_base64 = base64.b64encode(audio_data).decode("utf-8")

            ws.send_json({"type": "audio_data", "audio": audio_base64})

            # Should handle STT failure gracefully
            # Might not get a response or get an error response
            try:
                response = ws.receive_json()
                logger.info(f"STT failure response: {response}")
            except Exception as e:
                logger.info(f"No response due to STT failure: {e}")

            # Try text fallback
            ws.send_json({"type": "user_message", "text": "This is a text message"})

            # Should work with text input
            response = None
            for _ in range(5):
                msg = ws.receive_json()
                if msg.get("is_final"):
                    response = msg
                    break

            if response:
                logger.info(f"Text fallback worked: {response}")

    logger.info("Finished test_e2e_web_call_stt_failure_handling")


@pytest.mark.asyncio
async def test_e2e_web_call_custom_prompts(db_session: AsyncSession):
    """Test web call with custom system prompts."""
    logger.info("Starting test_e2e_web_call_custom_prompts")

    client = TestClient(app)
    customer = await create_test_customer(db_session, "Custom Web Call Customer")

    # Initiate web call with custom prompts
    response = client.post(
        "/api/call",
        json={
            "type": "web",
            "custom_prompt": "You are a specialized technical support voice assistant. Speak clearly and be very technical.",
            "custom_first_message": "Welcome to technical support voice assistance! Please describe your technical issue.",
        },
    )

    assert response.status_code == 200
    call_history_id = response.json()["call_history_id"]

    mock_stt = MockSTTService(["I'm having API connectivity issues"])
    mock_tts = MockTTSService()

    with (
        patch("modules.call.service.get_stt_service", return_value=mock_stt),
        patch("modules.call.service.get_tts_service", return_value=mock_tts),
    ):
        with WebSocketTestClient(client, "/api/call/ws/web-call") as ws:
            # Initialize conversation
            ws.send_json({"data": {"call_history_id": call_history_id}})

            # Check custom first message
            first_msg = ws.receive_json()
            logger.info(f"Custom first message: {first_msg}")

            if first_msg.get("type") == "audio_response":
                assert "technical support" in first_msg["transcript"].lower()
            elif first_msg.get("type") == "transcript":
                assert "technical support" in first_msg["transcript"].lower()

            # Send technical question
            audio_data = create_mock_audio_data(1.0)
            audio_base64 = base64.b64encode(audio_data).decode("utf-8")

            ws.send_json({"type": "audio_data", "audio": audio_base64})

            # Should get technical response
            response = None
            for _ in range(5):
                msg = ws.receive_json()
                if msg.get("is_final"):
                    response = msg
                    break

            assert response is not None
            logger.info(f"Technical response: {response}")

    logger.info("Finished test_e2e_web_call_custom_prompts")


@pytest.mark.asyncio
async def test_e2e_web_call_error_scenarios(db_session: AsyncSession):
    """Test various error scenarios in web calls."""
    logger.info("Starting test_e2e_web_call_error_scenarios")

    client = TestClient(app)

    # Test with invalid call history ID
    with WebSocketTestClient(client, "/api/call/ws/web-call") as ws:
        try:
            ws.send_json({"data": {"call_history_id": 99999}})
            # Should handle gracefully
            msg = ws.receive_json()
            logger.info(f"Invalid ID response: {msg}")
        except Exception as e:
            logger.info(f"Expected error for invalid call history: {e}")

    # Test with missing call history ID
    with WebSocketTestClient(client, "/api/call/ws/web-call") as ws:
        try:
            ws.send_json({"data": {}})
            msg = ws.receive_json()
            logger.info(f"Missing ID response: {msg}")
        except Exception as e:
            logger.info(f"Expected error for missing ID: {e}")

    # Test with invalid audio data
    helper = ConversationTestHelper(client, db_session)
    call_history_id = await helper.setup_test_data("web_call")

    with WebSocketTestClient(client, "/api/call/ws/web-call") as ws:
        ws.send_json({"data": {"call_history_id": call_history_id}})

        # Skip first message
        first_msg = ws.receive_json()

        # Send invalid audio data
        ws.send_json({"type": "audio_data", "audio": "invalid_base64_data"})

        # Should handle gracefully
        try:
            response = ws.receive_json()
            logger.info(f"Invalid audio response: {response}")
        except Exception as e:
            logger.info(f"No response to invalid audio: {e}")

    logger.info("Finished test_e2e_web_call_error_scenarios")
