import logging

import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.ext.asyncio import AsyncSession

from src.main import app
from tests.utils.test_helpers import (
    ConversationTestHelper,
    WebSocketTestClient,
    assert_valid_conversation_response,
    create_test_customer,
)

logger = logging.getLogger(__name__)


@pytest.mark.asyncio
async def test_e2e_text_chat_basic_conversation(db_session: AsyncSession):
    """Test basic text chat conversation flow."""
    logger.info("Starting test_e2e_text_chat_basic_conversation")

    # Setup test data
    helper = ConversationTestHelper(TestClient(app), db_session)
    call_history_id = await helper.setup_test_data("text_chat")

    # Test conversation flow
    with WebSocketTestClient(helper.client, "/api/call/ws/text-chat") as ws:
        # Initialize conversation
        ws.send_json({"data": {"call_history_id": call_history_id}})

        # Expect first agent message
        first_msg = ws.receive_json()
        logger.info(f"Agent first message: {first_msg}")
        assert_valid_conversation_response(first_msg, "transcript")
        assert first_msg["sender"] == "agent"
        assert first_msg["is_final"] is True

        # Send user message and get response
        ws.send_json({"type": "user_message", "text": "What can you help me with?"})

        # May receive multiple chunks, wait for final response
        response = None
        for _ in range(5):  # Max 5 attempts to get final response
            msg = ws.receive_json()
            logger.info(f"Agent response chunk: {msg}")
            assert_valid_conversation_response(msg, "transcript")
            if msg.get("is_final"):
                response = msg
                break

        assert response is not None, "Did not receive final response"
        assert response["sender"] == "agent"
        assert len(response["transcript"]) > 0

        # Test another exchange
        ws.send_json({"type": "user_message", "text": "Thank you for your help"})

        final_response = None
        for _ in range(5):
            msg = ws.receive_json()
            if msg.get("is_final"):
                final_response = msg
                break

        assert final_response is not None
        assert_valid_conversation_response(final_response, "transcript")

    logger.info("Finished test_e2e_text_chat_basic_conversation")


@pytest.mark.asyncio
async def test_e2e_text_chat_multiple_exchanges(db_session: AsyncSession):
    """Test text chat with multiple message exchanges."""
    logger.info("Starting test_e2e_text_chat_multiple_exchanges")

    helper = ConversationTestHelper(TestClient(app), db_session)
    call_history_id = await helper.setup_test_data("text_chat")

    user_messages = [
        "Hello, I need help with my account",
        "Can you tell me about your services?",
        "What are your business hours?",
        "Thank you for the information",
    ]

    with WebSocketTestClient(helper.client, "/api/call/ws/text-chat") as ws:
        ws.send_json({"data": {"call_history_id": call_history_id}})

        # Skip first message
        first_msg = ws.receive_json()
        assert_valid_conversation_response(first_msg, "transcript")

        responses = await helper.simulate_conversation_flow(ws, user_messages)

        # Verify all responses
        assert len(responses) >= len(user_messages)
        for response in responses:
            if response.get("is_final"):
                assert_valid_conversation_response(response, "transcript")
                assert response["sender"] == "agent"

    logger.info("Finished test_e2e_text_chat_multiple_exchanges")


@pytest.mark.asyncio
async def test_e2e_text_chat_error_handling(db_session: AsyncSession):
    """Test text chat error handling scenarios."""
    logger.info("Starting test_e2e_text_chat_error_handling")

    helper = ConversationTestHelper(TestClient(app), db_session)

    # Test with invalid call history ID
    with WebSocketTestClient(helper.client, "/api/call/ws/text-chat") as ws:
        ws.send_json({"data": {"call_history_id": 99999}})

        # Should handle gracefully - connection might close or send error
        try:
            msg = ws.receive_json()
            logger.info(f"Error handling response: {msg}")
        except Exception as e:
            logger.info(f"Connection closed as expected: {e}")

    # Test with missing call history ID
    with WebSocketTestClient(helper.client, "/api/call/ws/text-chat") as ws:
        try:
            ws.send_json({"data": {}})
            msg = ws.receive_json()
            logger.info(f"Missing ID response: {msg}")
        except Exception as e:
            logger.info(f"Connection closed as expected for missing ID: {e}")

    logger.info("Finished test_e2e_text_chat_error_handling")


@pytest.mark.asyncio
async def test_e2e_text_chat_custom_prompts(db_session: AsyncSession):
    """Test text chat with custom system prompts and first messages."""
    logger.info("Starting test_e2e_text_chat_custom_prompts")

    client = TestClient(app)
    customer = await create_test_customer(db_session, "Custom Test Customer")

    # Initiate chat with custom prompts
    response = client.post(
        "/api/call",
        json={
            "type": "text",
            "custom_prompt": "You are a specialized technical support agent. Be very technical in your responses.",
            "custom_first_message": "Welcome to technical support! How can I assist you with your technical issue today?",
        },
    )

    assert response.status_code == 200
    data = response.json()
    call_history_id = data["call_history_id"]

    with WebSocketTestClient(client, "/api/call/ws/text-chat") as ws:
        ws.send_json({"data": {"call_history_id": call_history_id}})

        # Check custom first message
        first_msg = ws.receive_json()
        assert_valid_conversation_response(first_msg, "transcript")
        assert "technical support" in first_msg["transcript"].lower()

        # Send technical question
        ws.send_json(
            {"type": "user_message", "text": "I'm having API connectivity issues"}
        )

        response = None
        for _ in range(5):
            msg = ws.receive_json()
            if msg.get("is_final"):
                response = msg
                break

        assert response is not None
        assert_valid_conversation_response(response, "transcript")
        # Response should be technical due to custom prompt
        assert len(response["transcript"]) > 0

    logger.info("Finished test_e2e_text_chat_custom_prompts")


@pytest.mark.asyncio
async def test_e2e_text_chat_conversation_persistence(db_session: AsyncSession):
    """Test that conversation history is properly maintained."""
    logger.info("Starting test_e2e_text_chat_conversation_persistence")

    helper = ConversationTestHelper(TestClient(app), db_session)
    call_history_id = await helper.setup_test_data("text_chat")

    with WebSocketTestClient(helper.client, "/api/call/ws/text-chat") as ws:
        ws.send_json({"data": {"call_history_id": call_history_id}})

        # Skip first message
        first_msg = ws.receive_json()
        assert_valid_conversation_response(first_msg, "transcript")

        # First exchange - establish context
        ws.send_json(
            {"type": "user_message", "text": "My name is John and I work at Acme Corp"}
        )

        response1 = None
        for _ in range(5):
            msg = ws.receive_json()
            if msg.get("is_final"):
                response1 = msg
                break

        assert response1 is not None
        assert_valid_conversation_response(response1, "transcript")

        # Second exchange - reference previous context
        ws.send_json(
            {"type": "user_message", "text": "What company did I mention I work for?"}
        )

        response2 = None
        for _ in range(5):
            msg = ws.receive_json()
            if msg.get("is_final"):
                response2 = msg
                break

        assert response2 is not None
        assert_valid_conversation_response(response2, "transcript")
        # The agent should remember the company name from context
        # Note: This test depends on the LLM's ability to maintain context

    logger.info("Finished test_e2e_text_chat_conversation_persistence")


@pytest.mark.asyncio
async def test_e2e_text_chat_concurrent_connections(db_session: AsyncSession):
    """Test multiple concurrent text chat connections."""
    logger.info("Starting test_e2e_text_chat_concurrent_connections")

    client = TestClient(app)

    # Create multiple customers and call histories
    customers = []
    call_history_ids = []

    for i in range(3):
        customer = await create_test_customer(
            db_session, f"Customer {i}", f"test{i}.customer"
        )
        customers.append(customer)

        response = client.post(
            "/api/call",
            json={
                "type": "text",
                "custom_first_message": f"Hello Customer {i}! How can I help you?",
            },
        )
        assert response.status_code == 200
        call_history_ids.append(response.json()["call_history_id"])

    # Test concurrent connections
    connections = []
    try:
        for i, call_history_id in enumerate(call_history_ids):
            ws = WebSocketTestClient(client, "/api/call/ws/text-chat")
            ws.__enter__()
            connections.append(ws)

            # Initialize each connection
            ws.send_json({"data": {"call_history_id": call_history_id}})
            first_msg = ws.receive_json()
            assert_valid_conversation_response(first_msg, "transcript")
            assert f"Customer {i}" in first_msg["transcript"]

        # Send messages to all connections
        for i, ws in enumerate(connections):
            ws.send_json(
                {"type": "user_message", "text": f"This is message from connection {i}"}
            )

            response = None
            for _ in range(5):
                msg = ws.receive_json()
                if msg.get("is_final"):
                    response = msg
                    break

            assert response is not None
            assert_valid_conversation_response(response, "transcript")

    finally:
        # Clean up connections
        for ws in connections:
            try:
                ws.__exit__(None, None, None)
            except Exception as e:
                logger.warning(f"Error closing connection: {e}")

    logger.info("Finished test_e2e_text_chat_concurrent_connections")


@pytest.mark.asyncio
async def test_e2e_text_chat_empty_and_invalid_messages(db_session: AsyncSession):
    """Test handling of empty and invalid messages."""
    logger.info("Starting test_e2e_text_chat_empty_and_invalid_messages")

    helper = ConversationTestHelper(TestClient(app), db_session)
    call_history_id = await helper.setup_test_data("text_chat")

    with WebSocketTestClient(helper.client, "/api/call/ws/text-chat") as ws:
        ws.send_json({"data": {"call_history_id": call_history_id}})

        # Skip first message
        first_msg = ws.receive_json()
        assert_valid_conversation_response(first_msg, "transcript")

        # Test empty message
        ws.send_json({"type": "user_message", "text": ""})

        # Should handle gracefully - might not respond or send error
        try:
            response = ws.receive_json()
            logger.info(f"Empty message response: {response}")
        except Exception as e:
            logger.info(f"No response to empty message: {e}")

        # Test invalid message format
        ws.send_json({"invalid": "format"})

        # Should handle gracefully
        try:
            response = ws.receive_json()
            logger.info(f"Invalid format response: {response}")
        except Exception as e:
            logger.info(f"No response to invalid format: {e}")

        # Test valid message after invalid ones
        ws.send_json({"type": "user_message", "text": "This is a valid message"})

        response = None
        for _ in range(5):
            try:
                msg = ws.receive_json()
                if msg.get("is_final"):
                    response = msg
                    break
            except Exception as e:
                logger.warning(f"Error receiving message: {e}")
                break

        if response:
            assert_valid_conversation_response(response, "transcript")

    logger.info("Finished test_e2e_text_chat_empty_and_invalid_messages")
