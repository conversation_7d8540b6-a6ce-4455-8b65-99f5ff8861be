import pytest

from models.models import User
from services.user_service import get_password_hash, sync_shopify_customers


def test_get_password_hash():
    password = "testpassword"
    hashed_password = get_password_hash(password)
    assert isinstance(hashed_password, str)
    assert hashed_password != password


def test_sync_shopify_customers_add_new(db_session):
    customers = [{"node": {"email": "<EMAIL>", "firstName": "New", "lastName": "User"}}]
    added, updated, unchanged = sync_shopify_customers(db_session, customers)
    assert added == 1
    assert updated == 0
    assert unchanged == 0
    new_user = db_session.query(User).filter_by(email="<EMAIL>").first()
    assert new_user is not None
    assert new_user.full_name == "New User"


def test_sync_shopify_customers_update_existing(db_session):
    existing_user = User(email="<EMAIL>", full_name="Old Name", hashed_password="password")
    db_session.add(existing_user)
    db_session.commit()

    customers = [
        {
            "node": {
                "email": "<EMAIL>",
                "firstName": "Updated",
                "lastName": "Name",
            }
        }
    ]
    added, updated, unchanged = sync_shopify_customers(db_session, customers)
    assert added == 0
    assert updated == 1
    assert unchanged == 0
    updated_user = db_session.query(User).filter_by(email="<EMAIL>").first()
    assert updated_user.full_name == "Updated Name"


def test_sync_shopify_customers_no_change(db_session):
    existing_user = User(email="<EMAIL>", full_name="Same Name", hashed_password="password")
    db_session.add(existing_user)
    db_session.commit()

    customers = [
        {
            "node": {
                "email": "<EMAIL>",
                "firstName": "Same",
                "lastName": "Name",
            }
        }
    ]
    added, updated, unchanged = sync_shopify_customers(db_session, customers)
    assert added == 0
    assert updated == 0
    assert unchanged == 1


def test_sync_shopify_customers_no_email(db_session):
    customers = [{"node": {"firstName": "No", "lastName": "Email"}}]
    added, updated, unchanged = sync_shopify_customers(db_session, customers)
    assert added == 0
    assert updated == 0
    assert unchanged == 0
