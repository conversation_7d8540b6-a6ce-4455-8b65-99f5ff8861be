import pytest

from models.models import User


@pytest.fixture
def another_user(db_session):
    user = User(
        email="<EMAIL>",
        hashed_password="anotherpassword",
        full_name="Another User",
        is_active=True,
    )
    db_session.add(user)
    db_session.commit()
    return user


def test_get_all_users(client, authenticated_user, test_user, another_user):
    response = client.get("/api/users/", headers=authenticated_user)
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 2
    assert data[0]["email"] == test_user.email
    assert data[1]["email"] == another_user.email


def test_get_user_by_id(client, authenticated_user, test_user):
    response = client.get(f"/api/users/{test_user.id}", headers=authenticated_user)
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == test_user.email


def test_get_user_by_id_not_found(client, authenticated_user):
    response = client.get("/api/users/999", headers=authenticated_user)
    assert response.status_code == 404
    assert "User not found" in response.json()["detail"]
