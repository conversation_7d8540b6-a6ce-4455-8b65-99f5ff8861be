"""
Integration tests for the campaigns module.

Tests the complete workflow from campaign creation to analytics reporting.
"""

import pytest
import asyncio
from decimal import Decimal
from datetime import datetime, timedelta
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from main import app
from core.db.database import get_db
from modules.campaigns import crud, schemas
from modules.campaigns.models import BusinessProfile, Campaign, AnalyticsData, Appointment
from modules.users.models import User


@pytest.fixture
async def test_user(db_session: AsyncSession):
    """Create a test user."""
    user = User(
        email="<EMAIL>",
        full_name="Dr. Test User",
        hashed_password="hashed_password_here",
        is_active=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


@pytest.fixture
async def test_business_profile(db_session: AsyncSession, test_user: User):
    """Create a test business profile."""
    profile_data = schemas.BusinessProfileCreate(
        business_name="Test Wellness Center",
        business_type="massage_therapy",
        description="A test wellness center for integration testing",
        address="Bahnhofstrasse 1",
        city="Zurich",
        postal_code="8001",
        country="Switzerland",
        phone="+41 44 123 45 67",
        website="https://test-wellness.ch",
        services_offered=["Deep tissue massage", "Swedish massage", "Sports therapy"],
        booking_url="https://test-wellness.ch/book"
    )
    
    profile = await crud.create_business_profile(db_session, profile_data, test_user.id)
    return profile


@pytest.fixture
async def authenticated_client(test_user: User):
    """Create an authenticated HTTP client."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        # Mock authentication - in real tests, you'd use proper JWT tokens
        client.headers.update({"Authorization": f"Bearer mock_token_{test_user.id}"})
        yield client


class TestCampaignWorkflow:
    """Test the complete campaign workflow."""
    
    async def test_create_campaign_workflow(
        self, 
        authenticated_client: AsyncClient,
        test_business_profile: BusinessProfile
    ):
        """Test creating a campaign through the API."""
        
        campaign_data = {
            "name": "Test Summer Campaign",
            "business_profile_id": test_business_profile.id,
            "budget": 1000,
            "duration_days": 30,
            "primary_goal": "appointments",
            "target_appointments": 25,
            "target_cost_per_appointment": 40,
            "ad_platform": "meta_ads"
        }
        
        # Create campaign
        response = await authenticated_client.post("/api/campaigns/", json=campaign_data)
        assert response.status_code == 200
        
        campaign = response.json()
        assert campaign["name"] == "Test Summer Campaign"
        assert campaign["status"] == "draft"
        assert campaign["budget"] == 1000
        
        return campaign
    
    async def test_ai_ad_generation_workflow(
        self,
        authenticated_client: AsyncClient,
        test_business_profile: BusinessProfile
    ):
        """Test AI ad generation workflow."""
        
        ai_request = {
            "business_name": test_business_profile.business_name,
            "business_type": test_business_profile.business_type,
            "location": f"{test_business_profile.city}, {test_business_profile.country}",
            "services": test_business_profile.services_offered,
            "target_audience": "Adults seeking wellness services",
            "unique_selling_points": ["Professional", "Experienced", "Convenient location"],
            "goal": "appointments",
            "budget": 1000
        }
        
        # Generate AI content
        response = await authenticated_client.post("/api/campaigns/ai/generate-ad", json=ai_request)
        assert response.status_code == 200
        
        ai_content = response.json()
        assert "ad_copy_variations" in ai_content
        assert "headlines" in ai_content
        assert "descriptions" in ai_content
        assert "targeting_suggestions" in ai_content
        assert "estimated_performance" in ai_content
        
        # Verify content quality
        assert len(ai_content["ad_copy_variations"]) >= 3
        assert len(ai_content["headlines"]) >= 3
        assert len(ai_content["descriptions"]) >= 3
        
        return ai_content
    
    async def test_campaign_launch_workflow(
        self,
        authenticated_client: AsyncClient,
        db_session: AsyncSession,
        test_business_profile: BusinessProfile
    ):
        """Test launching a campaign on Meta Ads."""
        
        # First create a campaign
        campaign = await self.test_create_campaign_workflow(
            authenticated_client, test_business_profile
        )
        
        # Generate AI content
        ai_content = await self.test_ai_ad_generation_workflow(
            authenticated_client, test_business_profile
        )
        
        # Update campaign with AI content
        update_data = {
            "selected_ad_copy": ai_content["ad_copy_variations"][0],
            "selected_headline": ai_content["headlines"][0],
            "selected_description": ai_content["descriptions"][0],
            "selected_targeting": ai_content["targeting_suggestions"]
        }
        
        response = await authenticated_client.put(
            f"/api/campaigns/{campaign['id']}", 
            json=update_data
        )
        assert response.status_code == 200
        
        # Launch on Meta Ads (mock)
        launch_response = await authenticated_client.post(
            f"/api/campaigns/{campaign['id']}/launch-on-meta",
            json={"access_token": "mock_access_token"}
        )
        assert launch_response.status_code == 200
        
        launch_result = launch_response.json()
        assert launch_result["success"] == True
        assert "campaign" in launch_result
        
        return campaign, launch_result
    
    async def test_analytics_workflow(
        self,
        authenticated_client: AsyncClient,
        db_session: AsyncSession,
        test_business_profile: BusinessProfile
    ):
        """Test analytics data processing and reporting."""
        
        # Create and launch campaign
        campaign, launch_result = await self.test_campaign_launch_workflow(
            authenticated_client, db_session, test_business_profile
        )
        
        # Simulate webhook data from Meta Ads
        webhook_data = {
            "entry": [{
                "changes": [{
                    "field": "ads_insights",
                    "value": {
                        "campaign_id": launch_result["campaign"]["id"],
                        "spend": "125.50",
                        "impressions": "1250",
                        "clicks": "45",
                        "actions": [{"action_type": "lead", "value": "8"}]
                    }
                }]
            }]
        }
        
        # Process webhook
        webhook_response = await authenticated_client.post(
            "/api/campaigns/integrations/meta/webhook",
            json=webhook_data
        )
        assert webhook_response.status_code == 200
        
        # Get campaign analytics
        analytics_response = await authenticated_client.get(
            f"/api/campaigns/{campaign['id']}/analytics"
        )
        assert analytics_response.status_code == 200
        
        analytics_data = analytics_response.json()
        assert len(analytics_data) > 0
        
        # Get analytics summary
        summary_response = await authenticated_client.get(
            f"/api/campaigns/{campaign['id']}/analytics/summary"
        )
        assert summary_response.status_code == 200
        
        summary = summary_response.json()
        assert "total_spend" in summary
        assert "total_impressions" in summary
        assert "total_clicks" in summary
        
        return analytics_data, summary
    
    async def test_appointment_booking_workflow(
        self,
        authenticated_client: AsyncClient,
        db_session: AsyncSession,
        test_business_profile: BusinessProfile
    ):
        """Test appointment booking and ROI calculation."""
        
        # Create campaign
        campaign, _ = await self.test_campaign_launch_workflow(
            authenticated_client, db_session, test_business_profile
        )
        
        # Create appointments
        appointments_data = [
            {
                "campaign_id": campaign["id"],
                "customer_name": "John Doe",
                "customer_email": "<EMAIL>",
                "customer_phone": "+41 79 123 45 67",
                "service_type": "Deep tissue massage",
                "appointment_date": (datetime.now() + timedelta(days=1)).isoformat(),
                "estimated_value": 120,
                "status": "confirmed"
            },
            {
                "campaign_id": campaign["id"],
                "customer_name": "Jane Smith",
                "customer_email": "<EMAIL>",
                "customer_phone": "+41 79 987 65 43",
                "service_type": "Swedish massage",
                "appointment_date": (datetime.now() + timedelta(days=2)).isoformat(),
                "estimated_value": 100,
                "status": "confirmed"
            }
        ]
        
        created_appointments = []
        for appointment_data in appointments_data:
            response = await authenticated_client.post(
                "/api/campaigns/appointments",
                json=appointment_data
            )
            assert response.status_code == 200
            created_appointments.append(response.json())
        
        # Get ROI report
        roi_response = await authenticated_client.get(
            f"/api/campaigns/{campaign['id']}/roi-report"
        )
        assert roi_response.status_code == 200
        
        roi_report = roi_response.json()
        assert "total_appointments" in roi_report
        assert "total_revenue" in roi_report
        assert "roi_percentage" in roi_report
        assert "cost_per_appointment" in roi_report
        
        # Verify calculations
        assert roi_report["total_appointments"] == 2
        assert roi_report["total_revenue"] == 220  # 120 + 100
        
        return created_appointments, roi_report
    
    async def test_dashboard_data_workflow(
        self,
        authenticated_client: AsyncClient,
        db_session: AsyncSession,
        test_business_profile: BusinessProfile
    ):
        """Test dashboard data aggregation."""
        
        # Run through complete workflow
        await self.test_appointment_booking_workflow(
            authenticated_client, db_session, test_business_profile
        )
        
        # Get dashboard summary
        dashboard_response = await authenticated_client.get("/api/campaigns/dashboard/summary")
        assert dashboard_response.status_code == 200
        
        dashboard_data = dashboard_response.json()
        assert "total_campaigns" in dashboard_data
        assert "active_campaigns" in dashboard_data
        assert "total_appointments" in dashboard_data
        assert "total_spend" in dashboard_data
        assert "average_cost_per_appointment" in dashboard_data
        
        # Get full dashboard data
        full_dashboard_response = await authenticated_client.get("/api/campaigns/dashboard/full")
        assert full_dashboard_response.status_code == 200
        
        full_dashboard = full_dashboard_response.json()
        assert "summary" in full_dashboard
        assert "recent_campaigns" in full_dashboard
        assert "top_performing_campaigns" in full_dashboard
        assert "recent_appointments" in full_dashboard
        
        return dashboard_data, full_dashboard


class TestErrorHandling:
    """Test error handling and edge cases."""
    
    async def test_invalid_campaign_creation(self, authenticated_client: AsyncClient):
        """Test campaign creation with invalid data."""
        
        invalid_data = {
            "name": "",  # Empty name
            "business_profile_id": 999,  # Non-existent profile
            "budget": -100,  # Negative budget
            "duration_days": 0,  # Zero duration
            "primary_goal": "invalid_goal"  # Invalid goal
        }
        
        response = await authenticated_client.post("/api/campaigns/", json=invalid_data)
        assert response.status_code == 422  # Validation error
    
    async def test_unauthorized_access(self):
        """Test unauthorized access to protected endpoints."""
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            # No authentication header
            response = await client.get("/api/campaigns/")
            assert response.status_code == 401
    
    async def test_campaign_not_found(self, authenticated_client: AsyncClient):
        """Test accessing non-existent campaign."""
        
        response = await authenticated_client.get("/api/campaigns/999")
        assert response.status_code == 404


class TestPerformance:
    """Test performance and scalability."""
    
    async def test_bulk_analytics_processing(
        self,
        authenticated_client: AsyncClient,
        db_session: AsyncSession,
        test_business_profile: BusinessProfile
    ):
        """Test processing large amounts of analytics data."""
        
        # Create campaign
        campaign, _ = await self.test_campaign_launch_workflow(
            authenticated_client, db_session, test_business_profile
        )
        
        # Create multiple analytics entries
        analytics_entries = []
        for i in range(30):  # 30 days of data
            date = datetime.now() - timedelta(days=i)
            analytics_data = schemas.AnalyticsDataCreate(
                campaign_id=campaign["id"],
                date=date,
                total_spend=Decimal("50.00"),
                daily_spend=Decimal("50.00"),
                impressions=1000 + i * 10,
                clicks=30 + i,
                leads=2 + (i % 3),
                cost_per_click=Decimal("1.67"),
                platform_data={"test": True}
            )
            
            entry = await crud.create_analytics_data(db_session, analytics_data)
            analytics_entries.append(entry)
        
        # Test analytics retrieval performance
        import time
        start_time = time.time()
        
        response = await authenticated_client.get(f"/api/campaigns/{campaign['id']}/analytics")
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        assert response.status_code == 200
        assert processing_time < 2.0  # Should complete within 2 seconds
        
        analytics_data = response.json()
        assert len(analytics_data) == 30
        
        return analytics_entries


@pytest.mark.asyncio
async def test_complete_integration_workflow():
    """Test the complete end-to-end workflow."""
    
    # This would be run with proper test database setup
    # For now, it's a placeholder for the complete integration test
    
    print("Complete integration workflow test would run here")
    print("This test would cover:")
    print("1. User registration and authentication")
    print("2. Business profile creation")
    print("3. Campaign creation with AI generation")
    print("4. Campaign launch on Meta Ads")
    print("5. Analytics data processing")
    print("6. Appointment booking")
    print("7. ROI calculation and reporting")
    print("8. Dashboard data aggregation")
    
    assert True  # Placeholder assertion


if __name__ == "__main__":
    # Run integration tests
    asyncio.run(test_complete_integration_workflow())
