import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from unittest.mock import MagicMock

from models.models import User, Store
from schemas.schemas import StoreCreate
from routers.stores import get_store_mappings # Import the function to test


@pytest.fixture
def mock_shopify_mappings():
    """Mock shopify_mappings for testing get_store_mappings"""
    class MockFieldDetail:
        def __init__(self, name, type):
            self.name = name
            self.type = type

    class MockActionDetail:
        def __init__(self, name, payload_type, input_type, payload_fields, input_fields):
            self.name = name
            self.payload_type = payload_type
            self.input_type = input_type
            self.payload_fields = payload_fields
            self.input_fields = input_fields

    mock_mutation_payload_map = {
        "productCreate": {
            "payload_type": type("ProductCreatePayload", (object,), {"__annotations__": {"product": str}}),
            "input_type": type("ProductInput", (object,), {"__annotations__": {"title": str, "bodyHtml": str}}),
        }
    }
    mock_QUERY_PAYLOAD_MAP = {
        "products": {
            "return_type": type("ProductConnection", (object,), {"__annotations__": {"edges": str}}),
            "node_type": type("Product", (object,), {"__annotations__": {"id": str, "title": str}}),
        }
    }

    return MagicMock(
        MUTATION_PAYLOAD_MAP=mock_mutation_payload_map,
        QUERY_PAYLOAD_MAP=mock_QUERY_PAYLOAD_MAP,
    )


def test_create_store(client: TestClient, authenticated_user: dict, authenticated_user_id: int):
    store_data = {
        "name": "My New Store",
        "platform": "shopify",
        "shop_domain": "mynewstore.myshopify.com",
        "admin_access_token": "new_admin_token",
        "storefront_access_token": "new_storefront_token",
        "api_secret_key": "new_secret_key",
    }
    response = client.post(
        "/api/stores/",
        headers=authenticated_user,
        json=store_data,
    )
    assert response.status_code == 200
    assert response.json()["name"] == "My New Store"
    assert response.json()["owner_id"] == authenticated_user_id


def test_create_store_unauthorized(client: TestClient):
    store_data = {
        "name": "Unauthorized Store",
        "platform": "shopify",
        "shop_domain": "unauth.myshopify.com",
        "admin_access_token": "token",
        "storefront_access_token": "token",
        "api_secret_key": "key",
    }
    response = client.post(
        "/api/stores/",
        json=store_data,
    )
    assert response.status_code == 403


def test_get_stores(client: TestClient, authenticated_user: dict, test_store: Store):
    response = client.get(
        "/api/stores/",
        headers=authenticated_user,
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["name"] == test_store.name


def test_get_stores_no_stores(client: TestClient, authenticated_user: dict, db_session: Session):
    # Clear existing stores for this test
    db_session.query(Store).delete()
    db_session.commit()

    response = client.get(
        "/api/stores/",
        headers=authenticated_user,
    )
    assert response.status_code == 200
    assert len(response.json()) == 0


def test_get_store_by_id(client: TestClient, authenticated_user: dict, test_store: Store):
    response = client.get(
        f"/api/stores/{test_store.id}",
        headers=authenticated_user,
    )
    assert response.status_code == 200
    assert response.json()["name"] == test_store.name


def test_get_store_by_id_not_found(client: TestClient, authenticated_user: dict):
    response = client.get(
        "/api/stores/999",  # Non-existent ID
        headers=authenticated_user,
    )
    assert response.status_code == 404


def test_get_store_by_id_not_owned(client: TestClient, db_session: Session, authenticated_user: dict):
    # Create a store owned by a different user
    another_user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        full_name="Another User",
    )
    db_session.add(another_user)
    db_session.commit()
    db_session.refresh(another_user)

    store_not_owned = Store(
        name="Not My Store",
        platform="woocommerce",
        owner_id=another_user.id,
        shop_domain="notmystore.com",
    )
    db_session.add(store_not_owned)
    db_session.commit()
    db_session.refresh(store_not_owned)

    response = client.get(
        f"/api/stores/{store_not_owned.id}",
        headers=authenticated_user,
    )
    assert response.status_code == 404


def test_delete_store(client: TestClient, authenticated_user: dict, test_store: Store, db_session: Session):
    response = client.delete(
        f"/api/stores/{test_store.id}",
        headers=authenticated_user,
    )
    assert response.status_code == 204
    # Verify it's deleted from DB
    assert db_session.query(Store).filter(Store.id == test_store.id).first() is None


def test_delete_store_not_found(client: TestClient, authenticated_user: dict):
    response = client.delete(
        "/api/stores/999",  # Non-existent ID
        headers=authenticated_user,
    )
    assert response.status_code == 404


def test_delete_store_not_owned(client: TestClient, db_session: Session, authenticated_user: dict):
    # Create a store owned by a different user
    another_user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        full_name="Delete User",
    )
    db_session.add(another_user)
    db_session.commit()
    db_session.refresh(another_user)

    store_not_owned = Store(
        name="Not My Store to Delete",
        platform="woocommerce",
        owner_id=another_user.id,
        shop_domain="notmystoretodelete.com",
    )
    db_session.add(store_not_owned)
    db_session.commit()
    db_session.refresh(store_not_owned)

    response = client.delete(
        f"/api/stores/{store_not_owned.id}",
        headers=authenticated_user,
    )
    assert response.status_code == 404


def test_toggle_store_activation(client: TestClient, authenticated_user: dict, test_store: Store):
    initial_status = test_store.is_active
    response = client.post(
        f"/api/stores/{test_store.id}/toggle-activation",
        headers=authenticated_user,
    )
    assert response.status_code == 200
    assert response.json()["message"] == f"Store {test_store.id} activation toggled to {not initial_status}"
    # Verify status in DB
    assert test_store.is_active == (not initial_status)


def test_toggle_store_activation_not_found(client: TestClient, authenticated_user: dict):
    response = client.post(
        "/api/stores/999/toggle-activation",  # Non-existent ID
        headers=authenticated_user,
    )
    assert response.status_code == 404


def test_toggle_store_activation_not_owned(client: TestClient, db_session: Session, authenticated_user: dict):
    # Create a store owned by a different user
    another_user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        full_name="Toggle User",
    )
    db_session.add(another_user)
    db_session.commit()
    db_session.refresh(another_user)

    store_not_owned = Store(
        name="Not My Store to Toggle",
        platform="woocommerce",
        owner_id=another_user.id,
        shop_domain="notmystoretotoggle.com",
    )
    db_session.add(store_not_owned)
    db_session.commit()
    db_session.refresh(store_not_owned)

    response = client.post(
        f"/api/stores/{store_not_owned.id}/toggle-activation",
        headers=authenticated_user,
    )
    assert response.status_code == 404


def test_get_store_mappings_shopify(client: TestClient, authenticated_user: dict, test_store: Store, mock_shopify_mappings):
    # Temporarily patch the shopify_mappings module
    import routers.stores
    routers.stores.shopify_mappings = mock_shopify_mappings

    response = client.get(
        f"/api/stores/mappings/{test_store.id}",
        headers=authenticated_user,
    )
    assert response.status_code == 200
    data = response.json()
    assert data["store_type"] == "shopify"
    assert len(data["mutations"]) == 1
    assert data["mutations"][0]["name"] == "productCreate"
    assert len(data["queries"]) == 1
    assert data["queries"][0]["name"] == "products"

    # Restore original module
    import importlib
    importlib.reload(routers.stores)


def test_get_store_mappings_not_found(client: TestClient, authenticated_user: dict):
    response = client.get(
        "/api/stores/mappings/999",  # Non-existent ID
        headers=authenticated_user,
    )
    assert response.status_code == 404


def test_get_store_mappings_not_owned(client: TestClient, db_session: Session, authenticated_user: dict):
    # Create a store owned by a different user
    another_user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        full_name="Mappings User",
    )
    db_session.add(another_user)
    db_session.commit()
    db_session.refresh(another_user)

    store_not_owned = Store(
        name="Not My Store for Mappings",
        platform="shopify",
        owner_id=another_user.id,
        shop_domain="notmystoreformappings.myshopify.com",
    )
    db_session.add(store_not_owned)
    db_session.commit()
    db_session.refresh(store_not_owned)

    response = client.get(
        f"/api/stores/mappings/{store_not_owned.id}",
        headers=authenticated_user,
    )
    assert response.status_code == 404


def test_get_store_mappings_unsupported_platform(client: TestClient, authenticated_user: dict, db_session: Session, authenticated_user_id: int):
    store_woocommerce = Store(
        name="WooCommerce Store",
        platform="woocommerce",
        owner_id=authenticated_user_id,
        shop_domain="woo.com",
    )
    db_session.add(store_woocommerce)
    db_session.commit()
    db_session.refresh(store_woocommerce)

    response = client.get(
        f"/api/stores/mappings/{store_woocommerce.id}",
        headers=authenticated_user,
    )
    assert response.status_code == 404
    assert "Store type not supported for mappings." in response.json()["detail"]
