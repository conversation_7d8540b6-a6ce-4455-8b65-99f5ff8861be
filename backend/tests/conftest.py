import os
import sys

import httpx
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# Add project root to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from config import settings  # Import settings first

settings.testing = True  # Set testing mode before importing database

from database import Base, get_db, engine, SessionLocal  # Import engine and SessionLocal
from main import app
from models.models import User


@pytest.fixture(scope="session")
def db_engine():
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def db_session(db_engine):
    connection = db_engine.connect()
    transaction = connection.begin()
    session = SessionLocal(bind=connection)
    yield session
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function")
def client(db_session):
    app.dependency_overrides[get_db] = lambda: db_session
    with TestClient(app) as c:
        yield c


@pytest.fixture(scope="function")
async def async_client(db_session):
    app.dependency_overrides[get_db] = lambda: db_session
    async with httpx.AsyncClient(transport=httpx.ASGITransport(app=app), base_url="http://test") as c:
        yield c


from models.models import Store
from routers.auth import get_password_hash


@pytest.fixture(scope="function")
def test_user(db_session):
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("testpassword"),
        full_name="Test User",
        is_active=True,
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def authenticated_user(client, test_user):
    """Create and authenticate a test user"""
    login_response = client.post(
        "/api/auth/login",
        json={"email": "<EMAIL>", "password": "testpassword"},
    )

    # Check if the login was successful before proceeding
    if login_response.status_code != 200:
        # Attempt to register the user if login fails, assuming it's a first-run scenario
        client.post(
            "/api/auth/register",
            json={
                "email": "<EMAIL>",
                "password": "testpassword",
                "full_name": "Test User",
            },
        )
        # Retry login
        login_response = client.post(
            "/api/auth/login",
            json={"email": "<EMAIL>", "password": "testpassword"},
        )

    assert login_response.status_code == 200, f"Failed to log in: {login_response.text}"

    token = login_response.json().get("access_token")
    assert token is not None, "Access token not found in login response"

    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def authenticated_user_id(test_user):
    return test_user.id


@pytest.fixture
def test_store(db_session, authenticated_user_id):
    store = Store(
        id=1,
        name="Test Store",
        platform="shopify",
        owner_id=authenticated_user_id,
        admin_access_token="test_token",
        storefront_access_token="test_token",
    )
    db_session.add(store)
    db_session.commit()
    return store
