import asyncio
import importlib.util
import os
import sys
from logging.config import fileConfig

from sqlalchemy.ext.asyncio import create_async_engine

from alembic import context
from core.config import Settings
from core.db.database import Base

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support


def import_models_from_modules(base_path: str):
    """
    Dynamically imports all 'models.py' files from subdirectories of the given base path.
    """
    src_path = os.path.join(base_path, "src")
    modules_dir = os.path.join(src_path, "modules")
    if not os.path.isdir(modules_dir):
        return

    for module_name in os.listdir(modules_dir):
        module_path = os.path.join(modules_dir, module_name)
        if os.path.isdir(module_path):
            models_file = os.path.join(module_path, "models.py")
            if os.path.isfile(models_file):
                module_full_name = f"modules.{module_name}.models"
                if module_full_name in sys.modules:
                    # Module already imported, skip to prevent re-definition
                    continue

                # Create a module spec
                spec = importlib.util.spec_from_file_location(
                    module_full_name, models_file
                )
                if spec and spec.loader:
                    # Create a new module
                    module = importlib.util.module_from_spec(spec)
                    # Add to sys.modules
                    sys.modules[module_full_name] = module
                    # Execute the module
                    spec.loader.exec_module(module)


# Import all models dynamically
import_models_from_modules(os.path.dirname(os.path.dirname(__file__)))


settings = Settings()
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection):
    context.configure(connection=connection, target_metadata=target_metadata)

    with context.begin_transaction():
        context.run_migrations()


async def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    configuration = config.get_section(config.config_ini_section)
    connectable = create_async_engine(configuration["sqlalchemy.url"])

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)


if context.is_offline_mode():
    run_migrations_offline()
else:
    asyncio.run(run_migrations_online())
