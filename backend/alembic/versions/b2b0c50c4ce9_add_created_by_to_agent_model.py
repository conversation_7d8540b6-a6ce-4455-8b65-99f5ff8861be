"""Add created_by to Agent model

Revision ID: b2b0c50c4ce9
Revises: 4ae8cef6bce8
Create Date: 2025-08-21 22:55:16.848574

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "b2b0c50c4ce9"
down_revision: Union[str, None] = "4ae8cef6bce8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("agents", sa.Column("created_by", sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("agents", "created_by")
    # ### end Alembic commands ###
