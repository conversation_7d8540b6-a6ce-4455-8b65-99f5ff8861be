"""Me<PERSON> heads

Revision ID: bfe89bde1914
Revises: 6607e10aa23e, add_phone_number_agent_routing
Create Date: 2025-08-21 22:53:51.248881

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "bfe89bde1914"
down_revision: Union[str, None] = ("6607e10aa23e", "add_phone_number_agent_routing")
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
