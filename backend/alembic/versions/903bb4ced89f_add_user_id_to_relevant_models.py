"""Add user_id to relevant models

Revision ID: 903bb4ced89f
Revises: b2b0c50c4ce9
Create Date: 2025-08-22 00:06:00.337132

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "903bb4ced89f"
down_revision: Union[str, None] = "b2b0c50c4ce9"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("agents", "created_by", existing_type=sa.INTEGER(), nullable=False)
    op.create_foreign_key(None, "agents", "users", ["created_by"], ["id"])
    op.add_column("call_history", sa.Column("user_id", sa.Integer(), nullable=True))
    op.create_foreign_key(None, "call_history", "users", ["user_id"], ["id"])
    op.add_column(
        "conversation_logs", sa.Column("user_id", sa.Integer(), nullable=True)
    )
    op.create_foreign_key(None, "conversation_logs", "users", ["user_id"], ["id"])
    op.add_column("customers", sa.Column("user_id", sa.Integer(), nullable=True))
    op.create_foreign_key(None, "customers", "users", ["user_id"], ["id"])
    op.add_column("documents", sa.Column("user_id", sa.Integer(), nullable=True))
    op.create_foreign_key(None, "documents", "users", ["user_id"], ["id"])
    op.add_column("embeddings", sa.Column("user_id", sa.Integer(), nullable=True))
    op.create_foreign_key(None, "embeddings", "users", ["user_id"], ["id"])
    op.add_column("jobs", sa.Column("user_id", sa.Integer(), nullable=True))
    op.create_foreign_key(None, "jobs", "users", ["user_id"], ["id"])
    op.add_column(
        "performance_metrics", sa.Column("user_id", sa.Integer(), nullable=True)
    )
    op.create_foreign_key(None, "performance_metrics", "users", ["user_id"], ["id"])
    op.add_column("phone_numbers", sa.Column("user_id", sa.Integer(), nullable=True))
    op.create_foreign_key(None, "phone_numbers", "users", ["user_id"], ["id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "phone_numbers", type_="foreignkey")
    op.drop_column("phone_numbers", "user_id")
    op.drop_constraint(None, "performance_metrics", type_="foreignkey")
    op.drop_column("performance_metrics", "user_id")
    op.drop_constraint(None, "jobs", type_="foreignkey")
    op.drop_column("jobs", "user_id")
    op.drop_constraint(None, "embeddings", type_="foreignkey")
    op.drop_column("embeddings", "user_id")
    op.drop_constraint(None, "documents", type_="foreignkey")
    op.drop_column("documents", "user_id")
    op.drop_constraint(None, "customers", type_="foreignkey")
    op.drop_column("customers", "user_id")
    op.drop_constraint(None, "conversation_logs", type_="foreignkey")
    op.drop_column("conversation_logs", "user_id")
    op.drop_constraint(None, "call_history", type_="foreignkey")
    op.drop_column("call_history", "user_id")
    op.drop_constraint(None, "agents", type_="foreignkey")
    op.alter_column("agents", "created_by", existing_type=sa.INTEGER(), nullable=True)
    # ### end Alembic commands ###
