"""Add category to Agent model

Revision ID: 4ae8cef6bce8
Revises: bfe89bde1914
Create Date: 2025-08-21 22:54:02.390720

"""

from typing import Sequence, Union

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "4ae8cef6bce8"
down_revision: Union[str, None] = "bfe89bde1914"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_test_prompts_id"), table_name="test_prompts")
    op.drop_index(op.f("ix_test_prompts_name"), table_name="test_prompts")
    op.drop_table("test_prompts")
    op.add_column("agents", sa.Column("category", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("agents", "category")
    op.create_table(
        "test_prompts",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("phone_number", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("system_prompt", sa.TEXT(), autoincrement=False, nullable=False),
        sa.Column("first_message", sa.TEXT(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("test_prompts_pkey")),
    )
    op.create_index(
        op.f("ix_test_prompts_name"), "test_prompts", ["name"], unique=False
    )
    op.create_index(op.f("ix_test_prompts_id"), "test_prompts", ["id"], unique=False)
    # ### end Alembic commands ###
