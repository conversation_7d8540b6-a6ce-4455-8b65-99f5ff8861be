"""Add agent routing to phone numbers

Revision ID: add_phone_number_agent_routing
Revises:
Create Date: 2025-01-21 10:00:00.000000

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "add_phone_number_agent_routing"
down_revision = "11687ed232b6"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add new columns to phone_numbers table
    op.add_column(
        "phone_numbers", sa.Column("default_agent_id", sa.Integer(), nullable=True)
    )
    op.add_column(
        "phone_numbers", sa.Column("routing_config", sa.JSON(), nullable=True)
    )
    op.add_column(
        "phone_numbers",
        sa.Column("is_active", sa.<PERSON>(), nullable=False, server_default="true"),
    )
    op.add_column(
        "phone_numbers", sa.Column("business_hours", sa.JSON(), nullable=True)
    )
    op.add_column(
        "phone_numbers", sa.Column("after_hours_agent_id", sa.Integer(), nullable=True)
    )
    op.add_column(
        "phone_numbers",
        sa.Column("max_queue_time", sa.Integer(), nullable=True, server_default="300"),
    )

    # Add foreign key constraints
    op.create_foreign_key(
        "fk_phone_numbers_default_agent_id",
        "phone_numbers",
        "agents",
        ["default_agent_id"],
        ["id"],
    )
    op.create_foreign_key(
        "fk_phone_numbers_after_hours_agent_id",
        "phone_numbers",
        "agents",
        ["after_hours_agent_id"],
        ["id"],
    )


def downgrade() -> None:
    # Drop foreign key constraints
    op.drop_constraint(
        "fk_phone_numbers_after_hours_agent_id", "phone_numbers", type_="foreignkey"
    )
    op.drop_constraint(
        "fk_phone_numbers_default_agent_id", "phone_numbers", type_="foreignkey"
    )

    # Drop columns
    op.drop_column("phone_numbers", "max_queue_time")
    op.drop_column("phone_numbers", "after_hours_agent_id")
    op.drop_column("phone_numbers", "business_hours")
    op.drop_column("phone_numbers", "is_active")
    op.drop_column("phone_numbers", "routing_config")
    op.drop_column("phone_numbers", "default_agent_id")
