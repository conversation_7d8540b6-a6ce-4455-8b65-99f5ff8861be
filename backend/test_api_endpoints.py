#!/usr/bin/env python3
"""
Simple test script to verify API endpoints are working correctly.
Run this from the backend directory: python test_api_endpoints.py
"""

import json
import sys
from typing import Any, Dict

import requests

BASE_URL = "http://localhost:8000"


def test_endpoint(
    method: str, endpoint: str, data: Dict[Any, Any] = None, expected_status: int = 200
) -> bool:
    """Test a single API endpoint."""
    url = f"{BASE_URL}{endpoint}"

    try:
        if method.upper() == "GET":
            response = requests.get(url)
        elif method.upper() == "POST":
            response = requests.post(url, json=data)
        else:
            print(f"❌ Unsupported method: {method}")
            return False

        print(f"Testing {method} {endpoint}")
        print(f"Status: {response.status_code}")

        if response.status_code == expected_status:
            print(f"✅ {method} {endpoint} - SUCCESS")
            return True
        else:
            print(
                f"❌ {method} {endpoint} - FAILED (expected {expected_status}, got {response.status_code})"
            )
            if response.text:
                print(f"Response: {response.text[:200]}...")
            return False

    except requests.exceptions.ConnectionError:
        print(f"❌ {method} {endpoint} - CONNECTION ERROR (is the server running?)")
        return False
    except Exception as e:
        print(f"❌ {method} {endpoint} - ERROR: {str(e)}")
        return False


def main():
    """Test all critical API endpoints."""
    print("🚀 Testing Agent Builder API Endpoints")
    print("=" * 50)

    # Test basic health check
    success_count = 0
    total_tests = 0

    # Health check
    total_tests += 1
    if test_endpoint("GET", "/health"):
        success_count += 1

    # UI Config
    total_tests += 1
    if test_endpoint("GET", "/api/ui-config"):
        success_count += 1

    # Node types
    total_tests += 1
    if test_endpoint("GET", "/api/nodes/types"):
        success_count += 1

    # DSPy modules
    total_tests += 1
    if test_endpoint("GET", "/api/agent-builder/dspy/modules"):
        success_count += 1

    # Templates
    total_tests += 1
    if test_endpoint("GET", "/api/agent-builder/templates"):
        success_count += 1

    # Test node validation
    sample_node = {
        "id": "test_node_1",
        "name": "Test LLM Node",
        "type": "llm",
        "system_prompt": "You are a helpful assistant",
        "parameters": {},
        "dependencies": [],
    }

    total_tests += 1
    if test_endpoint("POST", "/api/nodes/validate", sample_node):
        success_count += 1

    # Test workflow validation
    sample_workflow = {
        "id": "test_workflow_1",
        "name": "Test Workflow",
        "description": "A simple test workflow",
        "active": True,
        "agent_type": "single_node",
        "nodes": [sample_node],
        "connections": {},
        "start_node_id": "test_node_1",
    }

    total_tests += 1
    if test_endpoint("POST", "/api/agent-builder/workflows/validate", sample_workflow):
        success_count += 1

    # Test node testing endpoint
    test_input = {"user_message": "Hello, how are you?", "context": {}}

    total_tests += 1
    if test_endpoint(
        "POST",
        "/api/nodes/test",
        {"node_config": sample_node, "test_input": test_input},
    ):
        success_count += 1

    print("\n" + "=" * 50)
    print(f"📊 Test Results: {success_count}/{total_tests} endpoints working")

    if success_count == total_tests:
        print("🎉 All API endpoints are working correctly!")
        return 0
    else:
        print(f"⚠️  {total_tests - success_count} endpoints need attention")
        return 1


if __name__ == "__main__":
    sys.exit(main())
