# Home Service Management Platform

A comprehensive, AI-powered platform designed specifically for home service companies. This platform provides multi-modal AI agents, booking management, customer communication, and comprehensive analytics to help home service businesses scale and improve their operations.

## 🌟 Overview

The Home Service Management Platform is a complete tech solution that combines:

- **AI-Powered Agents**: Multi-modal agents supporting voice (Twilio→STT→LLM→TTS), WebSocket, and chat
- **Dual Agent System**: Switchable between ElevenLabs and Dify providers
- **Company Management**: Multi-tenant system with knowledge base integration
- **Communication Hub**: Phone number to agent mapping, conversation logging, and real-time chat
- **Booking System**: Google Calendar integration with service scheduling
- **Web Integration**: Typeform-like forms with prefill and embeddable chat widgets
- **Analytics Dashboard**: Comprehensive reporting and performance monitoring

## 🏗️ Architecture

### System Components

```text
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   External      │
│   (Next.js)     │◄──►│   (FastAPI)     │◄──►│   Services      │
│                 │    │                 │    │                 │
│ • Dashboard     │    │ • API Server    │    │ • Twilio        │
│ • Agent Builder │    │ • WebSocket     │    │ • ElevenLabs    │
│ • Chat Interface│    │ • Database      │    │ • OpenAI        │
│ • Analytics     │    │ • Auth System   │    │ • Google Cal    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Technology Stack

**Frontend:**
- Next.js 15 with TypeScript
- Tailwind CSS + Shadcn/ui
- Framer Motion animations
- React Query for state management
- WebSocket for real-time communication

**Backend:**
- FastAPI with Python 3.11+
- PostgreSQL database
- Redis for caching
- WebSocket support
- JWT authentication

**AI & Communication:**
- OpenAI GPT models
- ElevenLabs TTS
- Twilio for phone integration
- WebRTC for browser calls
- Custom STT/TTS pipeline

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- Python 3.11+
- PostgreSQL 14+
- Redis 6+

### Installation

1. **Clone the repository:**

```bash
git clone <repository-url>
cd home-service-platform
```

2. **Set up the backend:**

```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. **Set up the frontend:**

```bash
cd frontend
npm install
```

4. **Configure environment variables:**

Backend (`.env`):
```env
DATABASE_URL=postgresql://user:password@localhost/homeservice
REDIS_URL=redis://localhost:6379
SECRET_KEY=your-secret-key
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
ELEVENLABS_API_KEY=your-elevenlabs-key
OPENAI_API_KEY=your-openai-key
```

Frontend (`.env.local`):
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
```

5. **Start the services:**

Backend:
```bash
cd backend
uvicorn src.main:app --reload --port 8000
```

Frontend:
```bash
cd frontend
npm run dev
```

6. **Access the application:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## 📋 Features

### 🤖 AI Agent Management
- **Professional Agent Builder**: Visual drag-and-drop interface
- **Multi-Modal Support**: Voice, chat, and phone integration
- **Agent Templates**: Pre-built templates for common scenarios
- **Real-Time Testing**: Live agent testing and debugging
- **Performance Analytics**: Comprehensive agent performance metrics

### 🏢 Company Management
- **Multi-Tenant Architecture**: Support for multiple companies
- **Knowledge Base**: Company-specific knowledge management
- **User Roles**: Role-based access control
- **Branding**: Custom branding per company
- **Configuration**: Self-serve configuration interface

### 📞 Communication Features
- **Call Handling**: Professional call management interface
- **Conversation Logging**: Complete conversation history
- **Chat Plugin**: Embeddable chat widget for websites
- **Web Forms**: Advanced form builder with prefill capabilities
- **Phone Routing**: Intelligent phone number to agent mapping

### 📅 Booking & Scheduling
- **Google Calendar Integration**: Seamless appointment scheduling
- **Service Management**: Comprehensive service catalog
- **Customer Management**: Complete CRM functionality
- **Appointment Tracking**: Real-time booking status
- **Automated Reminders**: SMS and email reminders

### 📊 Analytics & Reporting
- **Real-Time Dashboard**: Live KPI tracking
- **Performance Metrics**: Agent and system performance
- **Revenue Analytics**: Financial tracking and reporting
- **Customer Satisfaction**: Rating and feedback management
- **Custom Reports**: Configurable reporting system

## 📁 Project Structure

```text
home-service-platform/
├── backend/                 # FastAPI backend
│   ├── src/
│   │   ├── core/           # Core functionality
│   │   ├── modules/        # Feature modules
│   │   └── main.py         # Application entry point
│   ├── requirements.txt    # Python dependencies
│   └── README.md          # Backend documentation
├── frontend/               # Next.js frontend
│   ├── src/
│   │   ├── app/           # Next.js app router
│   │   ├── components/    # React components
│   │   ├── contexts/      # React contexts
│   │   └── services/      # API services
│   ├── package.json       # Node.js dependencies
│   └── README.md         # Frontend documentation
├── landing/               # Marketing landing page
└── README.md             # This file
```

## 🔧 Development

### Backend Development
```bash
cd backend
source venv/bin/activate
pip install -r requirements-dev.txt
uvicorn src.main:app --reload --port 8000
```

### Frontend Development
```bash
cd frontend
npm run dev
```

### Database Migrations
```bash
cd backend
alembic upgrade head
```

## 🧪 Testing

### Backend Tests
```bash
cd backend
pytest
```

### Frontend Tests
```bash
cd frontend
npm run test
```

## 🚀 Deployment

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d
```

### Production Deployment
1. **Set production environment variables**
2. **Build frontend**: `npm run build`
3. **Set up reverse proxy** (Nginx recommended)
4. **Configure SSL certificates**
5. **Set up monitoring and logging**

## 📚 Documentation

- [Frontend Documentation](frontend/README.md)
- [Backend Documentation](backend/README.md)
- [API Documentation](http://localhost:8000/docs) (when running)

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the component documentation
- **Issues**: GitHub Issues for bug reports
- **Community**: Join our Discord community
- **Enterprise**: Contact us for enterprise support

---

**Empowering home service companies with AI-driven technology**
