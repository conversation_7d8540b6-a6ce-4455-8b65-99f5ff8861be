#!/bin/bash
set -e

export POSTGRES_USER='user'

# Wait for PostgreSQL to be ready
until pg_isready -h localhost -p 5432 -U "$POSTGRES_USER"; do
  echo "Waiting for postgres..."
  sleep 1
done

echo "Creating databases: homeservice, n8n, and test_db"

# Function to create a database if it doesn't exist
create_database_if_not_exists() {
  DB_NAME=$1
  if ! PGPASSWORD="password" psql -h localhost -p 5432 -U "$POSTGRES_USER" -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
    echo "Creating database $DB_NAME..."
    PGPASSWORD="password" psql -h localhost -p 5432 -U "$POSTGRES_USER" -d postgres -c "CREATE DATABASE $DB_NAME;"
  else
    echo "Database $DB_NAME already exists. Skipping creation."
  fi
}

create_database_if_not_exists homeservice
PGPASSWORD="password" psql -h localhost -p 5432 -U "$POSTGRES_USER" -d homeservice -c "CREATE EXTENSION IF NOT EXISTS vector;"
create_database_if_not_exists n8n
create_database_if_not_exists test_db
